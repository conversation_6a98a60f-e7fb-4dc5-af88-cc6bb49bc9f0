{"ast": null, "code": "// OpenRouter API Service for Knowledge Tree Generation\nimport webSearchService from './webSearchService';\nconst OPENROUTER_API_KEY = process.env.REACT_APP_OPENROUTER_API_KEY || 'your-api-key-here';\nconst OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';\nconst MODEL = process.env.REACT_APP_OPENROUTER_MODEL || 'deepseek/deepseek-r1-0528:free';\n\n// Site configuration for OpenRouter rankings\nconst SITE_CONFIG = {\n  'HTTP-Referer': process.env.REACT_APP_SITE_URL || 'http://localhost:3000',\n  'X-Title': process.env.REACT_APP_SITE_NAME || 'Knowledge Tree Explorer'\n};\nclass OpenRouterClient {\n  constructor() {\n    this.baseURL = OPENROUTER_BASE_URL;\n    this.apiKey = OPENROUTER_API_KEY;\n\n    // Debug logging\n    if (process.env.REACT_APP_DEBUG_MODE === 'true') {\n      console.log('🔧 OpenRouter Client initialized:');\n      console.log('- Base URL:', this.baseURL);\n      console.log('- Model:', MODEL);\n      console.log('- API Key:', this.apiKey ? `${this.apiKey.substring(0, 10)}...` : 'NOT SET');\n      console.log('- Site Config:', SITE_CONFIG);\n    }\n  }\n  async makeRequest(messages, temperature = 0.7) {\n    try {\n      const response = await fetch(`${this.baseURL}/chat/completions`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${this.apiKey}`,\n          'Content-Type': 'application/json',\n          ...SITE_CONFIG\n        },\n        body: JSON.stringify({\n          model: MODEL,\n          messages,\n          temperature,\n          max_tokens: 1500,\n          // Optimizat pentru răspunsuri mai rapide\n          stream: false\n        })\n      });\n      if (!response.ok) {\n        var _errorData$error;\n        const errorData = await response.json().catch(() => ({}));\n        throw new Error(`OpenRouter API Error: ${response.status} - ${((_errorData$error = errorData.error) === null || _errorData$error === void 0 ? void 0 : _errorData$error.message) || 'Unknown error'}`);\n      }\n      const data = await response.json();\n      return data.choices[0].message.content;\n    } catch (error) {\n      console.error('OpenRouter API request failed:', error);\n      throw error;\n    }\n  }\n}\nconst client = new OpenRouterClient();\n\n// Generate Knowledge Tree from topic\nexport async function generateKnowledgeTree(topic, language = 'en') {\n  // Get current language from localStorage if not provided\n  const currentLang = language || localStorage.getItem('language') || 'en';\n  const prompts = {\n    ro: `Analizează cu atenție subiectul \"${topic}\" și creează un arbore de cunoștințe FOARTE SPECIFIC și RELEVANT pentru acest domeniu exact.\n\nIMPORTANT: Generează ramuri care sunt DIRECT LEGATE de \"${topic}\", nu concepte generale!\n\nReturnează DOAR un obiect JSON valid cu această structură exactă:\n{\n  \"tema\": \"${topic}\",\n  \"ramuri\": [\n    {\n      \"nume\": \"Nume Ramură Specifică\",\n      \"descriere\": \"Descriere scurtă și precisă\",\n      \"emoji\": \"📚\",\n      \"subcategorii\": [\"subcategorie1\", \"subcategorie2\", \"subcategorie3\"]\n    }\n  ]\n}\n\nCerințe STRICTE:\n- Generează 6-8 ramuri principale SPECIFICE pentru \"${topic}\"\n- Fiecare ramură TREBUIE să fie direct legată de subiectul principal\n- Emoji-uri relevante pentru fiecare ramură\n- 3-4 subcategorii specifice pentru fiecare ramură\n- Descrieri de maxim 1 propoziție\n- Focalizează-te pe aspectele PRACTICE și APLICABILE\n- Evită conceptele generale sau irelevante\n\nSubiect: ${topic}`,\n    en: `Analyze the subject \"${topic}\" carefully and create a VERY SPECIFIC and RELEVANT knowledge tree for this exact domain.\n\nIMPORTANT: Generate branches that are DIRECTLY RELATED to \"${topic}\", not general concepts!\n\nReturn ONLY a valid JSON object with this exact structure:\n{\n  \"tema\": \"${topic}\",\n  \"ramuri\": [\n    {\n      \"nume\": \"Specific Branch Name\",\n      \"descriere\": \"Short and precise description\",\n      \"emoji\": \"📚\",\n      \"subcategorii\": [\"subcategory1\", \"subcategory2\", \"subcategory3\"]\n    }\n  ]\n}\n\nSTRICT Requirements:\n- Generate 6-8 main branches SPECIFIC to \"${topic}\"\n- Each branch MUST be directly related to the main subject\n- Relevant emojis for each branch\n- 3-4 specific subcategories for each branch\n- Descriptions of maximum 1 sentence\n- Focus on PRACTICAL and APPLICABLE aspects\n- Avoid general or irrelevant concepts\n\nSubject: ${topic}`\n  };\n  const prompt = prompts[currentLang] || prompts.en;\n  try {\n    const response = await client.makeRequest([{\n      role: 'system',\n      content: 'Expert în organizarea cunoștințelor. Generează arbori de cunoștințe specifici în format JSON valid. Răspunde DOAR cu JSON, fără text explicativ.'\n    }, {\n      role: 'user',\n      content: prompt\n    }], 0.3); // Temperatură mai mică pentru răspunsuri mai consistente și rapide\n\n    // Parse and validate the JSON response\n    const cleanResponse = response.trim();\n    const jsonMatch = cleanResponse.match(/\\{[\\s\\S]*\\}/);\n    if (!jsonMatch) {\n      throw new Error('No valid JSON found in response');\n    }\n    const tree = JSON.parse(jsonMatch[0]);\n\n    // Validate structure\n    if (!tree.tema || !Array.isArray(tree.ramuri)) {\n      throw new Error('Invalid tree structure');\n    }\n    return tree;\n  } catch (error) {\n    console.error('Error generating knowledge tree:', error);\n\n    // Fallback tree structure\n    return {\n      tema: topic,\n      ramuri: [{\n        nume: \"Fundamentals\",\n        descriere: `Basic concepts and principles of ${topic}`,\n        emoji: \"📚\",\n        subcategorii: [\"Core Concepts\", \"Key Principles\", \"Basic Theory\"]\n      }, {\n        nume: \"Applications\",\n        descriere: `Practical applications and use cases of ${topic}`,\n        emoji: \"🔧\",\n        subcategorii: [\"Real-world Uses\", \"Industry Applications\", \"Case Studies\"]\n      }, {\n        nume: \"Advanced Topics\",\n        descriere: `Complex and specialized aspects of ${topic}`,\n        emoji: \"🎓\",\n        subcategorii: [\"Expert Level\", \"Research Areas\", \"Cutting Edge\"]\n      }]\n    };\n  }\n}\n\n// Generate Article for specific branch\nexport async function generateArticle(topic, branch, flags = ['-a']) {\n  var _branch$subcategorii;\n  const flagInstructions = {\n    // Basic flags cu prompt-uri FOARTE SPECIFICE\n    '-a': 'Scrie un articol informativ standard (400-600 cuvinte) cu structură clară: introducere, dezvoltare cu 3-4 secțiuni principale, și concluzie.',\n    '-t': 'Formatează ÎNTREGUL conținut ca tabele și date structurate. Creează minimum 3 tabele cu informații organizate în coloane și rânduri. Fiecare tabel să aibă titlu și explicații.',\n    '-ex': 'Include EXACT 3 exemple practice detaliate cu explicații pas cu pas. Fiecare exemplu să aibă: context, implementare, rezultat așteptat. Numerotează exemplele 1, 2, 3.',\n    '-p': 'Include OBLIGATORIU exemple de cod și demonstrații tehnice. Minimum 2 blocuri de cod cu explicații linie cu linie. Adaugă comentarii în cod.',\n    '-q': 'Creează EXACT 5 întrebări tip grilă la sfârșitul articolului. Fiecare întrebare să aibă 4 variante (A, B, C, D). Poate fi corectă 1, 2, 3, 4 sau niciuna. Include baremul cu răspunsurile corecte la final.',\n    '-rap': 'Scrie un raport exhaustiv (800-1200 cuvinte) cu acoperire comprehensivă: rezumat executiv, analiză detaliată, concluzii și recomandări.',\n    '-def': 'Focalizează-te pe definiții de nivel expert și terminologie tehnică. Include minimum 10 termeni specializați cu definiții precise.',\n    // Learning & Visualization flags\n    '-path': 'Creează o cale de învățare personalizată cu 5-7 pași concreți, milestone-uri măsurabile și estimări de timp pentru fiecare etapă.',\n    '-vis': 'Generează descrieri detaliate pentru minimum 3 infografice/diagrame. Pentru fiecare diagramă: titlu, elemente vizuale, culori sugerate, și explicația fiecărui element.',\n    '-mind': 'Prezintă informația ca o hartă mentală interactivă cu concepte conectate. Descrie structura: nod central, 5-8 ramuri principale, sub-ramuri și conexiuni între concepte.',\n    '-flow': 'Creează diagrame de flux și procese cu puncte de decizie și rezultate. Include minimum 2 flowchart-uri cu forme geometrice specifice și săgeți directionale.',\n    // Industry-specific flags\n    '-case': 'Include 2-3 studii de caz reale cu rezultate măsurabile și analiză detaliată. Pentru fiecare caz: context, provocări, soluții implementate, rezultate concrete cu cifre.',\n    '-calc': 'Include calculatoare și instrumente interactive pentru calcule cu formule. Prezintă minimum 3 formule matematice cu exemple numerice concrete.',\n    '-game': 'Adaugă elemente de gamificare cu puncte, realizări și competiții. Creează un sistem de punctaj cu 5 nivele și recompense pentru fiecare nivel.',\n    // Localization flags\n    '-ro': 'Adaptează pentru piața românească și legislația locală cu exemple românești. Include referințe la legi românești, companii românești și practici locale specifice.',\n    // Advanced features flags\n    '-auto': 'Focalizează-te pe automatizarea proceselor cu instrumente și pași de implementare. Include minimum 3 instrumente software cu tutorial de configurare.'\n  };\n  const activeFlags = flags.filter(flag => flagInstructions[flag]);\n  const flagText = activeFlags.map(flag => flagInstructions[flag]).join('. ');\n\n  // Special handling for Romanian and localization flags\n  const hasRomanianFlag = flags.includes('-ro');\n  const hasEUFlag = flags.includes('-eu');\n  const hasLocalFlag = flags.includes('-local');\n  let localizationContext = '';\n  if (hasRomanianFlag) {\n    localizationContext = 'Adaptează conținutul pentru piața românească, include legislația locală, exemple de companii românești și practici specifice României.';\n  } else if (hasEUFlag) {\n    localizationContext = 'Focus on European Union regulations, GDPR compliance, EU market practices, and European case studies.';\n  } else if (hasLocalFlag) {\n    localizationContext = 'Include local examples, regional practices, and area-specific considerations.';\n  }\n\n  // Special handling for advanced features\n  const hasAdvancedFlags = flags.some(flag => ['-auto', '-predict', '-optimize'].includes(flag));\n  let advancedContext = '';\n  if (hasAdvancedFlags) {\n    advancedContext = 'Include specific tools, software recommendations, implementation steps, and measurable outcomes.';\n  }\n\n  // Search for web sources\n  let webSources = [];\n  let sourcesContext = '';\n  try {\n    console.log('🔍 Searching for web sources for:', branch.nume);\n    webSources = await webSearchService.searchSources(branch.nume, 5);\n    console.log('✅ Found sources:', webSources.length, 'sources');\n    if (webSources.length > 0) {\n      sourcesContext = `\\n\\nIMPORTANT: Include these web sources in the article:\\n${webSources.map(source => `- ${source.title}: ${source.description} (${source.source})`).join('\\n')}\\n\\nMake sure to reference these sources in the content and add a \"Sources & Further Reading\" section at the end.`;\n    }\n  } catch (error) {\n    console.warn('⚠️ Web search failed, continuing without sources:', error);\n  }\n  const prompt = `Generate a comprehensive, well-structured article about \"${branch.nume}\" in the context of \"${topic}\".\n\nArticle Requirements:\n${flagText || flagInstructions['-a']}\n\n${localizationContext ? `Localization Requirements: ${localizationContext}` : ''}\n${advancedContext ? `Advanced Features: ${advancedContext}` : ''}\n${sourcesContext}\n\nCRITICAL FORMATTING RULES:\n- Use CLEAN text without asterisks (*) or special formatting symbols\n- Write in plain, readable markdown format\n- Use proper headings (# ## ###) instead of asterisks\n- Ensure content is EXACTLY 600-800 words for optimal learning\n- Structure in logical sections for easy comprehension\n- Include practical examples and actionable insights\n\nContent Guidelines:\n- Write in an engaging, educational style optimized for rapid learning\n- Use clear headings and structure with clean markdown formatting\n- Include relevant examples and explanations for better understanding\n- Apply proven learning techniques: spaced repetition, active recall, chunking\n- Target length: EXACTLY 600-800 words for optimal learning retention\n- If -rap flag is used, extend to 1000-1200 words with comprehensive coverage\n- Structure content in digestible sections of 150-200 words each\n- Use bullet points and numbered lists for better information processing\n- If visualization flags are used, describe diagrams and charts in detail\n- If interactive flags are used, provide step-by-step instructions\n- If Romanian flag is used, write in Romanian language\n- IMPORTANT: Do NOT use asterisks (*) or other formatting symbols in the text\n- Use clean, readable text without special characters for formatting\n\nReturn ONLY a valid JSON object with this structure:\n{\n  \"titlu\": \"Article Title\",\n  \"continut\": \"Full article content with proper markdown formatting\",\n  \"subcategorii\": [\"related topic 1\", \"related topic 2\", \"related topic 3\"],\n  \"flags\": ${JSON.stringify(flags)},\n  \"pozitie\": \"${topic} → ${branch.nume}\",\n  \"estimatedReadTime\": \"X minutes\",\n  \"difficulty\": \"Beginner|Intermediate|Advanced\",\n  \"practicalValue\": \"High|Medium|Low\"\n}\n\nTopic Context: ${topic}\nBranch: ${branch.nume}\nBranch Description: ${branch.descriere}\nSubcategories: ${((_branch$subcategorii = branch.subcategorii) === null || _branch$subcategorii === void 0 ? void 0 : _branch$subcategorii.join(', ')) || 'None'}\nSelected Flags: ${flags.join(' ')}`;\n  try {\n    const response = await client.makeRequest([{\n      role: 'system',\n      content: 'You are an expert content writer and educator. Generate comprehensive, well-structured articles in valid JSON format only. Do not include any explanatory text outside the JSON.'\n    }, {\n      role: 'user',\n      content: prompt\n    }], 0.8);\n\n    // Parse and validate the JSON response\n    const cleanResponse = response.trim();\n    const jsonMatch = cleanResponse.match(/\\{[\\s\\S]*\\}/);\n    if (!jsonMatch) {\n      throw new Error('No valid JSON found in response');\n    }\n    const article = JSON.parse(jsonMatch[0]);\n\n    // Validate structure\n    if (!article.titlu || !article.continut) {\n      throw new Error('Invalid article structure');\n    }\n\n    // Add web sources to the article\n    if (webSources.length > 0) {\n      const sourcesHTML = webSearchService.formatSourcesHTML(webSources);\n      article.continut += '\\n\\n' + sourcesHTML;\n      article.webSources = webSources;\n    }\n    return article;\n  } catch (error) {\n    console.error('Error generating article:', error);\n\n    // Fallback article\n    return {\n      titlu: `${branch.nume} - ${topic}`,\n      continut: `# ${branch.nume}\n\nThis section explores ${branch.nume} in the context of ${topic}.\n\n## Overview\n${branch.descriere}\n\n## Key Concepts\nUnderstanding ${branch.nume} is essential for mastering ${topic}. This area covers fundamental principles and practical applications.\n\n## Applications\nThe concepts in ${branch.nume} have wide-ranging applications across various domains and industries.\n\n## Further Learning\nTo deepen your understanding, consider exploring related topics and practical exercises.`,\n      subcategorii: branch.subcategorii || [],\n      flags: flags,\n      pozitie: `${topic} → ${branch.nume}`\n    };\n  }\n}\n\n// Test API connection\nexport async function testConnection() {\n  try {\n    const response = await client.makeRequest([{\n      role: 'user',\n      content: 'Hello, please respond with \"API connection successful\"'\n    }]);\n    return response.includes('successful');\n  } catch (error) {\n    console.error('API connection test failed:', error);\n    return false;\n  }\n}", "map": {"version": 3, "names": ["webSearchService", "OPENROUTER_API_KEY", "process", "env", "REACT_APP_OPENROUTER_API_KEY", "OPENROUTER_BASE_URL", "MODEL", "REACT_APP_OPENROUTER_MODEL", "SITE_CONFIG", "REACT_APP_SITE_URL", "REACT_APP_SITE_NAME", "OpenRouterClient", "constructor", "baseURL", "<PERSON><PERSON><PERSON><PERSON>", "REACT_APP_DEBUG_MODE", "console", "log", "substring", "makeRequest", "messages", "temperature", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "model", "max_tokens", "stream", "ok", "_errorData$error", "errorData", "json", "catch", "Error", "status", "error", "message", "data", "choices", "content", "client", "generateKnowledgeTree", "topic", "language", "currentLang", "localStorage", "getItem", "prompts", "ro", "en", "prompt", "role", "cleanResponse", "trim", "jsonMatch", "match", "tree", "parse", "tema", "Array", "isArray", "<PERSON><PERSON>", "nume", "desc<PERSON><PERSON>", "emoji", "subcategorii", "generateArticle", "branch", "flags", "_branch$subcategorii", "flagInstructions", "activeFlags", "filter", "flag", "flagText", "map", "join", "hasRomanianFlag", "includes", "hasEUFlag", "hasLocalFlag", "localizationContext", "hasAdvancedFlags", "some", "advancedContext", "webSources", "sourcesContext", "searchSources", "length", "source", "title", "description", "warn", "article", "titlu", "continut", "sourcesHTML", "formatSourcesHTML", "pozitie", "testConnection"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Appv1/src/services/openRouterService.js"], "sourcesContent": ["// OpenRouter API Service for Knowledge Tree Generation\nimport webSearchService from './webSearchService';\n\nconst OPENROUTER_API_KEY = process.env.REACT_APP_OPENROUTER_API_KEY || 'your-api-key-here';\nconst OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';\nconst MODEL = process.env.REACT_APP_OPENROUTER_MODEL || 'deepseek/deepseek-r1-0528:free';\n\n// Site configuration for OpenRouter rankings\nconst SITE_CONFIG = {\n  'HTTP-Referer': process.env.REACT_APP_SITE_URL || 'http://localhost:3000',\n  'X-Title': process.env.REACT_APP_SITE_NAME || 'Knowledge Tree Explorer'\n};\n\nclass OpenRouterClient {\n  constructor() {\n    this.baseURL = OPENROUTER_BASE_URL;\n    this.apiKey = OPENROUTER_API_KEY;\n\n    // Debug logging\n    if (process.env.REACT_APP_DEBUG_MODE === 'true') {\n      console.log('🔧 OpenRouter Client initialized:');\n      console.log('- Base URL:', this.baseURL);\n      console.log('- Model:', MODEL);\n      console.log('- API Key:', this.apiKey ? `${this.apiKey.substring(0, 10)}...` : 'NOT SET');\n      console.log('- Site Config:', SITE_CONFIG);\n    }\n  }\n\n  async makeRequest(messages, temperature = 0.7) {\n    try {\n      const response = await fetch(`${this.baseURL}/chat/completions`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${this.apiKey}`,\n          'Content-Type': 'application/json',\n          ...SITE_CONFIG\n        },\n        body: JSON.stringify({\n          model: MODEL,\n          messages,\n          temperature,\n          max_tokens: 1500, // Optimizat pentru răspunsuri mai rapide\n          stream: false\n        })\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        throw new Error(`OpenRouter API Error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);\n      }\n\n      const data = await response.json();\n      return data.choices[0].message.content;\n    } catch (error) {\n      console.error('OpenRouter API request failed:', error);\n      throw error;\n    }\n  }\n}\n\nconst client = new OpenRouterClient();\n\n// Generate Knowledge Tree from topic\nexport async function generateKnowledgeTree(topic, language = 'en') {\n  // Get current language from localStorage if not provided\n  const currentLang = language || localStorage.getItem('language') || 'en';\n\n  const prompts = {\n    ro: `Analizează cu atenție subiectul \"${topic}\" și creează un arbore de cunoștințe FOARTE SPECIFIC și RELEVANT pentru acest domeniu exact.\n\nIMPORTANT: Generează ramuri care sunt DIRECT LEGATE de \"${topic}\", nu concepte generale!\n\nReturnează DOAR un obiect JSON valid cu această structură exactă:\n{\n  \"tema\": \"${topic}\",\n  \"ramuri\": [\n    {\n      \"nume\": \"Nume Ramură Specifică\",\n      \"descriere\": \"Descriere scurtă și precisă\",\n      \"emoji\": \"📚\",\n      \"subcategorii\": [\"subcategorie1\", \"subcategorie2\", \"subcategorie3\"]\n    }\n  ]\n}\n\nCerințe STRICTE:\n- Generează 6-8 ramuri principale SPECIFICE pentru \"${topic}\"\n- Fiecare ramură TREBUIE să fie direct legată de subiectul principal\n- Emoji-uri relevante pentru fiecare ramură\n- 3-4 subcategorii specifice pentru fiecare ramură\n- Descrieri de maxim 1 propoziție\n- Focalizează-te pe aspectele PRACTICE și APLICABILE\n- Evită conceptele generale sau irelevante\n\nSubiect: ${topic}`,\n\n    en: `Analyze the subject \"${topic}\" carefully and create a VERY SPECIFIC and RELEVANT knowledge tree for this exact domain.\n\nIMPORTANT: Generate branches that are DIRECTLY RELATED to \"${topic}\", not general concepts!\n\nReturn ONLY a valid JSON object with this exact structure:\n{\n  \"tema\": \"${topic}\",\n  \"ramuri\": [\n    {\n      \"nume\": \"Specific Branch Name\",\n      \"descriere\": \"Short and precise description\",\n      \"emoji\": \"📚\",\n      \"subcategorii\": [\"subcategory1\", \"subcategory2\", \"subcategory3\"]\n    }\n  ]\n}\n\nSTRICT Requirements:\n- Generate 6-8 main branches SPECIFIC to \"${topic}\"\n- Each branch MUST be directly related to the main subject\n- Relevant emojis for each branch\n- 3-4 specific subcategories for each branch\n- Descriptions of maximum 1 sentence\n- Focus on PRACTICAL and APPLICABLE aspects\n- Avoid general or irrelevant concepts\n\nSubject: ${topic}`\n  };\n\n  const prompt = prompts[currentLang] || prompts.en;\n\n  try {\n    const response = await client.makeRequest([\n      {\n        role: 'system',\n        content: 'Expert în organizarea cunoștințelor. Generează arbori de cunoștințe specifici în format JSON valid. Răspunde DOAR cu JSON, fără text explicativ.'\n      },\n      {\n        role: 'user',\n        content: prompt\n      }\n    ], 0.3); // Temperatură mai mică pentru răspunsuri mai consistente și rapide\n\n    // Parse and validate the JSON response\n    const cleanResponse = response.trim();\n    const jsonMatch = cleanResponse.match(/\\{[\\s\\S]*\\}/);\n    \n    if (!jsonMatch) {\n      throw new Error('No valid JSON found in response');\n    }\n\n    const tree = JSON.parse(jsonMatch[0]);\n    \n    // Validate structure\n    if (!tree.tema || !Array.isArray(tree.ramuri)) {\n      throw new Error('Invalid tree structure');\n    }\n\n    return tree;\n  } catch (error) {\n    console.error('Error generating knowledge tree:', error);\n    \n    // Fallback tree structure\n    return {\n      tema: topic,\n      ramuri: [\n        {\n          nume: \"Fundamentals\",\n          descriere: `Basic concepts and principles of ${topic}`,\n          emoji: \"📚\",\n          subcategorii: [\"Core Concepts\", \"Key Principles\", \"Basic Theory\"]\n        },\n        {\n          nume: \"Applications\",\n          descriere: `Practical applications and use cases of ${topic}`,\n          emoji: \"🔧\",\n          subcategorii: [\"Real-world Uses\", \"Industry Applications\", \"Case Studies\"]\n        },\n        {\n          nume: \"Advanced Topics\",\n          descriere: `Complex and specialized aspects of ${topic}`,\n          emoji: \"🎓\",\n          subcategorii: [\"Expert Level\", \"Research Areas\", \"Cutting Edge\"]\n        }\n      ]\n    };\n  }\n}\n\n// Generate Article for specific branch\nexport async function generateArticle(topic, branch, flags = ['-a']) {\n  const flagInstructions = {\n    // Basic flags cu prompt-uri FOARTE SPECIFICE\n    '-a': 'Scrie un articol informativ standard (400-600 cuvinte) cu structură clară: introducere, dezvoltare cu 3-4 secțiuni principale, și concluzie.',\n\n    '-t': 'Formatează ÎNTREGUL conținut ca tabele și date structurate. Creează minimum 3 tabele cu informații organizate în coloane și rânduri. Fiecare tabel să aibă titlu și explicații.',\n\n    '-ex': 'Include EXACT 3 exemple practice detaliate cu explicații pas cu pas. Fiecare exemplu să aibă: context, implementare, rezultat așteptat. Numerotează exemplele 1, 2, 3.',\n\n    '-p': 'Include OBLIGATORIU exemple de cod și demonstrații tehnice. Minimum 2 blocuri de cod cu explicații linie cu linie. Adaugă comentarii în cod.',\n\n    '-q': 'Creează EXACT 5 întrebări tip grilă la sfârșitul articolului. Fiecare întrebare să aibă 4 variante (A, B, C, D). Poate fi corectă 1, 2, 3, 4 sau niciuna. Include baremul cu răspunsurile corecte la final.',\n\n    '-rap': 'Scrie un raport exhaustiv (800-1200 cuvinte) cu acoperire comprehensivă: rezumat executiv, analiză detaliată, concluzii și recomandări.',\n\n    '-def': 'Focalizează-te pe definiții de nivel expert și terminologie tehnică. Include minimum 10 termeni specializați cu definiții precise.',\n\n    // Learning & Visualization flags\n    '-path': 'Creează o cale de învățare personalizată cu 5-7 pași concreți, milestone-uri măsurabile și estimări de timp pentru fiecare etapă.',\n\n    '-vis': 'Generează descrieri detaliate pentru minimum 3 infografice/diagrame. Pentru fiecare diagramă: titlu, elemente vizuale, culori sugerate, și explicația fiecărui element.',\n\n    '-mind': 'Prezintă informația ca o hartă mentală interactivă cu concepte conectate. Descrie structura: nod central, 5-8 ramuri principale, sub-ramuri și conexiuni între concepte.',\n\n    '-flow': 'Creează diagrame de flux și procese cu puncte de decizie și rezultate. Include minimum 2 flowchart-uri cu forme geometrice specifice și săgeți directionale.',\n\n    // Industry-specific flags\n    '-case': 'Include 2-3 studii de caz reale cu rezultate măsurabile și analiză detaliată. Pentru fiecare caz: context, provocări, soluții implementate, rezultate concrete cu cifre.',\n\n    '-calc': 'Include calculatoare și instrumente interactive pentru calcule cu formule. Prezintă minimum 3 formule matematice cu exemple numerice concrete.',\n\n    '-game': 'Adaugă elemente de gamificare cu puncte, realizări și competiții. Creează un sistem de punctaj cu 5 nivele și recompense pentru fiecare nivel.',\n\n    // Localization flags\n    '-ro': 'Adaptează pentru piața românească și legislația locală cu exemple românești. Include referințe la legi românești, companii românești și practici locale specifice.',\n\n    // Advanced features flags\n    '-auto': 'Focalizează-te pe automatizarea proceselor cu instrumente și pași de implementare. Include minimum 3 instrumente software cu tutorial de configurare.'\n  };\n\n  const activeFlags = flags.filter(flag => flagInstructions[flag]);\n  const flagText = activeFlags.map(flag => flagInstructions[flag]).join('. ');\n\n  // Special handling for Romanian and localization flags\n  const hasRomanianFlag = flags.includes('-ro');\n  const hasEUFlag = flags.includes('-eu');\n  const hasLocalFlag = flags.includes('-local');\n\n  let localizationContext = '';\n  if (hasRomanianFlag) {\n    localizationContext = 'Adaptează conținutul pentru piața românească, include legislația locală, exemple de companii românești și practici specifice României.';\n  } else if (hasEUFlag) {\n    localizationContext = 'Focus on European Union regulations, GDPR compliance, EU market practices, and European case studies.';\n  } else if (hasLocalFlag) {\n    localizationContext = 'Include local examples, regional practices, and area-specific considerations.';\n  }\n\n  // Special handling for advanced features\n  const hasAdvancedFlags = flags.some(flag => ['-auto', '-predict', '-optimize'].includes(flag));\n  let advancedContext = '';\n  if (hasAdvancedFlags) {\n    advancedContext = 'Include specific tools, software recommendations, implementation steps, and measurable outcomes.';\n  }\n\n  // Search for web sources\n  let webSources = [];\n  let sourcesContext = '';\n\n  try {\n    console.log('🔍 Searching for web sources for:', branch.nume);\n    webSources = await webSearchService.searchSources(branch.nume, 5);\n    console.log('✅ Found sources:', webSources.length, 'sources');\n\n    if (webSources.length > 0) {\n      sourcesContext = `\\n\\nIMPORTANT: Include these web sources in the article:\\n${webSources.map(source =>\n        `- ${source.title}: ${source.description} (${source.source})`\n      ).join('\\n')}\\n\\nMake sure to reference these sources in the content and add a \"Sources & Further Reading\" section at the end.`;\n    }\n  } catch (error) {\n    console.warn('⚠️ Web search failed, continuing without sources:', error);\n  }\n\n  const prompt = `Generate a comprehensive, well-structured article about \"${branch.nume}\" in the context of \"${topic}\".\n\nArticle Requirements:\n${flagText || flagInstructions['-a']}\n\n${localizationContext ? `Localization Requirements: ${localizationContext}` : ''}\n${advancedContext ? `Advanced Features: ${advancedContext}` : ''}\n${sourcesContext}\n\nCRITICAL FORMATTING RULES:\n- Use CLEAN text without asterisks (*) or special formatting symbols\n- Write in plain, readable markdown format\n- Use proper headings (# ## ###) instead of asterisks\n- Ensure content is EXACTLY 600-800 words for optimal learning\n- Structure in logical sections for easy comprehension\n- Include practical examples and actionable insights\n\nContent Guidelines:\n- Write in an engaging, educational style optimized for rapid learning\n- Use clear headings and structure with clean markdown formatting\n- Include relevant examples and explanations for better understanding\n- Apply proven learning techniques: spaced repetition, active recall, chunking\n- Target length: EXACTLY 600-800 words for optimal learning retention\n- If -rap flag is used, extend to 1000-1200 words with comprehensive coverage\n- Structure content in digestible sections of 150-200 words each\n- Use bullet points and numbered lists for better information processing\n- If visualization flags are used, describe diagrams and charts in detail\n- If interactive flags are used, provide step-by-step instructions\n- If Romanian flag is used, write in Romanian language\n- IMPORTANT: Do NOT use asterisks (*) or other formatting symbols in the text\n- Use clean, readable text without special characters for formatting\n\nReturn ONLY a valid JSON object with this structure:\n{\n  \"titlu\": \"Article Title\",\n  \"continut\": \"Full article content with proper markdown formatting\",\n  \"subcategorii\": [\"related topic 1\", \"related topic 2\", \"related topic 3\"],\n  \"flags\": ${JSON.stringify(flags)},\n  \"pozitie\": \"${topic} → ${branch.nume}\",\n  \"estimatedReadTime\": \"X minutes\",\n  \"difficulty\": \"Beginner|Intermediate|Advanced\",\n  \"practicalValue\": \"High|Medium|Low\"\n}\n\nTopic Context: ${topic}\nBranch: ${branch.nume}\nBranch Description: ${branch.descriere}\nSubcategories: ${branch.subcategorii?.join(', ') || 'None'}\nSelected Flags: ${flags.join(' ')}`;\n\n  try {\n    const response = await client.makeRequest([\n      {\n        role: 'system',\n        content: 'You are an expert content writer and educator. Generate comprehensive, well-structured articles in valid JSON format only. Do not include any explanatory text outside the JSON.'\n      },\n      {\n        role: 'user',\n        content: prompt\n      }\n    ], 0.8);\n\n    // Parse and validate the JSON response\n    const cleanResponse = response.trim();\n    const jsonMatch = cleanResponse.match(/\\{[\\s\\S]*\\}/);\n    \n    if (!jsonMatch) {\n      throw new Error('No valid JSON found in response');\n    }\n\n    const article = JSON.parse(jsonMatch[0]);\n    \n    // Validate structure\n    if (!article.titlu || !article.continut) {\n      throw new Error('Invalid article structure');\n    }\n\n    // Add web sources to the article\n    if (webSources.length > 0) {\n      const sourcesHTML = webSearchService.formatSourcesHTML(webSources);\n      article.continut += '\\n\\n' + sourcesHTML;\n      article.webSources = webSources;\n    }\n\n    return article;\n  } catch (error) {\n    console.error('Error generating article:', error);\n    \n    // Fallback article\n    return {\n      titlu: `${branch.nume} - ${topic}`,\n      continut: `# ${branch.nume}\n\nThis section explores ${branch.nume} in the context of ${topic}.\n\n## Overview\n${branch.descriere}\n\n## Key Concepts\nUnderstanding ${branch.nume} is essential for mastering ${topic}. This area covers fundamental principles and practical applications.\n\n## Applications\nThe concepts in ${branch.nume} have wide-ranging applications across various domains and industries.\n\n## Further Learning\nTo deepen your understanding, consider exploring related topics and practical exercises.`,\n      subcategorii: branch.subcategorii || [],\n      flags: flags,\n      pozitie: `${topic} → ${branch.nume}`\n    };\n  }\n}\n\n// Test API connection\nexport async function testConnection() {\n  try {\n    const response = await client.makeRequest([\n      {\n        role: 'user',\n        content: 'Hello, please respond with \"API connection successful\"'\n      }\n    ]);\n    \n    return response.includes('successful');\n  } catch (error) {\n    console.error('API connection test failed:', error);\n    return false;\n  }\n}\n"], "mappings": "AAAA;AACA,OAAOA,gBAAgB,MAAM,oBAAoB;AAEjD,MAAMC,kBAAkB,GAAGC,OAAO,CAACC,GAAG,CAACC,4BAA4B,IAAI,mBAAmB;AAC1F,MAAMC,mBAAmB,GAAG,8BAA8B;AAC1D,MAAMC,KAAK,GAAGJ,OAAO,CAACC,GAAG,CAACI,0BAA0B,IAAI,gCAAgC;;AAExF;AACA,MAAMC,WAAW,GAAG;EAClB,cAAc,EAAEN,OAAO,CAACC,GAAG,CAACM,kBAAkB,IAAI,uBAAuB;EACzE,SAAS,EAAEP,OAAO,CAACC,GAAG,CAACO,mBAAmB,IAAI;AAChD,CAAC;AAED,MAAMC,gBAAgB,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,OAAO,GAAGR,mBAAmB;IAClC,IAAI,CAACS,MAAM,GAAGb,kBAAkB;;IAEhC;IACA,IAAIC,OAAO,CAACC,GAAG,CAACY,oBAAoB,KAAK,MAAM,EAAE;MAC/CC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChDD,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAACJ,OAAO,CAAC;MACxCG,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEX,KAAK,CAAC;MAC9BU,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAACH,MAAM,GAAG,GAAG,IAAI,CAACA,MAAM,CAACI,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAAG,SAAS,CAAC;MACzFF,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAET,WAAW,CAAC;IAC5C;EACF;EAEA,MAAMW,WAAWA,CAACC,QAAQ,EAAEC,WAAW,GAAG,GAAG,EAAE;IAC7C,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACV,OAAO,mBAAmB,EAAE;QAC/DW,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,eAAe,EAAE,UAAU,IAAI,CAACX,MAAM,EAAE;UACxC,cAAc,EAAE,kBAAkB;UAClC,GAAGN;QACL,CAAC;QACDkB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,KAAK,EAAEvB,KAAK;UACZc,QAAQ;UACRC,WAAW;UACXS,UAAU,EAAE,IAAI;UAAE;UAClBC,MAAM,EAAE;QACV,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACT,QAAQ,CAACU,EAAE,EAAE;QAAA,IAAAC,gBAAA;QAChB,MAAMC,SAAS,GAAG,MAAMZ,QAAQ,CAACa,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACzD,MAAM,IAAIC,KAAK,CAAC,yBAAyBf,QAAQ,CAACgB,MAAM,MAAM,EAAAL,gBAAA,GAAAC,SAAS,CAACK,KAAK,cAAAN,gBAAA,uBAAfA,gBAAA,CAAiBO,OAAO,KAAI,eAAe,EAAE,CAAC;MAC9G;MAEA,MAAMC,IAAI,GAAG,MAAMnB,QAAQ,CAACa,IAAI,CAAC,CAAC;MAClC,OAAOM,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,CAACF,OAAO,CAACG,OAAO;IACxC,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdvB,OAAO,CAACuB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAMA,KAAK;IACb;EACF;AACF;AAEA,MAAMK,MAAM,GAAG,IAAIjC,gBAAgB,CAAC,CAAC;;AAErC;AACA,OAAO,eAAekC,qBAAqBA,CAACC,KAAK,EAAEC,QAAQ,GAAG,IAAI,EAAE;EAClE;EACA,MAAMC,WAAW,GAAGD,QAAQ,IAAIE,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAI,IAAI;EAExE,MAAMC,OAAO,GAAG;IACdC,EAAE,EAAE,oCAAoCN,KAAK;AACjD;AACA,0DAA0DA,KAAK;AAC/D;AACA;AACA;AACA,aAAaA,KAAK;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsDA,KAAK;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAWA,KAAK,EAAE;IAEdO,EAAE,EAAE,wBAAwBP,KAAK;AACrC;AACA,6DAA6DA,KAAK;AAClE;AACA;AACA;AACA,aAAaA,KAAK;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4CA,KAAK;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAWA,KAAK;EACd,CAAC;EAED,MAAMQ,MAAM,GAAGH,OAAO,CAACH,WAAW,CAAC,IAAIG,OAAO,CAACE,EAAE;EAEjD,IAAI;IACF,MAAM/B,QAAQ,GAAG,MAAMsB,MAAM,CAACzB,WAAW,CAAC,CACxC;MACEoC,IAAI,EAAE,QAAQ;MACdZ,OAAO,EAAE;IACX,CAAC,EACD;MACEY,IAAI,EAAE,MAAM;MACZZ,OAAO,EAAEW;IACX,CAAC,CACF,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET;IACA,MAAME,aAAa,GAAGlC,QAAQ,CAACmC,IAAI,CAAC,CAAC;IACrC,MAAMC,SAAS,GAAGF,aAAa,CAACG,KAAK,CAAC,aAAa,CAAC;IAEpD,IAAI,CAACD,SAAS,EAAE;MACd,MAAM,IAAIrB,KAAK,CAAC,iCAAiC,CAAC;IACpD;IAEA,MAAMuB,IAAI,GAAGjC,IAAI,CAACkC,KAAK,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC;;IAErC;IACA,IAAI,CAACE,IAAI,CAACE,IAAI,IAAI,CAACC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACK,MAAM,CAAC,EAAE;MAC7C,MAAM,IAAI5B,KAAK,CAAC,wBAAwB,CAAC;IAC3C;IAEA,OAAOuB,IAAI;EACb,CAAC,CAAC,OAAOrB,KAAK,EAAE;IACdvB,OAAO,CAACuB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;IAExD;IACA,OAAO;MACLuB,IAAI,EAAEhB,KAAK;MACXmB,MAAM,EAAE,CACN;QACEC,IAAI,EAAE,cAAc;QACpBC,SAAS,EAAE,oCAAoCrB,KAAK,EAAE;QACtDsB,KAAK,EAAE,IAAI;QACXC,YAAY,EAAE,CAAC,eAAe,EAAE,gBAAgB,EAAE,cAAc;MAClE,CAAC,EACD;QACEH,IAAI,EAAE,cAAc;QACpBC,SAAS,EAAE,2CAA2CrB,KAAK,EAAE;QAC7DsB,KAAK,EAAE,IAAI;QACXC,YAAY,EAAE,CAAC,iBAAiB,EAAE,uBAAuB,EAAE,cAAc;MAC3E,CAAC,EACD;QACEH,IAAI,EAAE,iBAAiB;QACvBC,SAAS,EAAE,sCAAsCrB,KAAK,EAAE;QACxDsB,KAAK,EAAE,IAAI;QACXC,YAAY,EAAE,CAAC,cAAc,EAAE,gBAAgB,EAAE,cAAc;MACjE,CAAC;IAEL,CAAC;EACH;AACF;;AAEA;AACA,OAAO,eAAeC,eAAeA,CAACxB,KAAK,EAAEyB,MAAM,EAAEC,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE;EAAA,IAAAC,oBAAA;EACnE,MAAMC,gBAAgB,GAAG;IACvB;IACA,IAAI,EAAE,8IAA8I;IAEpJ,IAAI,EAAE,iLAAiL;IAEvL,KAAK,EAAE,wKAAwK;IAE/K,IAAI,EAAE,8IAA8I;IAEpJ,IAAI,EAAE,6MAA6M;IAEnN,MAAM,EAAE,yIAAyI;IAEjJ,MAAM,EAAE,oIAAoI;IAE5I;IACA,OAAO,EAAE,mIAAmI;IAE5I,MAAM,EAAE,yKAAyK;IAEjL,OAAO,EAAE,0KAA0K;IAEnL,OAAO,EAAE,8JAA8J;IAEvK;IACA,OAAO,EAAE,0KAA0K;IAEnL,OAAO,EAAE,gJAAgJ;IAEzJ,OAAO,EAAE,gJAAgJ;IAEzJ;IACA,KAAK,EAAE,oKAAoK;IAE3K;IACA,OAAO,EAAE;EACX,CAAC;EAED,MAAMC,WAAW,GAAGH,KAAK,CAACI,MAAM,CAACC,IAAI,IAAIH,gBAAgB,CAACG,IAAI,CAAC,CAAC;EAChE,MAAMC,QAAQ,GAAGH,WAAW,CAACI,GAAG,CAACF,IAAI,IAAIH,gBAAgB,CAACG,IAAI,CAAC,CAAC,CAACG,IAAI,CAAC,IAAI,CAAC;;EAE3E;EACA,MAAMC,eAAe,GAAGT,KAAK,CAACU,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMC,SAAS,GAAGX,KAAK,CAACU,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAME,YAAY,GAAGZ,KAAK,CAACU,QAAQ,CAAC,QAAQ,CAAC;EAE7C,IAAIG,mBAAmB,GAAG,EAAE;EAC5B,IAAIJ,eAAe,EAAE;IACnBI,mBAAmB,GAAG,wIAAwI;EAChK,CAAC,MAAM,IAAIF,SAAS,EAAE;IACpBE,mBAAmB,GAAG,uGAAuG;EAC/H,CAAC,MAAM,IAAID,YAAY,EAAE;IACvBC,mBAAmB,GAAG,+EAA+E;EACvG;;EAEA;EACA,MAAMC,gBAAgB,GAAGd,KAAK,CAACe,IAAI,CAACV,IAAI,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,WAAW,CAAC,CAACK,QAAQ,CAACL,IAAI,CAAC,CAAC;EAC9F,IAAIW,eAAe,GAAG,EAAE;EACxB,IAAIF,gBAAgB,EAAE;IACpBE,eAAe,GAAG,kGAAkG;EACtH;;EAEA;EACA,IAAIC,UAAU,GAAG,EAAE;EACnB,IAAIC,cAAc,GAAG,EAAE;EAEvB,IAAI;IACF1E,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEsD,MAAM,CAACL,IAAI,CAAC;IAC7DuB,UAAU,GAAG,MAAMzF,gBAAgB,CAAC2F,aAAa,CAACpB,MAAM,CAACL,IAAI,EAAE,CAAC,CAAC;IACjElD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEwE,UAAU,CAACG,MAAM,EAAE,SAAS,CAAC;IAE7D,IAAIH,UAAU,CAACG,MAAM,GAAG,CAAC,EAAE;MACzBF,cAAc,GAAG,6DAA6DD,UAAU,CAACV,GAAG,CAACc,MAAM,IACjG,KAAKA,MAAM,CAACC,KAAK,KAAKD,MAAM,CAACE,WAAW,KAAKF,MAAM,CAACA,MAAM,GAC5D,CAAC,CAACb,IAAI,CAAC,IAAI,CAAC,mHAAmH;IACjI;EACF,CAAC,CAAC,OAAOzC,KAAK,EAAE;IACdvB,OAAO,CAACgF,IAAI,CAAC,mDAAmD,EAAEzD,KAAK,CAAC;EAC1E;EAEA,MAAMe,MAAM,GAAG,4DAA4DiB,MAAM,CAACL,IAAI,wBAAwBpB,KAAK;AACrH;AACA;AACA,EAAEgC,QAAQ,IAAIJ,gBAAgB,CAAC,IAAI,CAAC;AACpC;AACA,EAAEW,mBAAmB,GAAG,8BAA8BA,mBAAmB,EAAE,GAAG,EAAE;AAChF,EAAEG,eAAe,GAAG,sBAAsBA,eAAe,EAAE,GAAG,EAAE;AAChE,EAAEE,cAAc;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa/D,IAAI,CAACC,SAAS,CAAC4C,KAAK,CAAC;AAClC,gBAAgB1B,KAAK,MAAMyB,MAAM,CAACL,IAAI;AACtC;AACA;AACA;AACA;AACA;AACA,iBAAiBpB,KAAK;AACtB,UAAUyB,MAAM,CAACL,IAAI;AACrB,sBAAsBK,MAAM,CAACJ,SAAS;AACtC,iBAAiB,EAAAM,oBAAA,GAAAF,MAAM,CAACF,YAAY,cAAAI,oBAAA,uBAAnBA,oBAAA,CAAqBO,IAAI,CAAC,IAAI,CAAC,KAAI,MAAM;AAC1D,kBAAkBR,KAAK,CAACQ,IAAI,CAAC,GAAG,CAAC,EAAE;EAEjC,IAAI;IACF,MAAM1D,QAAQ,GAAG,MAAMsB,MAAM,CAACzB,WAAW,CAAC,CACxC;MACEoC,IAAI,EAAE,QAAQ;MACdZ,OAAO,EAAE;IACX,CAAC,EACD;MACEY,IAAI,EAAE,MAAM;MACZZ,OAAO,EAAEW;IACX,CAAC,CACF,EAAE,GAAG,CAAC;;IAEP;IACA,MAAME,aAAa,GAAGlC,QAAQ,CAACmC,IAAI,CAAC,CAAC;IACrC,MAAMC,SAAS,GAAGF,aAAa,CAACG,KAAK,CAAC,aAAa,CAAC;IAEpD,IAAI,CAACD,SAAS,EAAE;MACd,MAAM,IAAIrB,KAAK,CAAC,iCAAiC,CAAC;IACpD;IAEA,MAAM4D,OAAO,GAAGtE,IAAI,CAACkC,KAAK,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC;;IAExC;IACA,IAAI,CAACuC,OAAO,CAACC,KAAK,IAAI,CAACD,OAAO,CAACE,QAAQ,EAAE;MACvC,MAAM,IAAI9D,KAAK,CAAC,2BAA2B,CAAC;IAC9C;;IAEA;IACA,IAAIoD,UAAU,CAACG,MAAM,GAAG,CAAC,EAAE;MACzB,MAAMQ,WAAW,GAAGpG,gBAAgB,CAACqG,iBAAiB,CAACZ,UAAU,CAAC;MAClEQ,OAAO,CAACE,QAAQ,IAAI,MAAM,GAAGC,WAAW;MACxCH,OAAO,CAACR,UAAU,GAAGA,UAAU;IACjC;IAEA,OAAOQ,OAAO;EAChB,CAAC,CAAC,OAAO1D,KAAK,EAAE;IACdvB,OAAO,CAACuB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;;IAEjD;IACA,OAAO;MACL2D,KAAK,EAAE,GAAG3B,MAAM,CAACL,IAAI,MAAMpB,KAAK,EAAE;MAClCqD,QAAQ,EAAE,KAAK5B,MAAM,CAACL,IAAI;AAChC;AACA,wBAAwBK,MAAM,CAACL,IAAI,sBAAsBpB,KAAK;AAC9D;AACA;AACA,EAAEyB,MAAM,CAACJ,SAAS;AAClB;AACA;AACA,gBAAgBI,MAAM,CAACL,IAAI,+BAA+BpB,KAAK;AAC/D;AACA;AACA,kBAAkByB,MAAM,CAACL,IAAI;AAC7B;AACA;AACA,yFAAyF;MACnFG,YAAY,EAAEE,MAAM,CAACF,YAAY,IAAI,EAAE;MACvCG,KAAK,EAAEA,KAAK;MACZ8B,OAAO,EAAE,GAAGxD,KAAK,MAAMyB,MAAM,CAACL,IAAI;IACpC,CAAC;EACH;AACF;;AAEA;AACA,OAAO,eAAeqC,cAAcA,CAAA,EAAG;EACrC,IAAI;IACF,MAAMjF,QAAQ,GAAG,MAAMsB,MAAM,CAACzB,WAAW,CAAC,CACxC;MACEoC,IAAI,EAAE,MAAM;MACZZ,OAAO,EAAE;IACX,CAAC,CACF,CAAC;IAEF,OAAOrB,QAAQ,CAAC4D,QAAQ,CAAC,YAAY,CAAC;EACxC,CAAC,CAAC,OAAO3C,KAAK,EAAE;IACdvB,OAAO,CAACuB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACnD,OAAO,KAAK;EACd;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}