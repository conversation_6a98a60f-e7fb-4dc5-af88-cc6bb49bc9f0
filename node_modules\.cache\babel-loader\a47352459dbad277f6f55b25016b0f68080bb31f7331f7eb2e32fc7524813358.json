{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Appv1\\\\src\\\\components\\\\TabManager.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport tabService from '../services/tabService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TabManager = ({\n  onTabChange,\n  onNewTab\n}) => {\n  _s();\n  const [tabs, setTabs] = useState([]);\n  const [activeTabId, setActiveTabId] = useState(null);\n  useEffect(() => {\n    // Listen for tab changes\n    const handleTabUpdate = (updatedTabs, activeId) => {\n      setTabs(updatedTabs);\n      setActiveTabId(activeId);\n\n      // Notify parent component of active tab change\n      if (onTabChange && activeId) {\n        const activeTab = updatedTabs.find(tab => tab.id === activeId);\n        onTabChange(activeTab);\n      }\n    };\n    tabService.addListener(handleTabUpdate);\n\n    // Initial load\n    setTabs(tabService.getAllTabs());\n    setActiveTabId(tabService.activeTabId);\n    return () => {\n      tabService.removeListener(handleTabUpdate);\n    };\n  }, [onTabChange]);\n  const handleTabClick = tabId => {\n    tabService.setActiveTab(tabId);\n  };\n  const handleCloseTab = (tabId, event) => {\n    event.stopPropagation();\n    tabService.closeTab(tabId);\n  };\n  const handleNewTab = () => {\n    if (tabService.canCreateNewTab() && onNewTab) {\n      onNewTab();\n    }\n  };\n  const truncateText = (text, maxLength = 15) => {\n    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"tab-manager\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tab-bar\",\n      children: [tabs.map(tab => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `tab-item ${tab.id === activeTabId ? 'active' : ''}`,\n        onClick: () => handleTabClick(tab.id),\n        style: {\n          backgroundColor: tab.id === activeTabId ? '#ffffff' : '#f8fafc',\n          borderColor: tabService.getTabStatusColor(tab.status),\n          borderTopWidth: '3px',\n          borderTopStyle: 'solid'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"tab-icon\",\n            children: tabService.getTabStatusIcon(tab.status)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"tab-title\",\n            children: truncateText(tab.topic)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"tab-close\",\n            onClick: e => handleCloseTab(tab.id, e),\n            title: \"Close tab\",\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 13\n        }, this), tab.status === 'generating' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-progress\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tab-progress-bar\",\n            style: {\n              width: `${tab.progress}%`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 15\n        }, this)]\n      }, tab.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 11\n      }, this)), tabService.canCreateNewTab() && /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"tab-new\",\n        onClick: handleNewTab,\n        title: `Add new tab (${tabs.length}/${tabService.maxTabs})`,\n        children: \"+ New\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this), tabs.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tab-summary\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"tab-count\",\n        children: [tabs.length, \"/\", tabService.maxTabs, \" tabs\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tab-status-indicators\",\n        children: [tabs.filter(t => t.status === 'generating').length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"status-indicator generating\",\n          children: [\"\\uD83D\\uDD04 \", tabs.filter(t => t.status === 'generating').length, \" generating\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 15\n        }, this), tabs.filter(t => t.status === 'completed').length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"status-indicator completed\",\n          children: [\"\\u2705 \", tabs.filter(t => t.status === 'completed').length, \" ready\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 5\n  }, this);\n};\n_s(TabManager, \"z2j/jnLe5kwNI5ksyqRGy08UaZY=\");\n_c = TabManager;\nexport default TabManager;\nvar _c;\n$RefreshReg$(_c, \"TabManager\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "tabService", "jsxDEV", "_jsxDEV", "TabManager", "onTabChange", "onNewTab", "_s", "tabs", "setTabs", "activeTabId", "setActiveTabId", "handleTabUpdate", "updatedTabs", "activeId", "activeTab", "find", "tab", "id", "addListener", "getAllTabs", "removeListener", "handleTabClick", "tabId", "setActiveTab", "handleCloseTab", "event", "stopPropagation", "closeTab", "handleNewTab", "canCreateNewTab", "truncateText", "text", "max<PERSON><PERSON><PERSON>", "length", "substring", "className", "children", "map", "onClick", "style", "backgroundColor", "borderColor", "getTabStatusColor", "status", "borderTopWidth", "borderTopStyle", "getTabStatusIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "topic", "e", "title", "width", "progress", "maxTabs", "filter", "t", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Appv1/src/components/TabManager.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport tabService from '../services/tabService';\n\nconst TabManager = ({ onTabChange, onNewTab }) => {\n  const [tabs, setTabs] = useState([]);\n  const [activeTabId, setActiveTabId] = useState(null);\n\n  useEffect(() => {\n    // Listen for tab changes\n    const handleTabUpdate = (updatedTabs, activeId) => {\n      setTabs(updatedTabs);\n      setActiveTabId(activeId);\n      \n      // Notify parent component of active tab change\n      if (onTabChange && activeId) {\n        const activeTab = updatedTabs.find(tab => tab.id === activeId);\n        onTabChange(activeTab);\n      }\n    };\n\n    tabService.addListener(handleTabUpdate);\n    \n    // Initial load\n    setTabs(tabService.getAllTabs());\n    setActiveTabId(tabService.activeTabId);\n\n    return () => {\n      tabService.removeListener(handleTabUpdate);\n    };\n  }, [onTabChange]);\n\n  const handleTabClick = (tabId) => {\n    tabService.setActiveTab(tabId);\n  };\n\n  const handleCloseTab = (tabId, event) => {\n    event.stopPropagation();\n    tabService.closeTab(tabId);\n  };\n\n  const handleNewTab = () => {\n    if (tabService.canCreateNewTab() && onNewTab) {\n      onNewTab();\n    }\n  };\n\n  const truncateText = (text, maxLength = 15) => {\n    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;\n  };\n\n  return (\n    <div className=\"tab-manager\">\n      <div className=\"tab-bar\">\n        {tabs.map(tab => (\n          <div\n            key={tab.id}\n            className={`tab-item ${tab.id === activeTabId ? 'active' : ''}`}\n            onClick={() => handleTabClick(tab.id)}\n            style={{\n              backgroundColor: tab.id === activeTabId ? '#ffffff' : '#f8fafc',\n              borderColor: tabService.getTabStatusColor(tab.status),\n              borderTopWidth: '3px',\n              borderTopStyle: 'solid'\n            }}\n          >\n            <div className=\"tab-content\">\n              <span className=\"tab-icon\">\n                {tabService.getTabStatusIcon(tab.status)}\n              </span>\n              <span className=\"tab-title\">\n                {truncateText(tab.topic)}\n              </span>\n              <button\n                className=\"tab-close\"\n                onClick={(e) => handleCloseTab(tab.id, e)}\n                title=\"Close tab\"\n              >\n                ×\n              </button>\n            </div>\n            {tab.status === 'generating' && (\n              <div className=\"tab-progress\">\n                <div \n                  className=\"tab-progress-bar\"\n                  style={{ width: `${tab.progress}%` }}\n                />\n              </div>\n            )}\n          </div>\n        ))}\n        \n        {tabService.canCreateNewTab() && (\n          <button\n            className=\"tab-new\"\n            onClick={handleNewTab}\n            title={`Add new tab (${tabs.length}/${tabService.maxTabs})`}\n          >\n            + New\n          </button>\n        )}\n      </div>\n\n      {tabs.length > 0 && (\n        <div className=\"tab-summary\">\n          <span className=\"tab-count\">\n            {tabs.length}/{tabService.maxTabs} tabs\n          </span>\n          <div className=\"tab-status-indicators\">\n            {tabs.filter(t => t.status === 'generating').length > 0 && (\n              <span className=\"status-indicator generating\">\n                🔄 {tabs.filter(t => t.status === 'generating').length} generating\n              </span>\n            )}\n            {tabs.filter(t => t.status === 'completed').length > 0 && (\n              <span className=\"status-indicator completed\">\n                ✅ {tabs.filter(t => t.status === 'completed').length} ready\n              </span>\n            )}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default TabManager;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,UAAU,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,UAAU,GAAGA,CAAC;EAAEC,WAAW;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAChD,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACW,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAEpDC,SAAS,CAAC,MAAM;IACd;IACA,MAAMY,eAAe,GAAGA,CAACC,WAAW,EAAEC,QAAQ,KAAK;MACjDL,OAAO,CAACI,WAAW,CAAC;MACpBF,cAAc,CAACG,QAAQ,CAAC;;MAExB;MACA,IAAIT,WAAW,IAAIS,QAAQ,EAAE;QAC3B,MAAMC,SAAS,GAAGF,WAAW,CAACG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,EAAE,KAAKJ,QAAQ,CAAC;QAC9DT,WAAW,CAACU,SAAS,CAAC;MACxB;IACF,CAAC;IAEDd,UAAU,CAACkB,WAAW,CAACP,eAAe,CAAC;;IAEvC;IACAH,OAAO,CAACR,UAAU,CAACmB,UAAU,CAAC,CAAC,CAAC;IAChCT,cAAc,CAACV,UAAU,CAACS,WAAW,CAAC;IAEtC,OAAO,MAAM;MACXT,UAAU,CAACoB,cAAc,CAACT,eAAe,CAAC;IAC5C,CAAC;EACH,CAAC,EAAE,CAACP,WAAW,CAAC,CAAC;EAEjB,MAAMiB,cAAc,GAAIC,KAAK,IAAK;IAChCtB,UAAU,CAACuB,YAAY,CAACD,KAAK,CAAC;EAChC,CAAC;EAED,MAAME,cAAc,GAAGA,CAACF,KAAK,EAAEG,KAAK,KAAK;IACvCA,KAAK,CAACC,eAAe,CAAC,CAAC;IACvB1B,UAAU,CAAC2B,QAAQ,CAACL,KAAK,CAAC;EAC5B,CAAC;EAED,MAAMM,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI5B,UAAU,CAAC6B,eAAe,CAAC,CAAC,IAAIxB,QAAQ,EAAE;MAC5CA,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC;EAED,MAAMyB,YAAY,GAAGA,CAACC,IAAI,EAAEC,SAAS,GAAG,EAAE,KAAK;IAC7C,OAAOD,IAAI,CAACE,MAAM,GAAGD,SAAS,GAAGD,IAAI,CAACG,SAAS,CAAC,CAAC,EAAEF,SAAS,CAAC,GAAG,KAAK,GAAGD,IAAI;EAC9E,CAAC;EAED,oBACE7B,OAAA;IAAKiC,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1BlC,OAAA;MAAKiC,SAAS,EAAC,SAAS;MAAAC,QAAA,GACrB7B,IAAI,CAAC8B,GAAG,CAACrB,GAAG,iBACXd,OAAA;QAEEiC,SAAS,EAAE,YAAYnB,GAAG,CAACC,EAAE,KAAKR,WAAW,GAAG,QAAQ,GAAG,EAAE,EAAG;QAChE6B,OAAO,EAAEA,CAAA,KAAMjB,cAAc,CAACL,GAAG,CAACC,EAAE,CAAE;QACtCsB,KAAK,EAAE;UACLC,eAAe,EAAExB,GAAG,CAACC,EAAE,KAAKR,WAAW,GAAG,SAAS,GAAG,SAAS;UAC/DgC,WAAW,EAAEzC,UAAU,CAAC0C,iBAAiB,CAAC1B,GAAG,CAAC2B,MAAM,CAAC;UACrDC,cAAc,EAAE,KAAK;UACrBC,cAAc,EAAE;QAClB,CAAE;QAAAT,QAAA,gBAEFlC,OAAA;UAAKiC,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BlC,OAAA;YAAMiC,SAAS,EAAC,UAAU;YAAAC,QAAA,EACvBpC,UAAU,CAAC8C,gBAAgB,CAAC9B,GAAG,CAAC2B,MAAM;UAAC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACPhD,OAAA;YAAMiC,SAAS,EAAC,WAAW;YAAAC,QAAA,EACxBN,YAAY,CAACd,GAAG,CAACmC,KAAK;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eACPhD,OAAA;YACEiC,SAAS,EAAC,WAAW;YACrBG,OAAO,EAAGc,CAAC,IAAK5B,cAAc,CAACR,GAAG,CAACC,EAAE,EAAEmC,CAAC,CAAE;YAC1CC,KAAK,EAAC,WAAW;YAAAjB,QAAA,EAClB;UAED;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EACLlC,GAAG,CAAC2B,MAAM,KAAK,YAAY,iBAC1BzC,OAAA;UAAKiC,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BlC,OAAA;YACEiC,SAAS,EAAC,kBAAkB;YAC5BI,KAAK,EAAE;cAAEe,KAAK,EAAE,GAAGtC,GAAG,CAACuC,QAAQ;YAAI;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA,GAhCIlC,GAAG,CAACC,EAAE;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAiCR,CACN,CAAC,EAEDlD,UAAU,CAAC6B,eAAe,CAAC,CAAC,iBAC3B3B,OAAA;QACEiC,SAAS,EAAC,SAAS;QACnBG,OAAO,EAAEV,YAAa;QACtByB,KAAK,EAAE,gBAAgB9C,IAAI,CAAC0B,MAAM,IAAIjC,UAAU,CAACwD,OAAO,GAAI;QAAApB,QAAA,EAC7D;MAED;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAEL3C,IAAI,CAAC0B,MAAM,GAAG,CAAC,iBACd/B,OAAA;MAAKiC,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BlC,OAAA;QAAMiC,SAAS,EAAC,WAAW;QAAAC,QAAA,GACxB7B,IAAI,CAAC0B,MAAM,EAAC,GAAC,EAACjC,UAAU,CAACwD,OAAO,EAAC,OACpC;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACPhD,OAAA;QAAKiC,SAAS,EAAC,uBAAuB;QAAAC,QAAA,GACnC7B,IAAI,CAACkD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACf,MAAM,KAAK,YAAY,CAAC,CAACV,MAAM,GAAG,CAAC,iBACrD/B,OAAA;UAAMiC,SAAS,EAAC,6BAA6B;UAAAC,QAAA,GAAC,eACzC,EAAC7B,IAAI,CAACkD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACf,MAAM,KAAK,YAAY,CAAC,CAACV,MAAM,EAAC,aACzD;QAAA;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP,EACA3C,IAAI,CAACkD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACf,MAAM,KAAK,WAAW,CAAC,CAACV,MAAM,GAAG,CAAC,iBACpD/B,OAAA;UAAMiC,SAAS,EAAC,4BAA4B;UAAAC,QAAA,GAAC,SACzC,EAAC7B,IAAI,CAACkD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACf,MAAM,KAAK,WAAW,CAAC,CAACV,MAAM,EAAC,QACvD;QAAA;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC5C,EAAA,CAxHIH,UAAU;AAAAwD,EAAA,GAAVxD,UAAU;AA0HhB,eAAeA,UAAU;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}