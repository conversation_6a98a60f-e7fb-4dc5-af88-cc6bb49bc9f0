// Optimized Gamification Service - Pareto 80/20 Implementation
// Essential gamification features with minimal complexity and maximum engagement

const STORAGE_KEYS = {
  USER_STATS: 'knowledgeTree_userStats',
  ACHIEVEMENTS: 'knowledgeTree_achievements'
};

// Essential achievements that provide 80% of engagement value
const CORE_ACHIEVEMENTS = {
  FIRST_TREE: { id: 'first_tree', name: 'Explorer', description: 'Generated your first knowledge tree', points: 10, icon: '🌱' },
  TREE_MASTER: { id: 'tree_master', name: 'Tree Master', description: 'Generated 10 knowledge trees', points: 50, icon: '🌳' },
  ARTICLE_READER: { id: 'article_reader', name: 'Reader', description: 'Generated your first article', points: 10, icon: '📖' },
  KNOWLEDGE_SEEKER: { id: 'knowledge_seeker', name: 'Knowledge Seeker', description: 'Generated 25 articles', points: 100, icon: '🎓' },
  DAILY_USER: { id: 'daily_user', name: '<PERSON> Learner', description: 'Used the app for 7 consecutive days', points: 75, icon: '🔥' },
  SPEED_READER: { id: 'speed_reader', name: 'Speed Reader', description: 'Used text-to-speech feature', points: 25, icon: '🗣️' },
  SHARER: { id: 'sharer', name: 'Sharer', description: 'Exported your first article', points: 20, icon: '📤' }
};

// Optimized level system - Much harder to level up
const LEVELS = [
  { level: 1, name: 'Beginner', minPoints: 0, icon: '🌱', color: '#10b981' },
  { level: 2, name: 'Explorer', minPoints: 100, icon: '🔍', color: '#3b82f6' },
  { level: 3, name: 'Scholar', minPoints: 300, icon: '📚', color: '#8b5cf6' },
  { level: 4, name: 'Expert', minPoints: 750, icon: '🎓', color: '#f59e0b' },
  { level: 5, name: 'Master', minPoints: 1500, icon: '👑', color: '#ef4444' },
  { level: 6, name: 'Grandmaster', minPoints: 3000, icon: '💎', color: '#06b6d4' },
  { level: 7, name: 'Legend', minPoints: 6000, icon: '⭐', color: '#8b5cf6' },
  { level: 8, name: 'Mythic', minPoints: 12000, icon: '🔥', color: '#f59e0b' },
  { level: 9, name: 'Immortal', minPoints: 25000, icon: '⚡', color: '#ef4444' },
  { level: 10, name: 'Omniscient', minPoints: 50000, icon: '🌟', color: '#fbbf24' }
];

// Point values for actions - Reduced to make progression harder
const POINTS = {
  TREE_GENERATED: 3,
  ARTICLE_GENERATED: 8,
  SPEECH_USED: 2,
  EXPORT_USED: 3,
  DAILY_LOGIN: 5,
  STREAK_BONUS: 1,
  BRANCH_EXPANDED: 5, // New action for long-press branch expansion
  FLAG_USED: 1
};

class OptimizedGamificationService {
  constructor() {
    this.initializeUserStats();
  }

  // Initialize user statistics with minimal data
  initializeUserStats() {
    const defaultStats = {
      totalPoints: 0,
      treesGenerated: 0,
      articlesGenerated: 0,
      speechUsed: 0,
      exportsUsed: 0,
      currentStreak: 0,
      lastLoginDate: null,
      achievements: [],
      createdAt: new Date().toISOString()
    };

    const existingStats = this.getUserStats();
    if (!existingStats) {
      this.saveUserStats(defaultStats);
    }
  }

  // Get user statistics
  getUserStats() {
    try {
      const stats = localStorage.getItem(STORAGE_KEYS.USER_STATS);
      return stats ? JSON.parse(stats) : null;
    } catch (error) {
      console.error('Error getting user stats:', error);
      return null;
    }
  }

  // Save user statistics
  saveUserStats(stats) {
    try {
      stats.lastUpdated = new Date().toISOString();
      localStorage.setItem(STORAGE_KEYS.USER_STATS, JSON.stringify(stats));
      return true;
    } catch (error) {
      console.error('Error saving user stats:', error);
      return false;
    }
  }

  // Award points for an action
  awardPoints(action, additionalData = {}) {
    const stats = this.getUserStats() || {};
    let pointsAwarded = 0;
    let newAchievements = [];

    // Update stats and award points based on action
    switch (action) {
      case 'TREE_GENERATED':
        stats.treesGenerated = (stats.treesGenerated || 0) + 1;
        pointsAwarded = POINTS.TREE_GENERATED;
        
        // Check for tree-related achievements
        if (stats.treesGenerated === 1) {
          newAchievements.push(CORE_ACHIEVEMENTS.FIRST_TREE);
        } else if (stats.treesGenerated === 10) {
          newAchievements.push(CORE_ACHIEVEMENTS.TREE_MASTER);
        }
        break;

      case 'ARTICLE_GENERATED':
        stats.articlesGenerated = (stats.articlesGenerated || 0) + 1;
        pointsAwarded = POINTS.ARTICLE_GENERATED;
        
        // Check for article-related achievements
        if (stats.articlesGenerated === 1) {
          newAchievements.push(CORE_ACHIEVEMENTS.ARTICLE_READER);
        } else if (stats.articlesGenerated === 25) {
          newAchievements.push(CORE_ACHIEVEMENTS.KNOWLEDGE_SEEKER);
        }
        break;

      case 'SPEECH_USED':
        stats.speechUsed = (stats.speechUsed || 0) + 1;
        pointsAwarded = POINTS.SPEECH_USED;
        
        // Check for speech achievement
        if (stats.speechUsed === 1) {
          newAchievements.push(CORE_ACHIEVEMENTS.SPEED_READER);
        }
        break;

      case 'EXPORT_USED':
        stats.exportsUsed = (stats.exportsUsed || 0) + 1;
        pointsAwarded = POINTS.EXPORT_USED;
        
        // Check for export achievement
        if (stats.exportsUsed === 1) {
          newAchievements.push(CORE_ACHIEVEMENTS.SHARER);
        }
        break;

      case 'DAILY_LOGIN':
        pointsAwarded = POINTS.DAILY_LOGIN;
        this.updateLoginStreak(stats);

        // Streak bonus
        if (stats.currentStreak > 1) {
          pointsAwarded += (stats.currentStreak - 1) * POINTS.STREAK_BONUS;
        }

        // Check for daily user achievement
        if (stats.currentStreak === 7) {
          newAchievements.push(CORE_ACHIEVEMENTS.DAILY_USER);
        }
        break;

      case 'BRANCH_EXPANDED':
        stats.branchesExpanded = (stats.branchesExpanded || 0) + 1;
        pointsAwarded = POINTS.BRANCH_EXPANDED;

        // Check for tree expansion achievements
        if (stats.branchesExpanded === 5) {
          newAchievements.push({
            id: 'tree_explorer',
            name: 'Tree Explorer',
            description: 'Expanded 5 branches',
            points: 25,
            icon: '🌳'
          });
        } else if (stats.branchesExpanded === 20) {
          newAchievements.push({
            id: 'forest_creator',
            name: 'Forest Creator',
            description: 'Expanded 20 branches',
            points: 50,
            icon: '🌲'
          });
        }
        break;

      default:
        console.warn('Unknown action:', action);
        return { success: false, pointsAwarded: 0 };
    }

    // Update total points
    stats.totalPoints = (stats.totalPoints || 0) + pointsAwarded;

    // Add new achievements
    if (newAchievements.length > 0) {
      const existingAchievements = stats.achievements || [];
      const newAchievementIds = newAchievements.map(a => a.id);
      const uniqueAchievements = [...existingAchievements, ...newAchievementIds.filter(id => !existingAchievements.includes(id))];
      
      stats.achievements = uniqueAchievements;
      
      // Award achievement points
      const achievementPoints = newAchievements.reduce((total, achievement) => total + achievement.points, 0);
      stats.totalPoints += achievementPoints;
      pointsAwarded += achievementPoints;
    }

    // Save updated stats
    this.saveUserStats(stats);

    return {
      success: true,
      pointsAwarded,
      newAchievements,
      userLevel: this.getUserLevel(stats.totalPoints),
      stats
    };
  }

  // Update login streak
  updateLoginStreak(stats) {
    const today = new Date().toDateString();
    const lastLogin = stats.lastLoginDate ? new Date(stats.lastLoginDate).toDateString() : null;
    
    if (lastLogin === today) {
      return; // Already logged in today
    }

    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStr = yesterday.toDateString();

    if (lastLogin === yesterdayStr) {
      // Consecutive day login
      stats.currentStreak = (stats.currentStreak || 0) + 1;
    } else {
      // Streak broken or first login
      stats.currentStreak = 1;
    }

    stats.lastLoginDate = new Date().toISOString();
  }

  // Get user level based on points
  getUserLevel(points = null) {
    const userPoints = points !== null ? points : (this.getUserStats()?.totalPoints || 0);
    
    for (let i = LEVELS.length - 1; i >= 0; i--) {
      if (userPoints >= LEVELS[i].minPoints) {
        return LEVELS[i];
      }
    }
    
    return LEVELS[0];
  }

  // Get progress to next level
  getProgressToNextLevel(points = null) {
    const userPoints = points !== null ? points : (this.getUserStats()?.totalPoints || 0);
    const currentLevel = this.getUserLevel(userPoints);
    const nextLevel = LEVELS.find(level => level.minPoints > userPoints);
    
    if (!nextLevel) {
      return { progress: 100, pointsToNext: 0, nextLevel: null };
    }
    
    const pointsInCurrentLevel = userPoints - currentLevel.minPoints;
    const pointsNeededForNext = nextLevel.minPoints - currentLevel.minPoints;
    const progress = Math.round((pointsInCurrentLevel / pointsNeededForNext) * 100);
    
    return {
      progress,
      pointsToNext: nextLevel.minPoints - userPoints,
      nextLevel
    };
  }

  // Get user achievements
  getUserAchievements() {
    const stats = this.getUserStats();
    if (!stats || !stats.achievements) return [];

    return stats.achievements.map(achievementId => 
      Object.values(CORE_ACHIEVEMENTS).find(a => a.id === achievementId)
    ).filter(Boolean);
  }

  // Create minimalist gamification UI
  createGamificationUI(container) {
    if (!container) return null;

    const stats = this.getUserStats() || {};
    const level = this.getUserLevel();
    const progress = this.getProgressToNextLevel();

    const ui = document.createElement('div');
    ui.className = 'gamification-ui clickable';
    ui.style.cursor = 'pointer';
    ui.innerHTML = `
      <div class="gamification-compact">
        <div class="level-badge" style="background: ${level.color}">
          <span class="level-icon">${level.icon}</span>
          <span class="level-info">
            <div class="level-name">${level.name}</div>
            <div class="level-points">${stats.totalPoints || 0} pts</div>
          </span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill" style="width: ${progress.progress}%"></div>
        </div>
        <div class="achievements-count">
          🏆 ${stats.achievements?.length || 0}
        </div>
      </div>
    `;

    // Add click handler to show leaderboard
    ui.addEventListener('click', () => {
      this.showLeaderboardPopup();
    });

    // Add CSS styles
    const style = document.createElement('style');
    style.textContent = `
      .gamification-compact {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 8px 12px;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 8px;
        border: 1px solid #e2e8f0;
        font-size: 12px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      .level-badge {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 4px 8px;
        border-radius: 6px;
        color: white;
        font-weight: 600;
      }
      .level-icon {
        font-size: 16px;
      }
      .level-info {
        display: flex;
        flex-direction: column;
        line-height: 1.2;
      }
      .level-name {
        font-size: 11px;
        opacity: 0.9;
      }
      .level-points {
        font-size: 10px;
        opacity: 0.8;
      }
      .progress-bar {
        flex: 1;
        height: 4px;
        background: #e2e8f0;
        border-radius: 2px;
        overflow: hidden;
      }
      .progress-fill {
        height: 100%;
        background: ${level.color};
        transition: width 0.3s ease;
      }
      .achievements-count {
        font-size: 11px;
        color: #64748b;
        font-weight: 500;
      }
    `;
    document.head.appendChild(style);

    container.appendChild(ui);
    return ui;
  }

  // Show achievement notification
  showAchievementNotification(achievement) {
    const notification = document.createElement('div');
    notification.className = 'achievement-notification';
    notification.innerHTML = `
      <div class="achievement-content">
        <div class="achievement-icon">${achievement.icon}</div>
        <div class="achievement-text">
          <div class="achievement-title">Achievement Unlocked!</div>
          <div class="achievement-name">${achievement.name}</div>
          <div class="achievement-points">+${achievement.points} points</div>
        </div>
      </div>
    `;

    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      color: white;
      padding: 16px;
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(16, 185, 129, 0.3);
      z-index: 10000;
      animation: achievementSlideIn 0.5s ease-out;
      max-width: 300px;
    `;

    // Add CSS animation
    const style = document.createElement('style');
    style.textContent = `
      @keyframes achievementSlideIn {
        from {
          transform: translateX(100%);
          opacity: 0;
        }
        to {
          transform: translateX(0);
          opacity: 1;
        }
      }
      .achievement-content {
        display: flex;
        align-items: center;
        gap: 12px;
      }
      .achievement-icon {
        font-size: 32px;
      }
      .achievement-text {
        flex: 1;
      }
      .achievement-title {
        font-size: 12px;
        opacity: 0.9;
        margin-bottom: 2px;
      }
      .achievement-name {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 2px;
      }
      .achievement-points {
        font-size: 12px;
        opacity: 0.8;
      }
    `;
    document.head.appendChild(style);

    document.body.appendChild(notification);

    // Auto-remove after 4 seconds
    setTimeout(() => {
      if (notification && notification.parentNode) {
        notification.style.animation = 'achievementSlideIn 0.3s ease-in reverse';
        setTimeout(() => {
          if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
          }
        }, 300);
      }
    }, 4000);
  }

  // Show leaderboard popup
  showLeaderboardPopup() {
    const existingPopup = document.querySelector('.leaderboard-popup');
    if (existingPopup) {
      existingPopup.remove();
      return;
    }

    const popup = document.createElement('div');
    popup.className = 'leaderboard-popup';
    popup.innerHTML = `
      <div class="leaderboard-modal">
        <div class="leaderboard-header">
          <h2>🏆 Leaderboard</h2>
          <button class="close-btn" onclick="this.closest('.leaderboard-popup').remove()">✕</button>
        </div>
        <div class="leaderboard-content">
          ${this.generateLeaderboardHTML()}
        </div>
      </div>
    `;

    // Add styles
    const style = document.createElement('style');
    style.textContent = `
      .leaderboard-popup {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        animation: fadeIn 0.3s ease;
      }
      .leaderboard-modal {
        background: white;
        border-radius: 12px;
        padding: 24px;
        max-width: 500px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        animation: slideUp 0.3s ease;
      }
      .leaderboard-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        border-bottom: 1px solid #e2e8f0;
        padding-bottom: 16px;
      }
      .leaderboard-header h2 {
        margin: 0;
        color: #1e293b;
        font-size: 24px;
      }
      .close-btn {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: #64748b;
        padding: 4px;
        border-radius: 4px;
      }
      .close-btn:hover {
        background: #f1f5f9;
        color: #1e293b;
      }
      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }
      @keyframes slideUp {
        from { transform: translateY(20px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
      }
    `;
    document.head.appendChild(style);

    // Close on background click
    popup.addEventListener('click', (e) => {
      if (e.target === popup) {
        popup.remove();
      }
    });

    document.body.appendChild(popup);
  }

  // Generate leaderboard HTML
  generateLeaderboardHTML() {
    const leaderboard = this.getLeaderboard();

    let html = '<div class="leaderboard-list">';

    leaderboard.forEach((user, index) => {
      const level = this.getUserLevel(user.totalPoints);
      const isCurrentUser = user.id === 'user-1';

      html += `
        <div class="leaderboard-item ${isCurrentUser ? 'current-user' : ''}">
          <div class="rank">#${index + 1}</div>
          <div class="user-info">
            <div class="user-avatar" style="background: ${level.color}">
              ${level.icon}
            </div>
            <div class="user-details">
              <div class="user-name">${user.name}</div>
              <div class="user-level">${level.name}</div>
            </div>
          </div>
          <div class="user-points">${user.totalPoints} pts</div>
        </div>
      `;
    });

    html += '</div>';

    // Add leaderboard styles
    const style = document.createElement('style');
    style.textContent = `
      .leaderboard-list {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }
      .leaderboard-item {
        display: flex;
        align-items: center;
        padding: 12px;
        background: #f8fafc;
        border-radius: 8px;
        border: 1px solid #e2e8f0;
        transition: all 0.2s ease;
      }
      .leaderboard-item.current-user {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        border-color: #3b82f6;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
      }
      .leaderboard-item:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }
      .rank {
        font-weight: bold;
        font-size: 18px;
        margin-right: 16px;
        min-width: 40px;
      }
      .user-info {
        display: flex;
        align-items: center;
        flex: 1;
        gap: 12px;
      }
      .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
      }
      .user-details {
        display: flex;
        flex-direction: column;
      }
      .user-name {
        font-weight: 600;
        font-size: 14px;
      }
      .user-level {
        font-size: 12px;
        opacity: 0.8;
      }
      .user-points {
        font-weight: bold;
        font-size: 16px;
      }
    `;
    document.head.appendChild(style);

    return html;
  }

  // Get leaderboard data (mock data for now)
  getLeaderboard() {
    const currentUser = this.getUserStats();
    const mockUsers = [
      { id: 'user-1', name: 'You', totalPoints: currentUser?.totalPoints || 0 },
      { id: 'user-2', name: 'Alex Scholar', totalPoints: 1250 },
      { id: 'user-3', name: 'Maria Expert', totalPoints: 980 },
      { id: 'user-4', name: 'John Explorer', totalPoints: 750 },
      { id: 'user-5', name: 'Sarah Master', totalPoints: 2100 },
      { id: 'user-6', name: 'David Legend', totalPoints: 3500 },
      { id: 'user-7', name: 'Emma Genius', totalPoints: 890 },
      { id: 'user-8', name: 'Mike Scholar', totalPoints: 670 },
      { id: 'user-9', name: 'Lisa Expert', totalPoints: 1450 },
      { id: 'user-10', name: 'Tom Explorer', totalPoints: 520 }
    ];

    return mockUsers.sort((a, b) => b.totalPoints - a.totalPoints);
  }

  // Reset user statistics (for testing)
  resetUserStats() {
    localStorage.removeItem(STORAGE_KEYS.USER_STATS);
    this.initializeUserStats();
    return true;
  }
}

// Create singleton instance
const optimizedGamificationService = new OptimizedGamificationService();

export default optimizedGamificationService;
