// OpenRouter API Service for Knowledge Tree Generation
import webSearchService from './webSearchService';

const OPENROUTER_API_KEY = process.env.REACT_APP_OPENROUTER_API_KEY || '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';
const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';
const MODEL = process.env.REACT_APP_OPENROUTER_MODEL || 'deepseek/deepseek-r1-0528:free';

// Site configuration for OpenRouter rankings
const SITE_CONFIG = {
  'HTTP-Referer': process.env.REACT_APP_SITE_URL || 'http://localhost:3000',
  'X-Title': process.env.REACT_APP_SITE_NAME || 'Knowledge Tree Explorer'
};

class OpenRouterClient {
  constructor() {
    this.baseURL = OPENROUTER_BASE_URL;
    this.apiKey = OPENROUTER_API_KEY;

    // Debug logging - always show for troubleshooting
    console.log('🔧 OpenRouter Client initialized:');
    console.log('- Base URL:', this.baseURL);
    console.log('- Model:', MODEL);
    console.log('- API Key:', this.apiKey ? `${this.apiKey.substring(0, 10)}...` : 'NOT SET');
    console.log('- Site Config:', SITE_CONFIG);
  }

  async makeRequest(messages, temperature = 0.7) {
    try {
      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          ...SITE_CONFIG
        },
        body: JSON.stringify({
          model: MODEL,
          messages,
          temperature,
          max_tokens: 1500, // Optimizat pentru răspunsuri mai rapide
          stream: false
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`OpenRouter API Error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
      }

      const data = await response.json();
      return data.choices[0].message.content;
    } catch (error) {
      console.error('OpenRouter API request failed:', error);
      throw error;
    }
  }
}

const client = new OpenRouterClient();

// Generate Knowledge Tree from topic
export async function generateKnowledgeTree(topic, language = 'en') {
  // Get current language from localStorage if not provided
  const currentLang = language || localStorage.getItem('language') || 'en';

  const prompts = {
    ro: `Analizează cu atenție subiectul "${topic}" și creează un arbore de cunoștințe FOARTE SPECIFIC și RELEVANT pentru acest domeniu exact.

IMPORTANT: Generează ramuri care sunt DIRECT LEGATE de "${topic}", nu concepte generale!

Returnează DOAR un obiect JSON valid cu această structură exactă:
{
  "tema": "${topic}",
  "ramuri": [
    {
      "nume": "Nume Ramură Specifică",
      "descriere": "Descriere scurtă și precisă",
      "emoji": "📚",
      "subcategorii": ["subcategorie1", "subcategorie2", "subcategorie3"]
    }
  ]
}

Cerințe STRICTE:
- Generează 6-8 ramuri principale SPECIFICE pentru "${topic}"
- Fiecare ramură TREBUIE să fie direct legată de subiectul principal
- Emoji-uri relevante pentru fiecare ramură
- 3-4 subcategorii specifice pentru fiecare ramură
- Descrieri de maxim 1 propoziție
- Focalizează-te pe aspectele PRACTICE și APLICABILE
- Evită conceptele generale sau irelevante

Subiect: ${topic}`,

    en: `Analyze the subject "${topic}" carefully and create a VERY SPECIFIC and RELEVANT knowledge tree for this exact domain.

IMPORTANT: Generate branches that are DIRECTLY RELATED to "${topic}", not general concepts!

Return ONLY a valid JSON object with this exact structure:
{
  "tema": "${topic}",
  "ramuri": [
    {
      "nume": "Specific Branch Name",
      "descriere": "Short and precise description",
      "emoji": "📚",
      "subcategorii": ["subcategory1", "subcategory2", "subcategory3"]
    }
  ]
}

STRICT Requirements:
- Generate 6-8 main branches SPECIFIC to "${topic}"
- Each branch MUST be directly related to the main subject
- Relevant emojis for each branch
- 3-4 specific subcategories for each branch
- Descriptions of maximum 1 sentence
- Focus on PRACTICAL and APPLICABLE aspects
- Avoid general or irrelevant concepts

Subject: ${topic}`
  };

  const prompt = prompts[currentLang] || prompts.en;

  try {
    const response = await client.makeRequest([
      {
        role: 'system',
        content: 'Expert în organizarea cunoștințelor. Generează arbori de cunoștințe specifici în format JSON valid. Răspunde DOAR cu JSON, fără text explicativ.'
      },
      {
        role: 'user',
        content: prompt
      }
    ], 0.3); // Temperatură mai mică pentru răspunsuri mai consistente și rapide

    // Parse and validate the JSON response
    const cleanResponse = response.trim();
    const jsonMatch = cleanResponse.match(/\{[\s\S]*\}/);
    
    if (!jsonMatch) {
      throw new Error('No valid JSON found in response');
    }

    const tree = JSON.parse(jsonMatch[0]);
    
    // Validate structure
    if (!tree.tema || !Array.isArray(tree.ramuri)) {
      throw new Error('Invalid tree structure');
    }

    return tree;
  } catch (error) {
    console.error('Error generating knowledge tree:', error);
    
    // Fallback tree structure based on language
    const fallbacks = {
      ro: {
        tema: topic,
        ramuri: [
          {
            nume: "Fundamentele",
            descriere: `Concepte de bază și principii ale ${topic}`,
            emoji: "📚",
            subcategorii: ["Concepte Esențiale", "Principii Cheie", "Teoria de Bază"]
          },
          {
            nume: "Aplicații",
            descriere: `Aplicații practice și cazuri de utilizare ale ${topic}`,
            emoji: "🔧",
            subcategorii: ["Utilizări Reale", "Aplicații Industriale", "Studii de Caz"]
          },
          {
            nume: "Subiecte Avansate",
            descriere: `Aspecte complexe și specializate ale ${topic}`,
            emoji: "🎓",
            subcategorii: ["Nivel Expert", "Zone de Cercetare", "Tehnologii Noi"]
          }
        ]
      },
      en: {
        tema: topic,
        ramuri: [
          {
            nume: "Fundamentals",
            descriere: `Basic concepts and principles of ${topic}`,
            emoji: "📚",
            subcategorii: ["Core Concepts", "Key Principles", "Basic Theory"]
          },
          {
            nume: "Applications",
            descriere: `Practical applications and use cases of ${topic}`,
            emoji: "🔧",
            subcategorii: ["Real-world Uses", "Industry Applications", "Case Studies"]
          },
          {
            nume: "Advanced Topics",
            descriere: `Complex and specialized aspects of ${topic}`,
            emoji: "🎓",
            subcategorii: ["Expert Level", "Research Areas", "Cutting Edge"]
          }
        ]
      }
    };

    return fallbacks[currentLang] || fallbacks.en;
  }
}

// Generate Article for specific branch
export async function generateArticle(topic, branch, flags = ['-a']) {
  // Get current language from localStorage
  const currentLang = localStorage.getItem('language') || 'ro';

  const flagInstructions = {
    ro: {
      // Basic flags cu prompt-uri FOARTE SPECIFICE în ROMÂNĂ
      '-a': 'Scrie un articol informativ standard (600-800 cuvinte) cu structură clară: introducere, dezvoltare cu 3-4 secțiuni principale, și concluzie. SCRIE ÎNTREGUL ARTICOL ÎN ROMÂNĂ.',

      '-t': 'Formatează ÎNTREGUL conținut ca tabele și date structurate. Creează minimum 3 tabele cu informații organizate în coloane și rânduri. Fiecare tabel să aibă titlu și explicații. SCRIE TOT ÎN ROMÂNĂ.',

      '-ex': 'Include EXACT 3 exemple practice detaliate cu explicații pas cu pas. Fiecare exemplu să aibă: context, implementare, rezultat așteptat. Numerotează exemplele 1, 2, 3. SCRIE TOT ÎN ROMÂNĂ.',

      '-p': 'Include OBLIGATORIU exemple de cod și demonstrații tehnice. Minimum 2 blocuri de cod cu explicații linie cu linie. Adaugă comentarii în cod. SCRIE TOT ÎN ROMÂNĂ.',

      '-q': 'Creează EXACT 5 întrebări tip grilă la sfârșitul articolului. Fiecare întrebare să aibă 4 variante (A, B, C, D). Include baremul cu răspunsurile corecte la final. SCRIE TOT ÎN ROMÂNĂ.',

      '-rap': 'Scrie un raport exhaustiv (800-1200 cuvinte) cu acoperire comprehensivă: rezumat executiv, analiză detaliată, concluzii și recomandări. SCRIE TOT ÎN ROMÂNĂ.',

      '-def': 'Focalizează-te pe definiții de nivel expert și terminologie tehnică. Include minimum 10 termeni specializați cu definiții precise. SCRIE TOT ÎN ROMÂNĂ.',

      // Learning & Visualization flags
      '-path': 'Creează o cale de învățare personalizată cu 5-7 pași concreți, milestone-uri măsurabile și estimări de timp pentru fiecare etapă. SCRIE TOT ÎN ROMÂNĂ.',

      '-vis': 'Generează descrieri detaliate pentru minimum 3 infografice/diagrame. Pentru fiecare diagramă: titlu, elemente vizuale, culori sugerate, și explicația fiecărui element. SCRIE TOT ÎN ROMÂNĂ.',

      '-mind': 'Prezintă informația ca o hartă mentală interactivă cu concepte conectate. Descrie structura: nod central, 5-8 ramuri principale, sub-ramuri și conexiuni între concepte. SCRIE TOT ÎN ROMÂNĂ.',

      '-flow': 'Creează diagrame de flux și procese cu puncte de decizie și rezultate. Include minimum 2 flowchart-uri cu forme geometrice specifice și săgeți directionale. SCRIE TOT ÎN ROMÂNĂ.',

      // Industry-specific flags
      '-case': 'Include 2-3 studii de caz reale cu rezultate măsurabile și analiză detaliată. Pentru fiecare caz: context, provocări, soluții implementate, rezultate concrete cu cifre. SCRIE TOT ÎN ROMÂNĂ.',

      '-calc': 'Include calculatoare și instrumente interactive pentru calcule cu formule. Prezintă minimum 3 formule matematice cu exemple numerice concrete. SCRIE TOT ÎN ROMÂNĂ.',

      '-game': 'Adaugă elemente de gamificare cu puncte, realizări și competiții. Creează un sistem de punctaj cu 5 nivele și recompense pentru fiecare nivel. SCRIE TOT ÎN ROMÂNĂ.',

      // Localization flags
      '-ro': 'Adaptează pentru piața românească și legislația locală cu exemple românești. Include referințe la legi românești, companii românești și practici locale specifice. SCRIE TOT ÎN ROMÂNĂ.',

      // Advanced features flags
      '-auto': 'Focalizează-te pe automatizarea proceselor cu instrumente și pași de implementare. Include minimum 3 instrumente software cu tutorial de configurare. SCRIE TOT ÎN ROMÂNĂ.'
    },
    en: {
      // English versions
      '-a': 'Write a comprehensive informative article (600-800 words) with clear structure: introduction, development with 3-4 main sections, and conclusion. WRITE THE ENTIRE ARTICLE IN ENGLISH.',
      '-t': 'Format ALL content as tables and structured data. Create minimum 3 tables with organized information in columns and rows. Each table should have title and explanations. WRITE EVERYTHING IN ENGLISH.',
      '-ex': 'Include EXACTLY 3 detailed practical examples with step-by-step explanations. Each example should have: context, implementation, expected result. Number examples 1, 2, 3. WRITE EVERYTHING IN ENGLISH.',
      '-p': 'Include MANDATORY code examples and technical demonstrations. Minimum 2 code blocks with line-by-line explanations. Add comments in code. WRITE EVERYTHING IN ENGLISH.',
      '-q': 'Create EXACTLY 5 multiple choice questions at the end of the article. Each question should have 4 options (A, B, C, D). Include answer key with correct answers at the end. WRITE EVERYTHING IN ENGLISH.',
      '-rap': 'Write an exhaustive report (800-1200 words) with comprehensive coverage: executive summary, detailed analysis, conclusions and recommendations. WRITE EVERYTHING IN ENGLISH.',
      '-def': 'Focus on expert-level definitions and technical terminology. Include minimum 10 specialized terms with precise definitions. WRITE EVERYTHING IN ENGLISH.',
      '-path': 'Create a personalized learning path with 5-7 concrete steps, measurable milestones and time estimates for each stage. WRITE EVERYTHING IN ENGLISH.',
      '-vis': 'Generate detailed descriptions for minimum 3 infographics/diagrams. For each diagram: title, visual elements, suggested colors, and explanation of each element. WRITE EVERYTHING IN ENGLISH.',
      '-mind': 'Present information as an interactive mind map with connected concepts. Describe structure: central node, 5-8 main branches, sub-branches and connections between concepts. WRITE EVERYTHING IN ENGLISH.',
      '-flow': 'Create flow diagrams and processes with decision points and results. Include minimum 2 flowcharts with specific geometric shapes and directional arrows. WRITE EVERYTHING IN ENGLISH.',
      '-case': 'Include 2-3 real case studies with measurable results and detailed analysis. For each case: context, challenges, implemented solutions, concrete results with figures. WRITE EVERYTHING IN ENGLISH.',
      '-calc': 'Include calculators and interactive tools for calculations with formulas. Present minimum 3 mathematical formulas with concrete numerical examples. WRITE EVERYTHING IN ENGLISH.',
      '-game': 'Add gamification elements with points, achievements and competitions. Create a scoring system with 5 levels and rewards for each level. WRITE EVERYTHING IN ENGLISH.',
      '-ro': 'Adapt for Romanian market and local legislation with Romanian examples. Include references to Romanian laws, Romanian companies and specific local practices. WRITE EVERYTHING IN ENGLISH.',
      '-auto': 'Focus on process automation with tools and implementation steps. Include minimum 3 software tools with configuration tutorial. WRITE EVERYTHING IN ENGLISH.'
    }
  };

  const currentFlagInstructions = flagInstructions[currentLang] || flagInstructions.ro;

  // Combine selected flag instructions with language support
  const selectedInstructions = flags.map(flag => currentFlagInstructions[flag] || '').filter(Boolean);
  const combinedInstructions = selectedInstructions.join(' ');

  // Language-specific prompts
  const languagePrompts = {
    ro: {
      expertRole: `Ești un expert în ${topic}. Creează un articol detaliat despre "${branch.nume}" în contextul "${topic}".`,
      description: `Descrierea ramuri: ${branch.descriere}`,
      subcategories: branch.subcategorii ? `Subcategorii: ${branch.subcategorii.join(', ')}` : '',
      important: `IMPORTANT:
- Articolul trebuie să fie în format JSON cu structura: {"titlu": "...", "continut": "..."}
- Conținutul să fie în format markdown
- Să fie informativ și detaliat (600-800 cuvinte)
- Să includă exemple practice și aplicații concrete
- Să respecte EXACT instrucțiunile flag-urilor selectate
- SCRIE ÎNTREGUL ARTICOL ÎN ROMÂNĂ - nu amesteca cu engleză!
- Folosește minimum 5 surse web pentru informații actualizate`,
      response: `Răspunde DOAR cu JSON-ul, fără text suplimentar.`
    },
    en: {
      expertRole: `You are an expert in ${topic}. Create a detailed article about "${branch.nume}" in the context of "${topic}".`,
      description: `Branch description: ${branch.descriere}`,
      subcategories: branch.subcategorii ? `Subcategories: ${branch.subcategorii.join(', ')}` : '',
      important: `IMPORTANT:
- The article must be in JSON format with structure: {"titlu": "...", "continut": "..."}
- Content should be in markdown format
- Should be informative and detailed (600-800 words)
- Should include practical examples and concrete applications
- Must follow EXACTLY the selected flag instructions
- WRITE THE ENTIRE ARTICLE IN ENGLISH - don't mix with other languages!
- Use minimum 5 web sources for updated information`,
      response: `Respond ONLY with the JSON, without additional text.`
    }
  };

  const currentPrompts = languagePrompts[currentLang] || languagePrompts.ro;

  // Search for web sources with language-specific queries
  let webSources = [];
  let sourcesContext = '';

  try {
    console.log('🔍 Searching for web sources for:', branch.nume, 'in language:', currentLang);

    // Language-specific search query
    const searchQuery = currentLang === 'ro'
      ? `${topic} ${branch.nume} română`
      : `${topic} ${branch.nume}`;

    webSources = await webSearchService.searchSources(searchQuery, 5);
    console.log('✅ Found sources:', webSources.length, 'sources');

    if (webSources.length > 0) {
      const sourcesText = currentLang === 'ro'
        ? `\n\nIMPORTANT: Include aceste surse web în articol:\n${webSources.map(source =>
            `- ${source.title}: ${source.description} (${source.source})`
          ).join('\n')}\n\nAsigură-te că referențiezi aceste surse în conținut și adaugă o secțiune "Surse și Lectură Suplimentară" la final.`
        : `\n\nIMPORTANT: Include these web sources in the article:\n${webSources.map(source =>
            `- ${source.title}: ${source.description} (${source.source})`
          ).join('\n')}\n\nMake sure to reference these sources in the content and add a "Sources & Further Reading" section at the end.`;

      sourcesContext = sourcesText;
    }
  } catch (error) {
    console.warn('⚠️ Web search failed, continuing without sources:', error);
  }

  const prompt = `${currentPrompts.expertRole}

${currentPrompts.description}
${currentPrompts.subcategories}

${combinedInstructions}

${currentPrompts.important}

${sourcesContext}

${currentPrompts.response}`;

  try {
    console.log('🤖 Using DeepSeek R1 model for article generation...');

    // Use DeepSeek R1 model specifically
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer 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`,
        'Content-Type': 'application/json',
        'HTTP-Referer': window.location.origin,
        'X-Title': 'Knowledge Tree Explorer'
      },
      body: JSON.stringify({
        model: 'deepseek/deepseek-r1-0528:free', // Use R1 model specifically
        messages: [
          {
            role: 'system',
            content: currentLang === 'ro'
              ? 'Ești un expert în crearea de conținut educațional. Generează articole comprehensive, bine structurate în format JSON valid. Nu include text explicativ în afara JSON-ului. SCRIE ÎNTREGUL ARTICOL ÎN ROMÂNĂ.'
              : 'You are an expert content writer and educator. Generate comprehensive, well-structured articles in valid JSON format only. Do not include any explanatory text outside the JSON. WRITE THE ENTIRE ARTICLE IN ENGLISH.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.8,
        max_tokens: 4000,
        top_p: 0.9
      })
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const responseText = data.choices[0]?.message?.content || '';

    console.log('✅ R1 model response received, length:', responseText.length);

    // Parse and validate the JSON response
    const cleanResponse = responseText.trim();
    const jsonMatch = cleanResponse.match(/\{[\s\S]*\}/);

    if (!jsonMatch) {
      throw new Error('No valid JSON found in response');
    }

    const article = JSON.parse(jsonMatch[0]);

    // Validate structure
    if (!article.titlu || !article.continut) {
      throw new Error('Invalid article structure');
    }

    // Add web sources to the article
    if (webSources.length > 0) {
      const sourcesHTML = webSearchService.formatSourcesHTML(webSources);
      const sourcesTitle = currentLang === 'ro' ? '\n\n## Surse și Lectură Suplimentară\n\n' : '\n\n## Sources & Further Reading\n\n';
      article.continut += sourcesTitle + sourcesHTML;
      article.webSources = webSources;
    }

    console.log('✅ Article generated successfully with R1 model');
    return article;

  } catch (error) {
    console.error('❌ Error generating article with R1:', error);

    // Language-specific fallback article
    const fallbackContent = currentLang === 'ro' ? {
      titlu: `${branch.nume} - ${topic}`,
      continut: `# ${branch.nume}

Această secțiune explorează ${branch.nume} în contextul ${topic}.

## Prezentare Generală
${branch.descriere}

## Concepte Cheie
Înțelegerea ${branch.nume} este esențială pentru stăpânirea ${topic}. Această zonă acoperă principii fundamentale și aplicații practice.

## Aplicații
Conceptele din ${branch.nume} au aplicații extinse în diverse domenii și industrii.

## Învățare Suplimentară
Pentru a vă aprofunda înțelegerea, luați în considerare explorarea subiectelor conexe și exercițiilor practice.`,
      subcategorii: branch.subcategorii || [],
      flags: flags,
      pozitie: `${topic} → ${branch.nume}`
    } : {
      titlu: `${branch.nume} - ${topic}`,
      continut: `# ${branch.nume}

This section explores ${branch.nume} in the context of ${topic}.

## Overview
${branch.descriere}

## Key Concepts
Understanding ${branch.nume} is essential for mastering ${topic}. This area covers fundamental principles and practical applications.

## Applications
The concepts in ${branch.nume} have wide-ranging applications across various domains and industries.

## Further Learning
To deepen your understanding, consider exploring related topics and practical exercises.`,
      subcategorii: branch.subcategorii || [],
      flags: flags,
      pozitie: `${topic} → ${branch.nume}`
    };

    return fallbackContent;
  }
}

// Test API connection
export async function testConnection() {
  try {
    const response = await client.makeRequest([
      {
        role: 'user',
        content: 'Hello, please respond with "API connection successful"'
      }
    ]);
    
    return response.includes('successful');
  } catch (error) {
    console.error('API connection test failed:', error);
    return false;
  }
}
