// OpenRouter API Service for Knowledge Tree Generation
// STRICT IMPLEMENTATION - DeepSeek R1 + Web Search MANDATORY
import webSearchService from './webSearchService';

// CRITICAL: API Key și configurație EXACTĂ conform cerințelor
const OPENROUTER_API_KEY = '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';
const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';
const MODEL = 'deepseek/deepseek-r1-0528:free'; // OBLIGATORIU - DeepSeek R1 0528 free

// Site configuration for OpenRouter rankings
const SITE_CONFIG = {
  'HTTP-Referer': process.env.REACT_APP_SITE_URL || 'http://localhost:3000',
  'X-Title': process.env.REACT_APP_SITE_NAME || 'Knowledge Tree Explorer'
};

// CRITICAL: STRICT OpenRouter Client Implementation
// EXACT structure as specified - NO DEVIATIONS ALLOWED
class OpenRouterClient {
  constructor() {
    this.baseURL = OPENROUTER_BASE_URL;
    this.apiKey = OPENROUTER_API_KEY;

    // CRITICAL: Verify exact API key and model
    console.log('🔒 STRICT OpenRouter Client initialized:');
    console.log('- Base URL:', this.baseURL);
    console.log('- Model:', MODEL, '(MUST be deepseek/deepseek-r1-0528:free)');
    console.log('- API Key:', this.apiKey ? `${this.apiKey.substring(0, 15)}...` : 'CRITICAL ERROR: NOT SET');
    console.log('- Site Config:', SITE_CONFIG);

    if (!this.apiKey || this.apiKey === 'your-api-key-here') {
      throw new Error('CRITICAL: API Key not properly configured');
    }
  }

  // MANDATORY: Exact structure as specified in requirements
  async makeRequest(messages, temperature = 0.7, maxTokens = 2000) {
    console.log('🔒 STRICT API Call - DeepSeek R1 0528 free model ONLY');

    try {
      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': SITE_CONFIG['HTTP-Referer'],
          'X-Title': SITE_CONFIG['X-Title']
        },
        body: JSON.stringify({
          model: MODEL, // CRITICAL: MUST be deepseek/deepseek-r1-0528:free
          messages,
          temperature,
          max_tokens: maxTokens
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('❌ CRITICAL API ERROR:', response.status, errorData);
        throw new Error(`CRITICAL OpenRouter API Error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
      }

      const data = await response.json();
      console.log('✅ VERIFIED: API Response received from DeepSeek R1');

      if (!data.choices || !data.choices[0] || !data.choices[0].message) {
        throw new Error('CRITICAL: Invalid API response structure');
      }

      return data.choices[0].message.content;
    } catch (error) {
      console.error('❌ CRITICAL: OpenRouter API request failed:', error);
      throw error;
    }
  }

  // MANDATORY: Web search validation for ALL content - NO EXCEPTIONS
  async validateWithWebSearch(topic, content) {
    console.log('🔍 MANDATORY: Validating content with web search for:', topic);

    try {
      const webSources = await webSearchService.searchSources(topic, 5);
      console.log('✅ VERIFIED: Web validation completed with', webSources.length, 'sources');

      if (webSources.length === 0) {
        console.warn('⚠️ WARNING: No web sources found for validation');
      }

      return {
        validated: true,
        sources: webSources,
        content: content,
        validationTimestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ CRITICAL: Web validation failed:', error);
      throw new Error('CRITICAL: Content validation failed - web search required for all content');
    }
  }
}

const client = new OpenRouterClient();

// CRITICAL: Generate Knowledge Tree with MANDATORY AI + Web Search validation
export async function generateKnowledgeTree(topic, language = 'en') {
  console.log('🔒 STRICT: Starting knowledge tree generation with mandatory validation');
  console.log('- Topic:', topic);
  console.log('- Language:', language);

  // Get current language from localStorage if not provided
  const currentLang = language || localStorage.getItem('language') || 'en';

  // MANDATORY: Web search validation BEFORE AI generation
  console.log('🔍 STEP 1: MANDATORY web search validation for topic:', topic);
  let webValidation;
  try {
    webValidation = await client.validateWithWebSearch(topic, 'Initial topic validation');
    console.log('✅ VERIFIED: Topic validated with web sources');
  } catch (error) {
    console.error('❌ CRITICAL: Topic validation failed:', error);
    throw new Error(`CRITICAL: Cannot proceed without web validation for topic: ${topic}`);
  }

  const prompts = {
    ro: `Analizează cu atenție subiectul "${topic}" și creează un arbore de cunoștințe FOARTE SPECIFIC și RELEVANT pentru acest domeniu exact.

IMPORTANT: Generează ramuri care sunt DIRECT LEGATE de "${topic}", nu concepte generale!

Returnează DOAR un obiect JSON valid cu această structură exactă:
{
  "tema": "${topic}",
  "ramuri": [
    {
      "nume": "Nume Ramură Specifică",
      "descriere": "Descriere scurtă și precisă",
      "emoji": "📚",
      "subcategorii": ["subcategorie1", "subcategorie2", "subcategorie3"]
    }
  ]
}

Cerințe STRICTE:
- Generează 6-8 ramuri principale SPECIFICE pentru "${topic}"
- Fiecare ramură TREBUIE să fie direct legată de subiectul principal
- Emoji-uri relevante pentru fiecare ramură
- 3-4 subcategorii specifice pentru fiecare ramură
- Descrieri de maxim 1 propoziție
- Focalizează-te pe aspectele PRACTICE și APLICABILE
- Evită conceptele generale sau irelevante

Subiect: ${topic}`,

    en: `Analyze the subject "${topic}" carefully and create a VERY SPECIFIC and RELEVANT knowledge tree for this exact domain.

IMPORTANT: Generate branches that are DIRECTLY RELATED to "${topic}", not general concepts!

Return ONLY a valid JSON object with this exact structure:
{
  "tema": "${topic}",
  "ramuri": [
    {
      "nume": "Specific Branch Name",
      "descriere": "Short and precise description",
      "emoji": "📚",
      "subcategorii": ["subcategory1", "subcategory2", "subcategory3"]
    }
  ]
}

STRICT Requirements:
- Generate 6-8 main branches SPECIFIC to "${topic}"
- Each branch MUST be directly related to the main subject
- Relevant emojis for each branch
- 3-4 specific subcategories for each branch
- Descriptions of maximum 1 sentence
- Focus on PRACTICAL and APPLICABLE aspects
- Avoid general or irrelevant concepts

Subject: ${topic}`
  };

  const prompt = prompts[currentLang] || prompts.en;

  // STEP 2: AI Generation with DeepSeek R1 ONLY
  console.log('🤖 STEP 2: AI generation with DeepSeek R1 0528 free model');
  let aiResponse;
  try {
    aiResponse = await client.makeRequest([
      {
        role: 'system',
        content: currentLang === 'ro'
          ? 'Expert în organizarea cunoștințelor. Generează arbori de cunoștințe specifici în format JSON valid. Răspunde DOAR cu JSON, fără text explicativ. FOLOSEȘTE DOAR INFORMAȚII VERIFICATE.'
          : 'Expert in knowledge organization. Generate specific knowledge trees in valid JSON format. Respond ONLY with JSON, no explanatory text. USE ONLY VERIFIED INFORMATION.'
      },
      {
        role: 'user',
        content: prompt + `\n\nIMPORTANT: Bazează-te pe aceste surse web verificate: ${webValidation.sources.map(s => s.title).join(', ')}`
      }
    ], 0.3); // Temperatură mai mică pentru răspunsuri mai consistente și rapide

    console.log('✅ VERIFIED: AI response received from DeepSeek R1');

    // STEP 3: Parse and validate the JSON response
    console.log('🔍 STEP 3: Parsing and validating AI response');
    const cleanResponse = aiResponse.trim();
    const jsonMatch = cleanResponse.match(/\{[\s\S]*\}/);

    if (!jsonMatch) {
      throw new Error('CRITICAL: No valid JSON found in AI response');
    }

    const tree = JSON.parse(jsonMatch[0]);

    // STEP 4: Strict structure validation
    console.log('🔍 STEP 4: Strict structure validation');
    if (!tree.tema || !Array.isArray(tree.ramuri)) {
      throw new Error('CRITICAL: Invalid tree structure from AI');
    }

    if (tree.ramuri.length === 0) {
      throw new Error('CRITICAL: No branches generated by AI');
    }

    // STEP 5: Final validation with web sources
    console.log('🔍 STEP 5: Final validation with web sources');
    const finalValidation = await client.validateWithWebSearch(
      `${topic} knowledge tree branches`,
      JSON.stringify(tree)
    );

    // Add validation metadata
    tree._validation = {
      webSources: finalValidation.sources,
      validatedAt: finalValidation.validationTimestamp,
      aiModel: MODEL,
      strictMode: true
    };

    console.log('✅ SUCCESS: Knowledge tree generated and validated with strict mode');
    return tree;
  } catch (error) {
    console.error('Error generating knowledge tree:', error);
    
    // Fallback tree structure based on language
    const fallbacks = {
      ro: {
        tema: topic,
        ramuri: [
          {
            nume: "Fundamentele",
            descriere: `Concepte de bază și principii ale ${topic}`,
            emoji: "📚",
            subcategorii: ["Concepte Esențiale", "Principii Cheie", "Teoria de Bază"]
          },
          {
            nume: "Aplicații",
            descriere: `Aplicații practice și cazuri de utilizare ale ${topic}`,
            emoji: "🔧",
            subcategorii: ["Utilizări Reale", "Aplicații Industriale", "Studii de Caz"]
          },
          {
            nume: "Subiecte Avansate",
            descriere: `Aspecte complexe și specializate ale ${topic}`,
            emoji: "🎓",
            subcategorii: ["Nivel Expert", "Zone de Cercetare", "Tehnologii Noi"]
          }
        ]
      },
      en: {
        tema: topic,
        ramuri: [
          {
            nume: "Fundamentals",
            descriere: `Basic concepts and principles of ${topic}`,
            emoji: "📚",
            subcategorii: ["Core Concepts", "Key Principles", "Basic Theory"]
          },
          {
            nume: "Applications",
            descriere: `Practical applications and use cases of ${topic}`,
            emoji: "🔧",
            subcategorii: ["Real-world Uses", "Industry Applications", "Case Studies"]
          },
          {
            nume: "Advanced Topics",
            descriere: `Complex and specialized aspects of ${topic}`,
            emoji: "🎓",
            subcategorii: ["Expert Level", "Research Areas", "Cutting Edge"]
          }
        ]
      }
    };

    return fallbacks[currentLang] || fallbacks.en;
  }
}

// CRITICAL: Generate Article with MANDATORY AI + Web Search validation
export async function generateArticle(topic, branch, flags = ['-a']) {
  console.log('🔒 STRICT: Starting article generation with mandatory validation');
  console.log('- Topic:', topic);
  console.log('- Branch:', branch.nume);
  console.log('- Flags:', flags);

  // Get current language from localStorage
  const currentLang = localStorage.getItem('language') || 'ro';

  // MANDATORY: Web search validation BEFORE AI generation
  console.log('🔍 STEP 1: MANDATORY web search validation for article topic');
  const articleTopic = `${topic} ${branch.nume}`;
  let webValidation;
  try {
    webValidation = await client.validateWithWebSearch(articleTopic, 'Article topic validation');
    console.log('✅ VERIFIED: Article topic validated with', webValidation.sources.length, 'web sources');

    if (webValidation.sources.length === 0) {
      throw new Error('No web sources found for validation');
    }
  } catch (error) {
    console.error('❌ CRITICAL: Article topic validation failed:', error);
    throw new Error(`CRITICAL: Cannot proceed without web validation for article: ${articleTopic}`);
  }

  const flagInstructions = {
    ro: {
      // Basic flags cu prompt-uri FOARTE SPECIFICE în ROMÂNĂ
      '-a': 'Scrie un articol informativ standard (600-800 cuvinte) cu structură clară: introducere, dezvoltare cu 3-4 secțiuni principale, și concluzie. SCRIE ÎNTREGUL ARTICOL ÎN ROMÂNĂ.',

      '-t': 'Formatează ÎNTREGUL conținut ca tabele și date structurate. Creează minimum 3 tabele cu informații organizate în coloane și rânduri. Fiecare tabel să aibă titlu și explicații. SCRIE TOT ÎN ROMÂNĂ.',

      '-ex': 'Include EXACT 3 exemple practice detaliate cu explicații pas cu pas. Fiecare exemplu să aibă: context, implementare, rezultat așteptat. Numerotează exemplele 1, 2, 3. SCRIE TOT ÎN ROMÂNĂ.',

      '-p': 'Include OBLIGATORIU exemple de cod și demonstrații tehnice. Minimum 2 blocuri de cod cu explicații linie cu linie. Adaugă comentarii în cod. SCRIE TOT ÎN ROMÂNĂ.',

      '-q': 'Creează EXACT 5 întrebări tip grilă la sfârșitul articolului. Fiecare întrebare să aibă 4 variante (A, B, C, D). Include baremul cu răspunsurile corecte la final. SCRIE TOT ÎN ROMÂNĂ.',

      '-rap': 'Scrie un raport exhaustiv (800-1200 cuvinte) cu acoperire comprehensivă: rezumat executiv, analiză detaliată, concluzii și recomandări. SCRIE TOT ÎN ROMÂNĂ.',

      '-def': 'Focalizează-te pe definiții de nivel expert și terminologie tehnică. Include minimum 10 termeni specializați cu definiții precise. SCRIE TOT ÎN ROMÂNĂ.',

      // Learning & Visualization flags
      '-path': 'Creează o cale de învățare personalizată cu 5-7 pași concreți, milestone-uri măsurabile și estimări de timp pentru fiecare etapă. SCRIE TOT ÎN ROMÂNĂ.',

      '-vis': 'Generează descrieri detaliate pentru minimum 3 infografice/diagrame. Pentru fiecare diagramă: titlu, elemente vizuale, culori sugerate, și explicația fiecărui element. SCRIE TOT ÎN ROMÂNĂ.',

      '-mind': 'Prezintă informația ca o hartă mentală interactivă cu concepte conectate. Descrie structura: nod central, 5-8 ramuri principale, sub-ramuri și conexiuni între concepte. SCRIE TOT ÎN ROMÂNĂ.',

      '-flow': 'Creează diagrame de flux și procese cu puncte de decizie și rezultate. Include minimum 2 flowchart-uri cu forme geometrice specifice și săgeți directionale. SCRIE TOT ÎN ROMÂNĂ.',

      // Industry-specific flags
      '-case': 'Include 2-3 studii de caz reale cu rezultate măsurabile și analiză detaliată. Pentru fiecare caz: context, provocări, soluții implementate, rezultate concrete cu cifre. SCRIE TOT ÎN ROMÂNĂ.',

      '-calc': 'Include calculatoare și instrumente interactive pentru calcule cu formule. Prezintă minimum 3 formule matematice cu exemple numerice concrete. SCRIE TOT ÎN ROMÂNĂ.',

      '-game': 'Adaugă elemente de gamificare cu puncte, realizări și competiții. Creează un sistem de punctaj cu 5 nivele și recompense pentru fiecare nivel. SCRIE TOT ÎN ROMÂNĂ.',

      // Localization flags
      '-ro': 'Adaptează pentru piața românească și legislația locală cu exemple românești. Include referințe la legi românești, companii românești și practici locale specifice. SCRIE TOT ÎN ROMÂNĂ.',

      // Advanced features flags
      '-auto': 'Focalizează-te pe automatizarea proceselor cu instrumente și pași de implementare. Include minimum 3 instrumente software cu tutorial de configurare. SCRIE TOT ÎN ROMÂNĂ.'
    },
    en: {
      // English versions
      '-a': 'Write a comprehensive informative article (600-800 words) with clear structure: introduction, development with 3-4 main sections, and conclusion. WRITE THE ENTIRE ARTICLE IN ENGLISH.',
      '-t': 'Format ALL content as tables and structured data. Create minimum 3 tables with organized information in columns and rows. Each table should have title and explanations. WRITE EVERYTHING IN ENGLISH.',
      '-ex': 'Include EXACTLY 3 detailed practical examples with step-by-step explanations. Each example should have: context, implementation, expected result. Number examples 1, 2, 3. WRITE EVERYTHING IN ENGLISH.',
      '-p': 'Include MANDATORY code examples and technical demonstrations. Minimum 2 code blocks with line-by-line explanations. Add comments in code. WRITE EVERYTHING IN ENGLISH.',
      '-q': 'Create EXACTLY 5 multiple choice questions at the end of the article. Each question should have 4 options (A, B, C, D). Include answer key with correct answers at the end. WRITE EVERYTHING IN ENGLISH.',
      '-rap': 'Write an exhaustive report (800-1200 words) with comprehensive coverage: executive summary, detailed analysis, conclusions and recommendations. WRITE EVERYTHING IN ENGLISH.',
      '-def': 'Focus on expert-level definitions and technical terminology. Include minimum 10 specialized terms with precise definitions. WRITE EVERYTHING IN ENGLISH.',
      '-path': 'Create a personalized learning path with 5-7 concrete steps, measurable milestones and time estimates for each stage. WRITE EVERYTHING IN ENGLISH.',
      '-vis': 'Generate detailed descriptions for minimum 3 infographics/diagrams. For each diagram: title, visual elements, suggested colors, and explanation of each element. WRITE EVERYTHING IN ENGLISH.',
      '-mind': 'Present information as an interactive mind map with connected concepts. Describe structure: central node, 5-8 main branches, sub-branches and connections between concepts. WRITE EVERYTHING IN ENGLISH.',
      '-flow': 'Create flow diagrams and processes with decision points and results. Include minimum 2 flowcharts with specific geometric shapes and directional arrows. WRITE EVERYTHING IN ENGLISH.',
      '-case': 'Include 2-3 real case studies with measurable results and detailed analysis. For each case: context, challenges, implemented solutions, concrete results with figures. WRITE EVERYTHING IN ENGLISH.',
      '-calc': 'Include calculators and interactive tools for calculations with formulas. Present minimum 3 mathematical formulas with concrete numerical examples. WRITE EVERYTHING IN ENGLISH.',
      '-game': 'Add gamification elements with points, achievements and competitions. Create a scoring system with 5 levels and rewards for each level. WRITE EVERYTHING IN ENGLISH.',
      '-ro': 'Adapt for Romanian market and local legislation with Romanian examples. Include references to Romanian laws, Romanian companies and specific local practices. WRITE EVERYTHING IN ENGLISH.',
      '-auto': 'Focus on process automation with tools and implementation steps. Include minimum 3 software tools with configuration tutorial. WRITE EVERYTHING IN ENGLISH.'
    }
  };

  const currentFlagInstructions = flagInstructions[currentLang] || flagInstructions.ro;

  // Combine selected flag instructions with language support
  const selectedInstructions = flags.map(flag => currentFlagInstructions[flag] || '').filter(Boolean);
  const combinedInstructions = selectedInstructions.join(' ');

  // Language-specific prompts
  const languagePrompts = {
    ro: {
      expertRole: `Ești un expert în ${topic}. Creează un articol detaliat despre "${branch.nume}" în contextul "${topic}".`,
      description: `Descrierea ramuri: ${branch.descriere}`,
      subcategories: branch.subcategorii ? `Subcategorii: ${branch.subcategorii.join(', ')}` : '',
      important: `IMPORTANT:
- Articolul trebuie să fie în format JSON cu structura: {"titlu": "...", "continut": "..."}
- Conținutul să fie în format markdown
- Să fie informativ și detaliat (600-800 cuvinte)
- Să includă exemple practice și aplicații concrete
- Să respecte EXACT instrucțiunile flag-urilor selectate
- SCRIE ÎNTREGUL ARTICOL ÎN ROMÂNĂ - nu amesteca cu engleză!
- Folosește minimum 5 surse web pentru informații actualizate`,
      response: `Răspunde DOAR cu JSON-ul, fără text suplimentar.`
    },
    en: {
      expertRole: `You are an expert in ${topic}. Create a detailed article about "${branch.nume}" in the context of "${topic}".`,
      description: `Branch description: ${branch.descriere}`,
      subcategories: branch.subcategorii ? `Subcategories: ${branch.subcategorii.join(', ')}` : '',
      important: `IMPORTANT:
- The article must be in JSON format with structure: {"titlu": "...", "continut": "..."}
- Content should be in markdown format
- Should be informative and detailed (600-800 words)
- Should include practical examples and concrete applications
- Must follow EXACTLY the selected flag instructions
- WRITE THE ENTIRE ARTICLE IN ENGLISH - don't mix with other languages!
- Use minimum 5 web sources for updated information`,
      response: `Respond ONLY with the JSON, without additional text.`
    }
  };

  const currentPrompts = languagePrompts[currentLang] || languagePrompts.ro;

  // STEP 2: Additional web sources for comprehensive validation
  console.log('🔍 STEP 2: Additional web search for comprehensive article sources');
  let additionalWebSources = [];
  let sourcesContext = '';

  try {
    // Language-specific search query
    const searchQuery = currentLang === 'ro'
      ? `${topic} ${branch.nume} română`
      : `${topic} ${branch.nume}`;

    additionalWebSources = await webSearchService.searchSources(searchQuery, 5);
    console.log('✅ VERIFIED: Found additional sources:', additionalWebSources.length, 'sources');

    // Combine with initial validation sources
    const allWebSources = [...webValidation.sources, ...additionalWebSources];

    if (allWebSources.length > 0) {
      const sourcesText = currentLang === 'ro'
        ? `\n\nCRITICAL: Bazează articolul pe aceste surse web VERIFICATE:\n${allWebSources.map(source =>
            `- ${source.title}: ${source.description} (${source.source})`
          ).join('\n')}\n\nOBLIGATORIU: Referențiază aceste surse în conținut și adaugă secțiunea "Surse și Lectură Suplimentară" la final.`
        : `\n\nCRITICAL: Base the article on these VERIFIED web sources:\n${allWebSources.map(source =>
            `- ${source.title}: ${source.description} (${source.source})`
          ).join('\n')}\n\nMANDATORY: Reference these sources in the content and add "Sources & Further Reading" section at the end.`;

      sourcesContext = sourcesText;
    } else {
      throw new Error('CRITICAL: No web sources available for article validation');
    }
  } catch (error) {
    console.error('❌ CRITICAL: Additional web search failed:', error);
    throw new Error('CRITICAL: Cannot proceed without comprehensive web validation');
  }

  const prompt = `${currentPrompts.expertRole}

${currentPrompts.description}
${currentPrompts.subcategories}

${combinedInstructions}

${currentPrompts.important}

${sourcesContext}

${currentPrompts.response}`;

  // STEP 3: STRICT AI Generation with DeepSeek R1 ONLY
  console.log('🤖 STEP 3: STRICT AI generation with DeepSeek R1 0528 free model');
  let aiResponse;
  try {
    // CRITICAL: Use EXACT structure as specified
    aiResponse = await client.makeRequest([
      {
        role: 'system',
        content: currentLang === 'ro'
          ? 'Ești un expert în crearea de conținut educațional. Generează articole comprehensive, bine structurate în format JSON valid. Nu include text explicativ în afara JSON-ului. SCRIE ÎNTREGUL ARTICOL ÎN ROMÂNĂ. FOLOSEȘTE DOAR INFORMAȚII VERIFICATE DIN SURSE WEB.'
          : 'You are an expert content writer and educator. Generate comprehensive, well-structured articles in valid JSON format only. Do not include any explanatory text outside the JSON. WRITE THE ENTIRE ARTICLE IN ENGLISH. USE ONLY VERIFIED INFORMATION FROM WEB SOURCES.'
      },
      {
        role: 'user',
        content: prompt
      }
    ], 0.8, 4000); // temperature, max_tokens

    console.log('✅ VERIFIED: AI response received from DeepSeek R1, length:', aiResponse.length);

    // STEP 4: Parse and validate the JSON response
    console.log('🔍 STEP 4: Parsing and validating AI article response');
    const cleanResponse = aiResponse.trim();
    const jsonMatch = cleanResponse.match(/\{[\s\S]*\}/);

    if (!jsonMatch) {
      throw new Error('CRITICAL: No valid JSON found in AI article response');
    }

    const article = JSON.parse(jsonMatch[0]);

    // STEP 5: Strict structure validation
    console.log('🔍 STEP 5: Strict article structure validation');
    if (!article.titlu || !article.continut) {
      throw new Error('CRITICAL: Invalid article structure from AI');
    }

    if (article.continut.length < 500) {
      throw new Error('CRITICAL: Article content too short - minimum 500 characters required');
    }

    // STEP 6: Final validation with web sources
    console.log('🔍 STEP 6: Final article validation with web sources');
    const finalValidation = await client.validateWithWebSearch(
      `${topic} ${branch.nume} article content`,
      article.continut
    );

    // Combine all web sources
    const allWebSources = [...webValidation.sources, ...additionalWebSources, ...finalValidation.sources];
    const uniqueWebSources = allWebSources.filter((source, index, self) =>
      index === self.findIndex(s => s.url === source.url)
    );

    // Add web sources to the article
    if (uniqueWebSources.length > 0) {
      const sourcesHTML = webSearchService.formatSourcesHTML(uniqueWebSources);
      const sourcesTitle = currentLang === 'ro' ? '\n\n## Surse și Lectură Suplimentară\n\n' : '\n\n## Sources & Further Reading\n\n';
      article.continut += sourcesTitle + sourcesHTML;
      article.webSources = uniqueWebSources;
    }

    // Add validation metadata
    article._validation = {
      webSources: uniqueWebSources,
      validatedAt: finalValidation.validationTimestamp,
      aiModel: MODEL,
      strictMode: true,
      flags: flags
    };

    console.log('✅ SUCCESS: Article generated and validated with strict mode');
    console.log('- Web sources used:', uniqueWebSources.length);
    console.log('- Content length:', article.continut.length);
    console.log('- Flags applied:', flags);

    return article;

  } catch (error) {
    console.error('❌ Error generating article with R1:', error);

    // Language-specific fallback article
    const fallbackContent = currentLang === 'ro' ? {
      titlu: `${branch.nume} - ${topic}`,
      continut: `# ${branch.nume}

Această secțiune explorează ${branch.nume} în contextul ${topic}.

## Prezentare Generală
${branch.descriere}

## Concepte Cheie
Înțelegerea ${branch.nume} este esențială pentru stăpânirea ${topic}. Această zonă acoperă principii fundamentale și aplicații practice.

## Aplicații
Conceptele din ${branch.nume} au aplicații extinse în diverse domenii și industrii.

## Învățare Suplimentară
Pentru a vă aprofunda înțelegerea, luați în considerare explorarea subiectelor conexe și exercițiilor practice.`,
      subcategorii: branch.subcategorii || [],
      flags: flags,
      pozitie: `${topic} → ${branch.nume}`
    } : {
      titlu: `${branch.nume} - ${topic}`,
      continut: `# ${branch.nume}

This section explores ${branch.nume} in the context of ${topic}.

## Overview
${branch.descriere}

## Key Concepts
Understanding ${branch.nume} is essential for mastering ${topic}. This area covers fundamental principles and practical applications.

## Applications
The concepts in ${branch.nume} have wide-ranging applications across various domains and industries.

## Further Learning
To deepen your understanding, consider exploring related topics and practical exercises.`,
      subcategorii: branch.subcategorii || [],
      flags: flags,
      pozitie: `${topic} → ${branch.nume}`
    };

    return fallbackContent;
  }
}

// Test API connection
export async function testConnection() {
  try {
    const response = await client.makeRequest([
      {
        role: 'user',
        content: 'Hello, please respond with "API connection successful"'
      }
    ]);
    
    return response.includes('successful');
  } catch (error) {
    console.error('API connection test failed:', error);
    return false;
  }
}
