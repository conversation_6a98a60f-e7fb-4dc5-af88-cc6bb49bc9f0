// 🌍 Pareto 80/20 Localization System
// Only translates the 20% of text that represents 80% of user experience

import React from 'react';

const translations = {
  en: {
    // Header & Navigation (High Impact)
    appTitle: "🌳 Knowledge Tree Explorer",
    quickLogin: "🚀 Quick Login",
    welcome: "Welcome",
    backToTree: "← Back to Tree",
    
    // Main Actions (Critical)
    exploreKnowledge: "Explore Knowledge 🚀",
    generating: "Generating...",
    loading: "Loading...",
    generate: "🚀 Generate",
    
    // Input & Forms (Essential)
    topicPlaceholder: "e.g., Quantum Physics, Machine Learning, History of Art...",
    loginRequired: "Please log in to access the knowledge tree generator.",
    quickLoginDev: "🚀 Quick Login (Development)",
    
    // Article & Content (User-facing)
    partOf: "Part of:",
    flags: "Flags:",
    topics: "Topics:",
    
    // Gestures & Hints (User Guidance)
    gestureHint: "💡 Double-tap for flags • Long-press for quick article",
    
    // Export & Actions (Functional)
    copyToClipboard: "📋 Copy",
    exportPDF: "📄 PDF", 
    exportWord: "📝 Word",
    
    // Speech Controls (Interactive)
    play: "▶️",
    pause: "⏸️",
    stop: "⏹️",
    speed: "Speed",
    
    // Gamification (Engagement)
    level: "Level",
    points: "Points",
    achievements: "Achievements",
    leaderboard: "🏆 Leaderboard",
    
    // Error Messages (Critical)
    error: "⚠️ Error",
    tryAgain: "Try Again",
    
    // Additional UI Elements
    selectBranch: "Select a branch to explore in detail",
    failedToExpand: "Failed to expand branch. Please try again.",
    sources: "Sources & Further Reading",
    noSources: "No sources available",
    readAloud: "Read Aloud",
    stopReading: "Stop Reading",

    // Flag Descriptions (Content Quality)
    flagArticle: "Comprehensive article format",
    flagExamples: "Include practical examples",
    flagQuiz: "Interactive quiz questions",
    flagVisual: "Include diagrams and visualizations",
    flagPath: "Structured learning progression",
    flagCase: "Real-world case studies",
    flagRomanian: "Adapted for Romanian context"
  },
  
  ro: {
    // Header & Navigation (High Impact)
    appTitle: "🌳 Explorator Arbore Cunoștințe",
    quickLogin: "🚀 Login Rapid",
    welcome: "Bun venit",
    backToTree: "← Înapoi la Arbore",
    
    // Main Actions (Critical)
    exploreKnowledge: "Explorează Cunoștințe 🚀",
    generating: "Se generează...",
    loading: "Se încarcă...",
    generate: "🚀 Generează",
    
    // Input & Forms (Essential)
    topicPlaceholder: "ex: Fizica Cuantică, Machine Learning, Istoria Artei...",
    loginRequired: "Te rugăm să te conectezi pentru a accesa generatorul de arbori de cunoștințe.",
    quickLoginDev: "🚀 Login Rapid (Dezvoltare)",
    
    // Article & Content (User-facing)
    partOf: "Parte din:",
    flags: "Flag-uri:",
    topics: "Subiecte:",
    
    // Gestures & Hints (User Guidance)
    gestureHint: "💡 Dublu-tap pentru flag-uri • Apăsare lungă pentru articol rapid",
    
    // Export & Actions (Functional)
    copyToClipboard: "📋 Copiază",
    exportPDF: "📄 PDF",
    exportWord: "📝 Word",
    
    // Speech Controls (Interactive)
    play: "▶️",
    pause: "⏸️", 
    stop: "⏹️",
    speed: "Viteză",
    
    // Gamification (Engagement)
    level: "Nivel",
    points: "Puncte",
    achievements: "Realizări",
    leaderboard: "🏆 Clasament",
    
    // Error Messages (Critical)
    error: "⚠️ Eroare",
    tryAgain: "Încearcă din nou",
    
    // Additional UI Elements
    selectBranch: "Selectează o ramură pentru a explora în detaliu",
    failedToExpand: "Nu s-a putut extinde ramura. Te rugăm să încerci din nou.",
    sources: "Surse și Lectură Suplimentară",
    noSources: "Nu sunt surse disponibile",
    readAloud: "Citește cu Voce Tare",
    stopReading: "Oprește Citirea",

    // Flag Descriptions (Content Quality)
    flagArticle: "Format articol comprehensiv",
    flagExamples: "Include exemple practice",
    flagQuiz: "Întrebări interactive de quiz",
    flagVisual: "Include diagrame și vizualizări",
    flagPath: "Progresie structurată de învățare",
    flagCase: "Studii de caz din lumea reală",
    flagRomanian: "Adaptat pentru contextul românesc"
  }
};

// Current language state
let currentLanguage = localStorage.getItem('language') || 'en';

// Translation function - Simple and efficient
export const t = (key) => {
  return translations[currentLanguage]?.[key] || translations.en[key] || key;
};

// Language switcher
export const setLanguage = (lang) => {
  if (translations[lang]) {
    currentLanguage = lang;
    localStorage.setItem('language', lang);
    // Trigger re-render by dispatching custom event
    window.dispatchEvent(new CustomEvent('languageChanged', { detail: lang }));
    return true;
  }
  return false;
};

// Get current language
export const getCurrentLanguage = () => currentLanguage;

// Get available languages
export const getAvailableLanguages = () => [
  { code: 'en', name: 'English', flag: 'EN' },
  { code: 'ro', name: 'Română', flag: 'RO' }
];

// Hook for React components to re-render on language change
export const useTranslation = () => {
  const [, forceUpdate] = React.useReducer(x => x + 1, 0);

  React.useEffect(() => {
    const handleLanguageChange = () => forceUpdate();
    window.addEventListener('languageChanged', handleLanguageChange);
    return () => window.removeEventListener('languageChanged', handleLanguageChange);
  }, []);

  return { t, setLanguage, currentLanguage: getCurrentLanguage() };
};

// Default export
const i18nService = { t, setLanguage, getCurrentLanguage, getAvailableLanguages, useTranslation };
export default i18nService;
