{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Appv1\\\\src\\\\components\\\\LanguageSwitcher.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { getAvailableLanguages, getCurrentLanguage, setLanguage } from '../utils/i18n';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LanguageSwitcher = () => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const [currentLang, setCurrentLang] = useState(getCurrentLanguage());\n  const dropdownRef = useRef(null);\n  const languages = getAvailableLanguages();\n  const currentLanguage = languages.find(lang => lang.code === currentLang);\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n        setIsOpen(false);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  // Listen for language changes\n  useEffect(() => {\n    const handleLanguageChange = event => {\n      setCurrentLang(event.detail);\n    };\n    window.addEventListener('languageChanged', handleLanguageChange);\n    return () => window.removeEventListener('languageChanged', handleLanguageChange);\n  }, []);\n  const handleLanguageChange = langCode => {\n    setLanguage(langCode);\n    setCurrentLang(langCode);\n    setIsOpen(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"language-switcher\",\n    ref: dropdownRef,\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"language-button\",\n      onClick: () => setIsOpen(!isOpen),\n      \"aria-label\": \"Change language\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"language-flag\",\n        children: currentLanguage === null || currentLanguage === void 0 ? void 0 : currentLanguage.flag\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"language-code\",\n        children: currentLang.toUpperCase()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"dropdown-arrow\",\n        children: isOpen ? '▲' : '▼'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this), isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"language-dropdown\",\n      children: languages.map(lang => /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `language-option ${lang.code === currentLang ? 'active' : ''}`,\n        onClick: () => handleLanguageChange(lang.code),\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"language-flag\",\n          children: lang.flag\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"language-name\",\n          children: lang.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 15\n        }, this), lang.code === currentLang && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"checkmark\",\n          children: \"\\u2713\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 45\n        }, this)]\n      }, lang.code, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n};\n_s(LanguageSwitcher, \"vkAL6vmgnVJcM+0lSXA57Us354Y=\");\n_c = LanguageSwitcher;\nexport default LanguageSwitcher;\nvar _c;\n$RefreshReg$(_c, \"LanguageSwitcher\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "getAvailableLanguages", "getCurrentLanguage", "setLanguage", "jsxDEV", "_jsxDEV", "LanguageSwitcher", "_s", "isOpen", "setIsOpen", "currentLang", "setCurrentLang", "dropdownRef", "languages", "currentLanguage", "find", "lang", "code", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "handleLanguageChange", "detail", "window", "langCode", "className", "ref", "children", "onClick", "flag", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toUpperCase", "map", "name", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Appv1/src/components/LanguageSwitcher.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { getAvailableLanguages, getCurrentLanguage, setLanguage } from '../utils/i18n';\n\nconst LanguageSwitcher = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [currentLang, setCurrentLang] = useState(getCurrentLanguage());\n  const dropdownRef = useRef(null);\n  \n  const languages = getAvailableLanguages();\n  const currentLanguage = languages.find(lang => lang.code === currentLang);\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n        setIsOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  // Listen for language changes\n  useEffect(() => {\n    const handleLanguageChange = (event) => {\n      setCurrentLang(event.detail);\n    };\n\n    window.addEventListener('languageChanged', handleLanguageChange);\n    return () => window.removeEventListener('languageChanged', handleLanguageChange);\n  }, []);\n\n  const handleLanguageChange = (langCode) => {\n    setLanguage(langCode);\n    setCurrentLang(langCode);\n    setIsOpen(false);\n  };\n\n  return (\n    <div className=\"language-switcher\" ref={dropdownRef}>\n      <button \n        className=\"language-button\"\n        onClick={() => setIsOpen(!isOpen)}\n        aria-label=\"Change language\"\n      >\n        <span className=\"language-flag\">{currentLanguage?.flag}</span>\n        <span className=\"language-code\">{currentLang.toUpperCase()}</span>\n        <span className=\"dropdown-arrow\">{isOpen ? '▲' : '▼'}</span>\n      </button>\n      \n      {isOpen && (\n        <div className=\"language-dropdown\">\n          {languages.map(lang => (\n            <button\n              key={lang.code}\n              className={`language-option ${lang.code === currentLang ? 'active' : ''}`}\n              onClick={() => handleLanguageChange(lang.code)}\n            >\n              <span className=\"language-flag\">{lang.flag}</span>\n              <span className=\"language-name\">{lang.name}</span>\n              {lang.code === currentLang && <span className=\"checkmark\">✓</span>}\n            </button>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default LanguageSwitcher;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,qBAAqB,EAAEC,kBAAkB,EAAEC,WAAW,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvF,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACY,WAAW,EAAEC,cAAc,CAAC,GAAGb,QAAQ,CAACI,kBAAkB,CAAC,CAAC,CAAC;EACpE,MAAMU,WAAW,GAAGb,MAAM,CAAC,IAAI,CAAC;EAEhC,MAAMc,SAAS,GAAGZ,qBAAqB,CAAC,CAAC;EACzC,MAAMa,eAAe,GAAGD,SAAS,CAACE,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAKP,WAAW,CAAC;;EAEzE;EACAV,SAAS,CAAC,MAAM;IACd,MAAMkB,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAIP,WAAW,CAACQ,OAAO,IAAI,CAACR,WAAW,CAACQ,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QACtEb,SAAS,CAAC,KAAK,CAAC;MAClB;IACF,CAAC;IAEDc,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;IAC1D,OAAO,MAAMK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;EAC5E,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAlB,SAAS,CAAC,MAAM;IACd,MAAM0B,oBAAoB,GAAIP,KAAK,IAAK;MACtCR,cAAc,CAACQ,KAAK,CAACQ,MAAM,CAAC;IAC9B,CAAC;IAEDC,MAAM,CAACJ,gBAAgB,CAAC,iBAAiB,EAAEE,oBAAoB,CAAC;IAChE,OAAO,MAAME,MAAM,CAACH,mBAAmB,CAAC,iBAAiB,EAAEC,oBAAoB,CAAC;EAClF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,oBAAoB,GAAIG,QAAQ,IAAK;IACzC1B,WAAW,CAAC0B,QAAQ,CAAC;IACrBlB,cAAc,CAACkB,QAAQ,CAAC;IACxBpB,SAAS,CAAC,KAAK,CAAC;EAClB,CAAC;EAED,oBACEJ,OAAA;IAAKyB,SAAS,EAAC,mBAAmB;IAACC,GAAG,EAAEnB,WAAY;IAAAoB,QAAA,gBAClD3B,OAAA;MACEyB,SAAS,EAAC,iBAAiB;MAC3BG,OAAO,EAAEA,CAAA,KAAMxB,SAAS,CAAC,CAACD,MAAM,CAAE;MAClC,cAAW,iBAAiB;MAAAwB,QAAA,gBAE5B3B,OAAA;QAAMyB,SAAS,EAAC,eAAe;QAAAE,QAAA,EAAElB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEoB;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC9DjC,OAAA;QAAMyB,SAAS,EAAC,eAAe;QAAAE,QAAA,EAAEtB,WAAW,CAAC6B,WAAW,CAAC;MAAC;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAClEjC,OAAA;QAAMyB,SAAS,EAAC,gBAAgB;QAAAE,QAAA,EAAExB,MAAM,GAAG,GAAG,GAAG;MAAG;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD,CAAC,EAER9B,MAAM,iBACLH,OAAA;MAAKyB,SAAS,EAAC,mBAAmB;MAAAE,QAAA,EAC/BnB,SAAS,CAAC2B,GAAG,CAACxB,IAAI,iBACjBX,OAAA;QAEEyB,SAAS,EAAE,mBAAmBd,IAAI,CAACC,IAAI,KAAKP,WAAW,GAAG,QAAQ,GAAG,EAAE,EAAG;QAC1EuB,OAAO,EAAEA,CAAA,KAAMP,oBAAoB,CAACV,IAAI,CAACC,IAAI,CAAE;QAAAe,QAAA,gBAE/C3B,OAAA;UAAMyB,SAAS,EAAC,eAAe;UAAAE,QAAA,EAAEhB,IAAI,CAACkB;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAClDjC,OAAA;UAAMyB,SAAS,EAAC,eAAe;UAAAE,QAAA,EAAEhB,IAAI,CAACyB;QAAI;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACjDtB,IAAI,CAACC,IAAI,KAAKP,WAAW,iBAAIL,OAAA;UAAMyB,SAAS,EAAC,WAAW;UAAAE,QAAA,EAAC;QAAC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA,GAN7DtB,IAAI,CAACC,IAAI;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAOR,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC/B,EAAA,CAjEID,gBAAgB;AAAAoC,EAAA,GAAhBpC,gBAAgB;AAmEtB,eAAeA,gBAAgB;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}