// Optimized Gesture Service - Pareto 80/20 Implementation
// Focus on essential gestures that provide maximum value

class GestureService {
  constructor() {
    this.isListening = false;
    this.lastTap = 0;
    this.doubleTapDelay = 300; // ms
    this.callbacks = {
      doubleTap: null,
      singleTap: null,
      longPress: null
    };
    this.longPressTimer = null;
    this.longPressDelay = 500; // ms
  }

  // Initialize gesture listeners
  init(element, callbacks = {}) {
    if (this.isListening) {
      this.destroy();
    }

    this.callbacks = { ...this.callbacks, ...callbacks };
    this.element = element || document;
    this.isListening = true;

    // Add event listeners
    this.element.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: false });
    this.element.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: false });
    this.element.addEventListener('mousedown', this.handleMouseDown.bind(this));
    this.element.addEventListener('mouseup', this.handleMouseUp.bind(this));
    this.element.addEventListener('contextmenu', this.handleContextMenu.bind(this));

    return this;
  }

  // Handle touch start
  handleTouchStart(event) {
    this.startLongPressTimer(event);
  }

  // Handle touch end
  handleTouchEnd(event) {
    this.clearLongPressTimer();
    this.handleTap(event);
  }

  // Handle mouse down
  handleMouseDown(event) {
    this.startLongPressTimer(event);
  }

  // Handle mouse up
  handleMouseUp(event) {
    this.clearLongPressTimer();
    this.handleTap(event);
  }

  // Handle context menu (prevent on long press)
  handleContextMenu(event) {
    event.preventDefault();
  }

  // Handle tap detection (single/double)
  handleTap(event) {
    const now = Date.now();
    const timeSinceLastTap = now - this.lastTap;

    if (timeSinceLastTap < this.doubleTapDelay && timeSinceLastTap > 0) {
      // Double tap detected
      this.lastTap = 0; // Reset to prevent triple tap
      if (this.callbacks.doubleTap) {
        this.callbacks.doubleTap(event, this.getTargetInfo(event));
      }
    } else {
      // Potential single tap - wait to see if double tap follows
      this.lastTap = now;
      setTimeout(() => {
        if (this.lastTap === now && this.callbacks.singleTap) {
          this.callbacks.singleTap(event, this.getTargetInfo(event));
        }
      }, this.doubleTapDelay);
    }
  }

  // Start long press timer
  startLongPressTimer(event) {
    this.clearLongPressTimer();
    this.longPressTimer = setTimeout(() => {
      if (this.callbacks.longPress) {
        this.callbacks.longPress(event, this.getTargetInfo(event));
      }
    }, this.longPressDelay);
  }

  // Clear long press timer
  clearLongPressTimer() {
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }
  }

  // Get target information
  getTargetInfo(event) {
    const target = event.target;
    const rect = target.getBoundingClientRect();
    
    return {
      element: target,
      className: target.className,
      id: target.id,
      dataset: target.dataset,
      position: {
        x: event.clientX || (event.touches && event.touches[0]?.clientX) || 0,
        y: event.clientY || (event.touches && event.touches[0]?.clientY) || 0
      },
      elementRect: rect,
      isBranchItem: target.closest('.branch-item') !== null,
      branchData: this.getBranchData(target)
    };
  }

  // Extract branch data from target
  getBranchData(target) {
    const branchItem = target.closest('.branch-item');
    if (!branchItem) return null;

    return {
      index: branchItem.dataset.index,
      name: branchItem.dataset.name,
      description: branchItem.dataset.description,
      element: branchItem
    };
  }

  // Destroy gesture listeners
  destroy() {
    if (!this.isListening) return;

    this.clearLongPressTimer();
    
    if (this.element) {
      this.element.removeEventListener('touchstart', this.handleTouchStart.bind(this));
      this.element.removeEventListener('touchend', this.handleTouchEnd.bind(this));
      this.element.removeEventListener('mousedown', this.handleMouseDown.bind(this));
      this.element.removeEventListener('mouseup', this.handleMouseUp.bind(this));
      this.element.removeEventListener('contextmenu', this.handleContextMenu.bind(this));
    }

    this.isListening = false;
    this.element = null;
    this.callbacks = {
      doubleTap: null,
      singleTap: null,
      longPress: null
    };
  }

  // Update callbacks
  updateCallbacks(newCallbacks) {
    this.callbacks = { ...this.callbacks, ...newCallbacks };
  }

  // Check if gesture service is active
  isActive() {
    return this.isListening;
  }
}

// Create singleton instance
const gestureService = new GestureService();

export default gestureService;

// Helper function to create flag wheel
export const createFlagWheel = (position, availableFlags, onFlagSelect, onGenerate) => {
  // Remove existing wheel
  const existingWheel = document.querySelector('.flag-wheel');
  if (existingWheel) {
    existingWheel.remove();
  }

  // Create wheel container
  const wheel = document.createElement('div');
  wheel.className = 'flag-wheel';
  wheel.style.cssText = `
    position: fixed;
    left: ${position.x - 100}px;
    top: ${position.y - 100}px;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid #1d4ed8;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    animation: wheelAppear 0.2s ease-out;
  `;

  // Create center generate button
  const centerButton = document.createElement('button');
  centerButton.className = 'wheel-center-btn';
  centerButton.textContent = '🚀 Generate';
  centerButton.style.cssText = `
    position: absolute;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: #1d4ed8;
    color: white;
    border: none;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    z-index: 10001;
    transition: all 0.2s ease;
  `;

  centerButton.addEventListener('click', () => {
    const selectedFlags = Array.from(wheel.querySelectorAll('.flag-item.selected'))
      .map(item => item.dataset.flag);
    onGenerate(selectedFlags);
    wheel.remove();
  });

  // Create flag items around the wheel
  const flagCount = Math.min(availableFlags.length, 8); // Max 8 flags
  const angleStep = (2 * Math.PI) / flagCount;

  availableFlags.slice(0, flagCount).forEach((flag, index) => {
    const angle = index * angleStep - Math.PI / 2; // Start from top
    const radius = 70;
    const x = Math.cos(angle) * radius;
    const y = Math.sin(angle) * radius;

    const flagItem = document.createElement('div');
    flagItem.className = 'flag-item';
    flagItem.dataset.flag = flag.code;
    flagItem.style.cssText = `
      position: absolute;
      left: ${100 + x - 15}px;
      top: ${100 + y - 15}px;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      background: #f1f5f9;
      border: 2px solid #e2e8f0;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 10px;
      font-weight: 600;
      color: #1d4ed8;
      transition: all 0.2s ease;
      user-select: none;
    `;
    // Simboluri intuitive pentru fiecare flag
    const flagSymbols = {
      '-a': '📄',      // Article - document
      '-ex': '💡',     // Examples - lightbulb
      '-q': '❓',      // Quiz - question mark
      '-vis': '📊',    // Visual - chart
      '-path': '🛤️',   // Learning Path - path
      '-case': '📋',   // Case Study - clipboard
      '-ro': '🇷🇴',    // Romanian - flag
      '-t': '📋',      // Tables - table
      '-p': '💻',      // Programming - computer
      '-rap': '📚',    // Report - books
      '-def': '📖',    // Definitions - book
      '-mind': '🧠',   // Mind map - brain
      '-flow': '🔄',   // Flow - cycle
      '-calc': '🧮',   // Calculator - abacus
      '-game': '🎮',   // Game - gamepad
      '-auto': '⚙️'    // Automation - gear
    };

    flagItem.textContent = flagSymbols[flag.code] || flag.code;
    flagItem.title = `${flagSymbols[flag.code] || flag.code} ${flag.name}`;

    // Add hover tooltip
    let tooltip = null;

    flagItem.addEventListener('mouseenter', () => {
      // Remove existing tooltip
      const existingTooltip = document.querySelector('.flag-tooltip');
      if (existingTooltip) existingTooltip.remove();

      // Create tooltip
      tooltip = document.createElement('div');
      tooltip.className = 'flag-tooltip';
      tooltip.innerHTML = `
        <div class="tooltip-content">
          <div class="tooltip-title">${flag.name}</div>
          <div class="tooltip-description">${flag.description}</div>
        </div>
      `;
      tooltip.style.cssText = `
        position: fixed;
        left: ${position.x + 120}px;
        top: ${position.y - 20}px;
        background: rgba(30, 41, 59, 0.95);
        color: white;
        padding: 12px;
        border-radius: 8px;
        font-size: 12px;
        max-width: 200px;
        z-index: 10002;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        backdrop-filter: blur(10px);
        animation: tooltipFadeIn 0.2s ease;
      `;
      document.body.appendChild(tooltip);
    });

    flagItem.addEventListener('mouseleave', () => {
      if (tooltip) {
        tooltip.remove();
        tooltip = null;
      }
    });

    flagItem.addEventListener('click', () => {
      flagItem.classList.toggle('selected');
      if (flagItem.classList.contains('selected')) {
        flagItem.style.background = '#1d4ed8';
        flagItem.style.color = 'white';
        flagItem.style.borderColor = '#1d4ed8';
      } else {
        flagItem.style.background = '#f1f5f9';
        flagItem.style.color = '#1d4ed8';
        flagItem.style.borderColor = '#e2e8f0';
      }
    });

    wheel.appendChild(flagItem);
  });

  wheel.appendChild(centerButton);

  // Add CSS animations and tooltip styles
  const style = document.createElement('style');
  style.textContent = `
    @keyframes wheelAppear {
      from {
        opacity: 0;
        transform: scale(0.5);
      }
      to {
        opacity: 1;
        transform: scale(1);
      }
    }
    @keyframes tooltipFadeIn {
      from {
        opacity: 0;
        transform: translateY(5px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
    .tooltip-content {
      text-align: left;
    }
    .tooltip-title {
      font-weight: 600;
      margin-bottom: 4px;
      color: #60a5fa;
    }
    .tooltip-description {
      font-size: 11px;
      opacity: 0.9;
      line-height: 1.3;
    }
  `;
  document.head.appendChild(style);

  // Add to document
  document.body.appendChild(wheel);

  // Remove wheel when clicking outside
  const removeWheel = (event) => {
    if (!wheel.contains(event.target)) {
      wheel.remove();
      document.removeEventListener('click', removeWheel);
    }
  };
  
  setTimeout(() => {
    document.addEventListener('click', removeWheel);
  }, 100);

  return wheel;
};
