# 🌳 Knowledge Tree Explorer - AI-Powered Learning Platform

> **Versiunea 3.0** - Platformă avansată de învățare cu AI, cu design revoluționar și validare strictă

Un generator interactiv de copaci de cunoștințe alimentat de AI care creează căi de învățare comprehensive pentru orice subiect, folosind modelul DeepSeek R1 și căutare web în timp real cu validare 120% sigură.

## 🔒 **SECURITATE CRITICĂ - Validare Obligatorie AI + Web**

### **Logica Strictă de Validare**
- **TOATE** informațiile trec prin AI (DeepSeek R1) + căutare web obligatorie
- **ZERO** halucinații - doar informații verificate din surse web reale
- **120%** siguranță pentru dezvoltatori - fără riscuri legale
- **Validare în 6 pași** pentru fiecare operațiune:
  1. Validare web inițială a subiectului
  2. Generare AI cu DeepSeek R1 0528 free
  3. Parsing și validare structură JSON
  4. Validare strictă a conținutului
  5. Validare finală cu surse web suplimentare
  6. Metadata de validare și timestamp

### **API Configuration EXACTĂ**
```javascript
// CRITICAL: Structura EXACTĂ - NU se abate de la aceasta
const OPENROUTER_API_KEY = '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';
const MODEL = 'deepseek/deepseek-r1-0528:free'; // OBLIGATORIU

const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
    'Content-Type': 'application/json',
    'HTTP-Referer': 'http://localhost:3000',
    'X-Title': 'Knowledge Tree Explorer'
  },
  body: JSON.stringify({
    model: 'deepseek/deepseek-r1-0528:free',
    messages: [...],
    temperature: 0.7
  })
});
```

## 🎨 **Design Revoluționar - Nou în v3.0**

### 🖥️ **Desktop - Copac Infinit Interactiv**
- **Vizualizare Mind-Map**: Copac infinit cu ramuri în toate direcțiile
- **Pan & Zoom**: Navigare fluidă cu mouse-ul prin spațiul infinit
- **Centru Interactiv**: Subiectul principal în centru, ramurile se extind organic
- **Conexiuni Vizuale**: Linii de conexiune animate între ramuri și centru
- **Control Intuitiv**: Butoane pentru zoom in/out și resetare vizualizare

### 📱 **Mobil - Stil TikTok**
- **Scroll Vertical**: Navigare prin ramuri ca prin TikTok stories
- **Cards Full-Screen**: Fiecare ramură ocupă tot ecranul
- **Swipe Navigation**: Swipe up/down pentru navigare între ramuri
- **Design Modern**: Gradient backgrounds și animații fluide
- **Touch Optimized**: Gesturi native optimizate pentru mobil

## 🚀 **Instalare și Configurare**

### **1. Clonare și Instalare**
```bash
git clone <repository-url>
cd knowledge-tree-explorer
npm install
```

### **2. Configurare Environment**
```bash
cp .env.example .env
```

Editează `.env`:
```env
REACT_APP_OPENROUTER_API_KEY=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
REACT_APP_OPENROUTER_MODEL=deepseek/deepseek-r1-0528:free
REACT_APP_SITE_URL=http://localhost:3000
REACT_APP_SITE_NAME=Knowledge Tree Explorer
```

### **3. Pornire Aplicație**
```bash
npm start
```

Aplicația va fi disponibilă la `http://localhost:3000`

## 📖 **Ghid de Utilizare**

### **1. Autentificare**
- Click pe "Quick Login (Dev)" pentru acces rapid în dezvoltare
- Sau folosește sistemul complet de autentificare cu Google/Apple/Microsoft

### **2. Generarea Copacului de Cunoștințe**
- Introdu un subiect în câmpul de input
- Click "Explore Knowledge" pentru generare
- Sistemul va valida subiectul cu surse web înainte de generare AI

### **3. Explorarea Ramurilor**

#### **🖥️ Desktop (Copac Infinit)**
- **Pan**: Click și drag pentru a naviga prin copac
- **Zoom**: Scroll mouse pentru zoom in/out
- **Single Click**: Selectează ramura
- **Double-Click**: Generează articol cu flag wheel
- **Long-Press**: Expandează ramura cu sub-ramuri AI
- **Controale**: Butoane pentru zoom și resetare în colțul din dreapta

#### **📱 Mobil (Stil TikTok)**
- **Swipe Up/Down**: Navigare între ramuri
- **Single Tap**: Selectează ramura
- **Double-Tap**: Generează articol cu flag wheel
- **Long-Press (500ms+)**: Expandează ramura cu sub-ramuri AI
- **Scroll Natural**: Comportament similar cu TikTok/Instagram Stories

### **4. Generarea Articolelor**
- Double-click/tap pe o ramură pentru flag wheel
- Selectează flag-urile dorite (articol, exemple, quiz, etc.)
- Sistemul va valida conținutul cu surse web înainte de generare
- Articolul va include surse web verificate

### **5. Funcționalități Avansate**
- **Text-to-Speech**: Ascultă articolele cu control viteză
- **Export**: PDF, Word, sau clipboard
- **Gamification**: Puncte și realizări pentru activități
- **Multi-tab**: Lucrează cu mai multe subiecte simultan
- **Multi-language**: Română și Engleză cu switch automat

## 🎯 **Caracteristici Unice**

### **1. Dual-Design Architecture (NOU în v3.0)**
- **Desktop**: Copac infinit cu vizualizare mind-map și pan/zoom
- **Mobil**: Design TikTok cu scroll vertical și cards full-screen
- **Responsive**: Detectare automată și switch între designuri
- **Consistent UX**: Aceleași funcționalități pe ambele platforme

### **2. Validare Strictă 120% (NOU în v3.0)**
- **AI + Web Mandatory**: Toate informațiile validate cu surse web
- **Zero Halucinații**: Doar conținut verificat din surse reale
- **6-Step Validation**: Proces strict de validare în 6 pași
- **Metadata Tracking**: Timestamp și surse pentru fiecare generare

### **3. AI-Powered Sub-Branch Generation**
- Sub-ramurile sunt generate specific cu AI pentru fiecare ramură
- Nu sunt copii generice - fiecare expansiune este unică și relevantă
- Folosește contextul complet al ramuri principale
- Poziționare organică în copacul infinit

### **4. Advanced Visualization**
- **Infinite Canvas**: Spațiu de lucru nelimitat pentru explorare
- **Organic Positioning**: Ramuri poziționate cu algoritmi organici
- **Visual Connections**: Linii animate de conexiune între elemente
- **Smooth Animations**: Tranziții fluide și feedback vizual

### **5. Professional Mobile Interface**
- Touch gestures native optimizate
- Design TikTok-style pentru engagement maxim
- Scroll snap pentru experiență fluidă
- Animații și tranziții premium

### **6. Advanced Content Generation**
- Minimum 600-800 cuvinte per articol
- 5+ surse web verificate automat pentru fiecare articol
- Flag system cu prompt-uri foarte specifice
- Formatare curată fără simboluri speciale
- Strict language compliance (română/engleză)

## 🔧 **Arhitectura Tehnică**

### **Componente Principale**
- `src/components/OptimizedApp.jsx` - Componenta principală cu dual-design
- `src/services/openRouterService.js` - Serviciu AI cu validare strictă
- `src/services/webSearchService.js` - Serviciu căutare web
- `src/styles/optimized.css` - Stiluri pentru ambele designuri

### **Servicii de Suport**
- `gestureService.js` - Gesturi și interacțiuni
- `speechService.js` - Text-to-speech
- `exportService.js` - Export PDF/Word
- `gamificationService.js` - Puncte și realizări
- `tabService.js` - Management tab-uri multiple

### **Securitate și Validare**
- Validare obligatorie AI + Web pentru toate operațiunile
- API key hardcodat pentru securitate maximă
- Structură exactă OpenRouter fără abateri
- Metadata de validare pentru audit trail

## 📊 **Monitorizare și Debugging**

### **Console Logs Structurate**
```
🔒 STRICT: Starting operation with mandatory validation
🔍 STEP 1: MANDATORY web search validation
🤖 STEP 2: AI generation with DeepSeek R1
🔍 STEP 3: Parsing and validation
✅ VERIFIED: Operation completed successfully
❌ CRITICAL: Error with detailed information
```

### **Validation Metadata**
Fiecare operațiune include metadata:
```json
{
  "_validation": {
    "webSources": [...],
    "validatedAt": "2024-01-01T12:00:00Z",
    "aiModel": "deepseek/deepseek-r1-0528:free",
    "strictMode": true
  }
}
```

## 🎮 **Gamification System**

### **Puncte și Realizări**
- **Daily Login**: 10 puncte
- **Tree Generated**: 50 puncte
- **Article Generated**: 30 puncte
- **Branch Expanded**: 20 puncte
- **Speech Used**: 5 puncte
- **Export Used**: 15 puncte

### **Leaderboard**
- Clasament utilizatori după puncte
- Popup-uri pentru realizări noi
- Progresie vizuală și badge-uri

## 🌍 **Suport Multi-Language**

### **Limbi Suportate**
- **Română**: Interfață și conținut complet în română
- **English**: Interface and content fully in English
- **Switch Automat**: Detectare și aplicare automată
- **Strict Compliance**: Fără amestec de limbi

### **Configurare Limbă**
- Switch în header cu flag-uri EN/RO
- Salvare preferință în localStorage
- Aplicare automată la restart

---

**Versiunea**: 3.0  
**Ultima actualizare**: Ianuarie 2024  
**Status**: Production Ready cu Validare Strictă  
**Securitate**: 120% - Zero Halucinații Garantate
