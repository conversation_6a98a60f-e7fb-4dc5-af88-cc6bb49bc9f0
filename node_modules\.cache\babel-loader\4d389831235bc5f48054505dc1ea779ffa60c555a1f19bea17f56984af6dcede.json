{"ast": null, "code": "// Optimized Text-to-Speech Service - Pareto 80/20 Implementation\n// Essential speech functionality with speed control and stop capability\n\nclass SpeechService {\n  constructor() {\n    this.isSupported = 'speechSynthesis' in window;\n    this.isPlaying = false;\n    this.isPaused = false;\n    this.currentUtterance = null;\n    this.currentText = '';\n    this.settings = {\n      rate: 1.0,\n      // Speed: 0.1 to 10\n      pitch: 1.0,\n      // Pitch: 0 to 2\n      volume: 1.0,\n      // Volume: 0 to 1\n      voice: null,\n      // Selected voice\n      lang: 'en-US' // Language\n    };\n    this.callbacks = {\n      onStart: null,\n      onEnd: null,\n      onPause: null,\n      onResume: null,\n      onError: null\n    };\n\n    // Initialize voices when available\n    if (this.isSupported) {\n      this.initVoices();\n    }\n  }\n\n  // Initialize available voices\n  initVoices() {\n    const loadVoices = () => {\n      this.voices = speechSynthesis.getVoices();\n\n      // Set default voice (prefer English)\n      if (!this.settings.voice && this.voices.length > 0) {\n        const englishVoice = this.voices.find(voice => voice.lang.startsWith('en') && voice.default) || this.voices.find(voice => voice.lang.startsWith('en')) || this.voices[0];\n        this.settings.voice = englishVoice;\n      }\n    };\n\n    // Load voices immediately if available\n    loadVoices();\n\n    // Also listen for voices changed event (some browsers load voices asynchronously)\n    speechSynthesis.addEventListener('voiceschanged', loadVoices);\n  }\n\n  // Check if speech synthesis is supported\n  isAvailable() {\n    return this.isSupported;\n  }\n\n  // Get current status\n  getStatus() {\n    return {\n      isSupported: this.isSupported,\n      isPlaying: this.isPlaying,\n      isPaused: this.isPaused,\n      canSpeak: this.isSupported && !this.isPlaying\n    };\n  }\n\n  // Set speech settings\n  configure(newSettings) {\n    this.settings = {\n      ...this.settings,\n      ...newSettings\n    };\n\n    // Validate settings\n    this.settings.rate = Math.max(0.1, Math.min(10, this.settings.rate));\n    this.settings.pitch = Math.max(0, Math.min(2, this.settings.pitch));\n    this.settings.volume = Math.max(0, Math.min(1, this.settings.volume));\n    return this.settings;\n  }\n\n  // Set callbacks\n  setCallbacks(callbacks) {\n    this.callbacks = {\n      ...this.callbacks,\n      ...callbacks\n    };\n  }\n\n  // Detect language of text\n  detectLanguage(text) {\n    // Simple language detection based on common patterns\n    const romanianPatterns = [/\\b(și|sau|cu|de|la|în|pe|pentru|este|sunt|avea|face|dacă|când|unde|cum|ce|care|acest|această|aceste|acestea)\\b/gi, /\\b(România|român|română|românesc|românească)\\b/gi, /[ăâîșț]/g // Romanian diacritics\n    ];\n    const englishPatterns = [/\\b(the|and|or|with|of|to|in|on|for|is|are|have|make|if|when|where|how|what|which|this|that|these|those)\\b/gi, /\\b(English|American|British)\\b/gi];\n    let romanianScore = 0;\n    let englishScore = 0;\n\n    // Count Romanian patterns\n    romanianPatterns.forEach(pattern => {\n      const matches = text.match(pattern);\n      if (matches) {\n        romanianScore += matches.length;\n      }\n    });\n\n    // Count English patterns\n    englishPatterns.forEach(pattern => {\n      const matches = text.match(pattern);\n      if (matches) {\n        englishScore += matches.length;\n      }\n    });\n\n    // Return detected language\n    if (romanianScore > englishScore && romanianScore > 2) {\n      return 'ro-RO';\n    } else {\n      return 'en-US';\n    }\n  }\n\n  // Get best voice for language\n  getBestVoiceForLanguage(lang) {\n    if (!this.voices || this.voices.length === 0) {\n      return null;\n    }\n\n    // Find voices that match the language\n    const matchingVoices = this.voices.filter(voice => voice.lang.startsWith(lang.split('-')[0]));\n    if (matchingVoices.length === 0) {\n      return this.voices[0]; // Fallback to first available voice\n    }\n\n    // Prefer default voice for the language\n    const defaultVoice = matchingVoices.find(voice => voice.default);\n    if (defaultVoice) {\n      return defaultVoice;\n    }\n\n    // Return first matching voice\n    return matchingVoices[0];\n  }\n\n  // Speak text\n  speak(text, options = {}) {\n    if (!this.isSupported) {\n      console.warn('Speech synthesis not supported');\n      return false;\n    }\n    if (!text || text.trim() === '') {\n      console.warn('No text provided for speech');\n      return false;\n    }\n\n    // Stop any current speech\n    this.stop();\n\n    // Clean and prepare text\n    const cleanText = this.cleanText(text);\n    this.currentText = cleanText;\n\n    // Detect language if not specified\n    const detectedLang = options.lang || this.detectLanguage(cleanText);\n    const bestVoice = this.getBestVoiceForLanguage(detectedLang);\n\n    // Create utterance\n    this.currentUtterance = new SpeechSynthesisUtterance(cleanText);\n\n    // Apply settings\n    const settings = {\n      ...this.settings,\n      ...options\n    };\n    this.currentUtterance.rate = settings.rate;\n    this.currentUtterance.pitch = settings.pitch;\n    this.currentUtterance.volume = settings.volume;\n    this.currentUtterance.lang = detectedLang;\n\n    // Use best voice for detected language\n    if (bestVoice) {\n      this.currentUtterance.voice = bestVoice;\n    } else if (settings.voice) {\n      this.currentUtterance.voice = settings.voice;\n    }\n    console.log(`Speaking in ${detectedLang} with voice:`, (bestVoice === null || bestVoice === void 0 ? void 0 : bestVoice.name) || 'default');\n\n    // Set event handlers\n    this.currentUtterance.onstart = () => {\n      this.isPlaying = true;\n      this.isPaused = false;\n      if (this.callbacks.onStart) {\n        this.callbacks.onStart();\n      }\n    };\n    this.currentUtterance.onend = () => {\n      this.isPlaying = false;\n      this.isPaused = false;\n      this.currentUtterance = null;\n      if (this.callbacks.onEnd) {\n        this.callbacks.onEnd();\n      }\n    };\n    this.currentUtterance.onpause = () => {\n      this.isPaused = true;\n      if (this.callbacks.onPause) {\n        this.callbacks.onPause();\n      }\n    };\n    this.currentUtterance.onresume = () => {\n      this.isPaused = false;\n      if (this.callbacks.onResume) {\n        this.callbacks.onResume();\n      }\n    };\n    this.currentUtterance.onerror = event => {\n      this.isPlaying = false;\n      this.isPaused = false;\n      this.currentUtterance = null;\n      console.error('Speech synthesis error:', event);\n      if (this.callbacks.onError) {\n        this.callbacks.onError(event);\n      }\n    };\n\n    // Start speaking\n    speechSynthesis.speak(this.currentUtterance);\n    return true;\n  }\n\n  // Pause speech\n  pause() {\n    if (this.isSupported && this.isPlaying && !this.isPaused) {\n      speechSynthesis.pause();\n      return true;\n    }\n    return false;\n  }\n\n  // Resume speech\n  resume() {\n    if (this.isSupported && this.isPlaying && this.isPaused) {\n      speechSynthesis.resume();\n      return true;\n    }\n    return false;\n  }\n\n  // Stop speech\n  stop() {\n    if (this.isSupported) {\n      speechSynthesis.cancel();\n      this.isPlaying = false;\n      this.isPaused = false;\n      this.currentUtterance = null;\n      return true;\n    }\n    return false;\n  }\n\n  // Toggle play/pause\n  toggle() {\n    if (this.isPlaying) {\n      if (this.isPaused) {\n        return this.resume();\n      } else {\n        return this.pause();\n      }\n    }\n    return false;\n  }\n\n  // Change speech rate (speed)\n  setRate(rate) {\n    this.settings.rate = Math.max(0.1, Math.min(10, rate));\n\n    // If currently speaking, restart with new rate\n    if (this.isPlaying && this.currentText) {\n      const wasPlaying = !this.isPaused;\n      this.stop();\n      if (wasPlaying) {\n        this.speak(this.currentText);\n      }\n    }\n    return this.settings.rate;\n  }\n\n  // Get available voices\n  getVoices() {\n    return this.voices || [];\n  }\n\n  // Set voice\n  setVoice(voice) {\n    if (voice && this.voices && this.voices.includes(voice)) {\n      this.settings.voice = voice;\n      return true;\n    }\n    return false;\n  }\n\n  // Clean text for better speech synthesis\n  cleanText(text) {\n    return text\n    // Remove HTML tags\n    .replace(/<[^>]*>/g, ' ')\n    // Replace multiple spaces with single space\n    .replace(/\\s+/g, ' ')\n    // Remove special characters that might cause issues\n    .replace(/[^\\w\\s.,!?;:()-]/g, ' ')\n    // Trim whitespace\n    .trim();\n  }\n\n  // Get speech rate presets\n  getRatePresets() {\n    return {\n      'Very Slow': 0.5,\n      'Slow': 0.75,\n      'Normal': 1.0,\n      'Fast': 1.25,\n      'Very Fast': 1.5,\n      'Ultra Fast': 2.0\n    };\n  }\n\n  // Create speech control UI\n  createControls(container) {\n    if (!container) return null;\n    const controls = document.createElement('div');\n    controls.className = 'speech-controls';\n    controls.innerHTML = `\n      <div class=\"speech-controls-inner\">\n        <button class=\"speech-btn speech-play\" title=\"Play/Pause\">\n          <span class=\"play-icon\">▶️</span>\n          <span class=\"pause-icon\" style=\"display: none;\">⏸️</span>\n        </button>\n        <button class=\"speech-btn speech-stop\" title=\"Stop\">⏹️</button>\n        <div class=\"speech-rate-control\">\n          <label>Speed:</label>\n          <input type=\"range\" class=\"speech-rate-slider\" min=\"0.5\" max=\"2\" step=\"0.1\" value=\"1\">\n          <span class=\"speech-rate-value\">1.0x</span>\n        </div>\n      </div>\n    `;\n\n    // Add event listeners\n    const playBtn = controls.querySelector('.speech-play');\n    const stopBtn = controls.querySelector('.speech-stop');\n    const rateSlider = controls.querySelector('.speech-rate-slider');\n    const rateValue = controls.querySelector('.speech-rate-value');\n    playBtn.addEventListener('click', () => {\n      if (this.isPlaying) {\n        this.toggle();\n      }\n    });\n    stopBtn.addEventListener('click', () => {\n      this.stop();\n    });\n    rateSlider.addEventListener('input', e => {\n      const rate = parseFloat(e.target.value);\n      this.setRate(rate);\n      rateValue.textContent = `${rate}x`;\n    });\n\n    // Update UI based on speech status\n    this.setCallbacks({\n      onStart: () => {\n        playBtn.querySelector('.play-icon').style.display = 'none';\n        playBtn.querySelector('.pause-icon').style.display = 'inline';\n        controls.classList.add('playing');\n      },\n      onEnd: () => {\n        playBtn.querySelector('.play-icon').style.display = 'inline';\n        playBtn.querySelector('.pause-icon').style.display = 'none';\n        controls.classList.remove('playing');\n      },\n      onPause: () => {\n        playBtn.querySelector('.play-icon').style.display = 'inline';\n        playBtn.querySelector('.pause-icon').style.display = 'none';\n      },\n      onResume: () => {\n        playBtn.querySelector('.play-icon').style.display = 'none';\n        playBtn.querySelector('.pause-icon').style.display = 'inline';\n      }\n    });\n    container.appendChild(controls);\n    return controls;\n  }\n}\n\n// Create singleton instance\nconst speechService = new SpeechService();\nexport default speechService;", "map": {"version": 3, "names": ["SpeechService", "constructor", "isSupported", "window", "isPlaying", "isPaused", "currentUtterance", "currentText", "settings", "rate", "pitch", "volume", "voice", "lang", "callbacks", "onStart", "onEnd", "onPause", "onResume", "onError", "initVoices", "loadVoices", "voices", "speechSynthesis", "getVoices", "length", "englishVoice", "find", "startsWith", "default", "addEventListener", "isAvailable", "getStatus", "canSpeak", "configure", "newSettings", "Math", "max", "min", "setCallbacks", "detectLanguage", "text", "romanianPatterns", "englishPatterns", "romanianScore", "englishScore", "for<PERSON>ach", "pattern", "matches", "match", "getBestVoiceForLanguage", "matchingVoices", "filter", "split", "defaultVoice", "speak", "options", "console", "warn", "trim", "stop", "cleanText", "detectedLang", "bestVoice", "SpeechSynthesisUtterance", "log", "name", "onstart", "onend", "onpause", "onresume", "onerror", "event", "error", "pause", "resume", "cancel", "toggle", "setRate", "wasPlaying", "setVoice", "includes", "replace", "getRatePresets", "createControls", "container", "controls", "document", "createElement", "className", "innerHTML", "playBtn", "querySelector", "stopBtn", "rateSlider", "rateValue", "e", "parseFloat", "target", "value", "textContent", "style", "display", "classList", "add", "remove", "append<PERSON><PERSON><PERSON>", "speechService"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Appv1/src/services/speechService.js"], "sourcesContent": ["// Optimized Text-to-Speech Service - Pareto 80/20 Implementation\n// Essential speech functionality with speed control and stop capability\n\nclass SpeechService {\n  constructor() {\n    this.isSupported = 'speechSynthesis' in window;\n    this.isPlaying = false;\n    this.isPaused = false;\n    this.currentUtterance = null;\n    this.currentText = '';\n    this.settings = {\n      rate: 1.0,        // Speed: 0.1 to 10\n      pitch: 1.0,       // Pitch: 0 to 2\n      volume: 1.0,      // Volume: 0 to 1\n      voice: null,      // Selected voice\n      lang: 'en-US'     // Language\n    };\n    this.callbacks = {\n      onStart: null,\n      onEnd: null,\n      onPause: null,\n      onResume: null,\n      onError: null\n    };\n\n    // Initialize voices when available\n    if (this.isSupported) {\n      this.initVoices();\n    }\n  }\n\n  // Initialize available voices\n  initVoices() {\n    const loadVoices = () => {\n      this.voices = speechSynthesis.getVoices();\n      \n      // Set default voice (prefer English)\n      if (!this.settings.voice && this.voices.length > 0) {\n        const englishVoice = this.voices.find(voice => \n          voice.lang.startsWith('en') && voice.default\n        ) || this.voices.find(voice => \n          voice.lang.startsWith('en')\n        ) || this.voices[0];\n        \n        this.settings.voice = englishVoice;\n      }\n    };\n\n    // Load voices immediately if available\n    loadVoices();\n\n    // Also listen for voices changed event (some browsers load voices asynchronously)\n    speechSynthesis.addEventListener('voiceschanged', loadVoices);\n  }\n\n  // Check if speech synthesis is supported\n  isAvailable() {\n    return this.isSupported;\n  }\n\n  // Get current status\n  getStatus() {\n    return {\n      isSupported: this.isSupported,\n      isPlaying: this.isPlaying,\n      isPaused: this.isPaused,\n      canSpeak: this.isSupported && !this.isPlaying\n    };\n  }\n\n  // Set speech settings\n  configure(newSettings) {\n    this.settings = { ...this.settings, ...newSettings };\n    \n    // Validate settings\n    this.settings.rate = Math.max(0.1, Math.min(10, this.settings.rate));\n    this.settings.pitch = Math.max(0, Math.min(2, this.settings.pitch));\n    this.settings.volume = Math.max(0, Math.min(1, this.settings.volume));\n    \n    return this.settings;\n  }\n\n  // Set callbacks\n  setCallbacks(callbacks) {\n    this.callbacks = { ...this.callbacks, ...callbacks };\n  }\n\n  // Detect language of text\n  detectLanguage(text) {\n    // Simple language detection based on common patterns\n    const romanianPatterns = [\n      /\\b(și|sau|cu|de|la|în|pe|pentru|este|sunt|avea|face|dacă|când|unde|cum|ce|care|acest|această|aceste|acestea)\\b/gi,\n      /\\b(România|român|română|românesc|românească)\\b/gi,\n      /[ăâîșț]/g // Romanian diacritics\n    ];\n\n    const englishPatterns = [\n      /\\b(the|and|or|with|of|to|in|on|for|is|are|have|make|if|when|where|how|what|which|this|that|these|those)\\b/gi,\n      /\\b(English|American|British)\\b/gi\n    ];\n\n    let romanianScore = 0;\n    let englishScore = 0;\n\n    // Count Romanian patterns\n    romanianPatterns.forEach(pattern => {\n      const matches = text.match(pattern);\n      if (matches) {\n        romanianScore += matches.length;\n      }\n    });\n\n    // Count English patterns\n    englishPatterns.forEach(pattern => {\n      const matches = text.match(pattern);\n      if (matches) {\n        englishScore += matches.length;\n      }\n    });\n\n    // Return detected language\n    if (romanianScore > englishScore && romanianScore > 2) {\n      return 'ro-RO';\n    } else {\n      return 'en-US';\n    }\n  }\n\n  // Get best voice for language\n  getBestVoiceForLanguage(lang) {\n    if (!this.voices || this.voices.length === 0) {\n      return null;\n    }\n\n    // Find voices that match the language\n    const matchingVoices = this.voices.filter(voice =>\n      voice.lang.startsWith(lang.split('-')[0])\n    );\n\n    if (matchingVoices.length === 0) {\n      return this.voices[0]; // Fallback to first available voice\n    }\n\n    // Prefer default voice for the language\n    const defaultVoice = matchingVoices.find(voice => voice.default);\n    if (defaultVoice) {\n      return defaultVoice;\n    }\n\n    // Return first matching voice\n    return matchingVoices[0];\n  }\n\n  // Speak text\n  speak(text, options = {}) {\n    if (!this.isSupported) {\n      console.warn('Speech synthesis not supported');\n      return false;\n    }\n\n    if (!text || text.trim() === '') {\n      console.warn('No text provided for speech');\n      return false;\n    }\n\n    // Stop any current speech\n    this.stop();\n\n    // Clean and prepare text\n    const cleanText = this.cleanText(text);\n    this.currentText = cleanText;\n\n    // Detect language if not specified\n    const detectedLang = options.lang || this.detectLanguage(cleanText);\n    const bestVoice = this.getBestVoiceForLanguage(detectedLang);\n\n    // Create utterance\n    this.currentUtterance = new SpeechSynthesisUtterance(cleanText);\n\n    // Apply settings\n    const settings = { ...this.settings, ...options };\n    this.currentUtterance.rate = settings.rate;\n    this.currentUtterance.pitch = settings.pitch;\n    this.currentUtterance.volume = settings.volume;\n    this.currentUtterance.lang = detectedLang;\n\n    // Use best voice for detected language\n    if (bestVoice) {\n      this.currentUtterance.voice = bestVoice;\n    } else if (settings.voice) {\n      this.currentUtterance.voice = settings.voice;\n    }\n\n    console.log(`Speaking in ${detectedLang} with voice:`, bestVoice?.name || 'default');\n\n    // Set event handlers\n    this.currentUtterance.onstart = () => {\n      this.isPlaying = true;\n      this.isPaused = false;\n      if (this.callbacks.onStart) {\n        this.callbacks.onStart();\n      }\n    };\n\n    this.currentUtterance.onend = () => {\n      this.isPlaying = false;\n      this.isPaused = false;\n      this.currentUtterance = null;\n      if (this.callbacks.onEnd) {\n        this.callbacks.onEnd();\n      }\n    };\n\n    this.currentUtterance.onpause = () => {\n      this.isPaused = true;\n      if (this.callbacks.onPause) {\n        this.callbacks.onPause();\n      }\n    };\n\n    this.currentUtterance.onresume = () => {\n      this.isPaused = false;\n      if (this.callbacks.onResume) {\n        this.callbacks.onResume();\n      }\n    };\n\n    this.currentUtterance.onerror = (event) => {\n      this.isPlaying = false;\n      this.isPaused = false;\n      this.currentUtterance = null;\n      console.error('Speech synthesis error:', event);\n      if (this.callbacks.onError) {\n        this.callbacks.onError(event);\n      }\n    };\n\n    // Start speaking\n    speechSynthesis.speak(this.currentUtterance);\n    return true;\n  }\n\n  // Pause speech\n  pause() {\n    if (this.isSupported && this.isPlaying && !this.isPaused) {\n      speechSynthesis.pause();\n      return true;\n    }\n    return false;\n  }\n\n  // Resume speech\n  resume() {\n    if (this.isSupported && this.isPlaying && this.isPaused) {\n      speechSynthesis.resume();\n      return true;\n    }\n    return false;\n  }\n\n  // Stop speech\n  stop() {\n    if (this.isSupported) {\n      speechSynthesis.cancel();\n      this.isPlaying = false;\n      this.isPaused = false;\n      this.currentUtterance = null;\n      return true;\n    }\n    return false;\n  }\n\n  // Toggle play/pause\n  toggle() {\n    if (this.isPlaying) {\n      if (this.isPaused) {\n        return this.resume();\n      } else {\n        return this.pause();\n      }\n    }\n    return false;\n  }\n\n  // Change speech rate (speed)\n  setRate(rate) {\n    this.settings.rate = Math.max(0.1, Math.min(10, rate));\n    \n    // If currently speaking, restart with new rate\n    if (this.isPlaying && this.currentText) {\n      const wasPlaying = !this.isPaused;\n      this.stop();\n      if (wasPlaying) {\n        this.speak(this.currentText);\n      }\n    }\n    \n    return this.settings.rate;\n  }\n\n  // Get available voices\n  getVoices() {\n    return this.voices || [];\n  }\n\n  // Set voice\n  setVoice(voice) {\n    if (voice && this.voices && this.voices.includes(voice)) {\n      this.settings.voice = voice;\n      return true;\n    }\n    return false;\n  }\n\n  // Clean text for better speech synthesis\n  cleanText(text) {\n    return text\n      // Remove HTML tags\n      .replace(/<[^>]*>/g, ' ')\n      // Replace multiple spaces with single space\n      .replace(/\\s+/g, ' ')\n      // Remove special characters that might cause issues\n      .replace(/[^\\w\\s.,!?;:()-]/g, ' ')\n      // Trim whitespace\n      .trim();\n  }\n\n  // Get speech rate presets\n  getRatePresets() {\n    return {\n      'Very Slow': 0.5,\n      'Slow': 0.75,\n      'Normal': 1.0,\n      'Fast': 1.25,\n      'Very Fast': 1.5,\n      'Ultra Fast': 2.0\n    };\n  }\n\n  // Create speech control UI\n  createControls(container) {\n    if (!container) return null;\n\n    const controls = document.createElement('div');\n    controls.className = 'speech-controls';\n    controls.innerHTML = `\n      <div class=\"speech-controls-inner\">\n        <button class=\"speech-btn speech-play\" title=\"Play/Pause\">\n          <span class=\"play-icon\">▶️</span>\n          <span class=\"pause-icon\" style=\"display: none;\">⏸️</span>\n        </button>\n        <button class=\"speech-btn speech-stop\" title=\"Stop\">⏹️</button>\n        <div class=\"speech-rate-control\">\n          <label>Speed:</label>\n          <input type=\"range\" class=\"speech-rate-slider\" min=\"0.5\" max=\"2\" step=\"0.1\" value=\"1\">\n          <span class=\"speech-rate-value\">1.0x</span>\n        </div>\n      </div>\n    `;\n\n    // Add event listeners\n    const playBtn = controls.querySelector('.speech-play');\n    const stopBtn = controls.querySelector('.speech-stop');\n    const rateSlider = controls.querySelector('.speech-rate-slider');\n    const rateValue = controls.querySelector('.speech-rate-value');\n\n    playBtn.addEventListener('click', () => {\n      if (this.isPlaying) {\n        this.toggle();\n      }\n    });\n\n    stopBtn.addEventListener('click', () => {\n      this.stop();\n    });\n\n    rateSlider.addEventListener('input', (e) => {\n      const rate = parseFloat(e.target.value);\n      this.setRate(rate);\n      rateValue.textContent = `${rate}x`;\n    });\n\n    // Update UI based on speech status\n    this.setCallbacks({\n      onStart: () => {\n        playBtn.querySelector('.play-icon').style.display = 'none';\n        playBtn.querySelector('.pause-icon').style.display = 'inline';\n        controls.classList.add('playing');\n      },\n      onEnd: () => {\n        playBtn.querySelector('.play-icon').style.display = 'inline';\n        playBtn.querySelector('.pause-icon').style.display = 'none';\n        controls.classList.remove('playing');\n      },\n      onPause: () => {\n        playBtn.querySelector('.play-icon').style.display = 'inline';\n        playBtn.querySelector('.pause-icon').style.display = 'none';\n      },\n      onResume: () => {\n        playBtn.querySelector('.play-icon').style.display = 'none';\n        playBtn.querySelector('.pause-icon').style.display = 'inline';\n      }\n    });\n\n    container.appendChild(controls);\n    return controls;\n  }\n}\n\n// Create singleton instance\nconst speechService = new SpeechService();\n\nexport default speechService;\n"], "mappings": "AAAA;AACA;;AAEA,MAAMA,aAAa,CAAC;EAClBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,WAAW,GAAG,iBAAiB,IAAIC,MAAM;IAC9C,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,QAAQ,GAAG;MACdC,IAAI,EAAE,GAAG;MAAS;MAClBC,KAAK,EAAE,GAAG;MAAQ;MAClBC,MAAM,EAAE,GAAG;MAAO;MAClBC,KAAK,EAAE,IAAI;MAAO;MAClBC,IAAI,EAAE,OAAO,CAAK;IACpB,CAAC;IACD,IAAI,CAACC,SAAS,GAAG;MACfC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,IAAI;MACXC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,IAAI;MACdC,OAAO,EAAE;IACX,CAAC;;IAED;IACA,IAAI,IAAI,CAACjB,WAAW,EAAE;MACpB,IAAI,CAACkB,UAAU,CAAC,CAAC;IACnB;EACF;;EAEA;EACAA,UAAUA,CAAA,EAAG;IACX,MAAMC,UAAU,GAAGA,CAAA,KAAM;MACvB,IAAI,CAACC,MAAM,GAAGC,eAAe,CAACC,SAAS,CAAC,CAAC;;MAEzC;MACA,IAAI,CAAC,IAAI,CAAChB,QAAQ,CAACI,KAAK,IAAI,IAAI,CAACU,MAAM,CAACG,MAAM,GAAG,CAAC,EAAE;QAClD,MAAMC,YAAY,GAAG,IAAI,CAACJ,MAAM,CAACK,IAAI,CAACf,KAAK,IACzCA,KAAK,CAACC,IAAI,CAACe,UAAU,CAAC,IAAI,CAAC,IAAIhB,KAAK,CAACiB,OACvC,CAAC,IAAI,IAAI,CAACP,MAAM,CAACK,IAAI,CAACf,KAAK,IACzBA,KAAK,CAACC,IAAI,CAACe,UAAU,CAAC,IAAI,CAC5B,CAAC,IAAI,IAAI,CAACN,MAAM,CAAC,CAAC,CAAC;QAEnB,IAAI,CAACd,QAAQ,CAACI,KAAK,GAAGc,YAAY;MACpC;IACF,CAAC;;IAED;IACAL,UAAU,CAAC,CAAC;;IAEZ;IACAE,eAAe,CAACO,gBAAgB,CAAC,eAAe,EAAET,UAAU,CAAC;EAC/D;;EAEA;EACAU,WAAWA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC7B,WAAW;EACzB;;EAEA;EACA8B,SAASA,CAAA,EAAG;IACV,OAAO;MACL9B,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BE,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvB4B,QAAQ,EAAE,IAAI,CAAC/B,WAAW,IAAI,CAAC,IAAI,CAACE;IACtC,CAAC;EACH;;EAEA;EACA8B,SAASA,CAACC,WAAW,EAAE;IACrB,IAAI,CAAC3B,QAAQ,GAAG;MAAE,GAAG,IAAI,CAACA,QAAQ;MAAE,GAAG2B;IAAY,CAAC;;IAEpD;IACA,IAAI,CAAC3B,QAAQ,CAACC,IAAI,GAAG2B,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC9B,QAAQ,CAACC,IAAI,CAAC,CAAC;IACpE,IAAI,CAACD,QAAQ,CAACE,KAAK,GAAG0B,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC9B,QAAQ,CAACE,KAAK,CAAC,CAAC;IACnE,IAAI,CAACF,QAAQ,CAACG,MAAM,GAAGyB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC9B,QAAQ,CAACG,MAAM,CAAC,CAAC;IAErE,OAAO,IAAI,CAACH,QAAQ;EACtB;;EAEA;EACA+B,YAAYA,CAACzB,SAAS,EAAE;IACtB,IAAI,CAACA,SAAS,GAAG;MAAE,GAAG,IAAI,CAACA,SAAS;MAAE,GAAGA;IAAU,CAAC;EACtD;;EAEA;EACA0B,cAAcA,CAACC,IAAI,EAAE;IACnB;IACA,MAAMC,gBAAgB,GAAG,CACvB,kHAAkH,EAClH,kDAAkD,EAClD,UAAU,CAAC;IAAA,CACZ;IAED,MAAMC,eAAe,GAAG,CACtB,6GAA6G,EAC7G,kCAAkC,CACnC;IAED,IAAIC,aAAa,GAAG,CAAC;IACrB,IAAIC,YAAY,GAAG,CAAC;;IAEpB;IACAH,gBAAgB,CAACI,OAAO,CAACC,OAAO,IAAI;MAClC,MAAMC,OAAO,GAAGP,IAAI,CAACQ,KAAK,CAACF,OAAO,CAAC;MACnC,IAAIC,OAAO,EAAE;QACXJ,aAAa,IAAII,OAAO,CAACvB,MAAM;MACjC;IACF,CAAC,CAAC;;IAEF;IACAkB,eAAe,CAACG,OAAO,CAACC,OAAO,IAAI;MACjC,MAAMC,OAAO,GAAGP,IAAI,CAACQ,KAAK,CAACF,OAAO,CAAC;MACnC,IAAIC,OAAO,EAAE;QACXH,YAAY,IAAIG,OAAO,CAACvB,MAAM;MAChC;IACF,CAAC,CAAC;;IAEF;IACA,IAAImB,aAAa,GAAGC,YAAY,IAAID,aAAa,GAAG,CAAC,EAAE;MACrD,OAAO,OAAO;IAChB,CAAC,MAAM;MACL,OAAO,OAAO;IAChB;EACF;;EAEA;EACAM,uBAAuBA,CAACrC,IAAI,EAAE;IAC5B,IAAI,CAAC,IAAI,CAACS,MAAM,IAAI,IAAI,CAACA,MAAM,CAACG,MAAM,KAAK,CAAC,EAAE;MAC5C,OAAO,IAAI;IACb;;IAEA;IACA,MAAM0B,cAAc,GAAG,IAAI,CAAC7B,MAAM,CAAC8B,MAAM,CAACxC,KAAK,IAC7CA,KAAK,CAACC,IAAI,CAACe,UAAU,CAACf,IAAI,CAACwC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAC1C,CAAC;IAED,IAAIF,cAAc,CAAC1B,MAAM,KAAK,CAAC,EAAE;MAC/B,OAAO,IAAI,CAACH,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB;;IAEA;IACA,MAAMgC,YAAY,GAAGH,cAAc,CAACxB,IAAI,CAACf,KAAK,IAAIA,KAAK,CAACiB,OAAO,CAAC;IAChE,IAAIyB,YAAY,EAAE;MAChB,OAAOA,YAAY;IACrB;;IAEA;IACA,OAAOH,cAAc,CAAC,CAAC,CAAC;EAC1B;;EAEA;EACAI,KAAKA,CAACd,IAAI,EAAEe,OAAO,GAAG,CAAC,CAAC,EAAE;IACxB,IAAI,CAAC,IAAI,CAACtD,WAAW,EAAE;MACrBuD,OAAO,CAACC,IAAI,CAAC,gCAAgC,CAAC;MAC9C,OAAO,KAAK;IACd;IAEA,IAAI,CAACjB,IAAI,IAAIA,IAAI,CAACkB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC/BF,OAAO,CAACC,IAAI,CAAC,6BAA6B,CAAC;MAC3C,OAAO,KAAK;IACd;;IAEA;IACA,IAAI,CAACE,IAAI,CAAC,CAAC;;IAEX;IACA,MAAMC,SAAS,GAAG,IAAI,CAACA,SAAS,CAACpB,IAAI,CAAC;IACtC,IAAI,CAAClC,WAAW,GAAGsD,SAAS;;IAE5B;IACA,MAAMC,YAAY,GAAGN,OAAO,CAAC3C,IAAI,IAAI,IAAI,CAAC2B,cAAc,CAACqB,SAAS,CAAC;IACnE,MAAME,SAAS,GAAG,IAAI,CAACb,uBAAuB,CAACY,YAAY,CAAC;;IAE5D;IACA,IAAI,CAACxD,gBAAgB,GAAG,IAAI0D,wBAAwB,CAACH,SAAS,CAAC;;IAE/D;IACA,MAAMrD,QAAQ,GAAG;MAAE,GAAG,IAAI,CAACA,QAAQ;MAAE,GAAGgD;IAAQ,CAAC;IACjD,IAAI,CAAClD,gBAAgB,CAACG,IAAI,GAAGD,QAAQ,CAACC,IAAI;IAC1C,IAAI,CAACH,gBAAgB,CAACI,KAAK,GAAGF,QAAQ,CAACE,KAAK;IAC5C,IAAI,CAACJ,gBAAgB,CAACK,MAAM,GAAGH,QAAQ,CAACG,MAAM;IAC9C,IAAI,CAACL,gBAAgB,CAACO,IAAI,GAAGiD,YAAY;;IAEzC;IACA,IAAIC,SAAS,EAAE;MACb,IAAI,CAACzD,gBAAgB,CAACM,KAAK,GAAGmD,SAAS;IACzC,CAAC,MAAM,IAAIvD,QAAQ,CAACI,KAAK,EAAE;MACzB,IAAI,CAACN,gBAAgB,CAACM,KAAK,GAAGJ,QAAQ,CAACI,KAAK;IAC9C;IAEA6C,OAAO,CAACQ,GAAG,CAAC,eAAeH,YAAY,cAAc,EAAE,CAAAC,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEG,IAAI,KAAI,SAAS,CAAC;;IAEpF;IACA,IAAI,CAAC5D,gBAAgB,CAAC6D,OAAO,GAAG,MAAM;MACpC,IAAI,CAAC/D,SAAS,GAAG,IAAI;MACrB,IAAI,CAACC,QAAQ,GAAG,KAAK;MACrB,IAAI,IAAI,CAACS,SAAS,CAACC,OAAO,EAAE;QAC1B,IAAI,CAACD,SAAS,CAACC,OAAO,CAAC,CAAC;MAC1B;IACF,CAAC;IAED,IAAI,CAACT,gBAAgB,CAAC8D,KAAK,GAAG,MAAM;MAClC,IAAI,CAAChE,SAAS,GAAG,KAAK;MACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;MACrB,IAAI,CAACC,gBAAgB,GAAG,IAAI;MAC5B,IAAI,IAAI,CAACQ,SAAS,CAACE,KAAK,EAAE;QACxB,IAAI,CAACF,SAAS,CAACE,KAAK,CAAC,CAAC;MACxB;IACF,CAAC;IAED,IAAI,CAACV,gBAAgB,CAAC+D,OAAO,GAAG,MAAM;MACpC,IAAI,CAAChE,QAAQ,GAAG,IAAI;MACpB,IAAI,IAAI,CAACS,SAAS,CAACG,OAAO,EAAE;QAC1B,IAAI,CAACH,SAAS,CAACG,OAAO,CAAC,CAAC;MAC1B;IACF,CAAC;IAED,IAAI,CAACX,gBAAgB,CAACgE,QAAQ,GAAG,MAAM;MACrC,IAAI,CAACjE,QAAQ,GAAG,KAAK;MACrB,IAAI,IAAI,CAACS,SAAS,CAACI,QAAQ,EAAE;QAC3B,IAAI,CAACJ,SAAS,CAACI,QAAQ,CAAC,CAAC;MAC3B;IACF,CAAC;IAED,IAAI,CAACZ,gBAAgB,CAACiE,OAAO,GAAIC,KAAK,IAAK;MACzC,IAAI,CAACpE,SAAS,GAAG,KAAK;MACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;MACrB,IAAI,CAACC,gBAAgB,GAAG,IAAI;MAC5BmD,OAAO,CAACgB,KAAK,CAAC,yBAAyB,EAAED,KAAK,CAAC;MAC/C,IAAI,IAAI,CAAC1D,SAAS,CAACK,OAAO,EAAE;QAC1B,IAAI,CAACL,SAAS,CAACK,OAAO,CAACqD,KAAK,CAAC;MAC/B;IACF,CAAC;;IAED;IACAjD,eAAe,CAACgC,KAAK,CAAC,IAAI,CAACjD,gBAAgB,CAAC;IAC5C,OAAO,IAAI;EACb;;EAEA;EACAoE,KAAKA,CAAA,EAAG;IACN,IAAI,IAAI,CAACxE,WAAW,IAAI,IAAI,CAACE,SAAS,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;MACxDkB,eAAe,CAACmD,KAAK,CAAC,CAAC;MACvB,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;;EAEA;EACAC,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACzE,WAAW,IAAI,IAAI,CAACE,SAAS,IAAI,IAAI,CAACC,QAAQ,EAAE;MACvDkB,eAAe,CAACoD,MAAM,CAAC,CAAC;MACxB,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;;EAEA;EACAf,IAAIA,CAAA,EAAG;IACL,IAAI,IAAI,CAAC1D,WAAW,EAAE;MACpBqB,eAAe,CAACqD,MAAM,CAAC,CAAC;MACxB,IAAI,CAACxE,SAAS,GAAG,KAAK;MACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;MACrB,IAAI,CAACC,gBAAgB,GAAG,IAAI;MAC5B,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;;EAEA;EACAuE,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACzE,SAAS,EAAE;MAClB,IAAI,IAAI,CAACC,QAAQ,EAAE;QACjB,OAAO,IAAI,CAACsE,MAAM,CAAC,CAAC;MACtB,CAAC,MAAM;QACL,OAAO,IAAI,CAACD,KAAK,CAAC,CAAC;MACrB;IACF;IACA,OAAO,KAAK;EACd;;EAEA;EACAI,OAAOA,CAACrE,IAAI,EAAE;IACZ,IAAI,CAACD,QAAQ,CAACC,IAAI,GAAG2B,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,EAAE,EAAE7B,IAAI,CAAC,CAAC;;IAEtD;IACA,IAAI,IAAI,CAACL,SAAS,IAAI,IAAI,CAACG,WAAW,EAAE;MACtC,MAAMwE,UAAU,GAAG,CAAC,IAAI,CAAC1E,QAAQ;MACjC,IAAI,CAACuD,IAAI,CAAC,CAAC;MACX,IAAImB,UAAU,EAAE;QACd,IAAI,CAACxB,KAAK,CAAC,IAAI,CAAChD,WAAW,CAAC;MAC9B;IACF;IAEA,OAAO,IAAI,CAACC,QAAQ,CAACC,IAAI;EAC3B;;EAEA;EACAe,SAASA,CAAA,EAAG;IACV,OAAO,IAAI,CAACF,MAAM,IAAI,EAAE;EAC1B;;EAEA;EACA0D,QAAQA,CAACpE,KAAK,EAAE;IACd,IAAIA,KAAK,IAAI,IAAI,CAACU,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC2D,QAAQ,CAACrE,KAAK,CAAC,EAAE;MACvD,IAAI,CAACJ,QAAQ,CAACI,KAAK,GAAGA,KAAK;MAC3B,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;;EAEA;EACAiD,SAASA,CAACpB,IAAI,EAAE;IACd,OAAOA;IACL;IAAA,CACCyC,OAAO,CAAC,UAAU,EAAE,GAAG;IACxB;IAAA,CACCA,OAAO,CAAC,MAAM,EAAE,GAAG;IACpB;IAAA,CACCA,OAAO,CAAC,mBAAmB,EAAE,GAAG;IACjC;IAAA,CACCvB,IAAI,CAAC,CAAC;EACX;;EAEA;EACAwB,cAAcA,CAAA,EAAG;IACf,OAAO;MACL,WAAW,EAAE,GAAG;MAChB,MAAM,EAAE,IAAI;MACZ,QAAQ,EAAE,GAAG;MACb,MAAM,EAAE,IAAI;MACZ,WAAW,EAAE,GAAG;MAChB,YAAY,EAAE;IAChB,CAAC;EACH;;EAEA;EACAC,cAAcA,CAACC,SAAS,EAAE;IACxB,IAAI,CAACA,SAAS,EAAE,OAAO,IAAI;IAE3B,MAAMC,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC9CF,QAAQ,CAACG,SAAS,GAAG,iBAAiB;IACtCH,QAAQ,CAACI,SAAS,GAAG;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;IAED;IACA,MAAMC,OAAO,GAAGL,QAAQ,CAACM,aAAa,CAAC,cAAc,CAAC;IACtD,MAAMC,OAAO,GAAGP,QAAQ,CAACM,aAAa,CAAC,cAAc,CAAC;IACtD,MAAME,UAAU,GAAGR,QAAQ,CAACM,aAAa,CAAC,qBAAqB,CAAC;IAChE,MAAMG,SAAS,GAAGT,QAAQ,CAACM,aAAa,CAAC,oBAAoB,CAAC;IAE9DD,OAAO,CAAC7D,gBAAgB,CAAC,OAAO,EAAE,MAAM;MACtC,IAAI,IAAI,CAAC1B,SAAS,EAAE;QAClB,IAAI,CAACyE,MAAM,CAAC,CAAC;MACf;IACF,CAAC,CAAC;IAEFgB,OAAO,CAAC/D,gBAAgB,CAAC,OAAO,EAAE,MAAM;MACtC,IAAI,CAAC8B,IAAI,CAAC,CAAC;IACb,CAAC,CAAC;IAEFkC,UAAU,CAAChE,gBAAgB,CAAC,OAAO,EAAGkE,CAAC,IAAK;MAC1C,MAAMvF,IAAI,GAAGwF,UAAU,CAACD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC;MACvC,IAAI,CAACrB,OAAO,CAACrE,IAAI,CAAC;MAClBsF,SAAS,CAACK,WAAW,GAAG,GAAG3F,IAAI,GAAG;IACpC,CAAC,CAAC;;IAEF;IACA,IAAI,CAAC8B,YAAY,CAAC;MAChBxB,OAAO,EAAEA,CAAA,KAAM;QACb4E,OAAO,CAACC,aAAa,CAAC,YAAY,CAAC,CAACS,KAAK,CAACC,OAAO,GAAG,MAAM;QAC1DX,OAAO,CAACC,aAAa,CAAC,aAAa,CAAC,CAACS,KAAK,CAACC,OAAO,GAAG,QAAQ;QAC7DhB,QAAQ,CAACiB,SAAS,CAACC,GAAG,CAAC,SAAS,CAAC;MACnC,CAAC;MACDxF,KAAK,EAAEA,CAAA,KAAM;QACX2E,OAAO,CAACC,aAAa,CAAC,YAAY,CAAC,CAACS,KAAK,CAACC,OAAO,GAAG,QAAQ;QAC5DX,OAAO,CAACC,aAAa,CAAC,aAAa,CAAC,CAACS,KAAK,CAACC,OAAO,GAAG,MAAM;QAC3DhB,QAAQ,CAACiB,SAAS,CAACE,MAAM,CAAC,SAAS,CAAC;MACtC,CAAC;MACDxF,OAAO,EAAEA,CAAA,KAAM;QACb0E,OAAO,CAACC,aAAa,CAAC,YAAY,CAAC,CAACS,KAAK,CAACC,OAAO,GAAG,QAAQ;QAC5DX,OAAO,CAACC,aAAa,CAAC,aAAa,CAAC,CAACS,KAAK,CAACC,OAAO,GAAG,MAAM;MAC7D,CAAC;MACDpF,QAAQ,EAAEA,CAAA,KAAM;QACdyE,OAAO,CAACC,aAAa,CAAC,YAAY,CAAC,CAACS,KAAK,CAACC,OAAO,GAAG,MAAM;QAC1DX,OAAO,CAACC,aAAa,CAAC,aAAa,CAAC,CAACS,KAAK,CAACC,OAAO,GAAG,QAAQ;MAC/D;IACF,CAAC,CAAC;IAEFjB,SAAS,CAACqB,WAAW,CAACpB,QAAQ,CAAC;IAC/B,OAAOA,QAAQ;EACjB;AACF;;AAEA;AACA,MAAMqB,aAAa,GAAG,IAAI3G,aAAa,CAAC,CAAC;AAEzC,eAAe2G,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}