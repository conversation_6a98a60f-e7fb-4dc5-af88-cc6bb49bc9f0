{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Appv1\\\\src\\\\components\\\\OptimizedApp.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport '../styles/optimized.css';\nimport gestureService, { createFlagWheel } from '../services/gestureService';\nimport speechService from '../services/speechService';\nimport exportService from '../services/exportService';\nimport gamificationService from '../services/optimizedGamificationService';\nimport LanguageSwitcher from './LanguageSwitcher';\nimport { useTranslation } from '../utils/i18n';\n\n// Optimized Knowledge Tree Explorer - Pareto 80/20 Implementation\n// Focus on core functionality with maximum impact\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst OptimizedApp = () => {\n  _s();\n  const [currentView, setCurrentView] = useState('input'); // 'input', 'tree', 'article'\n  const [topic, setTopic] = useState('');\n  const [tree, setTree] = useState(null);\n  const [selectedBranch, setSelectedBranch] = useState(null);\n  const [article, setArticle] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [user, setUser] = useState(null);\n  const appRef = useRef(null);\n\n  // Translation hook\n  const {\n    t\n  } = useTranslation();\n\n  // Available flags for the optimized version\n  const availableFlags = React.useMemo(() => [{\n    code: '-a',\n    name: 'Article',\n    description: t('flagArticle')\n  }, {\n    code: '-ex',\n    name: 'Examples',\n    description: t('flagExamples')\n  }, {\n    code: '-q',\n    name: 'Quiz',\n    description: t('flagQuiz')\n  }, {\n    code: '-vis',\n    name: 'Visual',\n    description: t('flagVisual')\n  }, {\n    code: '-path',\n    name: 'Learning Path',\n    description: t('flagPath')\n  }, {\n    code: '-case',\n    name: 'Case Study',\n    description: t('flagCase')\n  }, {\n    code: '-ro',\n    name: 'Romanian',\n    description: t('flagRomanian')\n  }], [t]);\n\n  // Generate article for selected branch with flags\n  const generateArticle = React.useCallback(async (branch, flags = ['-a']) => {\n    setIsLoading(true);\n    setError(null);\n    try {\n      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${process.env.REACT_APP_OPENROUTER_API_KEY}`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': window.location.origin,\n          'X-Title': 'Knowledge Tree Explorer'\n        },\n        body: JSON.stringify({\n          model: 'deepseek/deepseek-r1-0528:free',\n          messages: [{\n            role: 'user',\n            content: `Write a comprehensive article about \"${branch.nume}\" in the context of \"${tree.tema}\".\n\n            Apply these flags: ${flags.join(', ')}\n\n            Flag meanings:\n            - \"-a\": Standard comprehensive article format\n            - \"-ex\": Include 3 practical examples\n            - \"-q\": Add 5 interactive quiz questions at the end\n            - \"-vis\": Describe visual elements and diagrams\n            - \"-path\": Structure as a learning path with steps\n            - \"-case\": Include real-world case studies\n            - \"-ro\": Adapt content for Romanian context and examples\n\n            Make it educational and engaging. Length: 800-1200 words.`\n          }],\n          temperature: 0.7,\n          max_tokens: 3000\n        })\n      });\n      if (!response.ok) {\n        throw new Error(`API Error: ${response.status}`);\n      }\n      const data = await response.json();\n      const content = data.choices[0].message.content;\n      const articleData = {\n        title: branch.nume,\n        content: content,\n        topic: tree.tema,\n        flags: flags\n      };\n      setArticle(articleData);\n      setCurrentView('article');\n\n      // Award points for article generation\n      const result = gamificationService.awardPoints('ARTICLE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (err) {\n      console.error('Error generating article:', err);\n      setError('Failed to generate article. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  }, [tree]);\n\n  // Gesture handlers\n  const handleDoubleTap = React.useCallback((event, targetInfo) => {\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      // Show flag wheel on double tap of branch\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        createFlagWheel(targetInfo.position, availableFlags, selectedFlags => {\n          console.log('Selected flags:', selectedFlags);\n        }, selectedFlags => {\n          generateArticle(branch, selectedFlags);\n        });\n      }\n    }\n  }, [tree, availableFlags, generateArticle]);\n  const handleSingleTap = React.useCallback((event, targetInfo) => {\n    // Single tap for normal selection\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        setSelectedBranch(branch);\n      }\n    }\n  }, [tree]);\n  const handleLongPress = React.useCallback((event, targetInfo) => {\n    // Long press for quick article generation with default flags\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        generateArticle(branch, ['-a']);\n      }\n    }\n  }, [tree, generateArticle]);\n\n  // Initialize services and authentication\n  useEffect(() => {\n    const storedUser = localStorage.getItem('user');\n    const bypassSecurity = localStorage.getItem('bypassSecurity');\n    if (storedUser || bypassSecurity) {\n      const userData = {\n        id: 'user-1',\n        name: 'User',\n        subscriptionTier: 'premium'\n      };\n      setUser(userData);\n\n      // Award daily login points\n      const result = gamificationService.awardPoints('DAILY_LOGIN');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n\n    // Initialize gesture service\n    if (appRef.current) {\n      gestureService.init(appRef.current, {\n        doubleTap: handleDoubleTap,\n        singleTap: handleSingleTap,\n        longPress: handleLongPress\n      });\n    }\n    return () => {\n      gestureService.destroy();\n    };\n  }, [handleDoubleTap, handleSingleTap, handleLongPress]);\n\n  // Initialize gamification UI when user is logged in\n  useEffect(() => {\n    if (user) {\n      const container = document.getElementById('gamification-container');\n      if (container) {\n        // Clear existing content\n        container.innerHTML = '';\n        // Create gamification UI\n        gamificationService.createGamificationUI(container);\n      }\n    }\n  }, [user]);\n\n  // Core API call - simplified\n  const generateKnowledgeTree = async topicInput => {\n    setIsLoading(true);\n    setError(null);\n    try {\n      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${process.env.REACT_APP_OPENROUTER_API_KEY}`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': window.location.origin,\n          'X-Title': 'Knowledge Tree Explorer'\n        },\n        body: JSON.stringify({\n          model: 'deepseek/deepseek-r1-0528:free',\n          messages: [{\n            role: 'user',\n            content: `Create a knowledge tree for \"${topicInput}\". Return JSON with:\n            {\n              \"tema\": \"${topicInput}\",\n              \"ramuri\": [\n                {\n                  \"nume\": \"Branch Name\",\n                  \"descriere\": \"Brief description\",\n                  \"emoji\": \"📚\",\n                  \"subcategorii\": [\"Sub1\", \"Sub2\", \"Sub3\"]\n                }\n              ]\n            }\n            Provide 6-8 main branches. Keep descriptions concise.`\n          }],\n          temperature: 0.7,\n          max_tokens: 2000\n        })\n      });\n      if (!response.ok) {\n        throw new Error(`API Error: ${response.status}`);\n      }\n      const data = await response.json();\n      const content = data.choices[0].message.content;\n\n      // Extract JSON from response\n      const jsonMatch = content.match(/\\{[\\s\\S]*\\}/);\n      if (jsonMatch) {\n        const treeData = JSON.parse(jsonMatch[0]);\n        setTree(treeData);\n        setCurrentView('tree');\n\n        // Award points for tree generation\n        const result = gamificationService.awardPoints('TREE_GENERATED');\n        if (result.newAchievements.length > 0) {\n          result.newAchievements.forEach(achievement => {\n            gamificationService.showAchievementNotification(achievement);\n          });\n        }\n      } else {\n        throw new Error('Invalid response format');\n      }\n    } catch (err) {\n      console.error('Error generating tree:', err);\n      setError('Failed to generate knowledge tree. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Handle form submission\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (topic.trim()) {\n      generateKnowledgeTree(topic.trim());\n    }\n  };\n\n  // Handle branch selection (single tap)\n  const handleBranchSelect = branch => {\n    setSelectedBranch(branch);\n    // Don't auto-generate article, wait for double-tap or explicit action\n  };\n\n  // Speech functions\n  const handleSpeechToggle = () => {\n    if (!article) return;\n    if (speechService.getStatus().isPlaying) {\n      speechService.toggle();\n    } else {\n      speechService.speak(article.content);\n      // Award points for using speech\n      const result = gamificationService.awardPoints('SPEECH_USED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleSpeechStop = () => {\n    speechService.stop();\n  };\n  const handleSpeechRateChange = rate => {\n    speechService.setRate(rate);\n  };\n\n  // Export functions\n  const handleExportPDF = () => {\n    if (!article) return;\n    const result = exportService.exportAsPDF(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleExportWord = () => {\n    if (!article) return;\n    const result = exportService.exportAsWord(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleCopyToClipboard = async () => {\n    if (!article) return;\n    const result = await exportService.copyToClipboard(article.content);\n    exportService.showMessage(result.message, result.success ? 'success' : 'error');\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  // Navigation functions\n  const goBack = () => {\n    if (currentView === 'article') {\n      setCurrentView('tree');\n      setArticle(null);\n    } else if (currentView === 'tree') {\n      setCurrentView('input');\n      setTree(null);\n      setSelectedBranch(null);\n    }\n  };\n  const goHome = () => {\n    setCurrentView('input');\n    setTree(null);\n    setSelectedBranch(null);\n    setArticle(null);\n    setTopic('');\n  };\n\n  // Quick login for development\n  const quickLogin = () => {\n    localStorage.setItem('bypassSecurity', 'true');\n    setUser({\n      id: 'dev-1',\n      name: 'Developer',\n      subscriptionTier: 'premium'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    ref: appRef,\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"app-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: goHome,\n          className: \"logo-text\",\n          children: t('appTitle')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-right\",\n          children: [user && /*#__PURE__*/_jsxDEV(\"div\", {\n            id: \"gamification-container\",\n            style: {\n              marginRight: '16px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(LanguageSwitcher, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 13\n          }, this), !user ? /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: quickLogin,\n            className: \"btn btn-primary\",\n            style: {\n              marginLeft: '12px'\n            },\n            children: t('quickLogin')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginLeft: '12px'\n            },\n            children: [t('welcome'), \", \", user.name, \"!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 381,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"main-content\",\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error\",\n        children: [\"\\u26A0\\uFE0F \", error, /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setError(null),\n          style: {\n            marginLeft: 'auto',\n            background: 'none',\n            border: 'none',\n            color: 'white',\n            cursor: 'pointer'\n          },\n          children: \"\\u2715\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 407,\n        columnNumber: 11\n      }, this), currentView === 'input' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"title\",\n          children: t('appTitle')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"subtitle\",\n          children: \"Enter any topic to generate an interactive knowledge tree with AI-powered content.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 13\n        }, this), !user ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#f1f5f9',\n            padding: '1rem',\n            borderRadius: '0.5rem',\n            marginBottom: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#334155',\n              marginBottom: '1rem'\n            },\n            children: t('loginRequired')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: quickLogin,\n            className: \"btn btn-primary\",\n            children: t('quickLoginDev')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: topic,\n              onChange: e => setTopic(e.target.value),\n              placeholder: t('topicPlaceholder'),\n              className: \"form-input\",\n              disabled: isLoading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: isLoading || !topic.trim(),\n            className: \"btn btn-primary\",\n            children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 23\n              }, this), t('generating')]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: t('exploreKnowledge')\n            }, void 0, false)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 11\n      }, this), currentView === 'tree' && tree && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tree-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tree-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: tree.tema\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Select a branch to explore in detail\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goBack,\n            className: \"btn btn-secondary\",\n            style: {\n              marginTop: '1rem'\n            },\n            children: \"\\u2190 Back to Input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 13\n        }, this), isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: t('loading')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"branches-grid\",\n          children: tree.ramuri.map((branch, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `branch-item ${selectedBranch === branch ? 'selected' : ''}`,\n            \"data-index\": index,\n            \"data-name\": branch.nume,\n            \"data-description\": branch.descriere,\n            onClick: () => handleBranchSelect(branch),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"branch-emoji\",\n              children: branch.emoji\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"branch-name\",\n              children: branch.nume\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"branch-description\",\n              children: branch.descriere\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 21\n            }, this), branch.subcategorii && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.875rem',\n                color: '#475569',\n                marginTop: '0.5rem'\n              },\n              children: [t('topics'), \": \", branch.subcategorii.slice(0, 3).join(', '), branch.subcategorii.length > 3 && '...']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 23\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"gesture-hint\",\n              style: {\n                fontSize: '0.75rem',\n                color: '#64748b',\n                marginTop: '0.5rem',\n                fontStyle: 'italic'\n              },\n              children: t('gestureHint')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 21\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 482,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 11\n      }, this), currentView === 'article' && article && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tree-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"article-header\",\n            style: {\n              marginBottom: '2rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: goBack,\n              className: \"btn btn-secondary\",\n              children: t('backToTree')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"article-controls\",\n              style: {\n                display: 'flex',\n                gap: '8px',\n                marginTop: '1rem',\n                flexWrap: 'wrap'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"speech-controls-compact\",\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '8px',\n                  padding: '8px 12px',\n                  background: '#f1f5f9',\n                  borderRadius: '6px',\n                  border: '1px solid #e2e8f0'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleSpeechToggle,\n                  className: \"btn-icon\",\n                  title: \"Play/Pause Speech\",\n                  style: {\n                    background: 'none',\n                    border: 'none',\n                    fontSize: '16px',\n                    cursor: 'pointer',\n                    padding: '4px'\n                  },\n                  children: speechService.getStatus().isPlaying ? '⏸️' : '▶️'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 542,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleSpeechStop,\n                  className: \"btn-icon\",\n                  title: \"Stop Speech\",\n                  style: {\n                    background: 'none',\n                    border: 'none',\n                    fontSize: '16px',\n                    cursor: 'pointer',\n                    padding: '4px'\n                  },\n                  children: \"\\u23F9\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 556,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"range\",\n                  min: \"0.5\",\n                  max: \"2\",\n                  step: \"0.1\",\n                  defaultValue: \"1\",\n                  onChange: e => handleSpeechRateChange(parseFloat(e.target.value)),\n                  style: {\n                    width: '60px'\n                  },\n                  title: \"Speech Speed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '12px',\n                    color: '#64748b'\n                  },\n                  children: \"\\uD83D\\uDDE3\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"export-controls-compact\",\n                style: {\n                  display: 'flex',\n                  gap: '4px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleCopyToClipboard,\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '6px 12px',\n                    fontSize: '12px'\n                  },\n                  title: \"Copy to Clipboard\",\n                  children: \"\\uD83D\\uDCCB Copy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleExportPDF,\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '6px 12px',\n                    fontSize: '12px'\n                  },\n                  title: \"Export as PDF\",\n                  children: \"\\uD83D\\uDCC4 PDF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 596,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleExportWord,\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '6px 12px',\n                    fontSize: '12px'\n                  },\n                  title: \"Export as Word\",\n                  children: \"\\uD83D\\uDCDD Word\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 604,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 584,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"title\",\n            children: article.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 616,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#475569',\n              marginBottom: '2rem',\n              fontSize: '0.9rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: [t('partOf'), \": \", article.topic]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 618,\n              columnNumber: 17\n            }, this), article.flags && article.flags.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                marginLeft: '16px'\n              },\n              children: [t('flags'), \": \", article.flags.join(', ')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 620,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"article-content\",\n            style: {\n              lineHeight: '1.8',\n              fontSize: '1.1rem'\n            },\n            children: article.content.split('\\n').map((paragraph, index) => paragraph.trim() && /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                marginBottom: '1rem'\n              },\n              children: paragraph\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 629,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 626,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 519,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 518,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 405,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 379,\n    columnNumber: 5\n  }, this);\n};\n_s(OptimizedApp, \"SBHX/m6km6wP0XZni4rxJXfKIVY=\", false, function () {\n  return [useTranslation];\n});\n_c = OptimizedApp;\nexport default OptimizedApp;\nvar _c;\n$RefreshReg$(_c, \"OptimizedApp\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "gestureService", "createFlagWheel", "speechService", "exportService", "gamificationService", "LanguageSwitcher", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "OptimizedApp", "_s", "current<PERSON>iew", "set<PERSON><PERSON><PERSON>View", "topic", "setTopic", "tree", "setTree", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedBranch", "article", "setArticle", "isLoading", "setIsLoading", "error", "setError", "user", "setUser", "appRef", "t", "availableFlags", "useMemo", "code", "name", "description", "generateArticle", "useCallback", "branch", "flags", "response", "fetch", "method", "headers", "process", "env", "REACT_APP_OPENROUTER_API_KEY", "window", "location", "origin", "body", "JSON", "stringify", "model", "messages", "role", "content", "nume", "tema", "join", "temperature", "max_tokens", "ok", "Error", "status", "data", "json", "choices", "message", "articleData", "title", "result", "awardPoints", "newAchievements", "length", "for<PERSON>ach", "achievement", "showAchievementNotification", "err", "console", "handleDoubleTap", "event", "targetInfo", "isBranchItem", "branchData", "<PERSON><PERSON>", "index", "position", "selected<PERSON><PERSON><PERSON>", "log", "handleSingleTap", "handleLongPress", "storedUser", "localStorage", "getItem", "bypassSecurity", "userData", "id", "subscriptionTier", "current", "init", "doubleTap", "singleTap", "longPress", "destroy", "container", "document", "getElementById", "innerHTML", "createGamificationUI", "generateKnowledgeTree", "topicInput", "jsonMatch", "match", "treeData", "parse", "handleSubmit", "e", "preventDefault", "trim", "handleBranchSelect", "handleSpeechToggle", "getStatus", "isPlaying", "toggle", "speak", "handleSpeechStop", "stop", "handleSpeechRateChange", "rate", "setRate", "handleExportPDF", "exportAsPDF", "replace", "success", "gamResult", "handleExportWord", "exportAsWord", "handleCopyToClipboard", "copyToClipboard", "showMessage", "goBack", "goHome", "quickLogin", "setItem", "className", "ref", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginRight", "marginLeft", "background", "border", "color", "cursor", "padding", "borderRadius", "marginBottom", "onSubmit", "type", "value", "onChange", "target", "placeholder", "disabled", "marginTop", "map", "desc<PERSON><PERSON>", "emoji", "subcategorii", "fontSize", "slice", "fontStyle", "display", "gap", "flexWrap", "alignItems", "min", "max", "step", "defaultValue", "parseFloat", "width", "lineHeight", "split", "paragraph", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Appv1/src/components/OptimizedApp.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport '../styles/optimized.css';\nimport gestureService, { createFlagWheel } from '../services/gestureService';\nimport speechService from '../services/speechService';\nimport exportService from '../services/exportService';\nimport gamificationService from '../services/optimizedGamificationService';\nimport LanguageSwitcher from './LanguageSwitcher';\nimport { useTranslation } from '../utils/i18n';\n\n// Optimized Knowledge Tree Explorer - Pareto 80/20 Implementation\n// Focus on core functionality with maximum impact\n\nconst OptimizedApp = () => {\n  const [currentView, setCurrentView] = useState('input'); // 'input', 'tree', 'article'\n  const [topic, setTopic] = useState('');\n  const [tree, setTree] = useState(null);\n  const [selectedBranch, setSelectedBranch] = useState(null);\n  const [article, setArticle] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [user, setUser] = useState(null);\n  const appRef = useRef(null);\n\n  // Translation hook\n  const { t } = useTranslation();\n\n  // Available flags for the optimized version\n  const availableFlags = React.useMemo(() => [\n    { code: '-a', name: 'Article', description: t('flagArticle') },\n    { code: '-ex', name: 'Examples', description: t('flagExamples') },\n    { code: '-q', name: 'Quiz', description: t('flagQuiz') },\n    { code: '-vis', name: 'Visual', description: t('flagVisual') },\n    { code: '-path', name: 'Learning Path', description: t('flagPath') },\n    { code: '-case', name: 'Case Study', description: t('flagCase') },\n    { code: '-ro', name: 'Romanian', description: t('flagRomanian') }\n  ], [t]);\n\n  // Generate article for selected branch with flags\n  const generateArticle = React.useCallback(async (branch, flags = ['-a']) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${process.env.REACT_APP_OPENROUTER_API_KEY}`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': window.location.origin,\n          'X-Title': 'Knowledge Tree Explorer'\n        },\n        body: JSON.stringify({\n          model: 'deepseek/deepseek-r1-0528:free',\n          messages: [{\n            role: 'user',\n            content: `Write a comprehensive article about \"${branch.nume}\" in the context of \"${tree.tema}\".\n\n            Apply these flags: ${flags.join(', ')}\n\n            Flag meanings:\n            - \"-a\": Standard comprehensive article format\n            - \"-ex\": Include 3 practical examples\n            - \"-q\": Add 5 interactive quiz questions at the end\n            - \"-vis\": Describe visual elements and diagrams\n            - \"-path\": Structure as a learning path with steps\n            - \"-case\": Include real-world case studies\n            - \"-ro\": Adapt content for Romanian context and examples\n\n            Make it educational and engaging. Length: 800-1200 words.`\n          }],\n          temperature: 0.7,\n          max_tokens: 3000\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error(`API Error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      const content = data.choices[0].message.content;\n\n      const articleData = {\n        title: branch.nume,\n        content: content,\n        topic: tree.tema,\n        flags: flags\n      };\n\n      setArticle(articleData);\n      setCurrentView('article');\n\n      // Award points for article generation\n      const result = gamificationService.awardPoints('ARTICLE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (err) {\n      console.error('Error generating article:', err);\n      setError('Failed to generate article. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  }, [tree]);\n\n  // Gesture handlers\n  const handleDoubleTap = React.useCallback((event, targetInfo) => {\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      // Show flag wheel on double tap of branch\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        createFlagWheel(\n          targetInfo.position,\n          availableFlags,\n          (selectedFlags) => {\n            console.log('Selected flags:', selectedFlags);\n          },\n          (selectedFlags) => {\n            generateArticle(branch, selectedFlags);\n          }\n        );\n      }\n    }\n  }, [tree, availableFlags, generateArticle]);\n\n  const handleSingleTap = React.useCallback((event, targetInfo) => {\n    // Single tap for normal selection\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        setSelectedBranch(branch);\n      }\n    }\n  }, [tree]);\n\n  const handleLongPress = React.useCallback((event, targetInfo) => {\n    // Long press for quick article generation with default flags\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        generateArticle(branch, ['-a']);\n      }\n    }\n  }, [tree, generateArticle]);\n\n  // Initialize services and authentication\n  useEffect(() => {\n    const storedUser = localStorage.getItem('user');\n    const bypassSecurity = localStorage.getItem('bypassSecurity');\n\n    if (storedUser || bypassSecurity) {\n      const userData = {\n        id: 'user-1',\n        name: 'User',\n        subscriptionTier: 'premium'\n      };\n      setUser(userData);\n\n      // Award daily login points\n      const result = gamificationService.awardPoints('DAILY_LOGIN');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n\n    // Initialize gesture service\n    if (appRef.current) {\n      gestureService.init(appRef.current, {\n        doubleTap: handleDoubleTap,\n        singleTap: handleSingleTap,\n        longPress: handleLongPress\n      });\n    }\n\n    return () => {\n      gestureService.destroy();\n    };\n  }, [handleDoubleTap, handleSingleTap, handleLongPress]);\n\n  // Initialize gamification UI when user is logged in\n  useEffect(() => {\n    if (user) {\n      const container = document.getElementById('gamification-container');\n      if (container) {\n        // Clear existing content\n        container.innerHTML = '';\n        // Create gamification UI\n        gamificationService.createGamificationUI(container);\n      }\n    }\n  }, [user]);\n\n  // Core API call - simplified\n  const generateKnowledgeTree = async (topicInput) => {\n    setIsLoading(true);\n    setError(null);\n    \n    try {\n      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${process.env.REACT_APP_OPENROUTER_API_KEY}`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': window.location.origin,\n          'X-Title': 'Knowledge Tree Explorer'\n        },\n        body: JSON.stringify({\n          model: 'deepseek/deepseek-r1-0528:free',\n          messages: [{\n            role: 'user',\n            content: `Create a knowledge tree for \"${topicInput}\". Return JSON with:\n            {\n              \"tema\": \"${topicInput}\",\n              \"ramuri\": [\n                {\n                  \"nume\": \"Branch Name\",\n                  \"descriere\": \"Brief description\",\n                  \"emoji\": \"📚\",\n                  \"subcategorii\": [\"Sub1\", \"Sub2\", \"Sub3\"]\n                }\n              ]\n            }\n            Provide 6-8 main branches. Keep descriptions concise.`\n          }],\n          temperature: 0.7,\n          max_tokens: 2000\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error(`API Error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      const content = data.choices[0].message.content;\n      \n      // Extract JSON from response\n      const jsonMatch = content.match(/\\{[\\s\\S]*\\}/);\n      if (jsonMatch) {\n        const treeData = JSON.parse(jsonMatch[0]);\n        setTree(treeData);\n        setCurrentView('tree');\n\n        // Award points for tree generation\n        const result = gamificationService.awardPoints('TREE_GENERATED');\n        if (result.newAchievements.length > 0) {\n          result.newAchievements.forEach(achievement => {\n            gamificationService.showAchievementNotification(achievement);\n          });\n        }\n      } else {\n        throw new Error('Invalid response format');\n      }\n    } catch (err) {\n      console.error('Error generating tree:', err);\n      setError('Failed to generate knowledge tree. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n\n\n  // Handle form submission\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    if (topic.trim()) {\n      generateKnowledgeTree(topic.trim());\n    }\n  };\n\n  // Handle branch selection (single tap)\n  const handleBranchSelect = (branch) => {\n    setSelectedBranch(branch);\n    // Don't auto-generate article, wait for double-tap or explicit action\n  };\n\n  // Speech functions\n  const handleSpeechToggle = () => {\n    if (!article) return;\n\n    if (speechService.getStatus().isPlaying) {\n      speechService.toggle();\n    } else {\n      speechService.speak(article.content);\n      // Award points for using speech\n      const result = gamificationService.awardPoints('SPEECH_USED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleSpeechStop = () => {\n    speechService.stop();\n  };\n\n  const handleSpeechRateChange = (rate) => {\n    speechService.setRate(rate);\n  };\n\n  // Export functions\n  const handleExportPDF = () => {\n    if (!article) return;\n    const result = exportService.exportAsPDF(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleExportWord = () => {\n    if (!article) return;\n    const result = exportService.exportAsWord(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleCopyToClipboard = async () => {\n    if (!article) return;\n    const result = await exportService.copyToClipboard(article.content);\n    exportService.showMessage(result.message, result.success ? 'success' : 'error');\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  // Navigation functions\n  const goBack = () => {\n    if (currentView === 'article') {\n      setCurrentView('tree');\n      setArticle(null);\n    } else if (currentView === 'tree') {\n      setCurrentView('input');\n      setTree(null);\n      setSelectedBranch(null);\n    }\n  };\n\n  const goHome = () => {\n    setCurrentView('input');\n    setTree(null);\n    setSelectedBranch(null);\n    setArticle(null);\n    setTopic('');\n  };\n\n  // Quick login for development\n  const quickLogin = () => {\n    localStorage.setItem('bypassSecurity', 'true');\n    setUser({ id: 'dev-1', name: 'Developer', subscriptionTier: 'premium' });\n  };\n\n  return (\n    <div className=\"app\" ref={appRef}>\n      {/* Header */}\n      <header className=\"app-header\">\n        <div className=\"header-content\">\n          <button onClick={goHome} className=\"logo-text\">\n            {t('appTitle')}\n          </button>\n          <div className=\"header-right\">\n            {user && (\n              <div id=\"gamification-container\" style={{ marginRight: '16px' }}>\n                {/* Gamification UI will be inserted here */}\n              </div>\n            )}\n            <LanguageSwitcher />\n            {!user ? (\n              <button onClick={quickLogin} className=\"btn btn-primary\" style={{ marginLeft: '12px' }}>\n                {t('quickLogin')}\n              </button>\n            ) : (\n              <span style={{ marginLeft: '12px' }}>{t('welcome')}, {user.name}!</span>\n            )}\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"main-content\">\n        {error && (\n          <div className=\"error\">\n            ⚠️ {error}\n            <button onClick={() => setError(null)} style={{marginLeft: 'auto', background: 'none', border: 'none', color: 'white', cursor: 'pointer'}}>\n              ✕\n            </button>\n          </div>\n        )}\n\n        {/* Topic Input View */}\n        {currentView === 'input' && (\n          <div className=\"card text-center\">\n            <h1 className=\"title\">{t('appTitle')}</h1>\n            <p className=\"subtitle\">\n              Enter any topic to generate an interactive knowledge tree with AI-powered content.\n            </p>\n\n            {!user ? (\n              <div style={{background: '#f1f5f9', padding: '1rem', borderRadius: '0.5rem', marginBottom: '2rem'}}>\n                <p style={{color: '#334155', marginBottom: '1rem'}}>\n                  {t('loginRequired')}\n                </p>\n                <button onClick={quickLogin} className=\"btn btn-primary\">\n                  {t('quickLoginDev')}\n                </button>\n              </div>\n            ) : (\n              <form onSubmit={handleSubmit}>\n                <div className=\"form-group\">\n                  <input\n                    type=\"text\"\n                    value={topic}\n                    onChange={(e) => setTopic(e.target.value)}\n                    placeholder={t('topicPlaceholder')}\n                    className=\"form-input\"\n                    disabled={isLoading}\n                  />\n                </div>\n                <button\n                  type=\"submit\"\n                  disabled={isLoading || !topic.trim()}\n                  className=\"btn btn-primary\"\n                >\n                  {isLoading ? (\n                    <>\n                      <span className=\"spinner\"></span>\n                      {t('generating')}\n                    </>\n                  ) : (\n                    <>\n                      {t('exploreKnowledge')}\n                    </>\n                  )}\n                </button>\n              </form>\n            )}\n          </div>\n        )}\n\n        {/* Tree View */}\n        {currentView === 'tree' && tree && (\n          <div className=\"tree-container\">\n            <div className=\"tree-header\">\n              <h1>{tree.tema}</h1>\n              <p>Select a branch to explore in detail</p>\n              <button onClick={goBack} className=\"btn btn-secondary\" style={{marginTop: '1rem'}}>\n                ← Back to Input\n              </button>\n            </div>\n\n            {isLoading ? (\n              <div className=\"loading\">\n                <span className=\"spinner\"></span>\n                <span>{t('loading')}</span>\n              </div>\n            ) : (\n              <div className=\"branches-grid\">\n                {tree.ramuri.map((branch, index) => (\n                  <div\n                    key={index}\n                    className={`branch-item ${selectedBranch === branch ? 'selected' : ''}`}\n                    data-index={index}\n                    data-name={branch.nume}\n                    data-description={branch.descriere}\n                    onClick={() => handleBranchSelect(branch)}\n                  >\n                    <div className=\"branch-emoji\">{branch.emoji}</div>\n                    <h3 className=\"branch-name\">{branch.nume}</h3>\n                    <p className=\"branch-description\">{branch.descriere}</p>\n                    {branch.subcategorii && (\n                      <div style={{fontSize: '0.875rem', color: '#475569', marginTop: '0.5rem'}}>\n                        {t('topics')}: {branch.subcategorii.slice(0, 3).join(', ')}\n                        {branch.subcategorii.length > 3 && '...'}\n                      </div>\n                    )}\n                    <div className=\"gesture-hint\" style={{\n                      fontSize: '0.75rem',\n                      color: '#64748b',\n                      marginTop: '0.5rem',\n                      fontStyle: 'italic'\n                    }}>\n                      {t('gestureHint')}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Article View */}\n        {currentView === 'article' && article && (\n          <div className=\"tree-container\">\n            <div className=\"card\">\n              <div className=\"article-header\" style={{marginBottom: '2rem'}}>\n                <button onClick={goBack} className=\"btn btn-secondary\">\n                  {t('backToTree')}\n                </button>\n\n                {/* Article Controls */}\n                <div className=\"article-controls\" style={{\n                  display: 'flex',\n                  gap: '8px',\n                  marginTop: '1rem',\n                  flexWrap: 'wrap'\n                }}>\n                  {/* Speech Controls */}\n                  <div className=\"speech-controls-compact\" style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '8px',\n                    padding: '8px 12px',\n                    background: '#f1f5f9',\n                    borderRadius: '6px',\n                    border: '1px solid #e2e8f0'\n                  }}>\n                    <button\n                      onClick={handleSpeechToggle}\n                      className=\"btn-icon\"\n                      title=\"Play/Pause Speech\"\n                      style={{\n                        background: 'none',\n                        border: 'none',\n                        fontSize: '16px',\n                        cursor: 'pointer',\n                        padding: '4px'\n                      }}\n                    >\n                      {speechService.getStatus().isPlaying ? '⏸️' : '▶️'}\n                    </button>\n                    <button\n                      onClick={handleSpeechStop}\n                      className=\"btn-icon\"\n                      title=\"Stop Speech\"\n                      style={{\n                        background: 'none',\n                        border: 'none',\n                        fontSize: '16px',\n                        cursor: 'pointer',\n                        padding: '4px'\n                      }}\n                    >\n                      ⏹️\n                    </button>\n                    <input\n                      type=\"range\"\n                      min=\"0.5\"\n                      max=\"2\"\n                      step=\"0.1\"\n                      defaultValue=\"1\"\n                      onChange={(e) => handleSpeechRateChange(parseFloat(e.target.value))}\n                      style={{width: '60px'}}\n                      title=\"Speech Speed\"\n                    />\n                    <span style={{fontSize: '12px', color: '#64748b'}}>🗣️</span>\n                  </div>\n\n                  {/* Export Controls */}\n                  <div className=\"export-controls-compact\" style={{\n                    display: 'flex',\n                    gap: '4px'\n                  }}>\n                    <button\n                      onClick={handleCopyToClipboard}\n                      className=\"btn btn-secondary\"\n                      style={{padding: '6px 12px', fontSize: '12px'}}\n                      title=\"Copy to Clipboard\"\n                    >\n                      📋 Copy\n                    </button>\n                    <button\n                      onClick={handleExportPDF}\n                      className=\"btn btn-secondary\"\n                      style={{padding: '6px 12px', fontSize: '12px'}}\n                      title=\"Export as PDF\"\n                    >\n                      📄 PDF\n                    </button>\n                    <button\n                      onClick={handleExportWord}\n                      className=\"btn btn-secondary\"\n                      style={{padding: '6px 12px', fontSize: '12px'}}\n                      title=\"Export as Word\"\n                    >\n                      📝 Word\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              <h1 className=\"title\">{article.title}</h1>\n              <div style={{color: '#475569', marginBottom: '2rem', fontSize: '0.9rem'}}>\n                <span>{t('partOf')}: {article.topic}</span>\n                {article.flags && article.flags.length > 0 && (\n                  <span style={{marginLeft: '16px'}}>\n                    {t('flags')}: {article.flags.join(', ')}\n                  </span>\n                )}\n              </div>\n\n              <div className=\"article-content\" style={{lineHeight: '1.8', fontSize: '1.1rem'}}>\n                {article.content.split('\\n').map((paragraph, index) => (\n                  paragraph.trim() && (\n                    <p key={index} style={{marginBottom: '1rem'}}>\n                      {paragraph}\n                    </p>\n                  )\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n      </main>\n    </div>\n  );\n};\n\nexport default OptimizedApp;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,yBAAyB;AAChC,OAAOC,cAAc,IAAIC,eAAe,QAAQ,4BAA4B;AAC5E,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,mBAAmB,MAAM,0CAA0C;AAC1E,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,cAAc,QAAQ,eAAe;;AAE9C;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEA,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACzD,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoB,IAAI,EAAEC,OAAO,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACsB,cAAc,EAAEC,iBAAiB,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC4B,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC8B,IAAI,EAAEC,OAAO,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAMgC,MAAM,GAAG9B,MAAM,CAAC,IAAI,CAAC;;EAE3B;EACA,MAAM;IAAE+B;EAAE,CAAC,GAAGxB,cAAc,CAAC,CAAC;;EAE9B;EACA,MAAMyB,cAAc,GAAGnC,KAAK,CAACoC,OAAO,CAAC,MAAM,CACzC;IAAEC,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,SAAS;IAAEC,WAAW,EAAEL,CAAC,CAAC,aAAa;EAAE,CAAC,EAC9D;IAAEG,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAE,UAAU;IAAEC,WAAW,EAAEL,CAAC,CAAC,cAAc;EAAE,CAAC,EACjE;IAAEG,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,MAAM;IAAEC,WAAW,EAAEL,CAAC,CAAC,UAAU;EAAE,CAAC,EACxD;IAAEG,IAAI,EAAE,MAAM;IAAEC,IAAI,EAAE,QAAQ;IAAEC,WAAW,EAAEL,CAAC,CAAC,YAAY;EAAE,CAAC,EAC9D;IAAEG,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,eAAe;IAAEC,WAAW,EAAEL,CAAC,CAAC,UAAU;EAAE,CAAC,EACpE;IAAEG,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,YAAY;IAAEC,WAAW,EAAEL,CAAC,CAAC,UAAU;EAAE,CAAC,EACjE;IAAEG,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAE,UAAU;IAAEC,WAAW,EAAEL,CAAC,CAAC,cAAc;EAAE,CAAC,CAClE,EAAE,CAACA,CAAC,CAAC,CAAC;;EAEP;EACA,MAAMM,eAAe,GAAGxC,KAAK,CAACyC,WAAW,CAAC,OAAOC,MAAM,EAAEC,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK;IAC1Ef,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMc,QAAQ,GAAG,MAAMC,KAAK,CAAC,+CAA+C,EAAE;QAC5EC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,OAAO,CAACC,GAAG,CAACC,4BAA4B,EAAE;UACrE,cAAc,EAAE,kBAAkB;UAClC,cAAc,EAAEC,MAAM,CAACC,QAAQ,CAACC,MAAM;UACtC,SAAS,EAAE;QACb,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,KAAK,EAAE,gCAAgC;UACvCC,QAAQ,EAAE,CAAC;YACTC,IAAI,EAAE,MAAM;YACZC,OAAO,EAAE,wCAAwClB,MAAM,CAACmB,IAAI,wBAAwBxC,IAAI,CAACyC,IAAI;AACzG;AACA,iCAAiCnB,KAAK,CAACoB,IAAI,CAAC,IAAI,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACU,CAAC,CAAC;UACFC,WAAW,EAAE,GAAG;UAChBC,UAAU,EAAE;QACd,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACrB,QAAQ,CAACsB,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,cAAcvB,QAAQ,CAACwB,MAAM,EAAE,CAAC;MAClD;MAEA,MAAMC,IAAI,GAAG,MAAMzB,QAAQ,CAAC0B,IAAI,CAAC,CAAC;MAClC,MAAMV,OAAO,GAAGS,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO,CAACZ,OAAO;MAE/C,MAAMa,WAAW,GAAG;QAClBC,KAAK,EAAEhC,MAAM,CAACmB,IAAI;QAClBD,OAAO,EAAEA,OAAO;QAChBzC,KAAK,EAAEE,IAAI,CAACyC,IAAI;QAChBnB,KAAK,EAAEA;MACT,CAAC;MAEDjB,UAAU,CAAC+C,WAAW,CAAC;MACvBvD,cAAc,CAAC,SAAS,CAAC;;MAEzB;MACA,MAAMyD,MAAM,GAAGnE,mBAAmB,CAACoE,WAAW,CAAC,mBAAmB,CAAC;MACnE,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5CxE,mBAAmB,CAACyE,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZC,OAAO,CAACtD,KAAK,CAAC,2BAA2B,EAAEqD,GAAG,CAAC;MAC/CpD,QAAQ,CAAC,+CAA+C,CAAC;IAC3D,CAAC,SAAS;MACRF,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACP,IAAI,CAAC,CAAC;;EAEV;EACA,MAAM+D,eAAe,GAAGpF,KAAK,CAACyC,WAAW,CAAC,CAAC4C,KAAK,EAAEC,UAAU,KAAK;IAC/D,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,IAAInE,IAAI,EAAE;MAC5D;MACA,MAAMqB,MAAM,GAAGrB,IAAI,CAACoE,MAAM,CAACH,UAAU,CAACE,UAAU,CAACE,KAAK,CAAC;MACvD,IAAIhD,MAAM,EAAE;QACVrC,eAAe,CACbiF,UAAU,CAACK,QAAQ,EACnBxD,cAAc,EACbyD,aAAa,IAAK;UACjBT,OAAO,CAACU,GAAG,CAAC,iBAAiB,EAAED,aAAa,CAAC;QAC/C,CAAC,EACAA,aAAa,IAAK;UACjBpD,eAAe,CAACE,MAAM,EAAEkD,aAAa,CAAC;QACxC,CACF,CAAC;MACH;IACF;EACF,CAAC,EAAE,CAACvE,IAAI,EAAEc,cAAc,EAAEK,eAAe,CAAC,CAAC;EAE3C,MAAMsD,eAAe,GAAG9F,KAAK,CAACyC,WAAW,CAAC,CAAC4C,KAAK,EAAEC,UAAU,KAAK;IAC/D;IACA,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,IAAInE,IAAI,EAAE;MAC5D,MAAMqB,MAAM,GAAGrB,IAAI,CAACoE,MAAM,CAACH,UAAU,CAACE,UAAU,CAACE,KAAK,CAAC;MACvD,IAAIhD,MAAM,EAAE;QACVlB,iBAAiB,CAACkB,MAAM,CAAC;MAC3B;IACF;EACF,CAAC,EAAE,CAACrB,IAAI,CAAC,CAAC;EAEV,MAAM0E,eAAe,GAAG/F,KAAK,CAACyC,WAAW,CAAC,CAAC4C,KAAK,EAAEC,UAAU,KAAK;IAC/D;IACA,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,IAAInE,IAAI,EAAE;MAC5D,MAAMqB,MAAM,GAAGrB,IAAI,CAACoE,MAAM,CAACH,UAAU,CAACE,UAAU,CAACE,KAAK,CAAC;MACvD,IAAIhD,MAAM,EAAE;QACVF,eAAe,CAACE,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC;MACjC;IACF;EACF,CAAC,EAAE,CAACrB,IAAI,EAAEmB,eAAe,CAAC,CAAC;;EAE3B;EACAtC,SAAS,CAAC,MAAM;IACd,MAAM8F,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAC/C,MAAMC,cAAc,GAAGF,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAE7D,IAAIF,UAAU,IAAIG,cAAc,EAAE;MAChC,MAAMC,QAAQ,GAAG;QACfC,EAAE,EAAE,QAAQ;QACZ/D,IAAI,EAAE,MAAM;QACZgE,gBAAgB,EAAE;MACpB,CAAC;MACDtE,OAAO,CAACoE,QAAQ,CAAC;;MAEjB;MACA,MAAMzB,MAAM,GAAGnE,mBAAmB,CAACoE,WAAW,CAAC,aAAa,CAAC;MAC7D,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5CxE,mBAAmB,CAACyE,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;;IAEA;IACA,IAAI/C,MAAM,CAACsE,OAAO,EAAE;MAClBnG,cAAc,CAACoG,IAAI,CAACvE,MAAM,CAACsE,OAAO,EAAE;QAClCE,SAAS,EAAErB,eAAe;QAC1BsB,SAAS,EAAEZ,eAAe;QAC1Ba,SAAS,EAAEZ;MACb,CAAC,CAAC;IACJ;IAEA,OAAO,MAAM;MACX3F,cAAc,CAACwG,OAAO,CAAC,CAAC;IAC1B,CAAC;EACH,CAAC,EAAE,CAACxB,eAAe,EAAEU,eAAe,EAAEC,eAAe,CAAC,CAAC;;EAEvD;EACA7F,SAAS,CAAC,MAAM;IACd,IAAI6B,IAAI,EAAE;MACR,MAAM8E,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,wBAAwB,CAAC;MACnE,IAAIF,SAAS,EAAE;QACb;QACAA,SAAS,CAACG,SAAS,GAAG,EAAE;QACxB;QACAxG,mBAAmB,CAACyG,oBAAoB,CAACJ,SAAS,CAAC;MACrD;IACF;EACF,CAAC,EAAE,CAAC9E,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMmF,qBAAqB,GAAG,MAAOC,UAAU,IAAK;IAClDvF,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMc,QAAQ,GAAG,MAAMC,KAAK,CAAC,+CAA+C,EAAE;QAC5EC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,OAAO,CAACC,GAAG,CAACC,4BAA4B,EAAE;UACrE,cAAc,EAAE,kBAAkB;UAClC,cAAc,EAAEC,MAAM,CAACC,QAAQ,CAACC,MAAM;UACtC,SAAS,EAAE;QACb,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,KAAK,EAAE,gCAAgC;UACvCC,QAAQ,EAAE,CAAC;YACTC,IAAI,EAAE,MAAM;YACZC,OAAO,EAAE,gCAAgCuD,UAAU;AAC/D;AACA,yBAAyBA,UAAU;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACU,CAAC,CAAC;UACFnD,WAAW,EAAE,GAAG;UAChBC,UAAU,EAAE;QACd,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACrB,QAAQ,CAACsB,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,cAAcvB,QAAQ,CAACwB,MAAM,EAAE,CAAC;MAClD;MAEA,MAAMC,IAAI,GAAG,MAAMzB,QAAQ,CAAC0B,IAAI,CAAC,CAAC;MAClC,MAAMV,OAAO,GAAGS,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO,CAACZ,OAAO;;MAE/C;MACA,MAAMwD,SAAS,GAAGxD,OAAO,CAACyD,KAAK,CAAC,aAAa,CAAC;MAC9C,IAAID,SAAS,EAAE;QACb,MAAME,QAAQ,GAAG/D,IAAI,CAACgE,KAAK,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC;QACzC9F,OAAO,CAACgG,QAAQ,CAAC;QACjBpG,cAAc,CAAC,MAAM,CAAC;;QAEtB;QACA,MAAMyD,MAAM,GAAGnE,mBAAmB,CAACoE,WAAW,CAAC,gBAAgB,CAAC;QAChE,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;UACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;YAC5CxE,mBAAmB,CAACyE,2BAA2B,CAACD,WAAW,CAAC;UAC9D,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL,MAAM,IAAIb,KAAK,CAAC,yBAAyB,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOe,GAAG,EAAE;MACZC,OAAO,CAACtD,KAAK,CAAC,wBAAwB,EAAEqD,GAAG,CAAC;MAC5CpD,QAAQ,CAAC,sDAAsD,CAAC;IAClE,CAAC,SAAS;MACRF,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAID;EACA,MAAM4F,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIvG,KAAK,CAACwG,IAAI,CAAC,CAAC,EAAE;MAChBT,qBAAqB,CAAC/F,KAAK,CAACwG,IAAI,CAAC,CAAC,CAAC;IACrC;EACF,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAIlF,MAAM,IAAK;IACrClB,iBAAiB,CAACkB,MAAM,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMmF,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI,CAACpG,OAAO,EAAE;IAEd,IAAInB,aAAa,CAACwH,SAAS,CAAC,CAAC,CAACC,SAAS,EAAE;MACvCzH,aAAa,CAAC0H,MAAM,CAAC,CAAC;IACxB,CAAC,MAAM;MACL1H,aAAa,CAAC2H,KAAK,CAACxG,OAAO,CAACmC,OAAO,CAAC;MACpC;MACA,MAAMe,MAAM,GAAGnE,mBAAmB,CAACoE,WAAW,CAAC,aAAa,CAAC;MAC7D,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5CxE,mBAAmB,CAACyE,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAMkD,gBAAgB,GAAGA,CAAA,KAAM;IAC7B5H,aAAa,CAAC6H,IAAI,CAAC,CAAC;EACtB,CAAC;EAED,MAAMC,sBAAsB,GAAIC,IAAI,IAAK;IACvC/H,aAAa,CAACgI,OAAO,CAACD,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAAC9G,OAAO,EAAE;IACd,MAAMkD,MAAM,GAAGpE,aAAa,CAACiI,WAAW,CAAC/G,OAAO,EAAE,GAAGA,OAAO,CAACiD,KAAK,CAAC+D,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,EAAE,CAAC;IACnG,IAAI9D,MAAM,CAAC+D,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAGnI,mBAAmB,CAACoE,WAAW,CAAC,aAAa,CAAC;MAChE,IAAI+D,SAAS,CAAC9D,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxC6D,SAAS,CAAC9D,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/CxE,mBAAmB,CAACyE,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAM4D,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAACnH,OAAO,EAAE;IACd,MAAMkD,MAAM,GAAGpE,aAAa,CAACsI,YAAY,CAACpH,OAAO,EAAE,GAAGA,OAAO,CAACiD,KAAK,CAAC+D,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,EAAE,CAAC;IACpG,IAAI9D,MAAM,CAAC+D,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAGnI,mBAAmB,CAACoE,WAAW,CAAC,aAAa,CAAC;MAChE,IAAI+D,SAAS,CAAC9D,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxC6D,SAAS,CAAC9D,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/CxE,mBAAmB,CAACyE,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAM8D,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI,CAACrH,OAAO,EAAE;IACd,MAAMkD,MAAM,GAAG,MAAMpE,aAAa,CAACwI,eAAe,CAACtH,OAAO,CAACmC,OAAO,CAAC;IACnErD,aAAa,CAACyI,WAAW,CAACrE,MAAM,CAACH,OAAO,EAAEG,MAAM,CAAC+D,OAAO,GAAG,SAAS,GAAG,OAAO,CAAC;IAC/E,IAAI/D,MAAM,CAAC+D,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAGnI,mBAAmB,CAACoE,WAAW,CAAC,aAAa,CAAC;MAChE,IAAI+D,SAAS,CAAC9D,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxC6D,SAAS,CAAC9D,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/CxE,mBAAmB,CAACyE,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;;EAED;EACA,MAAMiE,MAAM,GAAGA,CAAA,KAAM;IACnB,IAAIhI,WAAW,KAAK,SAAS,EAAE;MAC7BC,cAAc,CAAC,MAAM,CAAC;MACtBQ,UAAU,CAAC,IAAI,CAAC;IAClB,CAAC,MAAM,IAAIT,WAAW,KAAK,MAAM,EAAE;MACjCC,cAAc,CAAC,OAAO,CAAC;MACvBI,OAAO,CAAC,IAAI,CAAC;MACbE,iBAAiB,CAAC,IAAI,CAAC;IACzB;EACF,CAAC;EAED,MAAM0H,MAAM,GAAGA,CAAA,KAAM;IACnBhI,cAAc,CAAC,OAAO,CAAC;IACvBI,OAAO,CAAC,IAAI,CAAC;IACbE,iBAAiB,CAAC,IAAI,CAAC;IACvBE,UAAU,CAAC,IAAI,CAAC;IAChBN,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;;EAED;EACA,MAAM+H,UAAU,GAAGA,CAAA,KAAM;IACvBlD,YAAY,CAACmD,OAAO,CAAC,gBAAgB,EAAE,MAAM,CAAC;IAC9CpH,OAAO,CAAC;MAAEqE,EAAE,EAAE,OAAO;MAAE/D,IAAI,EAAE,WAAW;MAAEgE,gBAAgB,EAAE;IAAU,CAAC,CAAC;EAC1E,CAAC;EAED,oBACE1F,OAAA;IAAKyI,SAAS,EAAC,KAAK;IAACC,GAAG,EAAErH,MAAO;IAAAsH,QAAA,gBAE/B3I,OAAA;MAAQyI,SAAS,EAAC,YAAY;MAAAE,QAAA,eAC5B3I,OAAA;QAAKyI,SAAS,EAAC,gBAAgB;QAAAE,QAAA,gBAC7B3I,OAAA;UAAQ4I,OAAO,EAAEN,MAAO;UAACG,SAAS,EAAC,WAAW;UAAAE,QAAA,EAC3CrH,CAAC,CAAC,UAAU;QAAC;UAAAuH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACThJ,OAAA;UAAKyI,SAAS,EAAC,cAAc;UAAAE,QAAA,GAC1BxH,IAAI,iBACHnB,OAAA;YAAKyF,EAAE,EAAC,wBAAwB;YAACwD,KAAK,EAAE;cAAEC,WAAW,EAAE;YAAO;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE3D,CACN,eACDhJ,OAAA,CAACH,gBAAgB;YAAAgJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACnB,CAAC7H,IAAI,gBACJnB,OAAA;YAAQ4I,OAAO,EAAEL,UAAW;YAACE,SAAS,EAAC,iBAAiB;YAACQ,KAAK,EAAE;cAAEE,UAAU,EAAE;YAAO,CAAE;YAAAR,QAAA,EACpFrH,CAAC,CAAC,YAAY;UAAC;YAAAuH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,gBAEThJ,OAAA;YAAMiJ,KAAK,EAAE;cAAEE,UAAU,EAAE;YAAO,CAAE;YAAAR,QAAA,GAAErH,CAAC,CAAC,SAAS,CAAC,EAAC,IAAE,EAACH,IAAI,CAACO,IAAI,EAAC,GAAC;UAAA;YAAAmH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACxE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGThJ,OAAA;MAAMyI,SAAS,EAAC,cAAc;MAAAE,QAAA,GAC3B1H,KAAK,iBACJjB,OAAA;QAAKyI,SAAS,EAAC,OAAO;QAAAE,QAAA,GAAC,eAClB,EAAC1H,KAAK,eACTjB,OAAA;UAAQ4I,OAAO,EAAEA,CAAA,KAAM1H,QAAQ,CAAC,IAAI,CAAE;UAAC+H,KAAK,EAAE;YAACE,UAAU,EAAE,MAAM;YAAEC,UAAU,EAAE,MAAM;YAAEC,MAAM,EAAE,MAAM;YAAEC,KAAK,EAAE,OAAO;YAAEC,MAAM,EAAE;UAAS,CAAE;UAAAZ,QAAA,EAAC;QAE3I;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAGA3I,WAAW,KAAK,OAAO,iBACtBL,OAAA;QAAKyI,SAAS,EAAC,kBAAkB;QAAAE,QAAA,gBAC/B3I,OAAA;UAAIyI,SAAS,EAAC,OAAO;UAAAE,QAAA,EAAErH,CAAC,CAAC,UAAU;QAAC;UAAAuH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1ChJ,OAAA;UAAGyI,SAAS,EAAC,UAAU;UAAAE,QAAA,EAAC;QAExB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAEH,CAAC7H,IAAI,gBACJnB,OAAA;UAAKiJ,KAAK,EAAE;YAACG,UAAU,EAAE,SAAS;YAAEI,OAAO,EAAE,MAAM;YAAEC,YAAY,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAM,CAAE;UAAAf,QAAA,gBACjG3I,OAAA;YAAGiJ,KAAK,EAAE;cAACK,KAAK,EAAE,SAAS;cAAEI,YAAY,EAAE;YAAM,CAAE;YAAAf,QAAA,EAChDrH,CAAC,CAAC,eAAe;UAAC;YAAAuH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACJhJ,OAAA;YAAQ4I,OAAO,EAAEL,UAAW;YAACE,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EACrDrH,CAAC,CAAC,eAAe;UAAC;YAAAuH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAENhJ,OAAA;UAAM2J,QAAQ,EAAE/C,YAAa;UAAA+B,QAAA,gBAC3B3I,OAAA;YAAKyI,SAAS,EAAC,YAAY;YAAAE,QAAA,eACzB3I,OAAA;cACE4J,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEtJ,KAAM;cACbuJ,QAAQ,EAAGjD,CAAC,IAAKrG,QAAQ,CAACqG,CAAC,CAACkD,MAAM,CAACF,KAAK,CAAE;cAC1CG,WAAW,EAAE1I,CAAC,CAAC,kBAAkB,CAAE;cACnCmH,SAAS,EAAC,YAAY;cACtBwB,QAAQ,EAAElJ;YAAU;cAAA8H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNhJ,OAAA;YACE4J,IAAI,EAAC,QAAQ;YACbK,QAAQ,EAAElJ,SAAS,IAAI,CAACR,KAAK,CAACwG,IAAI,CAAC,CAAE;YACrC0B,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EAE1B5H,SAAS,gBACRf,OAAA,CAAAE,SAAA;cAAAyI,QAAA,gBACE3I,OAAA;gBAAMyI,SAAS,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAChC1H,CAAC,CAAC,YAAY,CAAC;YAAA,eAChB,CAAC,gBAEHtB,OAAA,CAAAE,SAAA;cAAAyI,QAAA,EACGrH,CAAC,CAAC,kBAAkB;YAAC,gBACtB;UACH;YAAAuH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGA3I,WAAW,KAAK,MAAM,IAAII,IAAI,iBAC7BT,OAAA;QAAKyI,SAAS,EAAC,gBAAgB;QAAAE,QAAA,gBAC7B3I,OAAA;UAAKyI,SAAS,EAAC,aAAa;UAAAE,QAAA,gBAC1B3I,OAAA;YAAA2I,QAAA,EAAKlI,IAAI,CAACyC;UAAI;YAAA2F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpBhJ,OAAA;YAAA2I,QAAA,EAAG;UAAoC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC3ChJ,OAAA;YAAQ4I,OAAO,EAAEP,MAAO;YAACI,SAAS,EAAC,mBAAmB;YAACQ,KAAK,EAAE;cAACiB,SAAS,EAAE;YAAM,CAAE;YAAAvB,QAAA,EAAC;UAEnF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAELjI,SAAS,gBACRf,OAAA;UAAKyI,SAAS,EAAC,SAAS;UAAAE,QAAA,gBACtB3I,OAAA;YAAMyI,SAAS,EAAC;UAAS;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjChJ,OAAA;YAAA2I,QAAA,EAAOrH,CAAC,CAAC,SAAS;UAAC;YAAAuH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,gBAENhJ,OAAA;UAAKyI,SAAS,EAAC,eAAe;UAAAE,QAAA,EAC3BlI,IAAI,CAACoE,MAAM,CAACsF,GAAG,CAAC,CAACrI,MAAM,EAAEgD,KAAK,kBAC7B9E,OAAA;YAEEyI,SAAS,EAAE,eAAe9H,cAAc,KAAKmB,MAAM,GAAG,UAAU,GAAG,EAAE,EAAG;YACxE,cAAYgD,KAAM;YAClB,aAAWhD,MAAM,CAACmB,IAAK;YACvB,oBAAkBnB,MAAM,CAACsI,SAAU;YACnCxB,OAAO,EAAEA,CAAA,KAAM5B,kBAAkB,CAAClF,MAAM,CAAE;YAAA6G,QAAA,gBAE1C3I,OAAA;cAAKyI,SAAS,EAAC,cAAc;cAAAE,QAAA,EAAE7G,MAAM,CAACuI;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClDhJ,OAAA;cAAIyI,SAAS,EAAC,aAAa;cAAAE,QAAA,EAAE7G,MAAM,CAACmB;YAAI;cAAA4F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9ChJ,OAAA;cAAGyI,SAAS,EAAC,oBAAoB;cAAAE,QAAA,EAAE7G,MAAM,CAACsI;YAAS;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACvDlH,MAAM,CAACwI,YAAY,iBAClBtK,OAAA;cAAKiJ,KAAK,EAAE;gBAACsB,QAAQ,EAAE,UAAU;gBAAEjB,KAAK,EAAE,SAAS;gBAAEY,SAAS,EAAE;cAAQ,CAAE;cAAAvB,QAAA,GACvErH,CAAC,CAAC,QAAQ,CAAC,EAAC,IAAE,EAACQ,MAAM,CAACwI,YAAY,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACrH,IAAI,CAAC,IAAI,CAAC,EACzDrB,MAAM,CAACwI,YAAY,CAACpG,MAAM,GAAG,CAAC,IAAI,KAAK;YAAA;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CACN,eACDhJ,OAAA;cAAKyI,SAAS,EAAC,cAAc;cAACQ,KAAK,EAAE;gBACnCsB,QAAQ,EAAE,SAAS;gBACnBjB,KAAK,EAAE,SAAS;gBAChBY,SAAS,EAAE,QAAQ;gBACnBO,SAAS,EAAE;cACb,CAAE;cAAA9B,QAAA,EACCrH,CAAC,CAAC,aAAa;YAAC;cAAAuH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC;UAAA,GAvBDlE,KAAK;YAAA+D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwBP,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGA3I,WAAW,KAAK,SAAS,IAAIQ,OAAO,iBACnCb,OAAA;QAAKyI,SAAS,EAAC,gBAAgB;QAAAE,QAAA,eAC7B3I,OAAA;UAAKyI,SAAS,EAAC,MAAM;UAAAE,QAAA,gBACnB3I,OAAA;YAAKyI,SAAS,EAAC,gBAAgB;YAACQ,KAAK,EAAE;cAACS,YAAY,EAAE;YAAM,CAAE;YAAAf,QAAA,gBAC5D3I,OAAA;cAAQ4I,OAAO,EAAEP,MAAO;cAACI,SAAS,EAAC,mBAAmB;cAAAE,QAAA,EACnDrH,CAAC,CAAC,YAAY;YAAC;cAAAuH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGThJ,OAAA;cAAKyI,SAAS,EAAC,kBAAkB;cAACQ,KAAK,EAAE;gBACvCyB,OAAO,EAAE,MAAM;gBACfC,GAAG,EAAE,KAAK;gBACVT,SAAS,EAAE,MAAM;gBACjBU,QAAQ,EAAE;cACZ,CAAE;cAAAjC,QAAA,gBAEA3I,OAAA;gBAAKyI,SAAS,EAAC,yBAAyB;gBAACQ,KAAK,EAAE;kBAC9CyB,OAAO,EAAE,MAAM;kBACfG,UAAU,EAAE,QAAQ;kBACpBF,GAAG,EAAE,KAAK;kBACVnB,OAAO,EAAE,UAAU;kBACnBJ,UAAU,EAAE,SAAS;kBACrBK,YAAY,EAAE,KAAK;kBACnBJ,MAAM,EAAE;gBACV,CAAE;gBAAAV,QAAA,gBACA3I,OAAA;kBACE4I,OAAO,EAAE3B,kBAAmB;kBAC5BwB,SAAS,EAAC,UAAU;kBACpB3E,KAAK,EAAC,mBAAmB;kBACzBmF,KAAK,EAAE;oBACLG,UAAU,EAAE,MAAM;oBAClBC,MAAM,EAAE,MAAM;oBACdkB,QAAQ,EAAE,MAAM;oBAChBhB,MAAM,EAAE,SAAS;oBACjBC,OAAO,EAAE;kBACX,CAAE;kBAAAb,QAAA,EAEDjJ,aAAa,CAACwH,SAAS,CAAC,CAAC,CAACC,SAAS,GAAG,IAAI,GAAG;gBAAI;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,eACThJ,OAAA;kBACE4I,OAAO,EAAEtB,gBAAiB;kBAC1BmB,SAAS,EAAC,UAAU;kBACpB3E,KAAK,EAAC,aAAa;kBACnBmF,KAAK,EAAE;oBACLG,UAAU,EAAE,MAAM;oBAClBC,MAAM,EAAE,MAAM;oBACdkB,QAAQ,EAAE,MAAM;oBAChBhB,MAAM,EAAE,SAAS;oBACjBC,OAAO,EAAE;kBACX,CAAE;kBAAAb,QAAA,EACH;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACThJ,OAAA;kBACE4J,IAAI,EAAC,OAAO;kBACZkB,GAAG,EAAC,KAAK;kBACTC,GAAG,EAAC,GAAG;kBACPC,IAAI,EAAC,KAAK;kBACVC,YAAY,EAAC,GAAG;kBAChBnB,QAAQ,EAAGjD,CAAC,IAAKW,sBAAsB,CAAC0D,UAAU,CAACrE,CAAC,CAACkD,MAAM,CAACF,KAAK,CAAC,CAAE;kBACpEZ,KAAK,EAAE;oBAACkC,KAAK,EAAE;kBAAM,CAAE;kBACvBrH,KAAK,EAAC;gBAAc;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACFhJ,OAAA;kBAAMiJ,KAAK,EAAE;oBAACsB,QAAQ,EAAE,MAAM;oBAAEjB,KAAK,EAAE;kBAAS,CAAE;kBAAAX,QAAA,EAAC;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eAGNhJ,OAAA;gBAAKyI,SAAS,EAAC,yBAAyB;gBAACQ,KAAK,EAAE;kBAC9CyB,OAAO,EAAE,MAAM;kBACfC,GAAG,EAAE;gBACP,CAAE;gBAAAhC,QAAA,gBACA3I,OAAA;kBACE4I,OAAO,EAAEV,qBAAsB;kBAC/BO,SAAS,EAAC,mBAAmB;kBAC7BQ,KAAK,EAAE;oBAACO,OAAO,EAAE,UAAU;oBAAEe,QAAQ,EAAE;kBAAM,CAAE;kBAC/CzG,KAAK,EAAC,mBAAmB;kBAAA6E,QAAA,EAC1B;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACThJ,OAAA;kBACE4I,OAAO,EAAEjB,eAAgB;kBACzBc,SAAS,EAAC,mBAAmB;kBAC7BQ,KAAK,EAAE;oBAACO,OAAO,EAAE,UAAU;oBAAEe,QAAQ,EAAE;kBAAM,CAAE;kBAC/CzG,KAAK,EAAC,eAAe;kBAAA6E,QAAA,EACtB;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACThJ,OAAA;kBACE4I,OAAO,EAAEZ,gBAAiB;kBAC1BS,SAAS,EAAC,mBAAmB;kBAC7BQ,KAAK,EAAE;oBAACO,OAAO,EAAE,UAAU;oBAAEe,QAAQ,EAAE;kBAAM,CAAE;kBAC/CzG,KAAK,EAAC,gBAAgB;kBAAA6E,QAAA,EACvB;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhJ,OAAA;YAAIyI,SAAS,EAAC,OAAO;YAAAE,QAAA,EAAE9H,OAAO,CAACiD;UAAK;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1ChJ,OAAA;YAAKiJ,KAAK,EAAE;cAACK,KAAK,EAAE,SAAS;cAAEI,YAAY,EAAE,MAAM;cAAEa,QAAQ,EAAE;YAAQ,CAAE;YAAA5B,QAAA,gBACvE3I,OAAA;cAAA2I,QAAA,GAAOrH,CAAC,CAAC,QAAQ,CAAC,EAAC,IAAE,EAACT,OAAO,CAACN,KAAK;YAAA;cAAAsI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAC1CnI,OAAO,CAACkB,KAAK,IAAIlB,OAAO,CAACkB,KAAK,CAACmC,MAAM,GAAG,CAAC,iBACxClE,OAAA;cAAMiJ,KAAK,EAAE;gBAACE,UAAU,EAAE;cAAM,CAAE;cAAAR,QAAA,GAC/BrH,CAAC,CAAC,OAAO,CAAC,EAAC,IAAE,EAACT,OAAO,CAACkB,KAAK,CAACoB,IAAI,CAAC,IAAI,CAAC;YAAA;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENhJ,OAAA;YAAKyI,SAAS,EAAC,iBAAiB;YAACQ,KAAK,EAAE;cAACmC,UAAU,EAAE,KAAK;cAAEb,QAAQ,EAAE;YAAQ,CAAE;YAAA5B,QAAA,EAC7E9H,OAAO,CAACmC,OAAO,CAACqI,KAAK,CAAC,IAAI,CAAC,CAAClB,GAAG,CAAC,CAACmB,SAAS,EAAExG,KAAK,KAChDwG,SAAS,CAACvE,IAAI,CAAC,CAAC,iBACd/G,OAAA;cAAeiJ,KAAK,EAAE;gBAACS,YAAY,EAAE;cAAM,CAAE;cAAAf,QAAA,EAC1C2C;YAAS,GADJxG,KAAK;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CAEN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC5I,EAAA,CApnBID,YAAY;EAAA,QAYFL,cAAc;AAAA;AAAAyL,EAAA,GAZxBpL,YAAY;AAsnBlB,eAAeA,YAAY;AAAC,IAAAoL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}