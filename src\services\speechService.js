// Optimized Text-to-Speech Service - Pareto 80/20 Implementation
// Essential speech functionality with speed control and stop capability

class SpeechService {
  constructor() {
    this.isSupported = 'speechSynthesis' in window;
    this.isPlaying = false;
    this.isPaused = false;
    this.currentUtterance = null;
    this.currentText = '';
    this.settings = {
      rate: 1.0,        // Speed: 0.1 to 10
      pitch: 1.0,       // Pitch: 0 to 2
      volume: 1.0,      // Volume: 0 to 1
      voice: null,      // Selected voice
      lang: 'en-US'     // Language
    };
    this.callbacks = {
      onStart: null,
      onEnd: null,
      onPause: null,
      onResume: null,
      onError: null
    };

    // Initialize voices when available
    if (this.isSupported) {
      this.initVoices();
    }
  }

  // Initialize available voices
  initVoices() {
    const loadVoices = () => {
      this.voices = speechSynthesis.getVoices();
      
      // Set default voice (prefer English)
      if (!this.settings.voice && this.voices.length > 0) {
        const englishVoice = this.voices.find(voice => 
          voice.lang.startsWith('en') && voice.default
        ) || this.voices.find(voice => 
          voice.lang.startsWith('en')
        ) || this.voices[0];
        
        this.settings.voice = englishVoice;
      }
    };

    // Load voices immediately if available
    loadVoices();

    // Also listen for voices changed event (some browsers load voices asynchronously)
    speechSynthesis.addEventListener('voiceschanged', loadVoices);
  }

  // Check if speech synthesis is supported
  isAvailable() {
    return this.isSupported;
  }

  // Get current status
  getStatus() {
    return {
      isSupported: this.isSupported,
      isPlaying: this.isPlaying,
      isPaused: this.isPaused,
      canSpeak: this.isSupported && !this.isPlaying
    };
  }

  // Set speech settings
  configure(newSettings) {
    this.settings = { ...this.settings, ...newSettings };
    
    // Validate settings
    this.settings.rate = Math.max(0.1, Math.min(10, this.settings.rate));
    this.settings.pitch = Math.max(0, Math.min(2, this.settings.pitch));
    this.settings.volume = Math.max(0, Math.min(1, this.settings.volume));
    
    return this.settings;
  }

  // Set callbacks
  setCallbacks(callbacks) {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  // Detect language of text
  detectLanguage(text) {
    // Simple language detection based on common patterns
    const romanianPatterns = [
      /\b(și|sau|cu|de|la|în|pe|pentru|este|sunt|avea|face|dacă|când|unde|cum|ce|care|acest|această|aceste|acestea)\b/gi,
      /\b(România|român|română|românesc|românească)\b/gi,
      /[ăâîșț]/g // Romanian diacritics
    ];

    const englishPatterns = [
      /\b(the|and|or|with|of|to|in|on|for|is|are|have|make|if|when|where|how|what|which|this|that|these|those)\b/gi,
      /\b(English|American|British)\b/gi
    ];

    let romanianScore = 0;
    let englishScore = 0;

    // Count Romanian patterns
    romanianPatterns.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) {
        romanianScore += matches.length;
      }
    });

    // Count English patterns
    englishPatterns.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) {
        englishScore += matches.length;
      }
    });

    // Return detected language
    if (romanianScore > englishScore && romanianScore > 2) {
      return 'ro-RO';
    } else {
      return 'en-US';
    }
  }

  // Get best voice for language
  getBestVoiceForLanguage(lang) {
    if (!this.voices || this.voices.length === 0) {
      return null;
    }

    // Find voices that match the language
    const matchingVoices = this.voices.filter(voice =>
      voice.lang.startsWith(lang.split('-')[0])
    );

    if (matchingVoices.length === 0) {
      return this.voices[0]; // Fallback to first available voice
    }

    // Prefer default voice for the language
    const defaultVoice = matchingVoices.find(voice => voice.default);
    if (defaultVoice) {
      return defaultVoice;
    }

    // Return first matching voice
    return matchingVoices[0];
  }

  // Speak text
  speak(text, options = {}) {
    if (!this.isSupported) {
      console.warn('Speech synthesis not supported');
      return false;
    }

    if (!text || text.trim() === '') {
      console.warn('No text provided for speech');
      return false;
    }

    // Stop any current speech
    this.stop();

    // Clean and prepare text
    const cleanText = this.cleanText(text);
    this.currentText = cleanText;

    // Detect language if not specified
    const detectedLang = options.lang || this.detectLanguage(cleanText);
    const bestVoice = this.getBestVoiceForLanguage(detectedLang);

    // Create utterance
    this.currentUtterance = new SpeechSynthesisUtterance(cleanText);

    // Apply settings
    const settings = { ...this.settings, ...options };
    this.currentUtterance.rate = settings.rate;
    this.currentUtterance.pitch = settings.pitch;
    this.currentUtterance.volume = settings.volume;
    this.currentUtterance.lang = detectedLang;

    // Use best voice for detected language
    if (bestVoice) {
      this.currentUtterance.voice = bestVoice;
    } else if (settings.voice) {
      this.currentUtterance.voice = settings.voice;
    }

    console.log(`Speaking in ${detectedLang} with voice:`, bestVoice?.name || 'default');

    // Set event handlers
    this.currentUtterance.onstart = () => {
      this.isPlaying = true;
      this.isPaused = false;
      if (this.callbacks.onStart) {
        this.callbacks.onStart();
      }
    };

    this.currentUtterance.onend = () => {
      this.isPlaying = false;
      this.isPaused = false;
      this.currentUtterance = null;
      if (this.callbacks.onEnd) {
        this.callbacks.onEnd();
      }
    };

    this.currentUtterance.onpause = () => {
      this.isPaused = true;
      if (this.callbacks.onPause) {
        this.callbacks.onPause();
      }
    };

    this.currentUtterance.onresume = () => {
      this.isPaused = false;
      if (this.callbacks.onResume) {
        this.callbacks.onResume();
      }
    };

    this.currentUtterance.onerror = (event) => {
      this.isPlaying = false;
      this.isPaused = false;
      this.currentUtterance = null;
      console.error('Speech synthesis error:', event);
      if (this.callbacks.onError) {
        this.callbacks.onError(event);
      }
    };

    // Start speaking
    speechSynthesis.speak(this.currentUtterance);
    return true;
  }

  // Pause speech
  pause() {
    if (this.isSupported && this.isPlaying && !this.isPaused) {
      speechSynthesis.pause();
      return true;
    }
    return false;
  }

  // Resume speech
  resume() {
    if (this.isSupported && this.isPlaying && this.isPaused) {
      speechSynthesis.resume();
      return true;
    }
    return false;
  }

  // Stop speech
  stop() {
    if (this.isSupported) {
      speechSynthesis.cancel();
      this.isPlaying = false;
      this.isPaused = false;
      this.currentUtterance = null;
      return true;
    }
    return false;
  }

  // Toggle play/pause
  toggle() {
    if (this.isPlaying) {
      if (this.isPaused) {
        return this.resume();
      } else {
        return this.pause();
      }
    }
    return false;
  }

  // Change speech rate (speed)
  setRate(rate) {
    this.settings.rate = Math.max(0.1, Math.min(10, rate));
    
    // If currently speaking, restart with new rate
    if (this.isPlaying && this.currentText) {
      const wasPlaying = !this.isPaused;
      this.stop();
      if (wasPlaying) {
        this.speak(this.currentText);
      }
    }
    
    return this.settings.rate;
  }

  // Get available voices
  getVoices() {
    return this.voices || [];
  }

  // Set voice
  setVoice(voice) {
    if (voice && this.voices && this.voices.includes(voice)) {
      this.settings.voice = voice;
      return true;
    }
    return false;
  }

  // Clean text for better speech synthesis
  cleanText(text) {
    return text
      // Remove HTML tags
      .replace(/<[^>]*>/g, ' ')
      // Replace multiple spaces with single space
      .replace(/\s+/g, ' ')
      // Remove special characters that might cause issues
      .replace(/[^\w\s.,!?;:()-]/g, ' ')
      // Trim whitespace
      .trim();
  }

  // Get speech rate presets
  getRatePresets() {
    return {
      'Very Slow': 0.5,
      'Slow': 0.75,
      'Normal': 1.0,
      'Fast': 1.25,
      'Very Fast': 1.5,
      'Ultra Fast': 2.0
    };
  }

  // Create speech control UI
  createControls(container) {
    if (!container) return null;

    const controls = document.createElement('div');
    controls.className = 'speech-controls';
    controls.innerHTML = `
      <div class="speech-controls-inner">
        <button class="speech-btn speech-play" title="Play/Pause">
          <span class="play-icon">▶️</span>
          <span class="pause-icon" style="display: none;">⏸️</span>
        </button>
        <button class="speech-btn speech-stop" title="Stop">⏹️</button>
        <div class="speech-rate-control">
          <label>Speed:</label>
          <input type="range" class="speech-rate-slider" min="0.5" max="2" step="0.1" value="1">
          <span class="speech-rate-value">1.0x</span>
        </div>
      </div>
    `;

    // Add event listeners
    const playBtn = controls.querySelector('.speech-play');
    const stopBtn = controls.querySelector('.speech-stop');
    const rateSlider = controls.querySelector('.speech-rate-slider');
    const rateValue = controls.querySelector('.speech-rate-value');

    playBtn.addEventListener('click', () => {
      if (this.isPlaying) {
        this.toggle();
      }
    });

    stopBtn.addEventListener('click', () => {
      this.stop();
    });

    rateSlider.addEventListener('input', (e) => {
      const rate = parseFloat(e.target.value);
      this.setRate(rate);
      rateValue.textContent = `${rate}x`;
    });

    // Update UI based on speech status
    this.setCallbacks({
      onStart: () => {
        playBtn.querySelector('.play-icon').style.display = 'none';
        playBtn.querySelector('.pause-icon').style.display = 'inline';
        controls.classList.add('playing');
      },
      onEnd: () => {
        playBtn.querySelector('.play-icon').style.display = 'inline';
        playBtn.querySelector('.pause-icon').style.display = 'none';
        controls.classList.remove('playing');
      },
      onPause: () => {
        playBtn.querySelector('.play-icon').style.display = 'inline';
        playBtn.querySelector('.pause-icon').style.display = 'none';
      },
      onResume: () => {
        playBtn.querySelector('.play-icon').style.display = 'none';
        playBtn.querySelector('.pause-icon').style.display = 'inline';
      }
    });

    container.appendChild(controls);
    return controls;
  }
}

// Create singleton instance
const speechService = new SpeechService();

export default speechService;
