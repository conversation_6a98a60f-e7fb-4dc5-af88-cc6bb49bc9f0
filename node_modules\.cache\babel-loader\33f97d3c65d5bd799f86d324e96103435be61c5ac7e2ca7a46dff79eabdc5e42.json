{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Appv1\\\\src\\\\components\\\\TabManager.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport tabService from '../services/tabService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TabManager = ({\n  onTabChange,\n  onNewTab,\n  onTabArticleAccess\n}) => {\n  _s();\n  const [tabs, setTabs] = useState([]);\n  const [activeTabId, setActiveTabId] = useState(null);\n  useEffect(() => {\n    // Listen for tab changes\n    const handleTabUpdate = (updatedTabs, activeId) => {\n      setTabs(updatedTabs);\n      setActiveTabId(activeId);\n\n      // Notify parent component of active tab change\n      if (onTabChange && activeId) {\n        const activeTab = updatedTabs.find(tab => tab.id === activeId);\n        onTabChange(activeTab);\n      }\n    };\n    tabService.addListener(handleTabUpdate);\n\n    // Initial load\n    setTabs(tabService.getAllTabs());\n    setActiveTabId(tabService.activeTabId);\n    return () => {\n      tabService.removeListener(handleTabUpdate);\n    };\n  }, [onTabChange]);\n  const handleTabClick = tabId => {\n    const tab = tabs.find(t => t.id === tabId);\n    tabService.setActiveTab(tabId);\n\n    // If tab has completed article, show it\n    if (tab && tab.status === 'completed' && tab.article && onTabArticleAccess) {\n      onTabArticleAccess(tab);\n    }\n  };\n  const handleCloseTab = (tabId, event) => {\n    event.stopPropagation();\n    tabService.closeTab(tabId);\n  };\n  const handleNewTab = () => {\n    if (tabService.canCreateNewTab() && onNewTab) {\n      onNewTab();\n    }\n  };\n  const truncateText = (text, maxLength = 15) => {\n    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"tab-manager\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tab-bar\",\n      children: [tabs.map(tab => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `tab-item ${tab.id === activeTabId ? 'active' : ''}`,\n        onClick: () => handleTabClick(tab.id),\n        style: {\n          backgroundColor: tab.id === activeTabId ? '#ffffff' : '#f8fafc',\n          borderColor: tabService.getTabStatusColor(tab.status),\n          borderTopWidth: '3px',\n          borderTopStyle: 'solid'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"tab-icon\",\n            children: tabService.getTabStatusIcon(tab.status)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"tab-title\",\n            children: truncateText(tab.topic)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"tab-close\",\n            onClick: e => handleCloseTab(tab.id, e),\n            title: \"Close tab\",\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 13\n        }, this), tab.status === 'generating' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tab-progress\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tab-progress-bar\",\n            style: {\n              width: `${tab.progress}%`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 15\n        }, this)]\n      }, tab.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 11\n      }, this)), tabService.canCreateNewTab() && /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"tab-new\",\n        onClick: handleNewTab,\n        title: `Add new tab (${tabs.length}/${tabService.maxTabs})`,\n        children: \"+ New\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this), tabs.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tab-summary\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"tab-count\",\n        children: [tabs.length, \"/\", tabService.maxTabs, \" tabs\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tab-status-indicators\",\n        children: [tabs.filter(t => t.status === 'generating').length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"status-indicator generating\",\n          children: [\"\\uD83D\\uDD04 \", tabs.filter(t => t.status === 'generating').length, \" generating\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 15\n        }, this), tabs.filter(t => t.status === 'completed').length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"status-indicator completed\",\n          children: [\"\\u2705 \", tabs.filter(t => t.status === 'completed').length, \" ready\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this);\n};\n_s(TabManager, \"z2j/jnLe5kwNI5ksyqRGy08UaZY=\");\n_c = TabManager;\nexport default TabManager;\nvar _c;\n$RefreshReg$(_c, \"TabManager\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "tabService", "jsxDEV", "_jsxDEV", "TabManager", "onTabChange", "onNewTab", "onTabArticleAccess", "_s", "tabs", "setTabs", "activeTabId", "setActiveTabId", "handleTabUpdate", "updatedTabs", "activeId", "activeTab", "find", "tab", "id", "addListener", "getAllTabs", "removeListener", "handleTabClick", "tabId", "t", "setActiveTab", "status", "article", "handleCloseTab", "event", "stopPropagation", "closeTab", "handleNewTab", "canCreateNewTab", "truncateText", "text", "max<PERSON><PERSON><PERSON>", "length", "substring", "className", "children", "map", "onClick", "style", "backgroundColor", "borderColor", "getTabStatusColor", "borderTopWidth", "borderTopStyle", "getTabStatusIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "topic", "e", "title", "width", "progress", "maxTabs", "filter", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Appv1/src/components/TabManager.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport tabService from '../services/tabService';\n\nconst TabManager = ({ onTabChange, onNewTab, onTabArticleAccess }) => {\n  const [tabs, setTabs] = useState([]);\n  const [activeTabId, setActiveTabId] = useState(null);\n\n  useEffect(() => {\n    // Listen for tab changes\n    const handleTabUpdate = (updatedTabs, activeId) => {\n      setTabs(updatedTabs);\n      setActiveTabId(activeId);\n\n      // Notify parent component of active tab change\n      if (onTabChange && activeId) {\n        const activeTab = updatedTabs.find(tab => tab.id === activeId);\n        onTabChange(activeTab);\n      }\n    };\n\n    tabService.addListener(handleTabUpdate);\n\n    // Initial load\n    setTabs(tabService.getAllTabs());\n    setActiveTabId(tabService.activeTabId);\n\n    return () => {\n      tabService.removeListener(handleTabUpdate);\n    };\n  }, [onTabChange]);\n\n  const handleTabClick = (tabId) => {\n    const tab = tabs.find(t => t.id === tabId);\n    tabService.setActiveTab(tabId);\n\n    // If tab has completed article, show it\n    if (tab && tab.status === 'completed' && tab.article && onTabArticleAccess) {\n      onTabArticleAccess(tab);\n    }\n  };\n\n  const handleCloseTab = (tabId, event) => {\n    event.stopPropagation();\n    tabService.closeTab(tabId);\n  };\n\n  const handleNewTab = () => {\n    if (tabService.canCreateNewTab() && onNewTab) {\n      onNewTab();\n    }\n  };\n\n  const truncateText = (text, maxLength = 15) => {\n    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;\n  };\n\n  return (\n    <div className=\"tab-manager\">\n      <div className=\"tab-bar\">\n        {tabs.map(tab => (\n          <div\n            key={tab.id}\n            className={`tab-item ${tab.id === activeTabId ? 'active' : ''}`}\n            onClick={() => handleTabClick(tab.id)}\n            style={{\n              backgroundColor: tab.id === activeTabId ? '#ffffff' : '#f8fafc',\n              borderColor: tabService.getTabStatusColor(tab.status),\n              borderTopWidth: '3px',\n              borderTopStyle: 'solid'\n            }}\n          >\n            <div className=\"tab-content\">\n              <span className=\"tab-icon\">\n                {tabService.getTabStatusIcon(tab.status)}\n              </span>\n              <span className=\"tab-title\">\n                {truncateText(tab.topic)}\n              </span>\n              <button\n                className=\"tab-close\"\n                onClick={(e) => handleCloseTab(tab.id, e)}\n                title=\"Close tab\"\n              >\n                ×\n              </button>\n            </div>\n            {tab.status === 'generating' && (\n              <div className=\"tab-progress\">\n                <div \n                  className=\"tab-progress-bar\"\n                  style={{ width: `${tab.progress}%` }}\n                />\n              </div>\n            )}\n          </div>\n        ))}\n        \n        {tabService.canCreateNewTab() && (\n          <button\n            className=\"tab-new\"\n            onClick={handleNewTab}\n            title={`Add new tab (${tabs.length}/${tabService.maxTabs})`}\n          >\n            + New\n          </button>\n        )}\n      </div>\n\n      {tabs.length > 0 && (\n        <div className=\"tab-summary\">\n          <span className=\"tab-count\">\n            {tabs.length}/{tabService.maxTabs} tabs\n          </span>\n          <div className=\"tab-status-indicators\">\n            {tabs.filter(t => t.status === 'generating').length > 0 && (\n              <span className=\"status-indicator generating\">\n                🔄 {tabs.filter(t => t.status === 'generating').length} generating\n              </span>\n            )}\n            {tabs.filter(t => t.status === 'completed').length > 0 && (\n              <span className=\"status-indicator completed\">\n                ✅ {tabs.filter(t => t.status === 'completed').length} ready\n              </span>\n            )}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default TabManager;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,UAAU,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,UAAU,GAAGA,CAAC;EAAEC,WAAW;EAAEC,QAAQ;EAAEC;AAAmB,CAAC,KAAK;EAAAC,EAAA;EACpE,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACY,WAAW,EAAEC,cAAc,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAEpDC,SAAS,CAAC,MAAM;IACd;IACA,MAAMa,eAAe,GAAGA,CAACC,WAAW,EAAEC,QAAQ,KAAK;MACjDL,OAAO,CAACI,WAAW,CAAC;MACpBF,cAAc,CAACG,QAAQ,CAAC;;MAExB;MACA,IAAIV,WAAW,IAAIU,QAAQ,EAAE;QAC3B,MAAMC,SAAS,GAAGF,WAAW,CAACG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,EAAE,KAAKJ,QAAQ,CAAC;QAC9DV,WAAW,CAACW,SAAS,CAAC;MACxB;IACF,CAAC;IAEDf,UAAU,CAACmB,WAAW,CAACP,eAAe,CAAC;;IAEvC;IACAH,OAAO,CAACT,UAAU,CAACoB,UAAU,CAAC,CAAC,CAAC;IAChCT,cAAc,CAACX,UAAU,CAACU,WAAW,CAAC;IAEtC,OAAO,MAAM;MACXV,UAAU,CAACqB,cAAc,CAACT,eAAe,CAAC;IAC5C,CAAC;EACH,CAAC,EAAE,CAACR,WAAW,CAAC,CAAC;EAEjB,MAAMkB,cAAc,GAAIC,KAAK,IAAK;IAChC,MAAMN,GAAG,GAAGT,IAAI,CAACQ,IAAI,CAACQ,CAAC,IAAIA,CAAC,CAACN,EAAE,KAAKK,KAAK,CAAC;IAC1CvB,UAAU,CAACyB,YAAY,CAACF,KAAK,CAAC;;IAE9B;IACA,IAAIN,GAAG,IAAIA,GAAG,CAACS,MAAM,KAAK,WAAW,IAAIT,GAAG,CAACU,OAAO,IAAIrB,kBAAkB,EAAE;MAC1EA,kBAAkB,CAACW,GAAG,CAAC;IACzB;EACF,CAAC;EAED,MAAMW,cAAc,GAAGA,CAACL,KAAK,EAAEM,KAAK,KAAK;IACvCA,KAAK,CAACC,eAAe,CAAC,CAAC;IACvB9B,UAAU,CAAC+B,QAAQ,CAACR,KAAK,CAAC;EAC5B,CAAC;EAED,MAAMS,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIhC,UAAU,CAACiC,eAAe,CAAC,CAAC,IAAI5B,QAAQ,EAAE;MAC5CA,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC;EAED,MAAM6B,YAAY,GAAGA,CAACC,IAAI,EAAEC,SAAS,GAAG,EAAE,KAAK;IAC7C,OAAOD,IAAI,CAACE,MAAM,GAAGD,SAAS,GAAGD,IAAI,CAACG,SAAS,CAAC,CAAC,EAAEF,SAAS,CAAC,GAAG,KAAK,GAAGD,IAAI;EAC9E,CAAC;EAED,oBACEjC,OAAA;IAAKqC,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1BtC,OAAA;MAAKqC,SAAS,EAAC,SAAS;MAAAC,QAAA,GACrBhC,IAAI,CAACiC,GAAG,CAACxB,GAAG,iBACXf,OAAA;QAEEqC,SAAS,EAAE,YAAYtB,GAAG,CAACC,EAAE,KAAKR,WAAW,GAAG,QAAQ,GAAG,EAAE,EAAG;QAChEgC,OAAO,EAAEA,CAAA,KAAMpB,cAAc,CAACL,GAAG,CAACC,EAAE,CAAE;QACtCyB,KAAK,EAAE;UACLC,eAAe,EAAE3B,GAAG,CAACC,EAAE,KAAKR,WAAW,GAAG,SAAS,GAAG,SAAS;UAC/DmC,WAAW,EAAE7C,UAAU,CAAC8C,iBAAiB,CAAC7B,GAAG,CAACS,MAAM,CAAC;UACrDqB,cAAc,EAAE,KAAK;UACrBC,cAAc,EAAE;QAClB,CAAE;QAAAR,QAAA,gBAEFtC,OAAA;UAAKqC,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BtC,OAAA;YAAMqC,SAAS,EAAC,UAAU;YAAAC,QAAA,EACvBxC,UAAU,CAACiD,gBAAgB,CAAChC,GAAG,CAACS,MAAM;UAAC;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACPnD,OAAA;YAAMqC,SAAS,EAAC,WAAW;YAAAC,QAAA,EACxBN,YAAY,CAACjB,GAAG,CAACqC,KAAK;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eACPnD,OAAA;YACEqC,SAAS,EAAC,WAAW;YACrBG,OAAO,EAAGa,CAAC,IAAK3B,cAAc,CAACX,GAAG,CAACC,EAAE,EAAEqC,CAAC,CAAE;YAC1CC,KAAK,EAAC,WAAW;YAAAhB,QAAA,EAClB;UAED;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EACLpC,GAAG,CAACS,MAAM,KAAK,YAAY,iBAC1BxB,OAAA;UAAKqC,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BtC,OAAA;YACEqC,SAAS,EAAC,kBAAkB;YAC5BI,KAAK,EAAE;cAAEc,KAAK,EAAE,GAAGxC,GAAG,CAACyC,QAAQ;YAAI;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA,GAhCIpC,GAAG,CAACC,EAAE;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAiCR,CACN,CAAC,EAEDrD,UAAU,CAACiC,eAAe,CAAC,CAAC,iBAC3B/B,OAAA;QACEqC,SAAS,EAAC,SAAS;QACnBG,OAAO,EAAEV,YAAa;QACtBwB,KAAK,EAAE,gBAAgBhD,IAAI,CAAC6B,MAAM,IAAIrC,UAAU,CAAC2D,OAAO,GAAI;QAAAnB,QAAA,EAC7D;MAED;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAEL7C,IAAI,CAAC6B,MAAM,GAAG,CAAC,iBACdnC,OAAA;MAAKqC,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BtC,OAAA;QAAMqC,SAAS,EAAC,WAAW;QAAAC,QAAA,GACxBhC,IAAI,CAAC6B,MAAM,EAAC,GAAC,EAACrC,UAAU,CAAC2D,OAAO,EAAC,OACpC;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACPnD,OAAA;QAAKqC,SAAS,EAAC,uBAAuB;QAAAC,QAAA,GACnChC,IAAI,CAACoD,MAAM,CAACpC,CAAC,IAAIA,CAAC,CAACE,MAAM,KAAK,YAAY,CAAC,CAACW,MAAM,GAAG,CAAC,iBACrDnC,OAAA;UAAMqC,SAAS,EAAC,6BAA6B;UAAAC,QAAA,GAAC,eACzC,EAAChC,IAAI,CAACoD,MAAM,CAACpC,CAAC,IAAIA,CAAC,CAACE,MAAM,KAAK,YAAY,CAAC,CAACW,MAAM,EAAC,aACzD;QAAA;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP,EACA7C,IAAI,CAACoD,MAAM,CAACpC,CAAC,IAAIA,CAAC,CAACE,MAAM,KAAK,WAAW,CAAC,CAACW,MAAM,GAAG,CAAC,iBACpDnC,OAAA;UAAMqC,SAAS,EAAC,4BAA4B;UAAAC,QAAA,GAAC,SACzC,EAAChC,IAAI,CAACoD,MAAM,CAACpC,CAAC,IAAIA,CAAC,CAACE,MAAM,KAAK,WAAW,CAAC,CAACW,MAAM,EAAC,QACvD;QAAA;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC9C,EAAA,CA9HIJ,UAAU;AAAA0D,EAAA,GAAV1D,UAAU;AAgIhB,eAAeA,UAAU;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}