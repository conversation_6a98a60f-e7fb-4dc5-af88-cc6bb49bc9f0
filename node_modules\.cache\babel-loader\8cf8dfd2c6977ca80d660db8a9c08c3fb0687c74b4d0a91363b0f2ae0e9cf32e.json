{"ast": null, "code": "// OpenRouter API Service for Knowledge Tree Generation\n// STRICT IMPLEMENTATION - DeepSeek R1 + Web Search MANDATORY\nimport webSearchService from './webSearchService';\n\n// CRITICAL: API Key și configurație EXACTĂ conform cerințelor\nconst OPENROUTER_API_KEY = '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';\nconst OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';\nconst MODEL = 'deepseek/deepseek-r1-0528:free'; // OBLIGATORIU - DeepSeek R1 0528 free\n\n// Site configuration for OpenRouter rankings\nconst SITE_CONFIG = {\n  'HTTP-Referer': process.env.REACT_APP_SITE_URL || 'http://localhost:3000',\n  'X-Title': process.env.REACT_APP_SITE_NAME || 'Knowledge Tree Explorer'\n};\n\n// CRITICAL: STRICT OpenRouter Client Implementation\n// EXACT structure as specified - NO DEVIATIONS ALLOWED\nclass OpenRouterClient {\n  constructor() {\n    this.baseURL = OPENROUTER_BASE_URL;\n    this.apiKey = OPENROUTER_API_KEY;\n\n    // CRITICAL: Verify exact API key and model\n    console.log('🔒 STRICT OpenRouter Client initialized:');\n    console.log('- Base URL:', this.baseURL);\n    console.log('- Model:', MODEL, '(MUST be deepseek/deepseek-r1-0528:free)');\n    console.log('- API Key:', this.apiKey ? `${this.apiKey.substring(0, 15)}...` : 'CRITICAL ERROR: NOT SET');\n    console.log('- Site Config:', SITE_CONFIG);\n    if (!this.apiKey || this.apiKey === 'your-api-key-here') {\n      throw new Error('CRITICAL: API Key not properly configured');\n    }\n  }\n\n  // MANDATORY: Exact structure as specified in requirements\n  async makeRequest(messages, temperature = 0.7, maxTokens = 2000) {\n    console.log('🔒 STRICT API Call - DeepSeek R1 0528 free model ONLY');\n    try {\n      const response = await fetch(`${this.baseURL}/chat/completions`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${this.apiKey}`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': SITE_CONFIG['HTTP-Referer'],\n          'X-Title': SITE_CONFIG['X-Title']\n        },\n        body: JSON.stringify({\n          model: MODEL,\n          // CRITICAL: MUST be deepseek/deepseek-r1-0528:free\n          messages,\n          temperature,\n          max_tokens: maxTokens\n        })\n      });\n      if (!response.ok) {\n        var _errorData$error;\n        const errorData = await response.json().catch(() => ({}));\n        console.error('❌ CRITICAL API ERROR:', response.status, errorData);\n        throw new Error(`CRITICAL OpenRouter API Error: ${response.status} - ${((_errorData$error = errorData.error) === null || _errorData$error === void 0 ? void 0 : _errorData$error.message) || 'Unknown error'}`);\n      }\n      const data = await response.json();\n      console.log('✅ VERIFIED: API Response received from DeepSeek R1');\n      if (!data.choices || !data.choices[0] || !data.choices[0].message) {\n        throw new Error('CRITICAL: Invalid API response structure');\n      }\n      return data.choices[0].message.content;\n    } catch (error) {\n      console.error('❌ CRITICAL: OpenRouter API request failed:', error);\n      throw error;\n    }\n  }\n\n  // MANDATORY: Web search validation for ALL content - NO EXCEPTIONS\n  async validateWithWebSearch(topic, content) {\n    console.log('🔍 MANDATORY: Validating content with web search for:', topic);\n    try {\n      const webSources = await webSearchService.searchSources(topic, 5);\n      console.log('✅ VERIFIED: Web validation completed with', webSources.length, 'sources');\n      if (webSources.length === 0) {\n        console.warn('⚠️ WARNING: No web sources found for validation');\n      }\n      return {\n        validated: true,\n        sources: webSources,\n        content: content,\n        validationTimestamp: new Date().toISOString()\n      };\n    } catch (error) {\n      console.error('❌ CRITICAL: Web validation failed:', error);\n      throw new Error('CRITICAL: Content validation failed - web search required for all content');\n    }\n  }\n}\nconst client = new OpenRouterClient();\n\n// CRITICAL: Generate Knowledge Tree with MANDATORY AI + Web Search validation\nexport async function generateKnowledgeTree(topic, language = 'en') {\n  console.log('🔒 STRICT: Starting knowledge tree generation with mandatory validation');\n  console.log('- Topic:', topic);\n  console.log('- Language:', language);\n\n  // Get current language from localStorage if not provided\n  const currentLang = language || localStorage.getItem('language') || 'en';\n\n  // MANDATORY: Web search validation BEFORE AI generation\n  console.log('🔍 STEP 1: MANDATORY web search validation for topic:', topic);\n  let webValidation;\n  try {\n    webValidation = await client.validateWithWebSearch(topic, 'Initial topic validation');\n    console.log('✅ VERIFIED: Topic validated with web sources');\n  } catch (error) {\n    console.error('❌ CRITICAL: Topic validation failed:', error);\n    throw new Error(`CRITICAL: Cannot proceed without web validation for topic: ${topic}`);\n  }\n  const prompts = {\n    ro: `Analizează cu atenție subiectul \"${topic}\" și creează un arbore de cunoștințe FOARTE SPECIFIC și RELEVANT pentru acest domeniu exact.\n\nIMPORTANT: Generează ramuri care sunt DIRECT LEGATE de \"${topic}\", nu concepte generale!\n\nReturnează DOAR un obiect JSON valid cu această structură exactă:\n{\n  \"tema\": \"${topic}\",\n  \"ramuri\": [\n    {\n      \"nume\": \"Nume Ramură Specifică\",\n      \"descriere\": \"Descriere scurtă și precisă\",\n      \"emoji\": \"📚\",\n      \"subcategorii\": [\"subcategorie1\", \"subcategorie2\", \"subcategorie3\"]\n    }\n  ]\n}\n\nCerințe STRICTE:\n- Generează 6-8 ramuri principale SPECIFICE pentru \"${topic}\"\n- Fiecare ramură TREBUIE să fie direct legată de subiectul principal\n- Emoji-uri relevante pentru fiecare ramură\n- 3-4 subcategorii specifice pentru fiecare ramură\n- Descrieri de maxim 1 propoziție\n- Focalizează-te pe aspectele PRACTICE și APLICABILE\n- Evită conceptele generale sau irelevante\n\nSubiect: ${topic}`,\n    en: `Analyze the subject \"${topic}\" carefully and create a VERY SPECIFIC and RELEVANT knowledge tree for this exact domain.\n\nIMPORTANT: Generate branches that are DIRECTLY RELATED to \"${topic}\", not general concepts!\n\nReturn ONLY a valid JSON object with this exact structure:\n{\n  \"tema\": \"${topic}\",\n  \"ramuri\": [\n    {\n      \"nume\": \"Specific Branch Name\",\n      \"descriere\": \"Short and precise description\",\n      \"emoji\": \"📚\",\n      \"subcategorii\": [\"subcategory1\", \"subcategory2\", \"subcategory3\"]\n    }\n  ]\n}\n\nSTRICT Requirements:\n- Generate 6-8 main branches SPECIFIC to \"${topic}\"\n- Each branch MUST be directly related to the main subject\n- Relevant emojis for each branch\n- 3-4 specific subcategories for each branch\n- Descriptions of maximum 1 sentence\n- Focus on PRACTICAL and APPLICABLE aspects\n- Avoid general or irrelevant concepts\n\nSubject: ${topic}`\n  };\n  const prompt = prompts[currentLang] || prompts.en;\n\n  // STEP 2: AI Generation with DeepSeek R1 ONLY\n  console.log('🤖 STEP 2: AI generation with DeepSeek R1 0528 free model');\n  let aiResponse;\n  try {\n    aiResponse = await client.makeRequest([{\n      role: 'system',\n      content: currentLang === 'ro' ? 'Expert în organizarea cunoștințelor. Generează arbori de cunoștințe specifici în format JSON valid. Răspunde DOAR cu JSON, fără text explicativ. FOLOSEȘTE DOAR INFORMAȚII VERIFICATE.' : 'Expert in knowledge organization. Generate specific knowledge trees in valid JSON format. Respond ONLY with JSON, no explanatory text. USE ONLY VERIFIED INFORMATION.'\n    }, {\n      role: 'user',\n      content: prompt + `\\n\\nIMPORTANT: Bazează-te pe aceste surse web verificate: ${webValidation.sources.map(s => s.title).join(', ')}`\n    }], 0.3); // Temperatură mai mică pentru răspunsuri mai consistente și rapide\n\n    console.log('✅ VERIFIED: AI response received from DeepSeek R1');\n\n    // STEP 3: Parse and validate the JSON response\n    console.log('🔍 STEP 3: Parsing and validating AI response');\n    const cleanResponse = aiResponse.trim();\n    const jsonMatch = cleanResponse.match(/\\{[\\s\\S]*\\}/);\n    if (!jsonMatch) {\n      throw new Error('CRITICAL: No valid JSON found in AI response');\n    }\n    const tree = JSON.parse(jsonMatch[0]);\n\n    // STEP 4: Strict structure validation\n    console.log('🔍 STEP 4: Strict structure validation');\n    if (!tree.tema || !Array.isArray(tree.ramuri)) {\n      throw new Error('CRITICAL: Invalid tree structure from AI');\n    }\n    if (tree.ramuri.length === 0) {\n      throw new Error('CRITICAL: No branches generated by AI');\n    }\n\n    // STEP 5: Final validation with web sources\n    console.log('🔍 STEP 5: Final validation with web sources');\n    const finalValidation = await client.validateWithWebSearch(`${topic} knowledge tree branches`, JSON.stringify(tree));\n\n    // Add validation metadata\n    tree._validation = {\n      webSources: finalValidation.sources,\n      validatedAt: finalValidation.validationTimestamp,\n      aiModel: MODEL,\n      strictMode: true\n    };\n    console.log('✅ SUCCESS: Knowledge tree generated and validated with strict mode');\n    return tree;\n  } catch (error) {\n    console.error('Error generating knowledge tree:', error);\n\n    // Fallback tree structure based on language\n    const fallbacks = {\n      ro: {\n        tema: topic,\n        ramuri: [{\n          nume: \"Fundamentele\",\n          descriere: `Concepte de bază și principii ale ${topic}`,\n          emoji: \"📚\",\n          subcategorii: [\"Concepte Esențiale\", \"Principii Cheie\", \"Teoria de Bază\"]\n        }, {\n          nume: \"Aplicații\",\n          descriere: `Aplicații practice și cazuri de utilizare ale ${topic}`,\n          emoji: \"🔧\",\n          subcategorii: [\"Utilizări Reale\", \"Aplicații Industriale\", \"Studii de Caz\"]\n        }, {\n          nume: \"Subiecte Avansate\",\n          descriere: `Aspecte complexe și specializate ale ${topic}`,\n          emoji: \"🎓\",\n          subcategorii: [\"Nivel Expert\", \"Zone de Cercetare\", \"Tehnologii Noi\"]\n        }]\n      },\n      en: {\n        tema: topic,\n        ramuri: [{\n          nume: \"Fundamentals\",\n          descriere: `Basic concepts and principles of ${topic}`,\n          emoji: \"📚\",\n          subcategorii: [\"Core Concepts\", \"Key Principles\", \"Basic Theory\"]\n        }, {\n          nume: \"Applications\",\n          descriere: `Practical applications and use cases of ${topic}`,\n          emoji: \"🔧\",\n          subcategorii: [\"Real-world Uses\", \"Industry Applications\", \"Case Studies\"]\n        }, {\n          nume: \"Advanced Topics\",\n          descriere: `Complex and specialized aspects of ${topic}`,\n          emoji: \"🎓\",\n          subcategorii: [\"Expert Level\", \"Research Areas\", \"Cutting Edge\"]\n        }]\n      }\n    };\n    return fallbacks[currentLang] || fallbacks.en;\n  }\n}\n\n// CRITICAL: Generate Article with MANDATORY AI + Web Search validation\nexport async function generateArticle(topic, branch, flags = ['-a']) {\n  console.log('🔒 STRICT: Starting article generation with mandatory validation');\n  console.log('- Topic:', topic);\n  console.log('- Branch:', branch.nume);\n  console.log('- Flags:', flags);\n\n  // Get current language from localStorage\n  const currentLang = localStorage.getItem('language') || 'ro';\n\n  // MANDATORY: Web search validation BEFORE AI generation\n  console.log('🔍 STEP 1: MANDATORY web search validation for article topic');\n  const articleTopic = `${topic} ${branch.nume}`;\n  let webValidation;\n  try {\n    webValidation = await client.validateWithWebSearch(articleTopic, 'Article topic validation');\n    console.log('✅ VERIFIED: Article topic validated with', webValidation.sources.length, 'web sources');\n    if (webValidation.sources.length === 0) {\n      throw new Error('No web sources found for validation');\n    }\n  } catch (error) {\n    console.error('❌ CRITICAL: Article topic validation failed:', error);\n    throw new Error(`CRITICAL: Cannot proceed without web validation for article: ${articleTopic}`);\n  }\n  const flagInstructions = {\n    ro: {\n      // Basic flags cu prompt-uri FOARTE SPECIFICE în ROMÂNĂ\n      '-a': 'Scrie un articol informativ standard (600-800 cuvinte) cu structură clară: introducere, dezvoltare cu 3-4 secțiuni principale, și concluzie. SCRIE ÎNTREGUL ARTICOL ÎN ROMÂNĂ.',\n      '-t': 'Formatează ÎNTREGUL conținut ca tabele și date structurate. Creează minimum 3 tabele cu informații organizate în coloane și rânduri. Fiecare tabel să aibă titlu și explicații. SCRIE TOT ÎN ROMÂNĂ.',\n      '-ex': 'Include EXACT 3 exemple practice detaliate cu explicații pas cu pas. Fiecare exemplu să aibă: context, implementare, rezultat așteptat. Numerotează exemplele 1, 2, 3. SCRIE TOT ÎN ROMÂNĂ.',\n      '-p': 'Include OBLIGATORIU exemple de cod și demonstrații tehnice. Minimum 2 blocuri de cod cu explicații linie cu linie. Adaugă comentarii în cod. SCRIE TOT ÎN ROMÂNĂ.',\n      '-q': 'Creează EXACT 5 întrebări tip grilă la sfârșitul articolului. Fiecare întrebare să aibă 4 variante (A, B, C, D). Include baremul cu răspunsurile corecte la final. SCRIE TOT ÎN ROMÂNĂ.',\n      '-rap': 'Scrie un raport exhaustiv (800-1200 cuvinte) cu acoperire comprehensivă: rezumat executiv, analiză detaliată, concluzii și recomandări. SCRIE TOT ÎN ROMÂNĂ.',\n      '-def': 'Focalizează-te pe definiții de nivel expert și terminologie tehnică. Include minimum 10 termeni specializați cu definiții precise. SCRIE TOT ÎN ROMÂNĂ.',\n      // Learning & Visualization flags\n      '-path': 'Creează o cale de învățare personalizată cu 5-7 pași concreți, milestone-uri măsurabile și estimări de timp pentru fiecare etapă. SCRIE TOT ÎN ROMÂNĂ.',\n      '-vis': 'Generează descrieri detaliate pentru minimum 3 infografice/diagrame. Pentru fiecare diagramă: titlu, elemente vizuale, culori sugerate, și explicația fiecărui element. SCRIE TOT ÎN ROMÂNĂ.',\n      '-mind': 'Prezintă informația ca o hartă mentală interactivă cu concepte conectate. Descrie structura: nod central, 5-8 ramuri principale, sub-ramuri și conexiuni între concepte. SCRIE TOT ÎN ROMÂNĂ.',\n      '-flow': 'Creează diagrame de flux și procese cu puncte de decizie și rezultate. Include minimum 2 flowchart-uri cu forme geometrice specifice și săgeți directionale. SCRIE TOT ÎN ROMÂNĂ.',\n      // Industry-specific flags\n      '-case': 'Include 2-3 studii de caz reale cu rezultate măsurabile și analiză detaliată. Pentru fiecare caz: context, provocări, soluții implementate, rezultate concrete cu cifre. SCRIE TOT ÎN ROMÂNĂ.',\n      '-calc': 'Include calculatoare și instrumente interactive pentru calcule cu formule. Prezintă minimum 3 formule matematice cu exemple numerice concrete. SCRIE TOT ÎN ROMÂNĂ.',\n      '-game': 'Adaugă elemente de gamificare cu puncte, realizări și competiții. Creează un sistem de punctaj cu 5 nivele și recompense pentru fiecare nivel. SCRIE TOT ÎN ROMÂNĂ.',\n      // Localization flags\n      '-ro': 'Adaptează pentru piața românească și legislația locală cu exemple românești. Include referințe la legi românești, companii românești și practici locale specifice. SCRIE TOT ÎN ROMÂNĂ.',\n      // Advanced features flags\n      '-auto': 'Focalizează-te pe automatizarea proceselor cu instrumente și pași de implementare. Include minimum 3 instrumente software cu tutorial de configurare. SCRIE TOT ÎN ROMÂNĂ.'\n    },\n    en: {\n      // English versions\n      '-a': 'Write a comprehensive informative article (600-800 words) with clear structure: introduction, development with 3-4 main sections, and conclusion. WRITE THE ENTIRE ARTICLE IN ENGLISH.',\n      '-t': 'Format ALL content as tables and structured data. Create minimum 3 tables with organized information in columns and rows. Each table should have title and explanations. WRITE EVERYTHING IN ENGLISH.',\n      '-ex': 'Include EXACTLY 3 detailed practical examples with step-by-step explanations. Each example should have: context, implementation, expected result. Number examples 1, 2, 3. WRITE EVERYTHING IN ENGLISH.',\n      '-p': 'Include MANDATORY code examples and technical demonstrations. Minimum 2 code blocks with line-by-line explanations. Add comments in code. WRITE EVERYTHING IN ENGLISH.',\n      '-q': 'Create EXACTLY 5 multiple choice questions at the end of the article. Each question should have 4 options (A, B, C, D). Include answer key with correct answers at the end. WRITE EVERYTHING IN ENGLISH.',\n      '-rap': 'Write an exhaustive report (800-1200 words) with comprehensive coverage: executive summary, detailed analysis, conclusions and recommendations. WRITE EVERYTHING IN ENGLISH.',\n      '-def': 'Focus on expert-level definitions and technical terminology. Include minimum 10 specialized terms with precise definitions. WRITE EVERYTHING IN ENGLISH.',\n      '-path': 'Create a personalized learning path with 5-7 concrete steps, measurable milestones and time estimates for each stage. WRITE EVERYTHING IN ENGLISH.',\n      '-vis': 'Generate detailed descriptions for minimum 3 infographics/diagrams. For each diagram: title, visual elements, suggested colors, and explanation of each element. WRITE EVERYTHING IN ENGLISH.',\n      '-mind': 'Present information as an interactive mind map with connected concepts. Describe structure: central node, 5-8 main branches, sub-branches and connections between concepts. WRITE EVERYTHING IN ENGLISH.',\n      '-flow': 'Create flow diagrams and processes with decision points and results. Include minimum 2 flowcharts with specific geometric shapes and directional arrows. WRITE EVERYTHING IN ENGLISH.',\n      '-case': 'Include 2-3 real case studies with measurable results and detailed analysis. For each case: context, challenges, implemented solutions, concrete results with figures. WRITE EVERYTHING IN ENGLISH.',\n      '-calc': 'Include calculators and interactive tools for calculations with formulas. Present minimum 3 mathematical formulas with concrete numerical examples. WRITE EVERYTHING IN ENGLISH.',\n      '-game': 'Add gamification elements with points, achievements and competitions. Create a scoring system with 5 levels and rewards for each level. WRITE EVERYTHING IN ENGLISH.',\n      '-ro': 'Adapt for Romanian market and local legislation with Romanian examples. Include references to Romanian laws, Romanian companies and specific local practices. WRITE EVERYTHING IN ENGLISH.',\n      '-auto': 'Focus on process automation with tools and implementation steps. Include minimum 3 software tools with configuration tutorial. WRITE EVERYTHING IN ENGLISH.'\n    }\n  };\n  const currentFlagInstructions = flagInstructions[currentLang] || flagInstructions.ro;\n\n  // Combine selected flag instructions with language support\n  const selectedInstructions = flags.map(flag => currentFlagInstructions[flag] || '').filter(Boolean);\n  const combinedInstructions = selectedInstructions.join(' ');\n\n  // Language-specific prompts\n  const languagePrompts = {\n    ro: {\n      expertRole: `Ești un expert în ${topic}. Creează un articol detaliat despre \"${branch.nume}\" în contextul \"${topic}\".`,\n      description: `Descrierea ramuri: ${branch.descriere}`,\n      subcategories: branch.subcategorii ? `Subcategorii: ${branch.subcategorii.join(', ')}` : '',\n      important: `IMPORTANT:\n- Articolul trebuie să fie în format JSON cu structura: {\"titlu\": \"...\", \"continut\": \"...\"}\n- Conținutul să fie în format markdown\n- Să fie informativ și detaliat (600-800 cuvinte)\n- Să includă exemple practice și aplicații concrete\n- Să respecte EXACT instrucțiunile flag-urilor selectate\n- SCRIE ÎNTREGUL ARTICOL ÎN ROMÂNĂ - nu amesteca cu engleză!\n- Folosește minimum 5 surse web pentru informații actualizate`,\n      response: `Răspunde DOAR cu JSON-ul, fără text suplimentar.`\n    },\n    en: {\n      expertRole: `You are an expert in ${topic}. Create a detailed article about \"${branch.nume}\" in the context of \"${topic}\".`,\n      description: `Branch description: ${branch.descriere}`,\n      subcategories: branch.subcategorii ? `Subcategories: ${branch.subcategorii.join(', ')}` : '',\n      important: `IMPORTANT:\n- The article must be in JSON format with structure: {\"titlu\": \"...\", \"continut\": \"...\"}\n- Content should be in markdown format\n- Should be informative and detailed (600-800 words)\n- Should include practical examples and concrete applications\n- Must follow EXACTLY the selected flag instructions\n- WRITE THE ENTIRE ARTICLE IN ENGLISH - don't mix with other languages!\n- Use minimum 5 web sources for updated information`,\n      response: `Respond ONLY with the JSON, without additional text.`\n    }\n  };\n  const currentPrompts = languagePrompts[currentLang] || languagePrompts.ro;\n\n  // STEP 2: Additional web sources for comprehensive validation\n  console.log('🔍 STEP 2: Additional web search for comprehensive article sources');\n  let additionalWebSources = [];\n  let sourcesContext = '';\n  try {\n    // Language-specific search query\n    const searchQuery = currentLang === 'ro' ? `${topic} ${branch.nume} română` : `${topic} ${branch.nume}`;\n    additionalWebSources = await webSearchService.searchSources(searchQuery, 5);\n    console.log('✅ VERIFIED: Found additional sources:', additionalWebSources.length, 'sources');\n\n    // Combine with initial validation sources\n    const allWebSources = [...webValidation.sources, ...additionalWebSources];\n    if (allWebSources.length > 0) {\n      const sourcesText = currentLang === 'ro' ? `\\n\\nCRITICAL: Bazează articolul pe aceste surse web VERIFICATE:\\n${allWebSources.map(source => `- ${source.title}: ${source.description} (${source.source})`).join('\\n')}\\n\\nOBLIGATORIU: Referențiază aceste surse în conținut și adaugă secțiunea \"Surse și Lectură Suplimentară\" la final.` : `\\n\\nCRITICAL: Base the article on these VERIFIED web sources:\\n${allWebSources.map(source => `- ${source.title}: ${source.description} (${source.source})`).join('\\n')}\\n\\nMANDATORY: Reference these sources in the content and add \"Sources & Further Reading\" section at the end.`;\n      sourcesContext = sourcesText;\n    } else {\n      throw new Error('CRITICAL: No web sources available for article validation');\n    }\n  } catch (error) {\n    console.error('❌ CRITICAL: Additional web search failed:', error);\n    throw new Error('CRITICAL: Cannot proceed without comprehensive web validation');\n  }\n  const prompt = `${currentPrompts.expertRole}\n\n${currentPrompts.description}\n${currentPrompts.subcategories}\n\n${combinedInstructions}\n\n${currentPrompts.important}\n\n${sourcesContext}\n\n${currentPrompts.response}`;\n\n  // STEP 3: STRICT AI Generation with DeepSeek R1 ONLY\n  console.log('🤖 STEP 3: STRICT AI generation with DeepSeek R1 0528 free model');\n  let aiResponse;\n  try {\n    // CRITICAL: Use EXACT structure as specified\n    aiResponse = await client.makeRequest([{\n      role: 'system',\n      content: currentLang === 'ro' ? 'Ești un expert în crearea de conținut educațional. Generează articole comprehensive, bine structurate în format JSON valid. Nu include text explicativ în afara JSON-ului. SCRIE ÎNTREGUL ARTICOL ÎN ROMÂNĂ. FOLOSEȘTE DOAR INFORMAȚII VERIFICATE DIN SURSE WEB.' : 'You are an expert content writer and educator. Generate comprehensive, well-structured articles in valid JSON format only. Do not include any explanatory text outside the JSON. WRITE THE ENTIRE ARTICLE IN ENGLISH. USE ONLY VERIFIED INFORMATION FROM WEB SOURCES.'\n    }, {\n      role: 'user',\n      content: prompt\n    }], 0.8, 4000); // temperature, max_tokens\n\n    console.log('✅ VERIFIED: AI response received from DeepSeek R1, length:', aiResponse.length);\n\n    // Parse and validate the JSON response\n    const cleanResponse = responseText.trim();\n    const jsonMatch = cleanResponse.match(/\\{[\\s\\S]*\\}/);\n    if (!jsonMatch) {\n      throw new Error('No valid JSON found in response');\n    }\n    const article = JSON.parse(jsonMatch[0]);\n\n    // Validate structure\n    if (!article.titlu || !article.continut) {\n      throw new Error('Invalid article structure');\n    }\n\n    // Add web sources to the article\n    if (webSources.length > 0) {\n      const sourcesHTML = webSearchService.formatSourcesHTML(webSources);\n      const sourcesTitle = currentLang === 'ro' ? '\\n\\n## Surse și Lectură Suplimentară\\n\\n' : '\\n\\n## Sources & Further Reading\\n\\n';\n      article.continut += sourcesTitle + sourcesHTML;\n      article.webSources = webSources;\n    }\n    console.log('✅ Article generated successfully with R1 model');\n    return article;\n  } catch (error) {\n    console.error('❌ Error generating article with R1:', error);\n\n    // Language-specific fallback article\n    const fallbackContent = currentLang === 'ro' ? {\n      titlu: `${branch.nume} - ${topic}`,\n      continut: `# ${branch.nume}\n\nAceastă secțiune explorează ${branch.nume} în contextul ${topic}.\n\n## Prezentare Generală\n${branch.descriere}\n\n## Concepte Cheie\nÎnțelegerea ${branch.nume} este esențială pentru stăpânirea ${topic}. Această zonă acoperă principii fundamentale și aplicații practice.\n\n## Aplicații\nConceptele din ${branch.nume} au aplicații extinse în diverse domenii și industrii.\n\n## Învățare Suplimentară\nPentru a vă aprofunda înțelegerea, luați în considerare explorarea subiectelor conexe și exercițiilor practice.`,\n      subcategorii: branch.subcategorii || [],\n      flags: flags,\n      pozitie: `${topic} → ${branch.nume}`\n    } : {\n      titlu: `${branch.nume} - ${topic}`,\n      continut: `# ${branch.nume}\n\nThis section explores ${branch.nume} in the context of ${topic}.\n\n## Overview\n${branch.descriere}\n\n## Key Concepts\nUnderstanding ${branch.nume} is essential for mastering ${topic}. This area covers fundamental principles and practical applications.\n\n## Applications\nThe concepts in ${branch.nume} have wide-ranging applications across various domains and industries.\n\n## Further Learning\nTo deepen your understanding, consider exploring related topics and practical exercises.`,\n      subcategorii: branch.subcategorii || [],\n      flags: flags,\n      pozitie: `${topic} → ${branch.nume}`\n    };\n    return fallbackContent;\n  }\n}\n\n// Test API connection\nexport async function testConnection() {\n  try {\n    const response = await client.makeRequest([{\n      role: 'user',\n      content: 'Hello, please respond with \"API connection successful\"'\n    }]);\n    return response.includes('successful');\n  } catch (error) {\n    console.error('API connection test failed:', error);\n    return false;\n  }\n}", "map": {"version": 3, "names": ["webSearchService", "OPENROUTER_API_KEY", "OPENROUTER_BASE_URL", "MODEL", "SITE_CONFIG", "process", "env", "REACT_APP_SITE_URL", "REACT_APP_SITE_NAME", "OpenRouterClient", "constructor", "baseURL", "<PERSON><PERSON><PERSON><PERSON>", "console", "log", "substring", "Error", "makeRequest", "messages", "temperature", "maxTokens", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "model", "max_tokens", "ok", "_errorData$error", "errorData", "json", "catch", "error", "status", "message", "data", "choices", "content", "validateWithWebSearch", "topic", "webSources", "searchSources", "length", "warn", "validated", "sources", "validationTimestamp", "Date", "toISOString", "client", "generateKnowledgeTree", "language", "currentLang", "localStorage", "getItem", "webValidation", "prompts", "ro", "en", "prompt", "aiResponse", "role", "map", "s", "title", "join", "cleanResponse", "trim", "jsonMatch", "match", "tree", "parse", "tema", "Array", "isArray", "<PERSON><PERSON>", "finalValidation", "_validation", "validatedAt", "aiModel", "strictMode", "fallbacks", "nume", "desc<PERSON><PERSON>", "emoji", "subcategorii", "generateArticle", "branch", "flags", "articleTopic", "flagInstructions", "currentFlagInstructions", "selectedInstructions", "flag", "filter", "Boolean", "combinedInstructions", "languagePrompts", "expertRole", "description", "subcategories", "important", "currentPrompts", "additionalWebSources", "sourcesContext", "searchQuery", "allWebSources", "sourcesText", "source", "responseText", "article", "titlu", "continut", "sourcesHTML", "formatSourcesHTML", "sourcesTitle", "fallback<PERSON><PERSON><PERSON>", "pozitie", "testConnection", "includes"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Appv1/src/services/openRouterService.js"], "sourcesContent": ["// OpenRouter API Service for Knowledge Tree Generation\n// STRICT IMPLEMENTATION - DeepSeek R1 + Web Search MANDATORY\nimport webSearchService from './webSearchService';\n\n// CRITICAL: API Key și configurație EXACTĂ conform cerințelor\nconst OPENROUTER_API_KEY = '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';\nconst OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';\nconst MODEL = 'deepseek/deepseek-r1-0528:free'; // OBLIGATORIU - DeepSeek R1 0528 free\n\n// Site configuration for OpenRouter rankings\nconst SITE_CONFIG = {\n  'HTTP-Referer': process.env.REACT_APP_SITE_URL || 'http://localhost:3000',\n  'X-Title': process.env.REACT_APP_SITE_NAME || 'Knowledge Tree Explorer'\n};\n\n// CRITICAL: STRICT OpenRouter Client Implementation\n// EXACT structure as specified - NO DEVIATIONS ALLOWED\nclass OpenRouterClient {\n  constructor() {\n    this.baseURL = OPENROUTER_BASE_URL;\n    this.apiKey = OPENROUTER_API_KEY;\n\n    // CRITICAL: Verify exact API key and model\n    console.log('🔒 STRICT OpenRouter Client initialized:');\n    console.log('- Base URL:', this.baseURL);\n    console.log('- Model:', MODEL, '(MUST be deepseek/deepseek-r1-0528:free)');\n    console.log('- API Key:', this.apiKey ? `${this.apiKey.substring(0, 15)}...` : 'CRITICAL ERROR: NOT SET');\n    console.log('- Site Config:', SITE_CONFIG);\n\n    if (!this.apiKey || this.apiKey === 'your-api-key-here') {\n      throw new Error('CRITICAL: API Key not properly configured');\n    }\n  }\n\n  // MANDATORY: Exact structure as specified in requirements\n  async makeRequest(messages, temperature = 0.7, maxTokens = 2000) {\n    console.log('🔒 STRICT API Call - DeepSeek R1 0528 free model ONLY');\n\n    try {\n      const response = await fetch(`${this.baseURL}/chat/completions`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${this.apiKey}`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': SITE_CONFIG['HTTP-Referer'],\n          'X-Title': SITE_CONFIG['X-Title']\n        },\n        body: JSON.stringify({\n          model: MODEL, // CRITICAL: MUST be deepseek/deepseek-r1-0528:free\n          messages,\n          temperature,\n          max_tokens: maxTokens\n        })\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        console.error('❌ CRITICAL API ERROR:', response.status, errorData);\n        throw new Error(`CRITICAL OpenRouter API Error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);\n      }\n\n      const data = await response.json();\n      console.log('✅ VERIFIED: API Response received from DeepSeek R1');\n\n      if (!data.choices || !data.choices[0] || !data.choices[0].message) {\n        throw new Error('CRITICAL: Invalid API response structure');\n      }\n\n      return data.choices[0].message.content;\n    } catch (error) {\n      console.error('❌ CRITICAL: OpenRouter API request failed:', error);\n      throw error;\n    }\n  }\n\n  // MANDATORY: Web search validation for ALL content - NO EXCEPTIONS\n  async validateWithWebSearch(topic, content) {\n    console.log('🔍 MANDATORY: Validating content with web search for:', topic);\n\n    try {\n      const webSources = await webSearchService.searchSources(topic, 5);\n      console.log('✅ VERIFIED: Web validation completed with', webSources.length, 'sources');\n\n      if (webSources.length === 0) {\n        console.warn('⚠️ WARNING: No web sources found for validation');\n      }\n\n      return {\n        validated: true,\n        sources: webSources,\n        content: content,\n        validationTimestamp: new Date().toISOString()\n      };\n    } catch (error) {\n      console.error('❌ CRITICAL: Web validation failed:', error);\n      throw new Error('CRITICAL: Content validation failed - web search required for all content');\n    }\n  }\n}\n\nconst client = new OpenRouterClient();\n\n// CRITICAL: Generate Knowledge Tree with MANDATORY AI + Web Search validation\nexport async function generateKnowledgeTree(topic, language = 'en') {\n  console.log('🔒 STRICT: Starting knowledge tree generation with mandatory validation');\n  console.log('- Topic:', topic);\n  console.log('- Language:', language);\n\n  // Get current language from localStorage if not provided\n  const currentLang = language || localStorage.getItem('language') || 'en';\n\n  // MANDATORY: Web search validation BEFORE AI generation\n  console.log('🔍 STEP 1: MANDATORY web search validation for topic:', topic);\n  let webValidation;\n  try {\n    webValidation = await client.validateWithWebSearch(topic, 'Initial topic validation');\n    console.log('✅ VERIFIED: Topic validated with web sources');\n  } catch (error) {\n    console.error('❌ CRITICAL: Topic validation failed:', error);\n    throw new Error(`CRITICAL: Cannot proceed without web validation for topic: ${topic}`);\n  }\n\n  const prompts = {\n    ro: `Analizează cu atenție subiectul \"${topic}\" și creează un arbore de cunoștințe FOARTE SPECIFIC și RELEVANT pentru acest domeniu exact.\n\nIMPORTANT: Generează ramuri care sunt DIRECT LEGATE de \"${topic}\", nu concepte generale!\n\nReturnează DOAR un obiect JSON valid cu această structură exactă:\n{\n  \"tema\": \"${topic}\",\n  \"ramuri\": [\n    {\n      \"nume\": \"Nume Ramură Specifică\",\n      \"descriere\": \"Descriere scurtă și precisă\",\n      \"emoji\": \"📚\",\n      \"subcategorii\": [\"subcategorie1\", \"subcategorie2\", \"subcategorie3\"]\n    }\n  ]\n}\n\nCerințe STRICTE:\n- Generează 6-8 ramuri principale SPECIFICE pentru \"${topic}\"\n- Fiecare ramură TREBUIE să fie direct legată de subiectul principal\n- Emoji-uri relevante pentru fiecare ramură\n- 3-4 subcategorii specifice pentru fiecare ramură\n- Descrieri de maxim 1 propoziție\n- Focalizează-te pe aspectele PRACTICE și APLICABILE\n- Evită conceptele generale sau irelevante\n\nSubiect: ${topic}`,\n\n    en: `Analyze the subject \"${topic}\" carefully and create a VERY SPECIFIC and RELEVANT knowledge tree for this exact domain.\n\nIMPORTANT: Generate branches that are DIRECTLY RELATED to \"${topic}\", not general concepts!\n\nReturn ONLY a valid JSON object with this exact structure:\n{\n  \"tema\": \"${topic}\",\n  \"ramuri\": [\n    {\n      \"nume\": \"Specific Branch Name\",\n      \"descriere\": \"Short and precise description\",\n      \"emoji\": \"📚\",\n      \"subcategorii\": [\"subcategory1\", \"subcategory2\", \"subcategory3\"]\n    }\n  ]\n}\n\nSTRICT Requirements:\n- Generate 6-8 main branches SPECIFIC to \"${topic}\"\n- Each branch MUST be directly related to the main subject\n- Relevant emojis for each branch\n- 3-4 specific subcategories for each branch\n- Descriptions of maximum 1 sentence\n- Focus on PRACTICAL and APPLICABLE aspects\n- Avoid general or irrelevant concepts\n\nSubject: ${topic}`\n  };\n\n  const prompt = prompts[currentLang] || prompts.en;\n\n  // STEP 2: AI Generation with DeepSeek R1 ONLY\n  console.log('🤖 STEP 2: AI generation with DeepSeek R1 0528 free model');\n  let aiResponse;\n  try {\n    aiResponse = await client.makeRequest([\n      {\n        role: 'system',\n        content: currentLang === 'ro'\n          ? 'Expert în organizarea cunoștințelor. Generează arbori de cunoștințe specifici în format JSON valid. Răspunde DOAR cu JSON, fără text explicativ. FOLOSEȘTE DOAR INFORMAȚII VERIFICATE.'\n          : 'Expert in knowledge organization. Generate specific knowledge trees in valid JSON format. Respond ONLY with JSON, no explanatory text. USE ONLY VERIFIED INFORMATION.'\n      },\n      {\n        role: 'user',\n        content: prompt + `\\n\\nIMPORTANT: Bazează-te pe aceste surse web verificate: ${webValidation.sources.map(s => s.title).join(', ')}`\n      }\n    ], 0.3); // Temperatură mai mică pentru răspunsuri mai consistente și rapide\n\n    console.log('✅ VERIFIED: AI response received from DeepSeek R1');\n\n    // STEP 3: Parse and validate the JSON response\n    console.log('🔍 STEP 3: Parsing and validating AI response');\n    const cleanResponse = aiResponse.trim();\n    const jsonMatch = cleanResponse.match(/\\{[\\s\\S]*\\}/);\n\n    if (!jsonMatch) {\n      throw new Error('CRITICAL: No valid JSON found in AI response');\n    }\n\n    const tree = JSON.parse(jsonMatch[0]);\n\n    // STEP 4: Strict structure validation\n    console.log('🔍 STEP 4: Strict structure validation');\n    if (!tree.tema || !Array.isArray(tree.ramuri)) {\n      throw new Error('CRITICAL: Invalid tree structure from AI');\n    }\n\n    if (tree.ramuri.length === 0) {\n      throw new Error('CRITICAL: No branches generated by AI');\n    }\n\n    // STEP 5: Final validation with web sources\n    console.log('🔍 STEP 5: Final validation with web sources');\n    const finalValidation = await client.validateWithWebSearch(\n      `${topic} knowledge tree branches`,\n      JSON.stringify(tree)\n    );\n\n    // Add validation metadata\n    tree._validation = {\n      webSources: finalValidation.sources,\n      validatedAt: finalValidation.validationTimestamp,\n      aiModel: MODEL,\n      strictMode: true\n    };\n\n    console.log('✅ SUCCESS: Knowledge tree generated and validated with strict mode');\n    return tree;\n  } catch (error) {\n    console.error('Error generating knowledge tree:', error);\n    \n    // Fallback tree structure based on language\n    const fallbacks = {\n      ro: {\n        tema: topic,\n        ramuri: [\n          {\n            nume: \"Fundamentele\",\n            descriere: `Concepte de bază și principii ale ${topic}`,\n            emoji: \"📚\",\n            subcategorii: [\"Concepte Esențiale\", \"Principii Cheie\", \"Teoria de Bază\"]\n          },\n          {\n            nume: \"Aplicații\",\n            descriere: `Aplicații practice și cazuri de utilizare ale ${topic}`,\n            emoji: \"🔧\",\n            subcategorii: [\"Utilizări Reale\", \"Aplicații Industriale\", \"Studii de Caz\"]\n          },\n          {\n            nume: \"Subiecte Avansate\",\n            descriere: `Aspecte complexe și specializate ale ${topic}`,\n            emoji: \"🎓\",\n            subcategorii: [\"Nivel Expert\", \"Zone de Cercetare\", \"Tehnologii Noi\"]\n          }\n        ]\n      },\n      en: {\n        tema: topic,\n        ramuri: [\n          {\n            nume: \"Fundamentals\",\n            descriere: `Basic concepts and principles of ${topic}`,\n            emoji: \"📚\",\n            subcategorii: [\"Core Concepts\", \"Key Principles\", \"Basic Theory\"]\n          },\n          {\n            nume: \"Applications\",\n            descriere: `Practical applications and use cases of ${topic}`,\n            emoji: \"🔧\",\n            subcategorii: [\"Real-world Uses\", \"Industry Applications\", \"Case Studies\"]\n          },\n          {\n            nume: \"Advanced Topics\",\n            descriere: `Complex and specialized aspects of ${topic}`,\n            emoji: \"🎓\",\n            subcategorii: [\"Expert Level\", \"Research Areas\", \"Cutting Edge\"]\n          }\n        ]\n      }\n    };\n\n    return fallbacks[currentLang] || fallbacks.en;\n  }\n}\n\n// CRITICAL: Generate Article with MANDATORY AI + Web Search validation\nexport async function generateArticle(topic, branch, flags = ['-a']) {\n  console.log('🔒 STRICT: Starting article generation with mandatory validation');\n  console.log('- Topic:', topic);\n  console.log('- Branch:', branch.nume);\n  console.log('- Flags:', flags);\n\n  // Get current language from localStorage\n  const currentLang = localStorage.getItem('language') || 'ro';\n\n  // MANDATORY: Web search validation BEFORE AI generation\n  console.log('🔍 STEP 1: MANDATORY web search validation for article topic');\n  const articleTopic = `${topic} ${branch.nume}`;\n  let webValidation;\n  try {\n    webValidation = await client.validateWithWebSearch(articleTopic, 'Article topic validation');\n    console.log('✅ VERIFIED: Article topic validated with', webValidation.sources.length, 'web sources');\n\n    if (webValidation.sources.length === 0) {\n      throw new Error('No web sources found for validation');\n    }\n  } catch (error) {\n    console.error('❌ CRITICAL: Article topic validation failed:', error);\n    throw new Error(`CRITICAL: Cannot proceed without web validation for article: ${articleTopic}`);\n  }\n\n  const flagInstructions = {\n    ro: {\n      // Basic flags cu prompt-uri FOARTE SPECIFICE în ROMÂNĂ\n      '-a': 'Scrie un articol informativ standard (600-800 cuvinte) cu structură clară: introducere, dezvoltare cu 3-4 secțiuni principale, și concluzie. SCRIE ÎNTREGUL ARTICOL ÎN ROMÂNĂ.',\n\n      '-t': 'Formatează ÎNTREGUL conținut ca tabele și date structurate. Creează minimum 3 tabele cu informații organizate în coloane și rânduri. Fiecare tabel să aibă titlu și explicații. SCRIE TOT ÎN ROMÂNĂ.',\n\n      '-ex': 'Include EXACT 3 exemple practice detaliate cu explicații pas cu pas. Fiecare exemplu să aibă: context, implementare, rezultat așteptat. Numerotează exemplele 1, 2, 3. SCRIE TOT ÎN ROMÂNĂ.',\n\n      '-p': 'Include OBLIGATORIU exemple de cod și demonstrații tehnice. Minimum 2 blocuri de cod cu explicații linie cu linie. Adaugă comentarii în cod. SCRIE TOT ÎN ROMÂNĂ.',\n\n      '-q': 'Creează EXACT 5 întrebări tip grilă la sfârșitul articolului. Fiecare întrebare să aibă 4 variante (A, B, C, D). Include baremul cu răspunsurile corecte la final. SCRIE TOT ÎN ROMÂNĂ.',\n\n      '-rap': 'Scrie un raport exhaustiv (800-1200 cuvinte) cu acoperire comprehensivă: rezumat executiv, analiză detaliată, concluzii și recomandări. SCRIE TOT ÎN ROMÂNĂ.',\n\n      '-def': 'Focalizează-te pe definiții de nivel expert și terminologie tehnică. Include minimum 10 termeni specializați cu definiții precise. SCRIE TOT ÎN ROMÂNĂ.',\n\n      // Learning & Visualization flags\n      '-path': 'Creează o cale de învățare personalizată cu 5-7 pași concreți, milestone-uri măsurabile și estimări de timp pentru fiecare etapă. SCRIE TOT ÎN ROMÂNĂ.',\n\n      '-vis': 'Generează descrieri detaliate pentru minimum 3 infografice/diagrame. Pentru fiecare diagramă: titlu, elemente vizuale, culori sugerate, și explicația fiecărui element. SCRIE TOT ÎN ROMÂNĂ.',\n\n      '-mind': 'Prezintă informația ca o hartă mentală interactivă cu concepte conectate. Descrie structura: nod central, 5-8 ramuri principale, sub-ramuri și conexiuni între concepte. SCRIE TOT ÎN ROMÂNĂ.',\n\n      '-flow': 'Creează diagrame de flux și procese cu puncte de decizie și rezultate. Include minimum 2 flowchart-uri cu forme geometrice specifice și săgeți directionale. SCRIE TOT ÎN ROMÂNĂ.',\n\n      // Industry-specific flags\n      '-case': 'Include 2-3 studii de caz reale cu rezultate măsurabile și analiză detaliată. Pentru fiecare caz: context, provocări, soluții implementate, rezultate concrete cu cifre. SCRIE TOT ÎN ROMÂNĂ.',\n\n      '-calc': 'Include calculatoare și instrumente interactive pentru calcule cu formule. Prezintă minimum 3 formule matematice cu exemple numerice concrete. SCRIE TOT ÎN ROMÂNĂ.',\n\n      '-game': 'Adaugă elemente de gamificare cu puncte, realizări și competiții. Creează un sistem de punctaj cu 5 nivele și recompense pentru fiecare nivel. SCRIE TOT ÎN ROMÂNĂ.',\n\n      // Localization flags\n      '-ro': 'Adaptează pentru piața românească și legislația locală cu exemple românești. Include referințe la legi românești, companii românești și practici locale specifice. SCRIE TOT ÎN ROMÂNĂ.',\n\n      // Advanced features flags\n      '-auto': 'Focalizează-te pe automatizarea proceselor cu instrumente și pași de implementare. Include minimum 3 instrumente software cu tutorial de configurare. SCRIE TOT ÎN ROMÂNĂ.'\n    },\n    en: {\n      // English versions\n      '-a': 'Write a comprehensive informative article (600-800 words) with clear structure: introduction, development with 3-4 main sections, and conclusion. WRITE THE ENTIRE ARTICLE IN ENGLISH.',\n      '-t': 'Format ALL content as tables and structured data. Create minimum 3 tables with organized information in columns and rows. Each table should have title and explanations. WRITE EVERYTHING IN ENGLISH.',\n      '-ex': 'Include EXACTLY 3 detailed practical examples with step-by-step explanations. Each example should have: context, implementation, expected result. Number examples 1, 2, 3. WRITE EVERYTHING IN ENGLISH.',\n      '-p': 'Include MANDATORY code examples and technical demonstrations. Minimum 2 code blocks with line-by-line explanations. Add comments in code. WRITE EVERYTHING IN ENGLISH.',\n      '-q': 'Create EXACTLY 5 multiple choice questions at the end of the article. Each question should have 4 options (A, B, C, D). Include answer key with correct answers at the end. WRITE EVERYTHING IN ENGLISH.',\n      '-rap': 'Write an exhaustive report (800-1200 words) with comprehensive coverage: executive summary, detailed analysis, conclusions and recommendations. WRITE EVERYTHING IN ENGLISH.',\n      '-def': 'Focus on expert-level definitions and technical terminology. Include minimum 10 specialized terms with precise definitions. WRITE EVERYTHING IN ENGLISH.',\n      '-path': 'Create a personalized learning path with 5-7 concrete steps, measurable milestones and time estimates for each stage. WRITE EVERYTHING IN ENGLISH.',\n      '-vis': 'Generate detailed descriptions for minimum 3 infographics/diagrams. For each diagram: title, visual elements, suggested colors, and explanation of each element. WRITE EVERYTHING IN ENGLISH.',\n      '-mind': 'Present information as an interactive mind map with connected concepts. Describe structure: central node, 5-8 main branches, sub-branches and connections between concepts. WRITE EVERYTHING IN ENGLISH.',\n      '-flow': 'Create flow diagrams and processes with decision points and results. Include minimum 2 flowcharts with specific geometric shapes and directional arrows. WRITE EVERYTHING IN ENGLISH.',\n      '-case': 'Include 2-3 real case studies with measurable results and detailed analysis. For each case: context, challenges, implemented solutions, concrete results with figures. WRITE EVERYTHING IN ENGLISH.',\n      '-calc': 'Include calculators and interactive tools for calculations with formulas. Present minimum 3 mathematical formulas with concrete numerical examples. WRITE EVERYTHING IN ENGLISH.',\n      '-game': 'Add gamification elements with points, achievements and competitions. Create a scoring system with 5 levels and rewards for each level. WRITE EVERYTHING IN ENGLISH.',\n      '-ro': 'Adapt for Romanian market and local legislation with Romanian examples. Include references to Romanian laws, Romanian companies and specific local practices. WRITE EVERYTHING IN ENGLISH.',\n      '-auto': 'Focus on process automation with tools and implementation steps. Include minimum 3 software tools with configuration tutorial. WRITE EVERYTHING IN ENGLISH.'\n    }\n  };\n\n  const currentFlagInstructions = flagInstructions[currentLang] || flagInstructions.ro;\n\n  // Combine selected flag instructions with language support\n  const selectedInstructions = flags.map(flag => currentFlagInstructions[flag] || '').filter(Boolean);\n  const combinedInstructions = selectedInstructions.join(' ');\n\n  // Language-specific prompts\n  const languagePrompts = {\n    ro: {\n      expertRole: `Ești un expert în ${topic}. Creează un articol detaliat despre \"${branch.nume}\" în contextul \"${topic}\".`,\n      description: `Descrierea ramuri: ${branch.descriere}`,\n      subcategories: branch.subcategorii ? `Subcategorii: ${branch.subcategorii.join(', ')}` : '',\n      important: `IMPORTANT:\n- Articolul trebuie să fie în format JSON cu structura: {\"titlu\": \"...\", \"continut\": \"...\"}\n- Conținutul să fie în format markdown\n- Să fie informativ și detaliat (600-800 cuvinte)\n- Să includă exemple practice și aplicații concrete\n- Să respecte EXACT instrucțiunile flag-urilor selectate\n- SCRIE ÎNTREGUL ARTICOL ÎN ROMÂNĂ - nu amesteca cu engleză!\n- Folosește minimum 5 surse web pentru informații actualizate`,\n      response: `Răspunde DOAR cu JSON-ul, fără text suplimentar.`\n    },\n    en: {\n      expertRole: `You are an expert in ${topic}. Create a detailed article about \"${branch.nume}\" in the context of \"${topic}\".`,\n      description: `Branch description: ${branch.descriere}`,\n      subcategories: branch.subcategorii ? `Subcategories: ${branch.subcategorii.join(', ')}` : '',\n      important: `IMPORTANT:\n- The article must be in JSON format with structure: {\"titlu\": \"...\", \"continut\": \"...\"}\n- Content should be in markdown format\n- Should be informative and detailed (600-800 words)\n- Should include practical examples and concrete applications\n- Must follow EXACTLY the selected flag instructions\n- WRITE THE ENTIRE ARTICLE IN ENGLISH - don't mix with other languages!\n- Use minimum 5 web sources for updated information`,\n      response: `Respond ONLY with the JSON, without additional text.`\n    }\n  };\n\n  const currentPrompts = languagePrompts[currentLang] || languagePrompts.ro;\n\n  // STEP 2: Additional web sources for comprehensive validation\n  console.log('🔍 STEP 2: Additional web search for comprehensive article sources');\n  let additionalWebSources = [];\n  let sourcesContext = '';\n\n  try {\n    // Language-specific search query\n    const searchQuery = currentLang === 'ro'\n      ? `${topic} ${branch.nume} română`\n      : `${topic} ${branch.nume}`;\n\n    additionalWebSources = await webSearchService.searchSources(searchQuery, 5);\n    console.log('✅ VERIFIED: Found additional sources:', additionalWebSources.length, 'sources');\n\n    // Combine with initial validation sources\n    const allWebSources = [...webValidation.sources, ...additionalWebSources];\n\n    if (allWebSources.length > 0) {\n      const sourcesText = currentLang === 'ro'\n        ? `\\n\\nCRITICAL: Bazează articolul pe aceste surse web VERIFICATE:\\n${allWebSources.map(source =>\n            `- ${source.title}: ${source.description} (${source.source})`\n          ).join('\\n')}\\n\\nOBLIGATORIU: Referențiază aceste surse în conținut și adaugă secțiunea \"Surse și Lectură Suplimentară\" la final.`\n        : `\\n\\nCRITICAL: Base the article on these VERIFIED web sources:\\n${allWebSources.map(source =>\n            `- ${source.title}: ${source.description} (${source.source})`\n          ).join('\\n')}\\n\\nMANDATORY: Reference these sources in the content and add \"Sources & Further Reading\" section at the end.`;\n\n      sourcesContext = sourcesText;\n    } else {\n      throw new Error('CRITICAL: No web sources available for article validation');\n    }\n  } catch (error) {\n    console.error('❌ CRITICAL: Additional web search failed:', error);\n    throw new Error('CRITICAL: Cannot proceed without comprehensive web validation');\n  }\n\n  const prompt = `${currentPrompts.expertRole}\n\n${currentPrompts.description}\n${currentPrompts.subcategories}\n\n${combinedInstructions}\n\n${currentPrompts.important}\n\n${sourcesContext}\n\n${currentPrompts.response}`;\n\n  // STEP 3: STRICT AI Generation with DeepSeek R1 ONLY\n  console.log('🤖 STEP 3: STRICT AI generation with DeepSeek R1 0528 free model');\n  let aiResponse;\n  try {\n    // CRITICAL: Use EXACT structure as specified\n    aiResponse = await client.makeRequest([\n      {\n        role: 'system',\n        content: currentLang === 'ro'\n          ? 'Ești un expert în crearea de conținut educațional. Generează articole comprehensive, bine structurate în format JSON valid. Nu include text explicativ în afara JSON-ului. SCRIE ÎNTREGUL ARTICOL ÎN ROMÂNĂ. FOLOSEȘTE DOAR INFORMAȚII VERIFICATE DIN SURSE WEB.'\n          : 'You are an expert content writer and educator. Generate comprehensive, well-structured articles in valid JSON format only. Do not include any explanatory text outside the JSON. WRITE THE ENTIRE ARTICLE IN ENGLISH. USE ONLY VERIFIED INFORMATION FROM WEB SOURCES.'\n      },\n      {\n        role: 'user',\n        content: prompt\n      }\n    ], 0.8, 4000); // temperature, max_tokens\n\n    console.log('✅ VERIFIED: AI response received from DeepSeek R1, length:', aiResponse.length);\n\n    // Parse and validate the JSON response\n    const cleanResponse = responseText.trim();\n    const jsonMatch = cleanResponse.match(/\\{[\\s\\S]*\\}/);\n\n    if (!jsonMatch) {\n      throw new Error('No valid JSON found in response');\n    }\n\n    const article = JSON.parse(jsonMatch[0]);\n\n    // Validate structure\n    if (!article.titlu || !article.continut) {\n      throw new Error('Invalid article structure');\n    }\n\n    // Add web sources to the article\n    if (webSources.length > 0) {\n      const sourcesHTML = webSearchService.formatSourcesHTML(webSources);\n      const sourcesTitle = currentLang === 'ro' ? '\\n\\n## Surse și Lectură Suplimentară\\n\\n' : '\\n\\n## Sources & Further Reading\\n\\n';\n      article.continut += sourcesTitle + sourcesHTML;\n      article.webSources = webSources;\n    }\n\n    console.log('✅ Article generated successfully with R1 model');\n    return article;\n\n  } catch (error) {\n    console.error('❌ Error generating article with R1:', error);\n\n    // Language-specific fallback article\n    const fallbackContent = currentLang === 'ro' ? {\n      titlu: `${branch.nume} - ${topic}`,\n      continut: `# ${branch.nume}\n\nAceastă secțiune explorează ${branch.nume} în contextul ${topic}.\n\n## Prezentare Generală\n${branch.descriere}\n\n## Concepte Cheie\nÎnțelegerea ${branch.nume} este esențială pentru stăpânirea ${topic}. Această zonă acoperă principii fundamentale și aplicații practice.\n\n## Aplicații\nConceptele din ${branch.nume} au aplicații extinse în diverse domenii și industrii.\n\n## Învățare Suplimentară\nPentru a vă aprofunda înțelegerea, luați în considerare explorarea subiectelor conexe și exercițiilor practice.`,\n      subcategorii: branch.subcategorii || [],\n      flags: flags,\n      pozitie: `${topic} → ${branch.nume}`\n    } : {\n      titlu: `${branch.nume} - ${topic}`,\n      continut: `# ${branch.nume}\n\nThis section explores ${branch.nume} in the context of ${topic}.\n\n## Overview\n${branch.descriere}\n\n## Key Concepts\nUnderstanding ${branch.nume} is essential for mastering ${topic}. This area covers fundamental principles and practical applications.\n\n## Applications\nThe concepts in ${branch.nume} have wide-ranging applications across various domains and industries.\n\n## Further Learning\nTo deepen your understanding, consider exploring related topics and practical exercises.`,\n      subcategorii: branch.subcategorii || [],\n      flags: flags,\n      pozitie: `${topic} → ${branch.nume}`\n    };\n\n    return fallbackContent;\n  }\n}\n\n// Test API connection\nexport async function testConnection() {\n  try {\n    const response = await client.makeRequest([\n      {\n        role: 'user',\n        content: 'Hello, please respond with \"API connection successful\"'\n      }\n    ]);\n    \n    return response.includes('successful');\n  } catch (error) {\n    console.error('API connection test failed:', error);\n    return false;\n  }\n}\n"], "mappings": "AAAA;AACA;AACA,OAAOA,gBAAgB,MAAM,oBAAoB;;AAEjD;AACA,MAAMC,kBAAkB,GAAG,2EAA2E;AACtG,MAAMC,mBAAmB,GAAG,8BAA8B;AAC1D,MAAMC,KAAK,GAAG,gCAAgC,CAAC,CAAC;;AAEhD;AACA,MAAMC,WAAW,GAAG;EAClB,cAAc,EAAEC,OAAO,CAACC,GAAG,CAACC,kBAAkB,IAAI,uBAAuB;EACzE,SAAS,EAAEF,OAAO,CAACC,GAAG,CAACE,mBAAmB,IAAI;AAChD,CAAC;;AAED;AACA;AACA,MAAMC,gBAAgB,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,OAAO,GAAGT,mBAAmB;IAClC,IAAI,CAACU,MAAM,GAAGX,kBAAkB;;IAEhC;IACAY,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IACvDD,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAACH,OAAO,CAAC;IACxCE,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEX,KAAK,EAAE,0CAA0C,CAAC;IAC1EU,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAACF,MAAM,GAAG,GAAG,IAAI,CAACA,MAAM,CAACG,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAAG,yBAAyB,CAAC;IACzGF,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEV,WAAW,CAAC;IAE1C,IAAI,CAAC,IAAI,CAACQ,MAAM,IAAI,IAAI,CAACA,MAAM,KAAK,mBAAmB,EAAE;MACvD,MAAM,IAAII,KAAK,CAAC,2CAA2C,CAAC;IAC9D;EACF;;EAEA;EACA,MAAMC,WAAWA,CAACC,QAAQ,EAAEC,WAAW,GAAG,GAAG,EAAEC,SAAS,GAAG,IAAI,EAAE;IAC/DP,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;IAEpE,IAAI;MACF,MAAMO,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACX,OAAO,mBAAmB,EAAE;QAC/DY,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,eAAe,EAAE,UAAU,IAAI,CAACZ,MAAM,EAAE;UACxC,cAAc,EAAE,kBAAkB;UAClC,cAAc,EAAER,WAAW,CAAC,cAAc,CAAC;UAC3C,SAAS,EAAEA,WAAW,CAAC,SAAS;QAClC,CAAC;QACDqB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,KAAK,EAAEzB,KAAK;UAAE;UACde,QAAQ;UACRC,WAAW;UACXU,UAAU,EAAET;QACd,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACC,QAAQ,CAACS,EAAE,EAAE;QAAA,IAAAC,gBAAA;QAChB,MAAMC,SAAS,GAAG,MAAMX,QAAQ,CAACY,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACzDrB,OAAO,CAACsB,KAAK,CAAC,uBAAuB,EAAEd,QAAQ,CAACe,MAAM,EAAEJ,SAAS,CAAC;QAClE,MAAM,IAAIhB,KAAK,CAAC,kCAAkCK,QAAQ,CAACe,MAAM,MAAM,EAAAL,gBAAA,GAAAC,SAAS,CAACG,KAAK,cAAAJ,gBAAA,uBAAfA,gBAAA,CAAiBM,OAAO,KAAI,eAAe,EAAE,CAAC;MACvH;MAEA,MAAMC,IAAI,GAAG,MAAMjB,QAAQ,CAACY,IAAI,CAAC,CAAC;MAClCpB,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;MAEjE,IAAI,CAACwB,IAAI,CAACC,OAAO,IAAI,CAACD,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,IAAI,CAACD,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,CAACF,OAAO,EAAE;QACjE,MAAM,IAAIrB,KAAK,CAAC,0CAA0C,CAAC;MAC7D;MAEA,OAAOsB,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,CAACF,OAAO,CAACG,OAAO;IACxC,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MAClE,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAMM,qBAAqBA,CAACC,KAAK,EAAEF,OAAO,EAAE;IAC1C3B,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAE4B,KAAK,CAAC;IAE3E,IAAI;MACF,MAAMC,UAAU,GAAG,MAAM3C,gBAAgB,CAAC4C,aAAa,CAACF,KAAK,EAAE,CAAC,CAAC;MACjE7B,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE6B,UAAU,CAACE,MAAM,EAAE,SAAS,CAAC;MAEtF,IAAIF,UAAU,CAACE,MAAM,KAAK,CAAC,EAAE;QAC3BhC,OAAO,CAACiC,IAAI,CAAC,iDAAiD,CAAC;MACjE;MAEA,OAAO;QACLC,SAAS,EAAE,IAAI;QACfC,OAAO,EAAEL,UAAU;QACnBH,OAAO,EAAEA,OAAO;QAChBS,mBAAmB,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MAC9C,CAAC;IACH,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,MAAM,IAAInB,KAAK,CAAC,2EAA2E,CAAC;IAC9F;EACF;AACF;AAEA,MAAMoC,MAAM,GAAG,IAAI3C,gBAAgB,CAAC,CAAC;;AAErC;AACA,OAAO,eAAe4C,qBAAqBA,CAACX,KAAK,EAAEY,QAAQ,GAAG,IAAI,EAAE;EAClEzC,OAAO,CAACC,GAAG,CAAC,yEAAyE,CAAC;EACtFD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE4B,KAAK,CAAC;EAC9B7B,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEwC,QAAQ,CAAC;;EAEpC;EACA,MAAMC,WAAW,GAAGD,QAAQ,IAAIE,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAI,IAAI;;EAExE;EACA5C,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAE4B,KAAK,CAAC;EAC3E,IAAIgB,aAAa;EACjB,IAAI;IACFA,aAAa,GAAG,MAAMN,MAAM,CAACX,qBAAqB,CAACC,KAAK,EAAE,0BAA0B,CAAC;IACrF7B,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;EAC7D,CAAC,CAAC,OAAOqB,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;IAC5D,MAAM,IAAInB,KAAK,CAAC,8DAA8D0B,KAAK,EAAE,CAAC;EACxF;EAEA,MAAMiB,OAAO,GAAG;IACdC,EAAE,EAAE,oCAAoClB,KAAK;AACjD;AACA,0DAA0DA,KAAK;AAC/D;AACA;AACA;AACA,aAAaA,KAAK;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsDA,KAAK;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAWA,KAAK,EAAE;IAEdmB,EAAE,EAAE,wBAAwBnB,KAAK;AACrC;AACA,6DAA6DA,KAAK;AAClE;AACA;AACA;AACA,aAAaA,KAAK;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4CA,KAAK;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAWA,KAAK;EACd,CAAC;EAED,MAAMoB,MAAM,GAAGH,OAAO,CAACJ,WAAW,CAAC,IAAII,OAAO,CAACE,EAAE;;EAEjD;EACAhD,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;EACxE,IAAIiD,UAAU;EACd,IAAI;IACFA,UAAU,GAAG,MAAMX,MAAM,CAACnC,WAAW,CAAC,CACpC;MACE+C,IAAI,EAAE,QAAQ;MACdxB,OAAO,EAAEe,WAAW,KAAK,IAAI,GACzB,wLAAwL,GACxL;IACN,CAAC,EACD;MACES,IAAI,EAAE,MAAM;MACZxB,OAAO,EAAEsB,MAAM,GAAG,6DAA6DJ,aAAa,CAACV,OAAO,CAACiB,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,KAAK,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;IACnI,CAAC,CACF,EAAE,GAAG,CAAC,CAAC,CAAC;;IAETvD,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;;IAEhE;IACAD,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAC5D,MAAMuD,aAAa,GAAGN,UAAU,CAACO,IAAI,CAAC,CAAC;IACvC,MAAMC,SAAS,GAAGF,aAAa,CAACG,KAAK,CAAC,aAAa,CAAC;IAEpD,IAAI,CAACD,SAAS,EAAE;MACd,MAAM,IAAIvD,KAAK,CAAC,8CAA8C,CAAC;IACjE;IAEA,MAAMyD,IAAI,GAAG/C,IAAI,CAACgD,KAAK,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC;;IAErC;IACA1D,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IACrD,IAAI,CAAC2D,IAAI,CAACE,IAAI,IAAI,CAACC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACK,MAAM,CAAC,EAAE;MAC7C,MAAM,IAAI9D,KAAK,CAAC,0CAA0C,CAAC;IAC7D;IAEA,IAAIyD,IAAI,CAACK,MAAM,CAACjC,MAAM,KAAK,CAAC,EAAE;MAC5B,MAAM,IAAI7B,KAAK,CAAC,uCAAuC,CAAC;IAC1D;;IAEA;IACAH,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;IAC3D,MAAMiE,eAAe,GAAG,MAAM3B,MAAM,CAACX,qBAAqB,CACxD,GAAGC,KAAK,0BAA0B,EAClChB,IAAI,CAACC,SAAS,CAAC8C,IAAI,CACrB,CAAC;;IAED;IACAA,IAAI,CAACO,WAAW,GAAG;MACjBrC,UAAU,EAAEoC,eAAe,CAAC/B,OAAO;MACnCiC,WAAW,EAAEF,eAAe,CAAC9B,mBAAmB;MAChDiC,OAAO,EAAE/E,KAAK;MACdgF,UAAU,EAAE;IACd,CAAC;IAEDtE,OAAO,CAACC,GAAG,CAAC,oEAAoE,CAAC;IACjF,OAAO2D,IAAI;EACb,CAAC,CAAC,OAAOtC,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;IAExD;IACA,MAAMiD,SAAS,GAAG;MAChBxB,EAAE,EAAE;QACFe,IAAI,EAAEjC,KAAK;QACXoC,MAAM,EAAE,CACN;UACEO,IAAI,EAAE,cAAc;UACpBC,SAAS,EAAE,qCAAqC5C,KAAK,EAAE;UACvD6C,KAAK,EAAE,IAAI;UACXC,YAAY,EAAE,CAAC,oBAAoB,EAAE,iBAAiB,EAAE,gBAAgB;QAC1E,CAAC,EACD;UACEH,IAAI,EAAE,WAAW;UACjBC,SAAS,EAAE,iDAAiD5C,KAAK,EAAE;UACnE6C,KAAK,EAAE,IAAI;UACXC,YAAY,EAAE,CAAC,iBAAiB,EAAE,uBAAuB,EAAE,eAAe;QAC5E,CAAC,EACD;UACEH,IAAI,EAAE,mBAAmB;UACzBC,SAAS,EAAE,wCAAwC5C,KAAK,EAAE;UAC1D6C,KAAK,EAAE,IAAI;UACXC,YAAY,EAAE,CAAC,cAAc,EAAE,mBAAmB,EAAE,gBAAgB;QACtE,CAAC;MAEL,CAAC;MACD3B,EAAE,EAAE;QACFc,IAAI,EAAEjC,KAAK;QACXoC,MAAM,EAAE,CACN;UACEO,IAAI,EAAE,cAAc;UACpBC,SAAS,EAAE,oCAAoC5C,KAAK,EAAE;UACtD6C,KAAK,EAAE,IAAI;UACXC,YAAY,EAAE,CAAC,eAAe,EAAE,gBAAgB,EAAE,cAAc;QAClE,CAAC,EACD;UACEH,IAAI,EAAE,cAAc;UACpBC,SAAS,EAAE,2CAA2C5C,KAAK,EAAE;UAC7D6C,KAAK,EAAE,IAAI;UACXC,YAAY,EAAE,CAAC,iBAAiB,EAAE,uBAAuB,EAAE,cAAc;QAC3E,CAAC,EACD;UACEH,IAAI,EAAE,iBAAiB;UACvBC,SAAS,EAAE,sCAAsC5C,KAAK,EAAE;UACxD6C,KAAK,EAAE,IAAI;UACXC,YAAY,EAAE,CAAC,cAAc,EAAE,gBAAgB,EAAE,cAAc;QACjE,CAAC;MAEL;IACF,CAAC;IAED,OAAOJ,SAAS,CAAC7B,WAAW,CAAC,IAAI6B,SAAS,CAACvB,EAAE;EAC/C;AACF;;AAEA;AACA,OAAO,eAAe4B,eAAeA,CAAC/C,KAAK,EAAEgD,MAAM,EAAEC,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE;EACnE9E,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;EAC/ED,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE4B,KAAK,CAAC;EAC9B7B,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE4E,MAAM,CAACL,IAAI,CAAC;EACrCxE,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE6E,KAAK,CAAC;;EAE9B;EACA,MAAMpC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAI,IAAI;;EAE5D;EACA5C,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;EAC3E,MAAM8E,YAAY,GAAG,GAAGlD,KAAK,IAAIgD,MAAM,CAACL,IAAI,EAAE;EAC9C,IAAI3B,aAAa;EACjB,IAAI;IACFA,aAAa,GAAG,MAAMN,MAAM,CAACX,qBAAqB,CAACmD,YAAY,EAAE,0BAA0B,CAAC;IAC5F/E,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE4C,aAAa,CAACV,OAAO,CAACH,MAAM,EAAE,aAAa,CAAC;IAEpG,IAAIa,aAAa,CAACV,OAAO,CAACH,MAAM,KAAK,CAAC,EAAE;MACtC,MAAM,IAAI7B,KAAK,CAAC,qCAAqC,CAAC;IACxD;EACF,CAAC,CAAC,OAAOmB,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;IACpE,MAAM,IAAInB,KAAK,CAAC,gEAAgE4E,YAAY,EAAE,CAAC;EACjG;EAEA,MAAMC,gBAAgB,GAAG;IACvBjC,EAAE,EAAE;MACF;MACA,IAAI,EAAE,gLAAgL;MAEtL,IAAI,EAAE,sMAAsM;MAE5M,KAAK,EAAE,6LAA6L;MAEpM,IAAI,EAAE,mKAAmK;MAEzK,IAAI,EAAE,yLAAyL;MAE/L,MAAM,EAAE,8JAA8J;MAEtK,MAAM,EAAE,yJAAyJ;MAEjK;MACA,OAAO,EAAE,wJAAwJ;MAEjK,MAAM,EAAE,8LAA8L;MAEtM,OAAO,EAAE,+LAA+L;MAExM,OAAO,EAAE,mLAAmL;MAE5L;MACA,OAAO,EAAE,+LAA+L;MAExM,OAAO,EAAE,qKAAqK;MAE9K,OAAO,EAAE,qKAAqK;MAE9K;MACA,KAAK,EAAE,yLAAyL;MAEhM;MACA,OAAO,EAAE;IACX,CAAC;IACDC,EAAE,EAAE;MACF;MACA,IAAI,EAAE,wLAAwL;MAC9L,IAAI,EAAE,uMAAuM;MAC7M,KAAK,EAAE,yMAAyM;MAChN,IAAI,EAAE,wKAAwK;MAC9K,IAAI,EAAE,0MAA0M;MAChN,MAAM,EAAE,8KAA8K;MACtL,MAAM,EAAE,0JAA0J;MAClK,OAAO,EAAE,oJAAoJ;MAC7J,MAAM,EAAE,+LAA+L;MACvM,OAAO,EAAE,0MAA0M;MACnN,OAAO,EAAE,uLAAuL;MAChM,OAAO,EAAE,qMAAqM;MAC9M,OAAO,EAAE,kLAAkL;MAC3L,OAAO,EAAE,sKAAsK;MAC/K,KAAK,EAAE,4LAA4L;MACnM,OAAO,EAAE;IACX;EACF,CAAC;EAED,MAAMiC,uBAAuB,GAAGD,gBAAgB,CAACtC,WAAW,CAAC,IAAIsC,gBAAgB,CAACjC,EAAE;;EAEpF;EACA,MAAMmC,oBAAoB,GAAGJ,KAAK,CAAC1B,GAAG,CAAC+B,IAAI,IAAIF,uBAAuB,CAACE,IAAI,CAAC,IAAI,EAAE,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;EACnG,MAAMC,oBAAoB,GAAGJ,oBAAoB,CAAC3B,IAAI,CAAC,GAAG,CAAC;;EAE3D;EACA,MAAMgC,eAAe,GAAG;IACtBxC,EAAE,EAAE;MACFyC,UAAU,EAAE,qBAAqB3D,KAAK,yCAAyCgD,MAAM,CAACL,IAAI,mBAAmB3C,KAAK,IAAI;MACtH4D,WAAW,EAAE,sBAAsBZ,MAAM,CAACJ,SAAS,EAAE;MACrDiB,aAAa,EAAEb,MAAM,CAACF,YAAY,GAAG,iBAAiBE,MAAM,CAACF,YAAY,CAACpB,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE;MAC3FoC,SAAS,EAAE;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,8DAA8D;MACxDnF,QAAQ,EAAE;IACZ,CAAC;IACDwC,EAAE,EAAE;MACFwC,UAAU,EAAE,wBAAwB3D,KAAK,sCAAsCgD,MAAM,CAACL,IAAI,wBAAwB3C,KAAK,IAAI;MAC3H4D,WAAW,EAAE,uBAAuBZ,MAAM,CAACJ,SAAS,EAAE;MACtDiB,aAAa,EAAEb,MAAM,CAACF,YAAY,GAAG,kBAAkBE,MAAM,CAACF,YAAY,CAACpB,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE;MAC5FoC,SAAS,EAAE;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD;MAC9CnF,QAAQ,EAAE;IACZ;EACF,CAAC;EAED,MAAMoF,cAAc,GAAGL,eAAe,CAAC7C,WAAW,CAAC,IAAI6C,eAAe,CAACxC,EAAE;;EAEzE;EACA/C,OAAO,CAACC,GAAG,CAAC,oEAAoE,CAAC;EACjF,IAAI4F,oBAAoB,GAAG,EAAE;EAC7B,IAAIC,cAAc,GAAG,EAAE;EAEvB,IAAI;IACF;IACA,MAAMC,WAAW,GAAGrD,WAAW,KAAK,IAAI,GACpC,GAAGb,KAAK,IAAIgD,MAAM,CAACL,IAAI,SAAS,GAChC,GAAG3C,KAAK,IAAIgD,MAAM,CAACL,IAAI,EAAE;IAE7BqB,oBAAoB,GAAG,MAAM1G,gBAAgB,CAAC4C,aAAa,CAACgE,WAAW,EAAE,CAAC,CAAC;IAC3E/F,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE4F,oBAAoB,CAAC7D,MAAM,EAAE,SAAS,CAAC;;IAE5F;IACA,MAAMgE,aAAa,GAAG,CAAC,GAAGnD,aAAa,CAACV,OAAO,EAAE,GAAG0D,oBAAoB,CAAC;IAEzE,IAAIG,aAAa,CAAChE,MAAM,GAAG,CAAC,EAAE;MAC5B,MAAMiE,WAAW,GAAGvD,WAAW,KAAK,IAAI,GACpC,oEAAoEsD,aAAa,CAAC5C,GAAG,CAAC8C,MAAM,IAC1F,KAAKA,MAAM,CAAC5C,KAAK,KAAK4C,MAAM,CAACT,WAAW,KAAKS,MAAM,CAACA,MAAM,GAC5D,CAAC,CAAC3C,IAAI,CAAC,IAAI,CAAC,sHAAsH,GAClI,kEAAkEyC,aAAa,CAAC5C,GAAG,CAAC8C,MAAM,IACxF,KAAKA,MAAM,CAAC5C,KAAK,KAAK4C,MAAM,CAACT,WAAW,KAAKS,MAAM,CAACA,MAAM,GAC5D,CAAC,CAAC3C,IAAI,CAAC,IAAI,CAAC,+GAA+G;MAE/HuC,cAAc,GAAGG,WAAW;IAC9B,CAAC,MAAM;MACL,MAAM,IAAI9F,KAAK,CAAC,2DAA2D,CAAC;IAC9E;EACF,CAAC,CAAC,OAAOmB,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;IACjE,MAAM,IAAInB,KAAK,CAAC,+DAA+D,CAAC;EAClF;EAEA,MAAM8C,MAAM,GAAG,GAAG2C,cAAc,CAACJ,UAAU;AAC7C;AACA,EAAEI,cAAc,CAACH,WAAW;AAC5B,EAAEG,cAAc,CAACF,aAAa;AAC9B;AACA,EAAEJ,oBAAoB;AACtB;AACA,EAAEM,cAAc,CAACD,SAAS;AAC1B;AACA,EAAEG,cAAc;AAChB;AACA,EAAEF,cAAc,CAACpF,QAAQ,EAAE;;EAEzB;EACAR,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;EAC/E,IAAIiD,UAAU;EACd,IAAI;IACF;IACAA,UAAU,GAAG,MAAMX,MAAM,CAACnC,WAAW,CAAC,CACpC;MACE+C,IAAI,EAAE,QAAQ;MACdxB,OAAO,EAAEe,WAAW,KAAK,IAAI,GACzB,kQAAkQ,GAClQ;IACN,CAAC,EACD;MACES,IAAI,EAAE,MAAM;MACZxB,OAAO,EAAEsB;IACX,CAAC,CACF,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEfjD,OAAO,CAACC,GAAG,CAAC,4DAA4D,EAAEiD,UAAU,CAAClB,MAAM,CAAC;;IAE5F;IACA,MAAMwB,aAAa,GAAG2C,YAAY,CAAC1C,IAAI,CAAC,CAAC;IACzC,MAAMC,SAAS,GAAGF,aAAa,CAACG,KAAK,CAAC,aAAa,CAAC;IAEpD,IAAI,CAACD,SAAS,EAAE;MACd,MAAM,IAAIvD,KAAK,CAAC,iCAAiC,CAAC;IACpD;IAEA,MAAMiG,OAAO,GAAGvF,IAAI,CAACgD,KAAK,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC;;IAExC;IACA,IAAI,CAAC0C,OAAO,CAACC,KAAK,IAAI,CAACD,OAAO,CAACE,QAAQ,EAAE;MACvC,MAAM,IAAInG,KAAK,CAAC,2BAA2B,CAAC;IAC9C;;IAEA;IACA,IAAI2B,UAAU,CAACE,MAAM,GAAG,CAAC,EAAE;MACzB,MAAMuE,WAAW,GAAGpH,gBAAgB,CAACqH,iBAAiB,CAAC1E,UAAU,CAAC;MAClE,MAAM2E,YAAY,GAAG/D,WAAW,KAAK,IAAI,GAAG,0CAA0C,GAAG,sCAAsC;MAC/H0D,OAAO,CAACE,QAAQ,IAAIG,YAAY,GAAGF,WAAW;MAC9CH,OAAO,CAACtE,UAAU,GAAGA,UAAU;IACjC;IAEA9B,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;IAC7D,OAAOmG,OAAO;EAEhB,CAAC,CAAC,OAAO9E,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;;IAE3D;IACA,MAAMoF,eAAe,GAAGhE,WAAW,KAAK,IAAI,GAAG;MAC7C2D,KAAK,EAAE,GAAGxB,MAAM,CAACL,IAAI,MAAM3C,KAAK,EAAE;MAClCyE,QAAQ,EAAE,KAAKzB,MAAM,CAACL,IAAI;AAChC;AACA,8BAA8BK,MAAM,CAACL,IAAI,iBAAiB3C,KAAK;AAC/D;AACA;AACA,EAAEgD,MAAM,CAACJ,SAAS;AAClB;AACA;AACA,cAAcI,MAAM,CAACL,IAAI,qCAAqC3C,KAAK;AACnE;AACA;AACA,iBAAiBgD,MAAM,CAACL,IAAI;AAC5B;AACA;AACA,gHAAgH;MAC1GG,YAAY,EAAEE,MAAM,CAACF,YAAY,IAAI,EAAE;MACvCG,KAAK,EAAEA,KAAK;MACZ6B,OAAO,EAAE,GAAG9E,KAAK,MAAMgD,MAAM,CAACL,IAAI;IACpC,CAAC,GAAG;MACF6B,KAAK,EAAE,GAAGxB,MAAM,CAACL,IAAI,MAAM3C,KAAK,EAAE;MAClCyE,QAAQ,EAAE,KAAKzB,MAAM,CAACL,IAAI;AAChC;AACA,wBAAwBK,MAAM,CAACL,IAAI,sBAAsB3C,KAAK;AAC9D;AACA;AACA,EAAEgD,MAAM,CAACJ,SAAS;AAClB;AACA;AACA,gBAAgBI,MAAM,CAACL,IAAI,+BAA+B3C,KAAK;AAC/D;AACA;AACA,kBAAkBgD,MAAM,CAACL,IAAI;AAC7B;AACA;AACA,yFAAyF;MACnFG,YAAY,EAAEE,MAAM,CAACF,YAAY,IAAI,EAAE;MACvCG,KAAK,EAAEA,KAAK;MACZ6B,OAAO,EAAE,GAAG9E,KAAK,MAAMgD,MAAM,CAACL,IAAI;IACpC,CAAC;IAED,OAAOkC,eAAe;EACxB;AACF;;AAEA;AACA,OAAO,eAAeE,cAAcA,CAAA,EAAG;EACrC,IAAI;IACF,MAAMpG,QAAQ,GAAG,MAAM+B,MAAM,CAACnC,WAAW,CAAC,CACxC;MACE+C,IAAI,EAAE,MAAM;MACZxB,OAAO,EAAE;IACX,CAAC,CACF,CAAC;IAEF,OAAOnB,QAAQ,CAACqG,QAAQ,CAAC,YAAY,CAAC;EACxC,CAAC,CAAC,OAAOvF,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACnD,OAAO,KAAK;EACd;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}