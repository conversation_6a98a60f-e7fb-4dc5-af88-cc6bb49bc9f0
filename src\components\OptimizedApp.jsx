import React, { useState, useEffect, useRef } from 'react';
import '../styles/optimized.css';
import gestureService, { createFlagWheel } from '../services/gestureService';
import speechService from '../services/speechService';
import exportService from '../services/exportService';
import gamificationService from '../services/optimizedGamificationService';
import { generateKnowledgeTree as generateTreeAPI, generateArticle as generateArticleAPI, testConnection } from '../services/openRouterService';
import tabService from '../services/tabService';
import webSearchService from '../services/webSearchService';
import TabManager from './TabManager';
import LanguageSwitcher from './LanguageSwitcher';
import { useTranslation, getCurrentLanguage } from '../utils/i18n';

// Optimized Knowledge Tree Explorer - Pareto 80/20 Implementation
// Focus on core functionality with maximum impact

const OptimizedApp = () => {
  // Core state - now managed by tabs
  const [currentView, setCurrentView] = useState('input'); // 'input', 'tree', 'article'
  const [topic, setTopic] = useState('');
  const [activeTab, setActiveTab] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [user, setUser] = useState(null);
  const appRef = useRef(null);

  // Canvas state for infinite tree view - CENTERED INITIALIZATION
  const [canvasTransform, setCanvasTransform] = useState({ x: -1000, y: -1000, scale: 1 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);
  const [errorMessage, setErrorMessage] = useState(null);

  // Get current tab data
  const tree = activeTab?.tree || null;
  const selectedBranch = activeTab?.selectedBranch || null;
  const article = activeTab?.article || null;

  // Translation hook
  const { t } = useTranslation();

  // Available flags for the optimized version
  const availableFlags = React.useMemo(() => [
    { code: '-a', name: 'Article', description: t('flagArticle') },
    { code: '-ex', name: 'Examples', description: t('flagExamples') },
    { code: '-q', name: 'Quiz', description: t('flagQuiz') },
    { code: '-vis', name: 'Visual', description: t('flagVisual') },
    { code: '-path', name: 'Learning Path', description: t('flagPath') },
    { code: '-case', name: 'Case Study', description: t('flagCase') },
    { code: '-ro', name: 'Romanian', description: t('flagRomanian') }
  ], [t]);

  // Generate article with tabs support
  const generateArticleForBranch = React.useCallback(async (branch, flags = ['-a']) => {
    if (!activeTab) return;

    setIsLoading(true);

    // Set tab to loading state (yellow)
    tabService.updateTabStatus(activeTab.id, 'loading', {
      selectedBranch: branch,
      article: null
    });
    setActiveTab(tabService.getTab(activeTab.id));

    try {
      console.log('📄 Generating article for branch:', branch.nume);
      const articleData = await generateArticleAPI(activeTab.topic, branch, flags);

      console.log('✅ Generated article data:', articleData);

      // Map the article data to expected format
      const mappedArticle = {
        title: articleData.titlu || articleData.title || `${branch.nume} - ${activeTab.topic}`,
        content: articleData.continut || articleData.content || 'Content not available',
        topic: activeTab.topic,
        flags: flags,
        position: articleData.pozitie || `${activeTab.topic} → ${branch.nume}`,
        webSources: articleData.webSources || []
      };

      // Update tab with article and set to completed (green)
      tabService.updateTabStatus(activeTab.id, 'completed', {
        selectedBranch: branch,
        article: mappedArticle
      });

      const updatedTab = tabService.getTab(activeTab.id);
      setActiveTab(updatedTab);
      setCurrentView('article');

      // Award points for article generation
      const result = gamificationService.awardPoints('ARTICLE_GENERATED');
      if (result.newAchievements.length > 0) {
        result.newAchievements.forEach(achievement => {
          gamificationService.showAchievementNotification(achievement);
        });
      }
    } catch (error) {
      console.error('❌ Error generating article:', error);
      setError('Failed to generate article. Please try again.');

      // Set tab back to pending on error
      tabService.updateTabStatus(activeTab.id, 'pending', {
        selectedBranch: branch,
        article: null
      });
      setActiveTab(tabService.getTab(activeTab.id));
    } finally {
      setIsLoading(false);
    }
  }, [activeTab]);

  // Handle branch selection (single tap)
  const handleBranchSelect = React.useCallback((branch) => {
    if (activeTab) {
      tabService.updateTabStatus(activeTab.id, activeTab.status, { selectedBranch: branch });
      setActiveTab(tabService.getTab(activeTab.id));
    }
  }, [activeTab]);

  // Gesture handlers
  const handleDoubleTap = React.useCallback((event, targetInfo) => {
    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {
      // Show flag wheel on double tap of branch
      const branch = tree.ramuri[targetInfo.branchData.index];
      if (branch) {
        createFlagWheel(
          targetInfo.position,
          availableFlags,
          (selectedFlags) => {
            console.log('Selected flags:', selectedFlags);
          },
          (selectedFlags) => {
            generateArticleForBranch(branch, selectedFlags);
          }
        );
      }
    }
  }, [tree, availableFlags, generateArticleForBranch]);

  const handleSingleTap = React.useCallback((event, targetInfo) => {
    // Single tap for normal selection
    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {
      const branch = tree.ramuri[targetInfo.branchData.index];
      if (branch) {
        handleBranchSelect(branch);
      }
    }
  }, [tree, handleBranchSelect]);

  // Expand branch to create relevant sub-branches with AI
  const expandBranch = React.useCallback(async (branch, branchIndex) => {
    if (!activeTab || !tree) {
      setError(t('noActiveTab') || 'No active tab or tree available');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log('🌿 Expanding branch:', branch.nume, 'with AI-generated sub-branches');

      // Create AI-generated sub-branches specifically for this branch
      const currentLang = getCurrentLanguage();
      const prompt = currentLang === 'ro'
        ? `Generează 4 sub-ramuri specifice și relevante pentru "${branch.nume}" în contextul "${tree.tema}".

Descrierea ramuri principale: ${branch.descriere}

Creează sub-ramuri care să fie:
- Specifice și relevante pentru "${branch.nume}"
- Logice și bine organizate
- Utile pentru învățare progresivă
- În limba română

Răspunde DOAR cu JSON în formatul:
{
  "ramuri": [
    {
      "nume": "Nume sub-ramură",
      "descriere": "Descriere detaliată",
      "emoji": "🔧",
      "subcategorii": ["subcategorie1", "subcategorie2", "subcategorie3"]
    }
  ]
}`
        : `Generate 4 specific and relevant sub-branches for "${branch.nume}" in the context of "${tree.tema}".

Main branch description: ${branch.descriere}

Create sub-branches that are:
- Specific and relevant to "${branch.nume}"
- Logical and well-organized
- Useful for progressive learning
- In English

Respond ONLY with JSON in the format:
{
  "branches": [
    {
      "nume": "Sub-branch name",
      "descriere": "Detailed description",
      "emoji": "🔧",
      "subcategorii": ["subcategory1", "subcategory2", "subcategory3"]
    }
  ]
}`;

      // CRITICAL: Use STRICT AI + Web validation for sub-branches
      console.log('🔒 STRICT: Starting sub-branch generation with mandatory validation');

      // STEP 1: Web validation BEFORE AI generation
      let webValidation = [];
      try {
        webValidation = await webSearchService.searchSources(`${tree.tema} ${branch.nume} sub-topics`, 5);
        console.log('✅ VERIFIED: Web validation completed with', webValidation.length, 'sources');
      } catch (webError) {
        console.warn('⚠️ Web validation failed, continuing with AI generation:', webError);
        webValidation = []; // Continue without web sources
      }

      // STEP 2: AI generation with DeepSeek R1 + web context
      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer 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`,
          'Content-Type': 'application/json',
          'HTTP-Referer': window.location.origin,
          'X-Title': 'Knowledge Tree Explorer'
        },
        body: JSON.stringify({
          model: 'deepseek/deepseek-r1-0528:free',
          messages: [
            {
              role: 'system',
              content: currentLang === 'ro'
                ? 'Ești un expert în organizarea cunoștințelor. Generează sub-ramuri relevante și specifice în format JSON valid.'
                : 'You are an expert in knowledge organization. Generate relevant and specific sub-branches in valid JSON format.'
            },
            {
              role: 'user',
              content: prompt + (webValidation.length > 0 ? `\n\nSurse web de referință:\n${webValidation.map(s => `- ${s.title}: ${s.description || s.snippet || 'No description'}`).join('\n')}` : '')
            }
          ],
          temperature: 0.7,
          max_tokens: 2000
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ API Error Response:', errorText);
        throw new Error(`API request failed: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log('🔍 Raw API Response:', data);

      const responseText = data.choices[0]?.message?.content || '';
      console.log('📝 Response Text:', responseText);

      // Parse the JSON response
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        console.warn('⚠️ No JSON found, using fallback sub-branches');
        // Fallback: generate simple sub-branches
        const fallbackBranches = [
          {
            nume: `${branch.nume} - Fundamentele`,
            descriere: `Concepte de bază și principii fundamentale pentru ${branch.nume}`,
            emoji: "📚",
            subcategorii: ["Definiții", "Principii", "Concepte"]
          },
          {
            nume: `${branch.nume} - Aplicații Practice`,
            descriere: `Aplicații și exemple practice pentru ${branch.nume}`,
            emoji: "🔧",
            subcategorii: ["Exemple", "Cazuri de studiu", "Exerciții"]
          },
          {
            nume: `${branch.nume} - Aspecte Avansate`,
            descriere: `Aspecte avansate și specializate pentru ${branch.nume}`,
            emoji: "🎯",
            subcategorii: ["Tehnici avansate", "Specializări", "Cercetare"]
          }
        ];

        // Update tree with fallback sub-branches
        const newTree = { ...tree };
        newTree.ramuri = [
          ...newTree.ramuri.slice(0, branchIndex + 1),
          ...fallbackBranches.map(subBranch => ({
            ...subBranch,
            isSubBranch: true,
            parentBranch: branch.nume,
            level: (branch.level || 0) + 1,
            id: `${branch.nume}-${subBranch.nume}`.replace(/\s+/g, '-').toLowerCase()
          })),
          ...newTree.ramuri.slice(branchIndex + 1)
        ];

        // Update tab with expanded tree
        tabService.updateTabStatus(activeTab.id, 'completed', { tree: newTree });
        setActiveTab(tabService.getTab(activeTab.id));
        console.log('🌳 Tree expanded successfully with fallback sub-branches');
        return;
      }

      const expandedData = JSON.parse(jsonMatch[0]);
      const subBranches = expandedData.ramuri || expandedData.branches || [];

      if (subBranches.length === 0) {
        throw new Error('No sub-branches generated');
      }

      console.log('✅ Generated', subBranches.length, 'AI sub-branches for:', branch.nume);

      // Update tree with AI-generated sub-branches
      const newTree = { ...tree };
      newTree.ramuri = [
        ...newTree.ramuri.slice(0, branchIndex + 1),
        ...subBranches.map(subBranch => ({
          ...subBranch,
          isSubBranch: true,
          parentBranch: branch.nume,
          level: (branch.level || 0) + 1,
          id: `${branch.nume}-${subBranch.nume}`.replace(/\s+/g, '-').toLowerCase()
        })),
        ...newTree.ramuri.slice(branchIndex + 1)
      ];

      // Update tab with expanded tree
      tabService.updateTabStatus(activeTab.id, 'completed', { tree: newTree });
      setActiveTab(tabService.getTab(activeTab.id));

      console.log('🌳 Tree expanded successfully with AI sub-branches');

      // Award points for branch expansion
      const result = gamificationService.awardPoints('BRANCH_EXPANDED');
      if (result.newAchievements.length > 0) {
        result.newAchievements.forEach(achievement => {
          gamificationService.showAchievementNotification(achievement);
        });
      }

    } catch (error) {
      console.error('❌ Error expanding branch with AI:', error);
      setError(t('failedToExpand') || 'Nu s-a putut extinde ramura. Te rugăm să încerci din nou.');
    } finally {
      setIsLoading(false);
    }
  }, [tree, activeTab, t]);

  const handleLongPress = React.useCallback(async (event, targetInfo) => {
    // Long press to expand branch and create tree effect
    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {
      const branch = tree.ramuri[targetInfo.branchData.index];
      if (branch) {
        await expandBranchWithErrorHandling(branch, targetInfo.branchData.index);
      }
    }
  }, [tree, expandBranchWithErrorHandling]);

  // Canvas control functions
  const zoomCanvas = React.useCallback((factor) => {
    setCanvasTransform(prev => ({
      ...prev,
      scale: Math.max(0.1, Math.min(3, prev.scale * factor))
    }));
  }, []);

  const resetCanvasView = React.useCallback(() => {
    // Center the large canvas properly in viewport
    const container = document.querySelector('.desktop-tree-view');
    if (container) {
      const containerRect = container.getBoundingClientRect();
      const canvasSize = 3000; // Match CSS canvas size

      setCanvasTransform({
        x: (containerRect.width - canvasSize) / 2,
        y: (containerRect.height - canvasSize) / 2,
        scale: 1
      });
    } else {
      setCanvasTransform({ x: -1000, y: -1000, scale: 1 }); // Fallback centering
    }
  }, []);

  // Pan & Drag functionality - using existing state
  const [dragOffset, setDragOffset] = React.useState({ x: 0, y: 0 });

  const handleMouseDown = React.useCallback((e) => {
    // Only start drag if not clicking on interactive elements
    if (e.target.closest('.tree-branch-node') || e.target.closest('.central-topic-node')) {
      return;
    }

    setIsDragging(true);
    setDragStart({ x: e.clientX, y: e.clientY });
    setDragOffset({ x: canvasTransform.x, y: canvasTransform.y });
    e.preventDefault();
  }, [canvasTransform, setIsDragging, setDragStart]);

  const handleMouseMove = React.useCallback((e) => {
    if (!isDragging) return;

    const deltaX = e.clientX - dragStart.x;
    const deltaY = e.clientY - dragStart.y;

    setCanvasTransform(prev => ({
      ...prev,
      x: dragOffset.x + deltaX,
      y: dragOffset.y + deltaY
    }));
  }, [isDragging, dragStart, dragOffset]);

  const handleMouseUp = React.useCallback(() => {
    setIsDragging(false);
  }, []);

  // Touch events for mobile drag
  const handleTouchStart = React.useCallback((e) => {
    if (e.target.closest('.tree-branch-node') || e.target.closest('.central-topic-node')) {
      return;
    }

    const touch = e.touches[0];
    setIsDragging(true);
    setDragStart({ x: touch.clientX, y: touch.clientY });
    setDragOffset({ x: canvasTransform.x, y: canvasTransform.y });
    e.preventDefault();
  }, [canvasTransform, setIsDragging, setDragStart]);

  const handleTouchMove = React.useCallback((e) => {
    if (!isDragging) return;

    const touch = e.touches[0];
    const deltaX = touch.clientX - dragStart.x;
    const deltaY = touch.clientY - dragStart.y;

    setCanvasTransform(prev => ({
      ...prev,
      x: dragOffset.x + deltaX,
      y: dragOffset.y + deltaY
    }));
    e.preventDefault();
  }, [isDragging, dragStart, dragOffset]);

  const handleTouchEnd = React.useCallback(() => {
    setIsDragging(false);
  }, []);

  const handleCanvasMouseDown = React.useCallback((e) => {
    if (e.button === 0) { // Left mouse button
      setIsDragging(true);
      setDragStart({ x: e.clientX - canvasTransform.x, y: e.clientY - canvasTransform.y });
    }
  }, [canvasTransform]);

  const handleCanvasMouseMove = React.useCallback((e) => {
    if (isDragging) {
      setCanvasTransform(prev => ({
        ...prev,
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      }));
    }
  }, [isDragging, dragStart]);

  const handleCanvasMouseUp = React.useCallback(() => {
    setIsDragging(false);
  }, []);

  const handleCanvasWheel = React.useCallback((e) => {
    e.preventDefault();
    const factor = e.deltaY > 0 ? 0.9 : 1.1;
    zoomCanvas(factor);
  }, [zoomCanvas]);

  // Handle window resize for mobile detection
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Initialize TikTok scroll behavior on mobile
  useEffect(() => {
    const tiktokContainer = document.getElementById('tiktok-scroll');
    if (tiktokContainer && isMobile) {
      // Smooth scroll behavior
      tiktokContainer.style.scrollBehavior = 'smooth';

      // Optional: Add snap scrolling enhancement
      let isScrolling = false;
      const handleScroll = () => {
        if (!isScrolling) {
          isScrolling = true;
          setTimeout(() => {
            isScrolling = false;
          }, 150);
        }
      };

      tiktokContainer.addEventListener('scroll', handleScroll);

      return () => {
        tiktokContainer.removeEventListener('scroll', handleScroll);
      };
    }
  }, [isMobile]);

  // Initialize canvas event listeners
  useEffect(() => {
    const canvas = document.getElementById('infinite-canvas');
    if (canvas && !isMobile) {
      // Apply transform
      canvas.style.transform = `translate(${canvasTransform.x}px, ${canvasTransform.y}px) scale(${canvasTransform.scale})`;

      canvas.addEventListener('mousedown', handleCanvasMouseDown);
      canvas.addEventListener('wheel', handleCanvasWheel);

      // Add global mouse events for dragging
      const handleGlobalMouseMove = (e) => {
        if (isDragging) {
          handleCanvasMouseMove(e);
        }
      };

      const handleGlobalMouseUp = () => {
        if (isDragging) {
          handleCanvasMouseUp();
        }
      };

      document.addEventListener('mousemove', handleGlobalMouseMove);
      document.addEventListener('mouseup', handleGlobalMouseUp);

      return () => {
        canvas.removeEventListener('mousedown', handleCanvasMouseDown);
        canvas.removeEventListener('wheel', handleCanvasWheel);
        document.removeEventListener('mousemove', handleGlobalMouseMove);
        document.removeEventListener('mouseup', handleGlobalMouseUp);
      };
    }
  }, [canvasTransform, isMobile, isDragging, handleCanvasMouseDown, handleCanvasMouseMove, handleCanvasMouseUp, handleCanvasWheel]);

  // Initialize canvas centering when tree loads
  useEffect(() => {
    if (tree && !isMobile) {
      // Delay to ensure DOM is ready
      setTimeout(() => {
        resetCanvasView();
      }, 100);
    }
  }, [tree, isMobile, resetCanvasView]);

  // Error message handler
  const showError = React.useCallback((message) => {
    setErrorMessage(message);
    setTimeout(() => setErrorMessage(null), 5000); // Auto-hide after 5 seconds
  }, []);

  // Enhanced expand branch with error handling
  const expandBranchWithErrorHandling = React.useCallback(async (branch, index) => {
    try {
      await expandBranch(branch, index);
    } catch (error) {
      console.error('Failed to expand branch:', error);
      showError('⚠️ Nu s-a putut extinde ramura. Te rugăm să încerci din nou.');
    }
  }, [expandBranch, showError]);

  // Initialize services and authentication
  useEffect(() => {
    const storedUser = localStorage.getItem('user');
    const bypassSecurity = localStorage.getItem('bypassSecurity');

    if (storedUser || bypassSecurity) {
      const userData = {
        id: 'user-1',
        name: 'User',
        subscriptionTier: 'premium'
      };
      setUser(userData);

      // Award daily login points
      const result = gamificationService.awardPoints('DAILY_LOGIN');
      if (result.newAchievements.length > 0) {
        result.newAchievements.forEach(achievement => {
          gamificationService.showAchievementNotification(achievement);
        });
      }
    }

    // Initialize gesture service
    if (appRef.current) {
      gestureService.init(appRef.current, {
        doubleTap: handleDoubleTap,
        singleTap: handleSingleTap,
        longPress: handleLongPress
      });
    }

    // Add drag event listeners
    const handleGlobalMouseMove = (e) => handleMouseMove(e);
    const handleGlobalMouseUp = (e) => handleMouseUp(e);
    const handleGlobalTouchMove = (e) => handleTouchMove(e);
    const handleGlobalTouchEnd = (e) => handleTouchEnd(e);

    document.addEventListener('mousemove', handleGlobalMouseMove);
    document.addEventListener('mouseup', handleGlobalMouseUp);
    document.addEventListener('touchmove', handleGlobalTouchMove, { passive: false });
    document.addEventListener('touchend', handleGlobalTouchEnd);

    return () => {
      gestureService.destroy();
      document.removeEventListener('mousemove', handleGlobalMouseMove);
      document.removeEventListener('mouseup', handleGlobalMouseUp);
      document.removeEventListener('touchmove', handleGlobalTouchMove);
      document.removeEventListener('touchend', handleGlobalTouchEnd);
    };
  }, [handleDoubleTap, handleSingleTap, handleLongPress, handleMouseMove, handleMouseUp, handleTouchMove, handleTouchEnd]);

  // Initialize gamification UI when user is logged in
  useEffect(() => {
    if (user) {
      const container = document.getElementById('gamification-container');
      if (container) {
        // Clear existing content
        container.innerHTML = '';
        // Create gamification UI
        gamificationService.createGamificationUI(container);
      }

      // Test API connection
      testConnection().then(isConnected => {
        console.log('🔌 API Connection Status:', isConnected ? '✅ Connected' : '❌ Failed');
        if (!isConnected) {
          console.warn('⚠️ API connection failed. Check your API key and internet connection.');
        }
      }).catch(error => {
        console.error('❌ API connection test error:', error);
      });
    }
  }, [user]);

  // Core API call - using optimized service with tabs
  const generateKnowledgeTree = async (topicInput, tabId = null) => {
    let currentTabId = tabId;

    // Create new tab if none provided
    if (!currentTabId) {
      try {
        const newTab = tabService.createTab(topicInput);
        currentTabId = newTab.id;
        setActiveTab(newTab);
        setCurrentView('tree');
      } catch (error) {
        setError(error.message);
        return;
      }
    }

    // Update tab status to generating
    tabService.updateTabStatus(currentTabId, 'generating', { progress: 10 });
    setIsLoading(true);
    setError(null);

    try {
      console.log('🌳 Generating knowledge tree for:', topicInput, 'in tab:', currentTabId);

      // Update progress
      tabService.updateTabStatus(currentTabId, 'generating', { progress: 30 });

      const treeData = await generateTreeAPI(topicInput, getCurrentLanguage());
      console.log('✅ Generated tree data:', treeData);

      // Update tab with completed tree
      tabService.updateTabStatus(currentTabId, 'completed', {
        tree: treeData,
        progress: 100
      });

      // Update active tab if this is the current one
      if (currentTabId === activeTab?.id) {
        setActiveTab(tabService.getTab(currentTabId));
      }

      // Award points for tree generation
      const result = gamificationService.awardPoints('TREE_GENERATED');
      if (result.newAchievements.length > 0) {
        result.newAchievements.forEach(achievement => {
          gamificationService.showAchievementNotification(achievement);
        });
      }
    } catch (err) {
      console.error('❌ Error generating tree:', err);
      tabService.updateTabStatus(currentTabId, 'error');
      setError(`Failed to generate knowledge tree: ${err.message}. Please try again.`);
    } finally {
      setIsLoading(false);
    }
  };



  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    if (topic.trim()) {
      generateKnowledgeTree(topic.trim());
      setTopic(''); // Clear input for next topic
    }
  };

  // Handle tab changes
  const handleTabChange = (tab) => {
    // Clear any existing errors when switching tabs
    setError(null);
    setIsLoading(false);

    setActiveTab(tab);
    if (tab?.tree) {
      setCurrentView('tree');
    } else {
      setCurrentView('input');
    }
  };

  // Handle new tab creation
  const handleNewTab = () => {
    // Clear any existing errors and loading states
    setError(null);
    setIsLoading(false);

    setCurrentView('input');
    setActiveTab(null);
    setTopic('');
  };

  const handleTabArticleAccess = (tab) => {
    // Clear any existing errors when accessing article
    setError(null);
    setIsLoading(false);

    setActiveTab(tab);
    setCurrentView('article');
  };



  // Speech functions
  const handleSpeechToggle = () => {
    if (!article?.content) return;

    if (speechService.getStatus().isPlaying) {
      speechService.toggle();
    } else {
      speechService.speak(article.content);
      // Award points for using speech
      const result = gamificationService.awardPoints('SPEECH_USED');
      if (result.newAchievements.length > 0) {
        result.newAchievements.forEach(achievement => {
          gamificationService.showAchievementNotification(achievement);
        });
      }
    }
  };

  const handleSpeechStop = () => {
    speechService.stop();
  };

  const handleSpeechRateChange = (rate) => {
    speechService.setRate(rate);
  };

  // Export functions
  const handleExportPDF = () => {
    if (!article?.title || !article?.content) return;
    const result = exportService.exportAsPDF(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);
    if (result.success) {
      // Award points for export
      const gamResult = gamificationService.awardPoints('EXPORT_USED');
      if (gamResult.newAchievements.length > 0) {
        gamResult.newAchievements.forEach(achievement => {
          gamificationService.showAchievementNotification(achievement);
        });
      }
    }
  };

  const handleExportWord = () => {
    if (!article?.title || !article?.content) return;
    const result = exportService.exportAsWord(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);
    if (result.success) {
      // Award points for export
      const gamResult = gamificationService.awardPoints('EXPORT_USED');
      if (gamResult.newAchievements.length > 0) {
        gamResult.newAchievements.forEach(achievement => {
          gamificationService.showAchievementNotification(achievement);
        });
      }
    }
  };

  const handleCopyToClipboard = async () => {
    if (!article?.content) return;
    const result = await exportService.copyToClipboard(article.content);
    exportService.showMessage(result.message, result.success ? 'success' : 'error');
    if (result.success) {
      // Award points for export
      const gamResult = gamificationService.awardPoints('EXPORT_USED');
      if (gamResult.newAchievements.length > 0) {
        gamResult.newAchievements.forEach(achievement => {
          gamificationService.showAchievementNotification(achievement);
        });
      }
    }
  };



  // Navigation functions
  const goBack = () => {
    if (currentView === 'article') {
      setCurrentView('tree');
      if (activeTab) {
        tabService.updateTabStatus(activeTab.id, activeTab.status, { article: null });
        setActiveTab(tabService.getTab(activeTab.id));
      }
    } else if (currentView === 'tree') {
      setCurrentView('input');
    }
  };



  const goHome = () => {
    setCurrentView('input');
    setActiveTab(null);
    setTopic('');
    // Clear all tabs
    tabService.clearAllTabs();
  };

  // Quick login for development
  const quickLogin = () => {
    localStorage.setItem('bypassSecurity', 'true');
    setUser({ id: 'dev-1', name: 'Developer', subscriptionTier: 'premium' });
  };

  return (
    <div className="app" ref={appRef}>
      {/* Header */}
      <header className="app-header">
        <div className="header-content">
          <button onClick={goHome} className="logo-text">
            {t('appTitle')}
          </button>
          <div className="header-right">
            {user && (
              <div id="gamification-container" style={{ marginRight: '16px' }}>
                {/* Gamification UI will be inserted here */}
              </div>
            )}
            <LanguageSwitcher />
            {!user ? (
              <button onClick={quickLogin} className="btn btn-primary" style={{ marginLeft: '12px' }}>
                {t('quickLogin')}
              </button>
            ) : (
              <span style={{ marginLeft: '12px' }}>{t('welcome')}, {user.name}!</span>
            )}
          </div>
        </div>
      </header>

      {/* Tab Manager */}
      {user && (
        <TabManager
          onTabChange={handleTabChange}
          onNewTab={handleNewTab}
          onTabArticleAccess={handleTabArticleAccess}
        />
      )}

      {/* Main Content */}
      <main className="main-content">
        {error && (
          <div className="error">
            ⚠️ {error}
            <button onClick={() => setError(null)} style={{marginLeft: 'auto', background: 'none', border: 'none', color: 'white', cursor: 'pointer'}}>
              ✕
            </button>
          </div>
        )}

        {/* Topic Input View */}
        {currentView === 'input' && (
          <div className="card text-center">
            <h1 className="title">{t('appTitle')}</h1>
            <p className="subtitle">
              Enter any topic to generate an interactive knowledge tree with AI-powered content.
            </p>

            {!user ? (
              <div style={{background: '#f1f5f9', padding: '1rem', borderRadius: '0.5rem', marginBottom: '2rem'}}>
                <p style={{color: '#334155', marginBottom: '1rem'}}>
                  {t('loginRequired')}
                </p>
                <button onClick={quickLogin} className="btn btn-primary">
                  {t('quickLoginDev')}
                </button>
              </div>
            ) : (
              <form onSubmit={handleSubmit}>
                <div className="form-group">
                  <input
                    type="text"
                    value={topic}
                    onChange={(e) => setTopic(e.target.value)}
                    placeholder={t('topicPlaceholder')}
                    className="form-input"
                    disabled={isLoading}
                  />
                </div>
                <button
                  type="submit"
                  disabled={isLoading || !topic.trim()}
                  className="btn btn-primary"
                >
                  {isLoading ? (
                    <>
                      <span className="spinner"></span>
                      {t('generating')}
                    </>
                  ) : (
                    <>
                      {t('exploreKnowledge')}
                    </>
                  )}
                </button>
              </form>
            )}
          </div>
        )}

        {/* Tree View - Desktop: Infinite Tree, Mobile: TikTok Style */}
        {currentView === 'tree' && tree && (
          <div className="tree-container">
            {/* Desktop Tree View - Infinite Mind Map */}
            <div
              className={`desktop-tree-view ${isDragging ? 'dragging' : ''}`}
              onMouseDown={handleMouseDown}
              onTouchStart={handleTouchStart}
            >
              <div
                className={`infinite-canvas ${isDragging ? 'dragging' : ''}`}
                id="infinite-canvas"
                style={{
                  transform: `translate(${canvasTransform.x}px, ${canvasTransform.y}px) scale(${canvasTransform.scale})`
                }}
              >
                {/* Central Topic Node */}
                <div className="central-topic-node">
                  <div className="topic-input-center">
                    <h2>{tree.tema}</h2>
                    <button onClick={goBack} className="btn btn-secondary back-btn">
                      {t('backToTree')}
                    </button>
                  </div>
                </div>

                {/* Branches positioned around center - FIXED POSITIONING */}
                {tree.ramuri.map((branch, index) => {
                  const totalBranches = tree.ramuri.length;

                  // Calculate proper spacing to prevent overlaps
                  const minAngleSpacing = Math.max(25, 360 / Math.max(totalBranches, 8)); // Minimum 25 degrees between branches
                  const angle = (index * minAngleSpacing) % 360;

                  // Dynamic radius based on number of branches and level
                  const baseRadius = Math.max(350, totalBranches * 35); // Increase radius with more branches
                  const levelOffset = (branch.level || 0) * 150; // More spacing between levels
                  const radius = baseRadius + levelOffset;

                  // Reduced randomness to prevent overlaps
                  const angleOffset = (Math.sin(index * 1.5) * 8); // Smaller random offset
                  const finalAngle = angle + angleOffset;

                  // Calculate position with collision detection
                  let x = Math.cos((finalAngle * Math.PI) / 180) * radius;
                  let y = Math.sin((finalAngle * Math.PI) / 180) * radius;

                  // Add spiral effect for many branches
                  if (totalBranches > 12) {
                    const spiralOffset = index * 15; // Spiral outward
                    x += Math.cos((spiralOffset * Math.PI) / 180) * (index * 8);
                    y += Math.sin((spiralOffset * Math.PI) / 180) * (index * 8);
                  }

                  return (
                    <div
                      key={index}
                      className={`tree-branch-node branch-item ${selectedBranch === branch ? 'selected' : ''}`}
                      style={{
                        transform: `translate(${x}px, ${y}px)`,
                        '--branch-angle': `${finalAngle}deg`
                      }}
                      data-index={index}
                      data-level={branch.level || 0}
                      data-name={branch.nume}
                      data-description={branch.descriere}
                      onClick={() => handleBranchSelect(branch)}
                    >
                      {/* Connection Line to Center */}
                      <div className="branch-connection-line" style={{
                        transform: `rotate(${finalAngle + 180}deg)`,
                        width: `${radius}px`
                      }}></div>

                      <div className="branch-content">
                        <div className="branch-emoji">{branch.emoji}</div>
                        <h3 className="branch-name">{branch.nume}</h3>
                        <p className="branch-description">{branch.descriere}</p>

                        {branch.subcategorii && (
                          <div className="branch-subcategories">
                            {branch.subcategorii.slice(0, 2).map((sub, subIndex) => (
                              <span key={subIndex} className="subcategory-tag">
                                {sub}
                              </span>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}

                {isLoading && (
                  <div className="loading-overlay">
                    <span className="spinner"></span>
                    <span>{t('loading')}</span>
                  </div>
                )}
              </div>

              {/* Pan & Zoom Controls */}
              <div className="canvas-controls">
                <button className="control-btn" onClick={() => zoomCanvas(1.2)}>🔍+</button>
                <button className="control-btn" onClick={() => zoomCanvas(0.8)}>🔍-</button>
                <button className="control-btn" onClick={() => resetCanvasView()}>🎯</button>
              </div>
            </div>

            {/* Mobile Tree View - TikTok Style */}
            <div className="mobile-tree-view">
              <div className="tiktok-container">
                <div className="tiktok-header">
                  <h2>{tree.tema}</h2>
                  <button onClick={goBack} className="btn btn-secondary">
                    {t('backToTree')}
                  </button>
                </div>

                <div className="tiktok-scroll-container" id="tiktok-scroll">
                  {tree.ramuri.map((branch, index) => (
                    <div
                      key={index}
                      className={`tiktok-branch-card branch-item ${selectedBranch === branch ? 'selected' : ''}`}
                      data-index={index}
                      data-name={branch.nume}
                      data-description={branch.descriere}
                      onClick={() => handleBranchSelect(branch)}
                    >
                      <div className="tiktok-card-content">
                        <div className="branch-emoji-large">{branch.emoji}</div>
                        <h3 className="branch-name-large">{branch.nume}</h3>
                        <p className="branch-description-large">{branch.descriere}</p>

                        {branch.subcategorii && (
                          <div className="tiktok-subcategories">
                            {branch.subcategorii.slice(0, 3).map((sub, subIndex) => (
                              <span key={subIndex} className="tiktok-subcategory-tag">
                                {sub}
                              </span>
                            ))}
                            {branch.subcategorii.length > 3 && (
                              <span className="tiktok-subcategory-tag more">
                                +{branch.subcategorii.length - 3}
                              </span>
                            )}
                          </div>
                        )}

                        <div className="tiktok-gesture-hint">
                          <span className="tiktok-action-hint">📖 Swipe down alte crengii</span>
                          <span className="tiktok-action-hint">🌿 Long-press pentru expansiune</span>
                        </div>
                      </div>

                      {/* Level indicator for sub-branches */}
                      {(branch.level || 0) > 0 && (
                        <div className="tiktok-level-indicator">
                          <div className="level-badge">Nivel {branch.level}</div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                {isLoading && (
                  <div className="tiktok-loading">
                    <span className="spinner"></span>
                    <span>{t('loading')}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Article View - Redesigned */}
        {currentView === 'article' && article && (
          <div className="article-container">
            <div className="article-card">
              <div className="article-header">
                <button onClick={goBack} className="btn btn-secondary article-back-btn">
                  {t('backToTree')}
                </button>

                {/* Article Controls */}
                <div className="article-controls" style={{
                  display: 'flex',
                  gap: '8px',
                  marginTop: '1rem',
                  flexWrap: 'wrap'
                }}>
                  {/* Speech Controls */}
                  <div className="speech-controls-compact" style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    padding: '8px 12px',
                    background: '#f1f5f9',
                    borderRadius: '6px',
                    border: '1px solid #e2e8f0'
                  }}>
                    <button
                      onClick={handleSpeechToggle}
                      className="btn-icon"
                      title="Play/Pause Speech"
                      style={{
                        background: 'none',
                        border: 'none',
                        fontSize: '16px',
                        cursor: 'pointer',
                        padding: '4px'
                      }}
                    >
                      {speechService.getStatus().isPlaying ? '⏸️' : '▶️'}
                    </button>
                    <button
                      onClick={handleSpeechStop}
                      className="btn-icon"
                      title="Stop Speech"
                      style={{
                        background: 'none',
                        border: 'none',
                        fontSize: '16px',
                        cursor: 'pointer',
                        padding: '4px'
                      }}
                    >
                      ⏹️
                    </button>
                    <input
                      type="range"
                      min="0.5"
                      max="2"
                      step="0.1"
                      defaultValue="1"
                      onChange={(e) => handleSpeechRateChange(parseFloat(e.target.value))}
                      style={{width: '60px'}}
                      title="Speech Speed"
                    />
                    <span style={{fontSize: '12px', color: '#64748b'}}>🗣️</span>
                  </div>

                  {/* Export Controls */}
                  <div className="export-controls-compact" style={{
                    display: 'flex',
                    gap: '4px'
                  }}>
                    <button
                      onClick={handleCopyToClipboard}
                      className="btn btn-secondary"
                      style={{padding: '6px 12px', fontSize: '12px'}}
                      title="Copy to Clipboard"
                    >
                      📋 Copy
                    </button>
                    <button
                      onClick={handleExportPDF}
                      className="btn btn-secondary"
                      style={{padding: '6px 12px', fontSize: '12px'}}
                      title="Export as PDF"
                    >
                      📄 PDF
                    </button>
                    <button
                      onClick={handleExportWord}
                      className="btn btn-secondary"
                      style={{padding: '6px 12px', fontSize: '12px'}}
                      title="Export as Word"
                    >
                      📝 Word
                    </button>
                  </div>
                </div>
              </div>

              <div className="article-title-section">
                <h1 className="article-title">{article?.title || 'Loading...'}</h1>
                <div className="article-meta">
                  <span className="article-topic">{t('partOf')}: {article?.topic || 'Unknown'}</span>
                  {article?.flags && article.flags.length > 0 && (
                    <div className="article-flags">
                      {t('flags')}:
                      {article.flags.map((flag, index) => (
                        <span key={index} className="flag-badge">{flag}</span>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              <div className="article-content">
                {article?.content ? article.content.split('\n').map((paragraph, index) => (
                  paragraph.trim() && (
                    <p key={index} className="article-paragraph">
                      {paragraph}
                    </p>
                  )
                )) : (
                  <div className="article-loading">
                    <span className="spinner"></span>
                    <p>Loading article content...</p>
                  </div>
                )}
              </div>

              {/* Web Sources Section */}
              {article?.webSources && article.webSources.length > 0 && (
                <div className="web-sources-section">
                  <h3>📚 Surse Web</h3>
                  <div className="sources-grid">
                    {article.webSources.map((source, index) => (
                      <div key={index} className="source-item">
                        <a href={source.url} target="_blank" rel="noopener noreferrer">
                          {source.title || `Sursa ${index + 1}`}
                        </a>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Error Message */}
        {errorMessage && (
          <div className="error-message">
            {errorMessage}
          </div>
        )}
      </main>
    </div>
  );
};

export default OptimizedApp;
