{"ast": null, "code": "// Web Search Service - Free Internet Search Integration\n// Uses DuckDuckGo Instant Answer API (free, no API key required)\n\nclass WebSearchService {\n  constructor() {\n    this.baseURL = 'https://api.duckduckgo.com/';\n    this.corsProxy = 'https://api.allorigins.win/raw?url=';\n    this.isEnabled = true;\n  }\n\n  // Search for sources related to a topic - OPTIMIZAT pentru fiabilitate\n  async searchSources(topic, maxResults = 5) {\n    console.log('🔍 Searching for sources for topic:', topic);\n    try {\n      // Încearcă să obțină surse curate direct (mai fiabil)\n      const curatedSources = this.getCuratedSources(topic, maxResults);\n      console.log('✅ Found curated sources:', curatedSources.length);\n      return curatedSources;\n    } catch (error) {\n      console.warn('⚠️ Curated search failed, using fallback sources:', error);\n      return this.getMockSources(topic);\n    }\n  }\n\n  // Process DuckDuckGo API results\n  processDuckDuckGoResults(data, topic, maxResults) {\n    const sources = [];\n\n    // Add abstract source if available\n    if (data.Abstract && data.AbstractURL) {\n      sources.push({\n        title: data.Heading || `${topic} - Overview`,\n        url: data.AbstractURL,\n        description: data.Abstract.substring(0, 150) + '...',\n        source: this.extractDomain(data.AbstractURL),\n        type: 'overview'\n      });\n    }\n\n    // Add related topics\n    if (data.RelatedTopics && data.RelatedTopics.length > 0) {\n      data.RelatedTopics.slice(0, maxResults - sources.length).forEach(topic => {\n        if (topic.FirstURL && topic.Text) {\n          sources.push({\n            title: topic.Text.split(' - ')[0] || topic.Text.substring(0, 50),\n            url: topic.FirstURL,\n            description: topic.Text.substring(0, 150) + '...',\n            source: this.extractDomain(topic.FirstURL),\n            type: 'related'\n          });\n        }\n      });\n    }\n\n    // Add definition if available\n    if (data.Definition && data.DefinitionURL) {\n      sources.push({\n        title: `${topic} - Definition`,\n        url: data.DefinitionURL,\n        description: data.Definition.substring(0, 150) + '...',\n        source: this.extractDomain(data.DefinitionURL),\n        type: 'definition'\n      });\n    }\n\n    // Fill remaining slots with curated sources if needed\n    while (sources.length < maxResults) {\n      const mockSources = this.getMockSources(topic);\n      const remainingSlots = maxResults - sources.length;\n      sources.push(...mockSources.slice(0, remainingSlots));\n      break;\n    }\n    return sources.slice(0, maxResults);\n  }\n\n  // Extract domain from URL\n  extractDomain(url) {\n    try {\n      const domain = new URL(url).hostname;\n      return domain.replace('www.', '');\n    } catch {\n      return 'Unknown Source';\n    }\n  }\n\n  // Surse curate pentru subiecte specifice - OPTIMIZAT pentru relevanță\n  getCuratedSources(topic, maxResults = 5) {\n    const topicLower = topic.toLowerCase();\n    let sources = [];\n\n    // Subiecte juridice românești\n    if (topicLower.includes('codul civil') || topicLower.includes('drept civil') || topicLower.includes('legislație')) {\n      sources = [{\n        title: \"Codul Civil al României - Monitorul Oficial\",\n        url: \"https://www.monitoruloficial.ro/\",\n        description: \"Textul oficial al Codului Civil român cu toate modificările și completările.\",\n        source: \"monitoruloficial.ro\",\n        type: \"legislation\"\n      }, {\n        title: \"Drept Civil - Universitatea București\",\n        url: \"https://drept.unibuc.ro/\",\n        description: \"Cursuri și materiale de drept civil de la Facultatea de Drept.\",\n        source: \"drept.unibuc.ro\",\n        type: \"academic\"\n      }, {\n        title: \"Jurisprudență - Curtea de Apel\",\n        url: \"https://www.curteadeapelbucuresti.ro/\",\n        description: \"Hotărâri judecătorești și interpretări ale Codului Civil.\",\n        source: \"curteadeapelbucuresti.ro\",\n        type: \"jurisprudence\"\n      }, {\n        title: \"Analize Juridice - Revista de Drept\",\n        url: \"https://www.revistadedrept.ro/\",\n        description: \"Analize și comentarii asupra legislației civile românești.\",\n        source: \"revistadedrept.ro\",\n        type: \"analysis\"\n      }, {\n        title: \"Ghid Practic - Barourile din România\",\n        url: \"https://www.unbr.ro/\",\n        description: \"Ghiduri practice pentru aplicarea Codului Civil în practică.\",\n        source: \"unbr.ro\",\n        type: \"practical\"\n      }];\n    }\n    // Subiecte de programare\n    else if (topicLower.includes('programming') || topicLower.includes('coding') || topicLower.includes('javascript') || topicLower.includes('python')) {\n      sources = [{\n        title: \"MDN Web Docs - Mozilla\",\n        url: `https://developer.mozilla.org/en-US/search?q=${encodeURIComponent(topic)}`,\n        description: \"Documentație oficială și tutoriale pentru dezvoltarea web.\",\n        source: \"developer.mozilla.org\",\n        type: \"documentation\"\n      }, {\n        title: \"Stack Overflow - Community\",\n        url: `https://stackoverflow.com/questions/tagged/${encodeURIComponent(topic.replace(/\\s+/g, '-'))}`,\n        description: \"Întrebări și răspunsuri din comunitatea de dezvoltatori.\",\n        source: \"stackoverflow.com\",\n        type: \"community\"\n      }, {\n        title: \"GitHub Repositories\",\n        url: `https://github.com/topics/${encodeURIComponent(topic.replace(/\\s+/g, '-'))}`,\n        description: \"Proiecte open source și exemple de cod.\",\n        source: \"github.com\",\n        type: \"repository\"\n      }, {\n        title: \"freeCodeCamp Tutorials\",\n        url: `https://www.freecodecamp.org/news/search/?query=${encodeURIComponent(topic)}`,\n        description: \"Tutoriale gratuite și cursuri de programare.\",\n        source: \"freecodecamp.org\",\n        type: \"tutorial\"\n      }, {\n        title: \"Dev.to Articles\",\n        url: `https://dev.to/search?q=${encodeURIComponent(topic)}`,\n        description: \"Articole și insights din comunitatea de dezvoltatori.\",\n        source: \"dev.to\",\n        type: \"community\"\n      }];\n    }\n    // Subiecte de business/marketing\n    else if (topicLower.includes('marketing') || topicLower.includes('business') || topicLower.includes('management')) {\n      sources = [{\n        title: \"Harvard Business Review\",\n        url: `https://hbr.org/search?term=${encodeURIComponent(topic)}`,\n        description: \"Articole și studii de caz din lumea business-ului.\",\n        source: \"hbr.org\",\n        type: \"research\"\n      }, {\n        title: \"McKinsey & Company Insights\",\n        url: `https://www.mckinsey.com/search?q=${encodeURIComponent(topic)}`,\n        description: \"Analize și strategii de business de la consultanții McKinsey.\",\n        source: \"mckinsey.com\",\n        type: \"consulting\"\n      }, {\n        title: \"Marketing Land\",\n        url: `https://marketingland.com/?s=${encodeURIComponent(topic)}`,\n        description: \"Știri și tendințe în marketing digital și tradițional.\",\n        source: \"marketingland.com\",\n        type: \"news\"\n      }, {\n        title: \"Google Marketing Platform\",\n        url: `https://marketingplatform.google.com/about/resources/`,\n        description: \"Resurse și ghiduri pentru marketing digital de la Google.\",\n        source: \"marketingplatform.google.com\",\n        type: \"guide\"\n      }, {\n        title: \"Coursera Business Courses\",\n        url: `https://www.coursera.org/search?query=${encodeURIComponent(topic)}`,\n        description: \"Cursuri online de business și management.\",\n        source: \"coursera.org\",\n        type: \"education\"\n      }];\n    }\n    // Fallback pentru alte subiecte\n    else {\n      sources = this.getMockSources(topic);\n    }\n    return sources.slice(0, maxResults);\n  }\n\n  // Fallback mock sources for when API fails\n  getMockSources(topic) {\n    const topicLower = topic.toLowerCase();\n\n    // Technology topics\n    if (topicLower.includes('ai') || topicLower.includes('artificial intelligence')) {\n      return [{\n        title: \"Artificial Intelligence - MIT Technology Review\",\n        url: \"https://www.technologyreview.com/topic/artificial-intelligence/\",\n        description: \"Latest developments and research in artificial intelligence technology.\",\n        source: \"technologyreview.com\",\n        type: \"research\"\n      }, {\n        title: \"AI Research - OpenAI\",\n        url: \"https://openai.com/research/\",\n        description: \"Cutting-edge research papers and findings in AI development.\",\n        source: \"openai.com\",\n        type: \"research\"\n      }, {\n        title: \"Machine Learning - Google AI\",\n        url: \"https://ai.google/research/\",\n        description: \"Google's latest research and developments in machine learning.\",\n        source: \"ai.google\",\n        type: \"research\"\n      }, {\n        title: \"AI Ethics - Stanford HAI\",\n        url: \"https://hai.stanford.edu/\",\n        description: \"Human-centered AI research and ethical considerations.\",\n        source: \"hai.stanford.edu\",\n        type: \"academic\"\n      }, {\n        title: \"AI News - IEEE Spectrum\",\n        url: \"https://spectrum.ieee.org/topic/artificial-intelligence/\",\n        description: \"Latest news and analysis on artificial intelligence developments.\",\n        source: \"spectrum.ieee.org\",\n        type: \"news\"\n      }];\n    }\n\n    // Programming topics\n    if (topicLower.includes('programming') || topicLower.includes('coding') || topicLower.includes('software')) {\n      return [{\n        title: \"Programming Best Practices - Stack Overflow\",\n        url: \"https://stackoverflow.com/questions/tagged/\" + encodeURIComponent(topic),\n        description: \"Community-driven programming questions and solutions.\",\n        source: \"stackoverflow.com\",\n        type: \"community\"\n      }, {\n        title: \"Software Development - GitHub\",\n        url: \"https://github.com/topics/\" + encodeURIComponent(topic.replace(/\\s+/g, '-')),\n        description: \"Open source projects and code repositories.\",\n        source: \"github.com\",\n        type: \"repository\"\n      }, {\n        title: \"Programming Tutorials - MDN Web Docs\",\n        url: \"https://developer.mozilla.org/en-US/search?q=\" + encodeURIComponent(topic),\n        description: \"Comprehensive web development documentation and tutorials.\",\n        source: \"developer.mozilla.org\",\n        type: \"documentation\"\n      }, {\n        title: \"Coding Resources - freeCodeCamp\",\n        url: \"https://www.freecodecamp.org/news/search/?query=\" + encodeURIComponent(topic),\n        description: \"Free programming tutorials and coding bootcamp resources.\",\n        source: \"freecodecamp.org\",\n        type: \"tutorial\"\n      }, {\n        title: \"Tech Articles - Dev.to\",\n        url: \"https://dev.to/search?q=\" + encodeURIComponent(topic),\n        description: \"Developer community articles and programming insights.\",\n        source: \"dev.to\",\n        type: \"community\"\n      }];\n    }\n\n    // Science topics\n    if (topicLower.includes('science') || topicLower.includes('research') || topicLower.includes('biology') || topicLower.includes('physics')) {\n      return [{\n        title: \"Scientific Research - Nature\",\n        url: \"https://www.nature.com/search?q=\" + encodeURIComponent(topic),\n        description: \"Peer-reviewed scientific research and discoveries.\",\n        source: \"nature.com\",\n        type: \"research\"\n      }, {\n        title: \"Science News - Science Magazine\",\n        url: \"https://www.science.org/search?q=\" + encodeURIComponent(topic),\n        description: \"Latest scientific breakthroughs and research findings.\",\n        source: \"science.org\",\n        type: \"research\"\n      }, {\n        title: \"Academic Papers - PubMed\",\n        url: \"https://pubmed.ncbi.nlm.nih.gov/?term=\" + encodeURIComponent(topic),\n        description: \"Biomedical literature and research publications.\",\n        source: \"pubmed.ncbi.nlm.nih.gov\",\n        type: \"academic\"\n      }, {\n        title: \"Science Education - Khan Academy\",\n        url: \"https://www.khanacademy.org/search?page_search_query=\" + encodeURIComponent(topic),\n        description: \"Free educational resources and scientific explanations.\",\n        source: \"khanacademy.org\",\n        type: \"education\"\n      }, {\n        title: \"Popular Science - Scientific American\",\n        url: \"https://www.scientificamerican.com/search/?q=\" + encodeURIComponent(topic),\n        description: \"Science news and analysis for general audiences.\",\n        source: \"scientificamerican.com\",\n        type: \"news\"\n      }];\n    }\n\n    // General fallback sources\n    return [{\n      title: `${topic} - Wikipedia`,\n      url: `https://en.wikipedia.org/wiki/${encodeURIComponent(topic.replace(/\\s+/g, '_'))}`,\n      description: `Comprehensive encyclopedia article about ${topic}.`,\n      source: \"wikipedia.org\",\n      type: \"encyclopedia\"\n    }, {\n      title: `${topic} Research - Google Scholar`,\n      url: `https://scholar.google.com/scholar?q=${encodeURIComponent(topic)}`,\n      description: `Academic papers and citations related to ${topic}.`,\n      source: \"scholar.google.com\",\n      type: \"academic\"\n    }, {\n      title: `${topic} News - Google News`,\n      url: `https://news.google.com/search?q=${encodeURIComponent(topic)}`,\n      description: `Latest news and articles about ${topic}.`,\n      source: \"news.google.com\",\n      type: \"news\"\n    }, {\n      title: `${topic} Videos - YouTube`,\n      url: `https://www.youtube.com/results?search_query=${encodeURIComponent(topic)}`,\n      description: `Educational videos and content about ${topic}.`,\n      source: \"youtube.com\",\n      type: \"video\"\n    }, {\n      title: `${topic} Discussion - Reddit`,\n      url: `https://www.reddit.com/search/?q=${encodeURIComponent(topic)}`,\n      description: `Community discussions and insights about ${topic}.`,\n      source: \"reddit.com\",\n      type: \"community\"\n    }];\n  }\n\n  // Format sources for display\n  formatSourcesHTML(sources) {\n    if (!sources || sources.length === 0) {\n      return '<p><em>No sources available</em></p>';\n    }\n    let html = '<div class=\"article-sources\"><h3>📚 Sources & Further Reading</h3><div class=\"sources-list\">';\n    sources.forEach((source, index) => {\n      const typeIcon = this.getTypeIcon(source.type);\n      html += `\n        <div class=\"source-item\">\n          <div class=\"source-header\">\n            <span class=\"source-icon\">${typeIcon}</span>\n            <a href=\"${source.url}\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"source-title\">\n              ${source.title}\n            </a>\n          </div>\n          <div class=\"source-description\">${source.description}</div>\n          <div class=\"source-meta\">\n            <span class=\"source-domain\">${source.source}</span>\n            <span class=\"source-type\">${source.type}</span>\n          </div>\n        </div>\n      `;\n    });\n    html += '</div></div>';\n    return html;\n  }\n\n  // Get icon for source type\n  getTypeIcon(type) {\n    const icons = {\n      'research': '🔬',\n      'academic': '🎓',\n      'news': '📰',\n      'tutorial': '📖',\n      'documentation': '📋',\n      'community': '👥',\n      'repository': '💻',\n      'encyclopedia': '📚',\n      'video': '🎥',\n      'education': '🏫',\n      'overview': '📄',\n      'related': '🔗',\n      'definition': '📝'\n    };\n    return icons[type] || '🌐';\n  }\n\n  // Enable/disable web search\n  setEnabled(enabled) {\n    this.isEnabled = enabled;\n  }\n\n  // Check if web search is available\n  isAvailable() {\n    return this.isEnabled;\n  }\n}\n\n// Create singleton instance\nconst webSearchService = new WebSearchService();\nexport default webSearchService;", "map": {"version": 3, "names": ["WebSearchService", "constructor", "baseURL", "corsProxy", "isEnabled", "searchSources", "topic", "maxResults", "console", "log", "curatedSources", "getCuratedSources", "length", "error", "warn", "getMockSources", "processDuckDuckGoResults", "data", "sources", "Abstract", "AbstractURL", "push", "title", "Heading", "url", "description", "substring", "source", "extractDomain", "type", "RelatedTopics", "slice", "for<PERSON>ach", "FirstURL", "Text", "split", "Definition", "DefinitionURL", "mockSources", "remainingSlots", "domain", "URL", "hostname", "replace", "topicLower", "toLowerCase", "includes", "encodeURIComponent", "formatSourcesHTML", "html", "index", "typeIcon", "getTypeIcon", "icons", "setEnabled", "enabled", "isAvailable", "webSearchService"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Appv1/src/services/webSearchService.js"], "sourcesContent": ["// Web Search Service - Free Internet Search Integration\n// Uses DuckDuckGo Instant Answer API (free, no API key required)\n\nclass WebSearchService {\n  constructor() {\n    this.baseURL = 'https://api.duckduckgo.com/';\n    this.corsProxy = 'https://api.allorigins.win/raw?url=';\n    this.isEnabled = true;\n  }\n\n  // Search for sources related to a topic - OPTIMIZAT pentru fiabilitate\n  async searchSources(topic, maxResults = 5) {\n    console.log('🔍 Searching for sources for topic:', topic);\n\n    try {\n      // Încearcă să obțină surse curate direct (mai fiabil)\n      const curatedSources = this.getCuratedSources(topic, maxResults);\n      console.log('✅ Found curated sources:', curatedSources.length);\n      return curatedSources;\n\n    } catch (error) {\n      console.warn('⚠️ Curated search failed, using fallback sources:', error);\n      return this.getMockSources(topic);\n    }\n  }\n\n  // Process DuckDuckGo API results\n  processDuckDuckGoResults(data, topic, maxResults) {\n    const sources = [];\n    \n    // Add abstract source if available\n    if (data.Abstract && data.AbstractURL) {\n      sources.push({\n        title: data.Heading || `${topic} - Overview`,\n        url: data.AbstractURL,\n        description: data.Abstract.substring(0, 150) + '...',\n        source: this.extractDomain(data.AbstractURL),\n        type: 'overview'\n      });\n    }\n\n    // Add related topics\n    if (data.RelatedTopics && data.RelatedTopics.length > 0) {\n      data.RelatedTopics.slice(0, maxResults - sources.length).forEach(topic => {\n        if (topic.FirstURL && topic.Text) {\n          sources.push({\n            title: topic.Text.split(' - ')[0] || topic.Text.substring(0, 50),\n            url: topic.FirstURL,\n            description: topic.Text.substring(0, 150) + '...',\n            source: this.extractDomain(topic.FirstURL),\n            type: 'related'\n          });\n        }\n      });\n    }\n\n    // Add definition if available\n    if (data.Definition && data.DefinitionURL) {\n      sources.push({\n        title: `${topic} - Definition`,\n        url: data.DefinitionURL,\n        description: data.Definition.substring(0, 150) + '...',\n        source: this.extractDomain(data.DefinitionURL),\n        type: 'definition'\n      });\n    }\n\n    // Fill remaining slots with curated sources if needed\n    while (sources.length < maxResults) {\n      const mockSources = this.getMockSources(topic);\n      const remainingSlots = maxResults - sources.length;\n      sources.push(...mockSources.slice(0, remainingSlots));\n      break;\n    }\n\n    return sources.slice(0, maxResults);\n  }\n\n  // Extract domain from URL\n  extractDomain(url) {\n    try {\n      const domain = new URL(url).hostname;\n      return domain.replace('www.', '');\n    } catch {\n      return 'Unknown Source';\n    }\n  }\n\n  // Surse curate pentru subiecte specifice - OPTIMIZAT pentru relevanță\n  getCuratedSources(topic, maxResults = 5) {\n    const topicLower = topic.toLowerCase();\n    let sources = [];\n\n    // Subiecte juridice românești\n    if (topicLower.includes('codul civil') || topicLower.includes('drept civil') || topicLower.includes('legislație')) {\n      sources = [\n        {\n          title: \"Codul Civil al României - Monitorul Oficial\",\n          url: \"https://www.monitoruloficial.ro/\",\n          description: \"Textul oficial al Codului Civil român cu toate modificările și completările.\",\n          source: \"monitoruloficial.ro\",\n          type: \"legislation\"\n        },\n        {\n          title: \"Drept Civil - Universitatea București\",\n          url: \"https://drept.unibuc.ro/\",\n          description: \"Cursuri și materiale de drept civil de la Facultatea de Drept.\",\n          source: \"drept.unibuc.ro\",\n          type: \"academic\"\n        },\n        {\n          title: \"Jurisprudență - Curtea de Apel\",\n          url: \"https://www.curteadeapelbucuresti.ro/\",\n          description: \"Hotărâri judecătorești și interpretări ale Codului Civil.\",\n          source: \"curteadeapelbucuresti.ro\",\n          type: \"jurisprudence\"\n        },\n        {\n          title: \"Analize Juridice - Revista de Drept\",\n          url: \"https://www.revistadedrept.ro/\",\n          description: \"Analize și comentarii asupra legislației civile românești.\",\n          source: \"revistadedrept.ro\",\n          type: \"analysis\"\n        },\n        {\n          title: \"Ghid Practic - Barourile din România\",\n          url: \"https://www.unbr.ro/\",\n          description: \"Ghiduri practice pentru aplicarea Codului Civil în practică.\",\n          source: \"unbr.ro\",\n          type: \"practical\"\n        }\n      ];\n    }\n    // Subiecte de programare\n    else if (topicLower.includes('programming') || topicLower.includes('coding') || topicLower.includes('javascript') || topicLower.includes('python')) {\n      sources = [\n        {\n          title: \"MDN Web Docs - Mozilla\",\n          url: `https://developer.mozilla.org/en-US/search?q=${encodeURIComponent(topic)}`,\n          description: \"Documentație oficială și tutoriale pentru dezvoltarea web.\",\n          source: \"developer.mozilla.org\",\n          type: \"documentation\"\n        },\n        {\n          title: \"Stack Overflow - Community\",\n          url: `https://stackoverflow.com/questions/tagged/${encodeURIComponent(topic.replace(/\\s+/g, '-'))}`,\n          description: \"Întrebări și răspunsuri din comunitatea de dezvoltatori.\",\n          source: \"stackoverflow.com\",\n          type: \"community\"\n        },\n        {\n          title: \"GitHub Repositories\",\n          url: `https://github.com/topics/${encodeURIComponent(topic.replace(/\\s+/g, '-'))}`,\n          description: \"Proiecte open source și exemple de cod.\",\n          source: \"github.com\",\n          type: \"repository\"\n        },\n        {\n          title: \"freeCodeCamp Tutorials\",\n          url: `https://www.freecodecamp.org/news/search/?query=${encodeURIComponent(topic)}`,\n          description: \"Tutoriale gratuite și cursuri de programare.\",\n          source: \"freecodecamp.org\",\n          type: \"tutorial\"\n        },\n        {\n          title: \"Dev.to Articles\",\n          url: `https://dev.to/search?q=${encodeURIComponent(topic)}`,\n          description: \"Articole și insights din comunitatea de dezvoltatori.\",\n          source: \"dev.to\",\n          type: \"community\"\n        }\n      ];\n    }\n    // Subiecte de business/marketing\n    else if (topicLower.includes('marketing') || topicLower.includes('business') || topicLower.includes('management')) {\n      sources = [\n        {\n          title: \"Harvard Business Review\",\n          url: `https://hbr.org/search?term=${encodeURIComponent(topic)}`,\n          description: \"Articole și studii de caz din lumea business-ului.\",\n          source: \"hbr.org\",\n          type: \"research\"\n        },\n        {\n          title: \"McKinsey & Company Insights\",\n          url: `https://www.mckinsey.com/search?q=${encodeURIComponent(topic)}`,\n          description: \"Analize și strategii de business de la consultanții McKinsey.\",\n          source: \"mckinsey.com\",\n          type: \"consulting\"\n        },\n        {\n          title: \"Marketing Land\",\n          url: `https://marketingland.com/?s=${encodeURIComponent(topic)}`,\n          description: \"Știri și tendințe în marketing digital și tradițional.\",\n          source: \"marketingland.com\",\n          type: \"news\"\n        },\n        {\n          title: \"Google Marketing Platform\",\n          url: `https://marketingplatform.google.com/about/resources/`,\n          description: \"Resurse și ghiduri pentru marketing digital de la Google.\",\n          source: \"marketingplatform.google.com\",\n          type: \"guide\"\n        },\n        {\n          title: \"Coursera Business Courses\",\n          url: `https://www.coursera.org/search?query=${encodeURIComponent(topic)}`,\n          description: \"Cursuri online de business și management.\",\n          source: \"coursera.org\",\n          type: \"education\"\n        }\n      ];\n    }\n    // Fallback pentru alte subiecte\n    else {\n      sources = this.getMockSources(topic);\n    }\n\n    return sources.slice(0, maxResults);\n  }\n\n  // Fallback mock sources for when API fails\n  getMockSources(topic) {\n    const topicLower = topic.toLowerCase();\n    \n    // Technology topics\n    if (topicLower.includes('ai') || topicLower.includes('artificial intelligence')) {\n      return [\n        {\n          title: \"Artificial Intelligence - MIT Technology Review\",\n          url: \"https://www.technologyreview.com/topic/artificial-intelligence/\",\n          description: \"Latest developments and research in artificial intelligence technology.\",\n          source: \"technologyreview.com\",\n          type: \"research\"\n        },\n        {\n          title: \"AI Research - OpenAI\",\n          url: \"https://openai.com/research/\",\n          description: \"Cutting-edge research papers and findings in AI development.\",\n          source: \"openai.com\",\n          type: \"research\"\n        },\n        {\n          title: \"Machine Learning - Google AI\",\n          url: \"https://ai.google/research/\",\n          description: \"Google's latest research and developments in machine learning.\",\n          source: \"ai.google\",\n          type: \"research\"\n        },\n        {\n          title: \"AI Ethics - Stanford HAI\",\n          url: \"https://hai.stanford.edu/\",\n          description: \"Human-centered AI research and ethical considerations.\",\n          source: \"hai.stanford.edu\",\n          type: \"academic\"\n        },\n        {\n          title: \"AI News - IEEE Spectrum\",\n          url: \"https://spectrum.ieee.org/topic/artificial-intelligence/\",\n          description: \"Latest news and analysis on artificial intelligence developments.\",\n          source: \"spectrum.ieee.org\",\n          type: \"news\"\n        }\n      ];\n    }\n\n    // Programming topics\n    if (topicLower.includes('programming') || topicLower.includes('coding') || topicLower.includes('software')) {\n      return [\n        {\n          title: \"Programming Best Practices - Stack Overflow\",\n          url: \"https://stackoverflow.com/questions/tagged/\" + encodeURIComponent(topic),\n          description: \"Community-driven programming questions and solutions.\",\n          source: \"stackoverflow.com\",\n          type: \"community\"\n        },\n        {\n          title: \"Software Development - GitHub\",\n          url: \"https://github.com/topics/\" + encodeURIComponent(topic.replace(/\\s+/g, '-')),\n          description: \"Open source projects and code repositories.\",\n          source: \"github.com\",\n          type: \"repository\"\n        },\n        {\n          title: \"Programming Tutorials - MDN Web Docs\",\n          url: \"https://developer.mozilla.org/en-US/search?q=\" + encodeURIComponent(topic),\n          description: \"Comprehensive web development documentation and tutorials.\",\n          source: \"developer.mozilla.org\",\n          type: \"documentation\"\n        },\n        {\n          title: \"Coding Resources - freeCodeCamp\",\n          url: \"https://www.freecodecamp.org/news/search/?query=\" + encodeURIComponent(topic),\n          description: \"Free programming tutorials and coding bootcamp resources.\",\n          source: \"freecodecamp.org\",\n          type: \"tutorial\"\n        },\n        {\n          title: \"Tech Articles - Dev.to\",\n          url: \"https://dev.to/search?q=\" + encodeURIComponent(topic),\n          description: \"Developer community articles and programming insights.\",\n          source: \"dev.to\",\n          type: \"community\"\n        }\n      ];\n    }\n\n    // Science topics\n    if (topicLower.includes('science') || topicLower.includes('research') || topicLower.includes('biology') || topicLower.includes('physics')) {\n      return [\n        {\n          title: \"Scientific Research - Nature\",\n          url: \"https://www.nature.com/search?q=\" + encodeURIComponent(topic),\n          description: \"Peer-reviewed scientific research and discoveries.\",\n          source: \"nature.com\",\n          type: \"research\"\n        },\n        {\n          title: \"Science News - Science Magazine\",\n          url: \"https://www.science.org/search?q=\" + encodeURIComponent(topic),\n          description: \"Latest scientific breakthroughs and research findings.\",\n          source: \"science.org\",\n          type: \"research\"\n        },\n        {\n          title: \"Academic Papers - PubMed\",\n          url: \"https://pubmed.ncbi.nlm.nih.gov/?term=\" + encodeURIComponent(topic),\n          description: \"Biomedical literature and research publications.\",\n          source: \"pubmed.ncbi.nlm.nih.gov\",\n          type: \"academic\"\n        },\n        {\n          title: \"Science Education - Khan Academy\",\n          url: \"https://www.khanacademy.org/search?page_search_query=\" + encodeURIComponent(topic),\n          description: \"Free educational resources and scientific explanations.\",\n          source: \"khanacademy.org\",\n          type: \"education\"\n        },\n        {\n          title: \"Popular Science - Scientific American\",\n          url: \"https://www.scientificamerican.com/search/?q=\" + encodeURIComponent(topic),\n          description: \"Science news and analysis for general audiences.\",\n          source: \"scientificamerican.com\",\n          type: \"news\"\n        }\n      ];\n    }\n\n    // General fallback sources\n    return [\n      {\n        title: `${topic} - Wikipedia`,\n        url: `https://en.wikipedia.org/wiki/${encodeURIComponent(topic.replace(/\\s+/g, '_'))}`,\n        description: `Comprehensive encyclopedia article about ${topic}.`,\n        source: \"wikipedia.org\",\n        type: \"encyclopedia\"\n      },\n      {\n        title: `${topic} Research - Google Scholar`,\n        url: `https://scholar.google.com/scholar?q=${encodeURIComponent(topic)}`,\n        description: `Academic papers and citations related to ${topic}.`,\n        source: \"scholar.google.com\",\n        type: \"academic\"\n      },\n      {\n        title: `${topic} News - Google News`,\n        url: `https://news.google.com/search?q=${encodeURIComponent(topic)}`,\n        description: `Latest news and articles about ${topic}.`,\n        source: \"news.google.com\",\n        type: \"news\"\n      },\n      {\n        title: `${topic} Videos - YouTube`,\n        url: `https://www.youtube.com/results?search_query=${encodeURIComponent(topic)}`,\n        description: `Educational videos and content about ${topic}.`,\n        source: \"youtube.com\",\n        type: \"video\"\n      },\n      {\n        title: `${topic} Discussion - Reddit`,\n        url: `https://www.reddit.com/search/?q=${encodeURIComponent(topic)}`,\n        description: `Community discussions and insights about ${topic}.`,\n        source: \"reddit.com\",\n        type: \"community\"\n      }\n    ];\n  }\n\n  // Format sources for display\n  formatSourcesHTML(sources) {\n    if (!sources || sources.length === 0) {\n      return '<p><em>No sources available</em></p>';\n    }\n\n    let html = '<div class=\"article-sources\"><h3>📚 Sources & Further Reading</h3><div class=\"sources-list\">';\n    \n    sources.forEach((source, index) => {\n      const typeIcon = this.getTypeIcon(source.type);\n      html += `\n        <div class=\"source-item\">\n          <div class=\"source-header\">\n            <span class=\"source-icon\">${typeIcon}</span>\n            <a href=\"${source.url}\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"source-title\">\n              ${source.title}\n            </a>\n          </div>\n          <div class=\"source-description\">${source.description}</div>\n          <div class=\"source-meta\">\n            <span class=\"source-domain\">${source.source}</span>\n            <span class=\"source-type\">${source.type}</span>\n          </div>\n        </div>\n      `;\n    });\n    \n    html += '</div></div>';\n    return html;\n  }\n\n  // Get icon for source type\n  getTypeIcon(type) {\n    const icons = {\n      'research': '🔬',\n      'academic': '🎓',\n      'news': '📰',\n      'tutorial': '📖',\n      'documentation': '📋',\n      'community': '👥',\n      'repository': '💻',\n      'encyclopedia': '📚',\n      'video': '🎥',\n      'education': '🏫',\n      'overview': '📄',\n      'related': '🔗',\n      'definition': '📝'\n    };\n    return icons[type] || '🌐';\n  }\n\n  // Enable/disable web search\n  setEnabled(enabled) {\n    this.isEnabled = enabled;\n  }\n\n  // Check if web search is available\n  isAvailable() {\n    return this.isEnabled;\n  }\n}\n\n// Create singleton instance\nconst webSearchService = new WebSearchService();\n\nexport default webSearchService;\n"], "mappings": "AAAA;AACA;;AAEA,MAAMA,gBAAgB,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,OAAO,GAAG,6BAA6B;IAC5C,IAAI,CAACC,SAAS,GAAG,qCAAqC;IACtD,IAAI,CAACC,SAAS,GAAG,IAAI;EACvB;;EAEA;EACA,MAAMC,aAAaA,CAACC,KAAK,EAAEC,UAAU,GAAG,CAAC,EAAE;IACzCC,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEH,KAAK,CAAC;IAEzD,IAAI;MACF;MACA,MAAMI,cAAc,GAAG,IAAI,CAACC,iBAAiB,CAACL,KAAK,EAAEC,UAAU,CAAC;MAChEC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEC,cAAc,CAACE,MAAM,CAAC;MAC9D,OAAOF,cAAc;IAEvB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdL,OAAO,CAACM,IAAI,CAAC,mDAAmD,EAAED,KAAK,CAAC;MACxE,OAAO,IAAI,CAACE,cAAc,CAACT,KAAK,CAAC;IACnC;EACF;;EAEA;EACAU,wBAAwBA,CAACC,IAAI,EAAEX,KAAK,EAAEC,UAAU,EAAE;IAChD,MAAMW,OAAO,GAAG,EAAE;;IAElB;IACA,IAAID,IAAI,CAACE,QAAQ,IAAIF,IAAI,CAACG,WAAW,EAAE;MACrCF,OAAO,CAACG,IAAI,CAAC;QACXC,KAAK,EAAEL,IAAI,CAACM,OAAO,IAAI,GAAGjB,KAAK,aAAa;QAC5CkB,GAAG,EAAEP,IAAI,CAACG,WAAW;QACrBK,WAAW,EAAER,IAAI,CAACE,QAAQ,CAACO,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK;QACpDC,MAAM,EAAE,IAAI,CAACC,aAAa,CAACX,IAAI,CAACG,WAAW,CAAC;QAC5CS,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIZ,IAAI,CAACa,aAAa,IAAIb,IAAI,CAACa,aAAa,CAAClB,MAAM,GAAG,CAAC,EAAE;MACvDK,IAAI,CAACa,aAAa,CAACC,KAAK,CAAC,CAAC,EAAExB,UAAU,GAAGW,OAAO,CAACN,MAAM,CAAC,CAACoB,OAAO,CAAC1B,KAAK,IAAI;QACxE,IAAIA,KAAK,CAAC2B,QAAQ,IAAI3B,KAAK,CAAC4B,IAAI,EAAE;UAChChB,OAAO,CAACG,IAAI,CAAC;YACXC,KAAK,EAAEhB,KAAK,CAAC4B,IAAI,CAACC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI7B,KAAK,CAAC4B,IAAI,CAACR,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;YAChEF,GAAG,EAAElB,KAAK,CAAC2B,QAAQ;YACnBR,WAAW,EAAEnB,KAAK,CAAC4B,IAAI,CAACR,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK;YACjDC,MAAM,EAAE,IAAI,CAACC,aAAa,CAACtB,KAAK,CAAC2B,QAAQ,CAAC;YAC1CJ,IAAI,EAAE;UACR,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIZ,IAAI,CAACmB,UAAU,IAAInB,IAAI,CAACoB,aAAa,EAAE;MACzCnB,OAAO,CAACG,IAAI,CAAC;QACXC,KAAK,EAAE,GAAGhB,KAAK,eAAe;QAC9BkB,GAAG,EAAEP,IAAI,CAACoB,aAAa;QACvBZ,WAAW,EAAER,IAAI,CAACmB,UAAU,CAACV,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK;QACtDC,MAAM,EAAE,IAAI,CAACC,aAAa,CAACX,IAAI,CAACoB,aAAa,CAAC;QAC9CR,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;;IAEA;IACA,OAAOX,OAAO,CAACN,MAAM,GAAGL,UAAU,EAAE;MAClC,MAAM+B,WAAW,GAAG,IAAI,CAACvB,cAAc,CAACT,KAAK,CAAC;MAC9C,MAAMiC,cAAc,GAAGhC,UAAU,GAAGW,OAAO,CAACN,MAAM;MAClDM,OAAO,CAACG,IAAI,CAAC,GAAGiB,WAAW,CAACP,KAAK,CAAC,CAAC,EAAEQ,cAAc,CAAC,CAAC;MACrD;IACF;IAEA,OAAOrB,OAAO,CAACa,KAAK,CAAC,CAAC,EAAExB,UAAU,CAAC;EACrC;;EAEA;EACAqB,aAAaA,CAACJ,GAAG,EAAE;IACjB,IAAI;MACF,MAAMgB,MAAM,GAAG,IAAIC,GAAG,CAACjB,GAAG,CAAC,CAACkB,QAAQ;MACpC,OAAOF,MAAM,CAACG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;IACnC,CAAC,CAAC,MAAM;MACN,OAAO,gBAAgB;IACzB;EACF;;EAEA;EACAhC,iBAAiBA,CAACL,KAAK,EAAEC,UAAU,GAAG,CAAC,EAAE;IACvC,MAAMqC,UAAU,GAAGtC,KAAK,CAACuC,WAAW,CAAC,CAAC;IACtC,IAAI3B,OAAO,GAAG,EAAE;;IAEhB;IACA,IAAI0B,UAAU,CAACE,QAAQ,CAAC,aAAa,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,aAAa,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,YAAY,CAAC,EAAE;MACjH5B,OAAO,GAAG,CACR;QACEI,KAAK,EAAE,6CAA6C;QACpDE,GAAG,EAAE,kCAAkC;QACvCC,WAAW,EAAE,8EAA8E;QAC3FE,MAAM,EAAE,qBAAqB;QAC7BE,IAAI,EAAE;MACR,CAAC,EACD;QACEP,KAAK,EAAE,uCAAuC;QAC9CE,GAAG,EAAE,0BAA0B;QAC/BC,WAAW,EAAE,gEAAgE;QAC7EE,MAAM,EAAE,iBAAiB;QACzBE,IAAI,EAAE;MACR,CAAC,EACD;QACEP,KAAK,EAAE,gCAAgC;QACvCE,GAAG,EAAE,uCAAuC;QAC5CC,WAAW,EAAE,2DAA2D;QACxEE,MAAM,EAAE,0BAA0B;QAClCE,IAAI,EAAE;MACR,CAAC,EACD;QACEP,KAAK,EAAE,qCAAqC;QAC5CE,GAAG,EAAE,gCAAgC;QACrCC,WAAW,EAAE,4DAA4D;QACzEE,MAAM,EAAE,mBAAmB;QAC3BE,IAAI,EAAE;MACR,CAAC,EACD;QACEP,KAAK,EAAE,sCAAsC;QAC7CE,GAAG,EAAE,sBAAsB;QAC3BC,WAAW,EAAE,8DAA8D;QAC3EE,MAAM,EAAE,SAAS;QACjBE,IAAI,EAAE;MACR,CAAC,CACF;IACH;IACA;IAAA,KACK,IAAIe,UAAU,CAACE,QAAQ,CAAC,aAAa,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,YAAY,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAClJ5B,OAAO,GAAG,CACR;QACEI,KAAK,EAAE,wBAAwB;QAC/BE,GAAG,EAAE,gDAAgDuB,kBAAkB,CAACzC,KAAK,CAAC,EAAE;QAChFmB,WAAW,EAAE,4DAA4D;QACzEE,MAAM,EAAE,uBAAuB;QAC/BE,IAAI,EAAE;MACR,CAAC,EACD;QACEP,KAAK,EAAE,4BAA4B;QACnCE,GAAG,EAAE,8CAA8CuB,kBAAkB,CAACzC,KAAK,CAACqC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,EAAE;QACnGlB,WAAW,EAAE,0DAA0D;QACvEE,MAAM,EAAE,mBAAmB;QAC3BE,IAAI,EAAE;MACR,CAAC,EACD;QACEP,KAAK,EAAE,qBAAqB;QAC5BE,GAAG,EAAE,6BAA6BuB,kBAAkB,CAACzC,KAAK,CAACqC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,EAAE;QAClFlB,WAAW,EAAE,yCAAyC;QACtDE,MAAM,EAAE,YAAY;QACpBE,IAAI,EAAE;MACR,CAAC,EACD;QACEP,KAAK,EAAE,wBAAwB;QAC/BE,GAAG,EAAE,mDAAmDuB,kBAAkB,CAACzC,KAAK,CAAC,EAAE;QACnFmB,WAAW,EAAE,8CAA8C;QAC3DE,MAAM,EAAE,kBAAkB;QAC1BE,IAAI,EAAE;MACR,CAAC,EACD;QACEP,KAAK,EAAE,iBAAiB;QACxBE,GAAG,EAAE,2BAA2BuB,kBAAkB,CAACzC,KAAK,CAAC,EAAE;QAC3DmB,WAAW,EAAE,uDAAuD;QACpEE,MAAM,EAAE,QAAQ;QAChBE,IAAI,EAAE;MACR,CAAC,CACF;IACH;IACA;IAAA,KACK,IAAIe,UAAU,CAACE,QAAQ,CAAC,WAAW,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,UAAU,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,YAAY,CAAC,EAAE;MACjH5B,OAAO,GAAG,CACR;QACEI,KAAK,EAAE,yBAAyB;QAChCE,GAAG,EAAE,+BAA+BuB,kBAAkB,CAACzC,KAAK,CAAC,EAAE;QAC/DmB,WAAW,EAAE,oDAAoD;QACjEE,MAAM,EAAE,SAAS;QACjBE,IAAI,EAAE;MACR,CAAC,EACD;QACEP,KAAK,EAAE,6BAA6B;QACpCE,GAAG,EAAE,qCAAqCuB,kBAAkB,CAACzC,KAAK,CAAC,EAAE;QACrEmB,WAAW,EAAE,+DAA+D;QAC5EE,MAAM,EAAE,cAAc;QACtBE,IAAI,EAAE;MACR,CAAC,EACD;QACEP,KAAK,EAAE,gBAAgB;QACvBE,GAAG,EAAE,gCAAgCuB,kBAAkB,CAACzC,KAAK,CAAC,EAAE;QAChEmB,WAAW,EAAE,wDAAwD;QACrEE,MAAM,EAAE,mBAAmB;QAC3BE,IAAI,EAAE;MACR,CAAC,EACD;QACEP,KAAK,EAAE,2BAA2B;QAClCE,GAAG,EAAE,uDAAuD;QAC5DC,WAAW,EAAE,2DAA2D;QACxEE,MAAM,EAAE,8BAA8B;QACtCE,IAAI,EAAE;MACR,CAAC,EACD;QACEP,KAAK,EAAE,2BAA2B;QAClCE,GAAG,EAAE,yCAAyCuB,kBAAkB,CAACzC,KAAK,CAAC,EAAE;QACzEmB,WAAW,EAAE,2CAA2C;QACxDE,MAAM,EAAE,cAAc;QACtBE,IAAI,EAAE;MACR,CAAC,CACF;IACH;IACA;IAAA,KACK;MACHX,OAAO,GAAG,IAAI,CAACH,cAAc,CAACT,KAAK,CAAC;IACtC;IAEA,OAAOY,OAAO,CAACa,KAAK,CAAC,CAAC,EAAExB,UAAU,CAAC;EACrC;;EAEA;EACAQ,cAAcA,CAACT,KAAK,EAAE;IACpB,MAAMsC,UAAU,GAAGtC,KAAK,CAACuC,WAAW,CAAC,CAAC;;IAEtC;IACA,IAAID,UAAU,CAACE,QAAQ,CAAC,IAAI,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,yBAAyB,CAAC,EAAE;MAC/E,OAAO,CACL;QACExB,KAAK,EAAE,iDAAiD;QACxDE,GAAG,EAAE,iEAAiE;QACtEC,WAAW,EAAE,yEAAyE;QACtFE,MAAM,EAAE,sBAAsB;QAC9BE,IAAI,EAAE;MACR,CAAC,EACD;QACEP,KAAK,EAAE,sBAAsB;QAC7BE,GAAG,EAAE,8BAA8B;QACnCC,WAAW,EAAE,8DAA8D;QAC3EE,MAAM,EAAE,YAAY;QACpBE,IAAI,EAAE;MACR,CAAC,EACD;QACEP,KAAK,EAAE,8BAA8B;QACrCE,GAAG,EAAE,6BAA6B;QAClCC,WAAW,EAAE,gEAAgE;QAC7EE,MAAM,EAAE,WAAW;QACnBE,IAAI,EAAE;MACR,CAAC,EACD;QACEP,KAAK,EAAE,0BAA0B;QACjCE,GAAG,EAAE,2BAA2B;QAChCC,WAAW,EAAE,wDAAwD;QACrEE,MAAM,EAAE,kBAAkB;QAC1BE,IAAI,EAAE;MACR,CAAC,EACD;QACEP,KAAK,EAAE,yBAAyB;QAChCE,GAAG,EAAE,0DAA0D;QAC/DC,WAAW,EAAE,mEAAmE;QAChFE,MAAM,EAAE,mBAAmB;QAC3BE,IAAI,EAAE;MACR,CAAC,CACF;IACH;;IAEA;IACA,IAAIe,UAAU,CAACE,QAAQ,CAAC,aAAa,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,UAAU,CAAC,EAAE;MAC1G,OAAO,CACL;QACExB,KAAK,EAAE,6CAA6C;QACpDE,GAAG,EAAE,6CAA6C,GAAGuB,kBAAkB,CAACzC,KAAK,CAAC;QAC9EmB,WAAW,EAAE,uDAAuD;QACpEE,MAAM,EAAE,mBAAmB;QAC3BE,IAAI,EAAE;MACR,CAAC,EACD;QACEP,KAAK,EAAE,+BAA+B;QACtCE,GAAG,EAAE,4BAA4B,GAAGuB,kBAAkB,CAACzC,KAAK,CAACqC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAClFlB,WAAW,EAAE,6CAA6C;QAC1DE,MAAM,EAAE,YAAY;QACpBE,IAAI,EAAE;MACR,CAAC,EACD;QACEP,KAAK,EAAE,sCAAsC;QAC7CE,GAAG,EAAE,+CAA+C,GAAGuB,kBAAkB,CAACzC,KAAK,CAAC;QAChFmB,WAAW,EAAE,4DAA4D;QACzEE,MAAM,EAAE,uBAAuB;QAC/BE,IAAI,EAAE;MACR,CAAC,EACD;QACEP,KAAK,EAAE,iCAAiC;QACxCE,GAAG,EAAE,kDAAkD,GAAGuB,kBAAkB,CAACzC,KAAK,CAAC;QACnFmB,WAAW,EAAE,2DAA2D;QACxEE,MAAM,EAAE,kBAAkB;QAC1BE,IAAI,EAAE;MACR,CAAC,EACD;QACEP,KAAK,EAAE,wBAAwB;QAC/BE,GAAG,EAAE,0BAA0B,GAAGuB,kBAAkB,CAACzC,KAAK,CAAC;QAC3DmB,WAAW,EAAE,wDAAwD;QACrEE,MAAM,EAAE,QAAQ;QAChBE,IAAI,EAAE;MACR,CAAC,CACF;IACH;;IAEA;IACA,IAAIe,UAAU,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,UAAU,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,SAAS,CAAC,EAAE;MACzI,OAAO,CACL;QACExB,KAAK,EAAE,8BAA8B;QACrCE,GAAG,EAAE,kCAAkC,GAAGuB,kBAAkB,CAACzC,KAAK,CAAC;QACnEmB,WAAW,EAAE,oDAAoD;QACjEE,MAAM,EAAE,YAAY;QACpBE,IAAI,EAAE;MACR,CAAC,EACD;QACEP,KAAK,EAAE,iCAAiC;QACxCE,GAAG,EAAE,mCAAmC,GAAGuB,kBAAkB,CAACzC,KAAK,CAAC;QACpEmB,WAAW,EAAE,wDAAwD;QACrEE,MAAM,EAAE,aAAa;QACrBE,IAAI,EAAE;MACR,CAAC,EACD;QACEP,KAAK,EAAE,0BAA0B;QACjCE,GAAG,EAAE,wCAAwC,GAAGuB,kBAAkB,CAACzC,KAAK,CAAC;QACzEmB,WAAW,EAAE,kDAAkD;QAC/DE,MAAM,EAAE,yBAAyB;QACjCE,IAAI,EAAE;MACR,CAAC,EACD;QACEP,KAAK,EAAE,kCAAkC;QACzCE,GAAG,EAAE,uDAAuD,GAAGuB,kBAAkB,CAACzC,KAAK,CAAC;QACxFmB,WAAW,EAAE,yDAAyD;QACtEE,MAAM,EAAE,iBAAiB;QACzBE,IAAI,EAAE;MACR,CAAC,EACD;QACEP,KAAK,EAAE,uCAAuC;QAC9CE,GAAG,EAAE,+CAA+C,GAAGuB,kBAAkB,CAACzC,KAAK,CAAC;QAChFmB,WAAW,EAAE,kDAAkD;QAC/DE,MAAM,EAAE,wBAAwB;QAChCE,IAAI,EAAE;MACR,CAAC,CACF;IACH;;IAEA;IACA,OAAO,CACL;MACEP,KAAK,EAAE,GAAGhB,KAAK,cAAc;MAC7BkB,GAAG,EAAE,iCAAiCuB,kBAAkB,CAACzC,KAAK,CAACqC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,EAAE;MACtFlB,WAAW,EAAE,4CAA4CnB,KAAK,GAAG;MACjEqB,MAAM,EAAE,eAAe;MACvBE,IAAI,EAAE;IACR,CAAC,EACD;MACEP,KAAK,EAAE,GAAGhB,KAAK,4BAA4B;MAC3CkB,GAAG,EAAE,wCAAwCuB,kBAAkB,CAACzC,KAAK,CAAC,EAAE;MACxEmB,WAAW,EAAE,4CAA4CnB,KAAK,GAAG;MACjEqB,MAAM,EAAE,oBAAoB;MAC5BE,IAAI,EAAE;IACR,CAAC,EACD;MACEP,KAAK,EAAE,GAAGhB,KAAK,qBAAqB;MACpCkB,GAAG,EAAE,oCAAoCuB,kBAAkB,CAACzC,KAAK,CAAC,EAAE;MACpEmB,WAAW,EAAE,kCAAkCnB,KAAK,GAAG;MACvDqB,MAAM,EAAE,iBAAiB;MACzBE,IAAI,EAAE;IACR,CAAC,EACD;MACEP,KAAK,EAAE,GAAGhB,KAAK,mBAAmB;MAClCkB,GAAG,EAAE,gDAAgDuB,kBAAkB,CAACzC,KAAK,CAAC,EAAE;MAChFmB,WAAW,EAAE,wCAAwCnB,KAAK,GAAG;MAC7DqB,MAAM,EAAE,aAAa;MACrBE,IAAI,EAAE;IACR,CAAC,EACD;MACEP,KAAK,EAAE,GAAGhB,KAAK,sBAAsB;MACrCkB,GAAG,EAAE,oCAAoCuB,kBAAkB,CAACzC,KAAK,CAAC,EAAE;MACpEmB,WAAW,EAAE,4CAA4CnB,KAAK,GAAG;MACjEqB,MAAM,EAAE,YAAY;MACpBE,IAAI,EAAE;IACR,CAAC,CACF;EACH;;EAEA;EACAmB,iBAAiBA,CAAC9B,OAAO,EAAE;IACzB,IAAI,CAACA,OAAO,IAAIA,OAAO,CAACN,MAAM,KAAK,CAAC,EAAE;MACpC,OAAO,sCAAsC;IAC/C;IAEA,IAAIqC,IAAI,GAAG,8FAA8F;IAEzG/B,OAAO,CAACc,OAAO,CAAC,CAACL,MAAM,EAAEuB,KAAK,KAAK;MACjC,MAAMC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACzB,MAAM,CAACE,IAAI,CAAC;MAC9CoB,IAAI,IAAI;AACd;AACA;AACA,wCAAwCE,QAAQ;AAChD,uBAAuBxB,MAAM,CAACH,GAAG;AACjC,gBAAgBG,MAAM,CAACL,KAAK;AAC5B;AACA;AACA,4CAA4CK,MAAM,CAACF,WAAW;AAC9D;AACA,0CAA0CE,MAAM,CAACA,MAAM;AACvD,wCAAwCA,MAAM,CAACE,IAAI;AACnD;AACA;AACA,OAAO;IACH,CAAC,CAAC;IAEFoB,IAAI,IAAI,cAAc;IACtB,OAAOA,IAAI;EACb;;EAEA;EACAG,WAAWA,CAACvB,IAAI,EAAE;IAChB,MAAMwB,KAAK,GAAG;MACZ,UAAU,EAAE,IAAI;MAChB,UAAU,EAAE,IAAI;MAChB,MAAM,EAAE,IAAI;MACZ,UAAU,EAAE,IAAI;MAChB,eAAe,EAAE,IAAI;MACrB,WAAW,EAAE,IAAI;MACjB,YAAY,EAAE,IAAI;MAClB,cAAc,EAAE,IAAI;MACpB,OAAO,EAAE,IAAI;MACb,WAAW,EAAE,IAAI;MACjB,UAAU,EAAE,IAAI;MAChB,SAAS,EAAE,IAAI;MACf,YAAY,EAAE;IAChB,CAAC;IACD,OAAOA,KAAK,CAACxB,IAAI,CAAC,IAAI,IAAI;EAC5B;;EAEA;EACAyB,UAAUA,CAACC,OAAO,EAAE;IAClB,IAAI,CAACnD,SAAS,GAAGmD,OAAO;EAC1B;;EAEA;EACAC,WAAWA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACpD,SAAS;EACvB;AACF;;AAEA;AACA,MAAMqD,gBAAgB,GAAG,IAAIzD,gBAAgB,CAAC,CAAC;AAE/C,eAAeyD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}