import React, { useState, useEffect } from 'react';
import tabService from '../services/tabService';

const TabManager = ({ onTabChange, onNewTab, onTabArticleAccess }) => {
  const [tabs, setTabs] = useState([]);
  const [activeTabId, setActiveTabId] = useState(null);

  useEffect(() => {
    // Listen for tab changes
    const handleTabUpdate = (updatedTabs, activeId) => {
      setTabs(updatedTabs);
      setActiveTabId(activeId);

      // Notify parent component of active tab change
      if (onTabChange && activeId) {
        const activeTab = updatedTabs.find(tab => tab.id === activeId);
        onTabChange(activeTab);
      }
    };

    tabService.addListener(handleTabUpdate);

    // Initial load
    setTabs(tabService.getAllTabs());
    setActiveTabId(tabService.activeTabId);

    return () => {
      tabService.removeListener(handleTabUpdate);
    };
  }, [onTabChange]);

  const handleTabClick = (tabId) => {
    const tab = tabs.find(t => t.id === tabId);
    tabService.setActiveTab(tabId);

    // If tab has completed article, show it
    if (tab && tab.status === 'completed' && tab.article && onTabArticleAccess) {
      onTabArticleAccess(tab);
    }
  };

  const handleCloseTab = (tabId, event) => {
    event.stopPropagation();
    tabService.closeTab(tabId);
  };

  const handleNewTab = () => {
    if (tabService.canCreateNewTab() && onNewTab) {
      onNewTab();
    }
  };

  const truncateText = (text, maxLength = 15) => {
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  };

  return (
    <div className="tab-manager">
      <div className="tab-bar">
        {tabs.map(tab => (
          <div
            key={tab.id}
            className={`tab-item ${tab.id === activeTabId ? 'active' : ''}`}
            onClick={() => handleTabClick(tab.id)}
            style={{
              backgroundColor: tab.id === activeTabId ? '#ffffff' : '#f8fafc',
              borderColor: tabService.getTabStatusColor(tab.status),
              borderTopWidth: '3px',
              borderTopStyle: 'solid'
            }}
          >
            <div className="tab-content">
              <span className="tab-icon">
                {tabService.getTabStatusIcon(tab.status)}
              </span>
              <span className="tab-title">
                {truncateText(tab.topic)}
              </span>
              <button
                className="tab-close"
                onClick={(e) => handleCloseTab(tab.id, e)}
                title="Close tab"
              >
                ×
              </button>
            </div>
            {tab.status === 'generating' && (
              <div className="tab-progress">
                <div 
                  className="tab-progress-bar"
                  style={{ width: `${tab.progress}%` }}
                />
              </div>
            )}
          </div>
        ))}
        
        {tabService.canCreateNewTab() && (
          <button
            className="tab-new"
            onClick={handleNewTab}
            title={`Add new tab (${tabs.length}/${tabService.maxTabs})`}
          >
            + New
          </button>
        )}
      </div>

      {tabs.length > 0 && (
        <div className="tab-summary">
          <span className="tab-count">
            {tabs.length}/{tabService.maxTabs} tabs
          </span>
          <div className="tab-status-indicators">
            {tabs.filter(t => t.status === 'generating').length > 0 && (
              <span className="status-indicator generating">
                🔄 {tabs.filter(t => t.status === 'generating').length} generating
              </span>
            )}
            {tabs.filter(t => t.status === 'completed').length > 0 && (
              <span className="status-indicator completed">
                ✅ {tabs.filter(t => t.status === 'completed').length} ready
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default TabManager;
