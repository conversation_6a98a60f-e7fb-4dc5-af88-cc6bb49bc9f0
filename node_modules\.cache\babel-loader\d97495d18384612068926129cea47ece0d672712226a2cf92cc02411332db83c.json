{"ast": null, "code": "// Optimized Gamification Service - Pareto 80/20 Implementation\n// Essential gamification features with minimal complexity and maximum engagement\n\nconst STORAGE_KEYS = {\n  USER_STATS: 'knowledgeTree_userStats',\n  ACHIEVEMENTS: 'knowledgeTree_achievements'\n};\n\n// Essential achievements that provide 80% of engagement value\nconst CORE_ACHIEVEMENTS = {\n  FIRST_TREE: {\n    id: 'first_tree',\n    name: 'Explorer',\n    description: 'Generated your first knowledge tree',\n    points: 10,\n    icon: '🌱'\n  },\n  TREE_MASTER: {\n    id: 'tree_master',\n    name: 'Tree Master',\n    description: 'Generated 10 knowledge trees',\n    points: 50,\n    icon: '🌳'\n  },\n  ARTICLE_READER: {\n    id: 'article_reader',\n    name: 'Reader',\n    description: 'Generated your first article',\n    points: 10,\n    icon: '📖'\n  },\n  KNOWLEDGE_SEEKER: {\n    id: 'knowledge_seeker',\n    name: 'Knowledge Seeker',\n    description: 'Generated 25 articles',\n    points: 100,\n    icon: '🎓'\n  },\n  DAILY_USER: {\n    id: 'daily_user',\n    name: '<PERSON> Learner',\n    description: 'Used the app for 7 consecutive days',\n    points: 75,\n    icon: '🔥'\n  },\n  SPEED_READER: {\n    id: 'speed_reader',\n    name: 'Speed Reader',\n    description: 'Used text-to-speech feature',\n    points: 25,\n    icon: '🗣️'\n  },\n  SHARER: {\n    id: 'sharer',\n    name: 'Sharer',\n    description: 'Exported your first article',\n    points: 20,\n    icon: '📤'\n  }\n};\n\n// Optimized level system - Much harder to level up\nconst LEVELS = [{\n  level: 1,\n  name: 'Beginner',\n  minPoints: 0,\n  icon: '🌱',\n  color: '#10b981'\n}, {\n  level: 2,\n  name: 'Explorer',\n  minPoints: 100,\n  icon: '🔍',\n  color: '#3b82f6'\n}, {\n  level: 3,\n  name: 'Scholar',\n  minPoints: 300,\n  icon: '📚',\n  color: '#8b5cf6'\n}, {\n  level: 4,\n  name: 'Expert',\n  minPoints: 750,\n  icon: '🎓',\n  color: '#f59e0b'\n}, {\n  level: 5,\n  name: 'Master',\n  minPoints: 1500,\n  icon: '👑',\n  color: '#ef4444'\n}, {\n  level: 6,\n  name: 'Grandmaster',\n  minPoints: 3000,\n  icon: '💎',\n  color: '#06b6d4'\n}, {\n  level: 7,\n  name: 'Legend',\n  minPoints: 6000,\n  icon: '⭐',\n  color: '#8b5cf6'\n}, {\n  level: 8,\n  name: 'Mythic',\n  minPoints: 12000,\n  icon: '🔥',\n  color: '#f59e0b'\n}, {\n  level: 9,\n  name: 'Immortal',\n  minPoints: 25000,\n  icon: '⚡',\n  color: '#ef4444'\n}, {\n  level: 10,\n  name: 'Omniscient',\n  minPoints: 50000,\n  icon: '🌟',\n  color: '#fbbf24'\n}];\n\n// Point values for actions - Reduced to make progression harder\nconst POINTS = {\n  TREE_GENERATED: 3,\n  ARTICLE_GENERATED: 8,\n  SPEECH_USED: 2,\n  EXPORT_USED: 3,\n  DAILY_LOGIN: 5,\n  STREAK_BONUS: 1,\n  BRANCH_EXPANDED: 5,\n  // New action for long-press branch expansion\n  FLAG_USED: 1\n};\nclass OptimizedGamificationService {\n  constructor() {\n    this.initializeUserStats();\n  }\n\n  // Initialize user statistics with minimal data\n  initializeUserStats() {\n    const defaultStats = {\n      totalPoints: 0,\n      treesGenerated: 0,\n      articlesGenerated: 0,\n      speechUsed: 0,\n      exportsUsed: 0,\n      currentStreak: 0,\n      lastLoginDate: null,\n      achievements: [],\n      createdAt: new Date().toISOString()\n    };\n    const existingStats = this.getUserStats();\n    if (!existingStats) {\n      this.saveUserStats(defaultStats);\n    }\n  }\n\n  // Get user statistics\n  getUserStats() {\n    try {\n      const stats = localStorage.getItem(STORAGE_KEYS.USER_STATS);\n      return stats ? JSON.parse(stats) : null;\n    } catch (error) {\n      console.error('Error getting user stats:', error);\n      return null;\n    }\n  }\n\n  // Save user statistics\n  saveUserStats(stats) {\n    try {\n      stats.lastUpdated = new Date().toISOString();\n      localStorage.setItem(STORAGE_KEYS.USER_STATS, JSON.stringify(stats));\n      return true;\n    } catch (error) {\n      console.error('Error saving user stats:', error);\n      return false;\n    }\n  }\n\n  // Award points for an action\n  awardPoints(action, additionalData = {}) {\n    const stats = this.getUserStats() || {};\n    let pointsAwarded = 0;\n    let newAchievements = [];\n\n    // Update stats and award points based on action\n    switch (action) {\n      case 'TREE_GENERATED':\n        stats.treesGenerated = (stats.treesGenerated || 0) + 1;\n        pointsAwarded = POINTS.TREE_GENERATED;\n\n        // Check for tree-related achievements\n        if (stats.treesGenerated === 1) {\n          newAchievements.push(CORE_ACHIEVEMENTS.FIRST_TREE);\n        } else if (stats.treesGenerated === 10) {\n          newAchievements.push(CORE_ACHIEVEMENTS.TREE_MASTER);\n        }\n        break;\n      case 'ARTICLE_GENERATED':\n        stats.articlesGenerated = (stats.articlesGenerated || 0) + 1;\n        pointsAwarded = POINTS.ARTICLE_GENERATED;\n\n        // Check for article-related achievements\n        if (stats.articlesGenerated === 1) {\n          newAchievements.push(CORE_ACHIEVEMENTS.ARTICLE_READER);\n        } else if (stats.articlesGenerated === 25) {\n          newAchievements.push(CORE_ACHIEVEMENTS.KNOWLEDGE_SEEKER);\n        }\n        break;\n      case 'SPEECH_USED':\n        stats.speechUsed = (stats.speechUsed || 0) + 1;\n        pointsAwarded = POINTS.SPEECH_USED;\n\n        // Check for speech achievement\n        if (stats.speechUsed === 1) {\n          newAchievements.push(CORE_ACHIEVEMENTS.SPEED_READER);\n        }\n        break;\n      case 'EXPORT_USED':\n        stats.exportsUsed = (stats.exportsUsed || 0) + 1;\n        pointsAwarded = POINTS.EXPORT_USED;\n\n        // Check for export achievement\n        if (stats.exportsUsed === 1) {\n          newAchievements.push(CORE_ACHIEVEMENTS.SHARER);\n        }\n        break;\n      case 'DAILY_LOGIN':\n        pointsAwarded = POINTS.DAILY_LOGIN;\n        this.updateLoginStreak(stats);\n\n        // Streak bonus\n        if (stats.currentStreak > 1) {\n          pointsAwarded += (stats.currentStreak - 1) * POINTS.STREAK_BONUS;\n        }\n\n        // Check for daily user achievement\n        if (stats.currentStreak === 7) {\n          newAchievements.push(CORE_ACHIEVEMENTS.DAILY_USER);\n        }\n        break;\n      default:\n        console.warn('Unknown action:', action);\n        return {\n          success: false,\n          pointsAwarded: 0\n        };\n    }\n\n    // Update total points\n    stats.totalPoints = (stats.totalPoints || 0) + pointsAwarded;\n\n    // Add new achievements\n    if (newAchievements.length > 0) {\n      const existingAchievements = stats.achievements || [];\n      const newAchievementIds = newAchievements.map(a => a.id);\n      const uniqueAchievements = [...existingAchievements, ...newAchievementIds.filter(id => !existingAchievements.includes(id))];\n      stats.achievements = uniqueAchievements;\n\n      // Award achievement points\n      const achievementPoints = newAchievements.reduce((total, achievement) => total + achievement.points, 0);\n      stats.totalPoints += achievementPoints;\n      pointsAwarded += achievementPoints;\n    }\n\n    // Save updated stats\n    this.saveUserStats(stats);\n    return {\n      success: true,\n      pointsAwarded,\n      newAchievements,\n      userLevel: this.getUserLevel(stats.totalPoints),\n      stats\n    };\n  }\n\n  // Update login streak\n  updateLoginStreak(stats) {\n    const today = new Date().toDateString();\n    const lastLogin = stats.lastLoginDate ? new Date(stats.lastLoginDate).toDateString() : null;\n    if (lastLogin === today) {\n      return; // Already logged in today\n    }\n    const yesterday = new Date();\n    yesterday.setDate(yesterday.getDate() - 1);\n    const yesterdayStr = yesterday.toDateString();\n    if (lastLogin === yesterdayStr) {\n      // Consecutive day login\n      stats.currentStreak = (stats.currentStreak || 0) + 1;\n    } else {\n      // Streak broken or first login\n      stats.currentStreak = 1;\n    }\n    stats.lastLoginDate = new Date().toISOString();\n  }\n\n  // Get user level based on points\n  getUserLevel(points = null) {\n    var _this$getUserStats;\n    const userPoints = points !== null ? points : ((_this$getUserStats = this.getUserStats()) === null || _this$getUserStats === void 0 ? void 0 : _this$getUserStats.totalPoints) || 0;\n    for (let i = LEVELS.length - 1; i >= 0; i--) {\n      if (userPoints >= LEVELS[i].minPoints) {\n        return LEVELS[i];\n      }\n    }\n    return LEVELS[0];\n  }\n\n  // Get progress to next level\n  getProgressToNextLevel(points = null) {\n    var _this$getUserStats2;\n    const userPoints = points !== null ? points : ((_this$getUserStats2 = this.getUserStats()) === null || _this$getUserStats2 === void 0 ? void 0 : _this$getUserStats2.totalPoints) || 0;\n    const currentLevel = this.getUserLevel(userPoints);\n    const nextLevel = LEVELS.find(level => level.minPoints > userPoints);\n    if (!nextLevel) {\n      return {\n        progress: 100,\n        pointsToNext: 0,\n        nextLevel: null\n      };\n    }\n    const pointsInCurrentLevel = userPoints - currentLevel.minPoints;\n    const pointsNeededForNext = nextLevel.minPoints - currentLevel.minPoints;\n    const progress = Math.round(pointsInCurrentLevel / pointsNeededForNext * 100);\n    return {\n      progress,\n      pointsToNext: nextLevel.minPoints - userPoints,\n      nextLevel\n    };\n  }\n\n  // Get user achievements\n  getUserAchievements() {\n    const stats = this.getUserStats();\n    if (!stats || !stats.achievements) return [];\n    return stats.achievements.map(achievementId => Object.values(CORE_ACHIEVEMENTS).find(a => a.id === achievementId)).filter(Boolean);\n  }\n\n  // Create minimalist gamification UI\n  createGamificationUI(container) {\n    var _stats$achievements;\n    if (!container) return null;\n    const stats = this.getUserStats() || {};\n    const level = this.getUserLevel();\n    const progress = this.getProgressToNextLevel();\n    const ui = document.createElement('div');\n    ui.className = 'gamification-ui';\n    ui.innerHTML = `\n      <div class=\"gamification-compact\">\n        <div class=\"level-badge\" style=\"background: ${level.color}\">\n          <span class=\"level-icon\">${level.icon}</span>\n          <span class=\"level-info\">\n            <div class=\"level-name\">${level.name}</div>\n            <div class=\"level-points\">${stats.totalPoints || 0} pts</div>\n          </span>\n        </div>\n        <div class=\"progress-bar\">\n          <div class=\"progress-fill\" style=\"width: ${progress.progress}%\"></div>\n        </div>\n        <div class=\"achievements-count\">\n          🏆 ${((_stats$achievements = stats.achievements) === null || _stats$achievements === void 0 ? void 0 : _stats$achievements.length) || 0}\n        </div>\n      </div>\n    `;\n\n    // Add CSS styles\n    const style = document.createElement('style');\n    style.textContent = `\n      .gamification-compact {\n        display: flex;\n        align-items: center;\n        gap: 12px;\n        padding: 8px 12px;\n        background: rgba(255, 255, 255, 0.95);\n        border-radius: 8px;\n        border: 1px solid #e2e8f0;\n        font-size: 12px;\n        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n      }\n      .level-badge {\n        display: flex;\n        align-items: center;\n        gap: 6px;\n        padding: 4px 8px;\n        border-radius: 6px;\n        color: white;\n        font-weight: 600;\n      }\n      .level-icon {\n        font-size: 16px;\n      }\n      .level-info {\n        display: flex;\n        flex-direction: column;\n        line-height: 1.2;\n      }\n      .level-name {\n        font-size: 11px;\n        opacity: 0.9;\n      }\n      .level-points {\n        font-size: 10px;\n        opacity: 0.8;\n      }\n      .progress-bar {\n        flex: 1;\n        height: 4px;\n        background: #e2e8f0;\n        border-radius: 2px;\n        overflow: hidden;\n      }\n      .progress-fill {\n        height: 100%;\n        background: ${level.color};\n        transition: width 0.3s ease;\n      }\n      .achievements-count {\n        font-size: 11px;\n        color: #64748b;\n        font-weight: 500;\n      }\n    `;\n    document.head.appendChild(style);\n    container.appendChild(ui);\n    return ui;\n  }\n\n  // Show achievement notification\n  showAchievementNotification(achievement) {\n    const notification = document.createElement('div');\n    notification.className = 'achievement-notification';\n    notification.innerHTML = `\n      <div class=\"achievement-content\">\n        <div class=\"achievement-icon\">${achievement.icon}</div>\n        <div class=\"achievement-text\">\n          <div class=\"achievement-title\">Achievement Unlocked!</div>\n          <div class=\"achievement-name\">${achievement.name}</div>\n          <div class=\"achievement-points\">+${achievement.points} points</div>\n        </div>\n      </div>\n    `;\n    notification.style.cssText = `\n      position: fixed;\n      top: 20px;\n      right: 20px;\n      background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n      color: white;\n      padding: 16px;\n      border-radius: 12px;\n      box-shadow: 0 8px 32px rgba(16, 185, 129, 0.3);\n      z-index: 10000;\n      animation: achievementSlideIn 0.5s ease-out;\n      max-width: 300px;\n    `;\n\n    // Add CSS animation\n    const style = document.createElement('style');\n    style.textContent = `\n      @keyframes achievementSlideIn {\n        from {\n          transform: translateX(100%);\n          opacity: 0;\n        }\n        to {\n          transform: translateX(0);\n          opacity: 1;\n        }\n      }\n      .achievement-content {\n        display: flex;\n        align-items: center;\n        gap: 12px;\n      }\n      .achievement-icon {\n        font-size: 32px;\n      }\n      .achievement-text {\n        flex: 1;\n      }\n      .achievement-title {\n        font-size: 12px;\n        opacity: 0.9;\n        margin-bottom: 2px;\n      }\n      .achievement-name {\n        font-size: 16px;\n        font-weight: 600;\n        margin-bottom: 2px;\n      }\n      .achievement-points {\n        font-size: 12px;\n        opacity: 0.8;\n      }\n    `;\n    document.head.appendChild(style);\n    document.body.appendChild(notification);\n\n    // Auto-remove after 4 seconds\n    setTimeout(() => {\n      if (notification && notification.parentNode) {\n        notification.style.animation = 'achievementSlideIn 0.3s ease-in reverse';\n        setTimeout(() => {\n          if (notification.parentNode) {\n            notification.parentNode.removeChild(notification);\n          }\n        }, 300);\n      }\n    }, 4000);\n  }\n\n  // Reset user statistics (for testing)\n  resetUserStats() {\n    localStorage.removeItem(STORAGE_KEYS.USER_STATS);\n    this.initializeUserStats();\n    return true;\n  }\n}\n\n// Create singleton instance\nconst optimizedGamificationService = new OptimizedGamificationService();\nexport default optimizedGamificationService;", "map": {"version": 3, "names": ["STORAGE_KEYS", "USER_STATS", "ACHIEVEMENTS", "CORE_ACHIEVEMENTS", "FIRST_TREE", "id", "name", "description", "points", "icon", "TREE_MASTER", "ARTICLE_READER", "KNOWLEDGE_SEEKER", "DAILY_USER", "SPEED_READER", "SHARER", "LEVELS", "level", "minPoints", "color", "POINTS", "TREE_GENERATED", "ARTICLE_GENERATED", "SPEECH_USED", "EXPORT_USED", "DAILY_LOGIN", "STREAK_BONUS", "BRANCH_EXPANDED", "FLAG_USED", "OptimizedGamificationService", "constructor", "initializeUserStats", "defaultStats", "totalPoints", "treesGenerated", "articlesGenerated", "speechUsed", "exportsUsed", "currentStreak", "lastLoginDate", "achievements", "createdAt", "Date", "toISOString", "existingStats", "getUserStats", "saveUserStats", "stats", "localStorage", "getItem", "JSON", "parse", "error", "console", "lastUpdated", "setItem", "stringify", "awardPoints", "action", "additionalData", "pointsAwarded", "newAchievements", "push", "updateLoginStreak", "warn", "success", "length", "existingAchievements", "newAchievementIds", "map", "a", "uniqueAchievements", "filter", "includes", "achievementPoints", "reduce", "total", "achievement", "userLevel", "getUserLevel", "today", "toDateString", "lastLogin", "yesterday", "setDate", "getDate", "yesterdayStr", "_this$getUserStats", "userPoints", "i", "getProgressToNextLevel", "_this$getUserStats2", "currentLevel", "nextLevel", "find", "progress", "pointsToNext", "pointsInCurrentLevel", "pointsNeededForNext", "Math", "round", "getUserAchievements", "achievementId", "Object", "values", "Boolean", "createGamificationUI", "container", "_stats$achievements", "ui", "document", "createElement", "className", "innerHTML", "style", "textContent", "head", "append<PERSON><PERSON><PERSON>", "showAchievementNotification", "notification", "cssText", "body", "setTimeout", "parentNode", "animation", "<PERSON><PERSON><PERSON><PERSON>", "resetUserStats", "removeItem", "optimizedGamificationService"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Appv1/src/services/optimizedGamificationService.js"], "sourcesContent": ["// Optimized Gamification Service - Pareto 80/20 Implementation\n// Essential gamification features with minimal complexity and maximum engagement\n\nconst STORAGE_KEYS = {\n  USER_STATS: 'knowledgeTree_userStats',\n  ACHIEVEMENTS: 'knowledgeTree_achievements'\n};\n\n// Essential achievements that provide 80% of engagement value\nconst CORE_ACHIEVEMENTS = {\n  FIRST_TREE: { id: 'first_tree', name: 'Explorer', description: 'Generated your first knowledge tree', points: 10, icon: '🌱' },\n  TREE_MASTER: { id: 'tree_master', name: 'Tree Master', description: 'Generated 10 knowledge trees', points: 50, icon: '🌳' },\n  ARTICLE_READER: { id: 'article_reader', name: 'Reader', description: 'Generated your first article', points: 10, icon: '📖' },\n  KNOWLEDGE_SEEKER: { id: 'knowledge_seeker', name: 'Knowledge Seeker', description: 'Generated 25 articles', points: 100, icon: '🎓' },\n  DAILY_USER: { id: 'daily_user', name: '<PERSON> Learner', description: 'Used the app for 7 consecutive days', points: 75, icon: '🔥' },\n  SPEED_READER: { id: 'speed_reader', name: 'Speed Reader', description: 'Used text-to-speech feature', points: 25, icon: '🗣️' },\n  SHARER: { id: 'sharer', name: 'Sharer', description: 'Exported your first article', points: 20, icon: '📤' }\n};\n\n// Optimized level system - Much harder to level up\nconst LEVELS = [\n  { level: 1, name: 'Beginner', minPoints: 0, icon: '🌱', color: '#10b981' },\n  { level: 2, name: 'Explorer', minPoints: 100, icon: '🔍', color: '#3b82f6' },\n  { level: 3, name: 'Scholar', minPoints: 300, icon: '📚', color: '#8b5cf6' },\n  { level: 4, name: 'Expert', minPoints: 750, icon: '🎓', color: '#f59e0b' },\n  { level: 5, name: 'Master', minPoints: 1500, icon: '👑', color: '#ef4444' },\n  { level: 6, name: 'Grandmaster', minPoints: 3000, icon: '💎', color: '#06b6d4' },\n  { level: 7, name: 'Legend', minPoints: 6000, icon: '⭐', color: '#8b5cf6' },\n  { level: 8, name: 'Mythic', minPoints: 12000, icon: '🔥', color: '#f59e0b' },\n  { level: 9, name: 'Immortal', minPoints: 25000, icon: '⚡', color: '#ef4444' },\n  { level: 10, name: 'Omniscient', minPoints: 50000, icon: '🌟', color: '#fbbf24' }\n];\n\n// Point values for actions - Reduced to make progression harder\nconst POINTS = {\n  TREE_GENERATED: 3,\n  ARTICLE_GENERATED: 8,\n  SPEECH_USED: 2,\n  EXPORT_USED: 3,\n  DAILY_LOGIN: 5,\n  STREAK_BONUS: 1,\n  BRANCH_EXPANDED: 5, // New action for long-press branch expansion\n  FLAG_USED: 1\n};\n\nclass OptimizedGamificationService {\n  constructor() {\n    this.initializeUserStats();\n  }\n\n  // Initialize user statistics with minimal data\n  initializeUserStats() {\n    const defaultStats = {\n      totalPoints: 0,\n      treesGenerated: 0,\n      articlesGenerated: 0,\n      speechUsed: 0,\n      exportsUsed: 0,\n      currentStreak: 0,\n      lastLoginDate: null,\n      achievements: [],\n      createdAt: new Date().toISOString()\n    };\n\n    const existingStats = this.getUserStats();\n    if (!existingStats) {\n      this.saveUserStats(defaultStats);\n    }\n  }\n\n  // Get user statistics\n  getUserStats() {\n    try {\n      const stats = localStorage.getItem(STORAGE_KEYS.USER_STATS);\n      return stats ? JSON.parse(stats) : null;\n    } catch (error) {\n      console.error('Error getting user stats:', error);\n      return null;\n    }\n  }\n\n  // Save user statistics\n  saveUserStats(stats) {\n    try {\n      stats.lastUpdated = new Date().toISOString();\n      localStorage.setItem(STORAGE_KEYS.USER_STATS, JSON.stringify(stats));\n      return true;\n    } catch (error) {\n      console.error('Error saving user stats:', error);\n      return false;\n    }\n  }\n\n  // Award points for an action\n  awardPoints(action, additionalData = {}) {\n    const stats = this.getUserStats() || {};\n    let pointsAwarded = 0;\n    let newAchievements = [];\n\n    // Update stats and award points based on action\n    switch (action) {\n      case 'TREE_GENERATED':\n        stats.treesGenerated = (stats.treesGenerated || 0) + 1;\n        pointsAwarded = POINTS.TREE_GENERATED;\n        \n        // Check for tree-related achievements\n        if (stats.treesGenerated === 1) {\n          newAchievements.push(CORE_ACHIEVEMENTS.FIRST_TREE);\n        } else if (stats.treesGenerated === 10) {\n          newAchievements.push(CORE_ACHIEVEMENTS.TREE_MASTER);\n        }\n        break;\n\n      case 'ARTICLE_GENERATED':\n        stats.articlesGenerated = (stats.articlesGenerated || 0) + 1;\n        pointsAwarded = POINTS.ARTICLE_GENERATED;\n        \n        // Check for article-related achievements\n        if (stats.articlesGenerated === 1) {\n          newAchievements.push(CORE_ACHIEVEMENTS.ARTICLE_READER);\n        } else if (stats.articlesGenerated === 25) {\n          newAchievements.push(CORE_ACHIEVEMENTS.KNOWLEDGE_SEEKER);\n        }\n        break;\n\n      case 'SPEECH_USED':\n        stats.speechUsed = (stats.speechUsed || 0) + 1;\n        pointsAwarded = POINTS.SPEECH_USED;\n        \n        // Check for speech achievement\n        if (stats.speechUsed === 1) {\n          newAchievements.push(CORE_ACHIEVEMENTS.SPEED_READER);\n        }\n        break;\n\n      case 'EXPORT_USED':\n        stats.exportsUsed = (stats.exportsUsed || 0) + 1;\n        pointsAwarded = POINTS.EXPORT_USED;\n        \n        // Check for export achievement\n        if (stats.exportsUsed === 1) {\n          newAchievements.push(CORE_ACHIEVEMENTS.SHARER);\n        }\n        break;\n\n      case 'DAILY_LOGIN':\n        pointsAwarded = POINTS.DAILY_LOGIN;\n        this.updateLoginStreak(stats);\n        \n        // Streak bonus\n        if (stats.currentStreak > 1) {\n          pointsAwarded += (stats.currentStreak - 1) * POINTS.STREAK_BONUS;\n        }\n        \n        // Check for daily user achievement\n        if (stats.currentStreak === 7) {\n          newAchievements.push(CORE_ACHIEVEMENTS.DAILY_USER);\n        }\n        break;\n\n      default:\n        console.warn('Unknown action:', action);\n        return { success: false, pointsAwarded: 0 };\n    }\n\n    // Update total points\n    stats.totalPoints = (stats.totalPoints || 0) + pointsAwarded;\n\n    // Add new achievements\n    if (newAchievements.length > 0) {\n      const existingAchievements = stats.achievements || [];\n      const newAchievementIds = newAchievements.map(a => a.id);\n      const uniqueAchievements = [...existingAchievements, ...newAchievementIds.filter(id => !existingAchievements.includes(id))];\n      \n      stats.achievements = uniqueAchievements;\n      \n      // Award achievement points\n      const achievementPoints = newAchievements.reduce((total, achievement) => total + achievement.points, 0);\n      stats.totalPoints += achievementPoints;\n      pointsAwarded += achievementPoints;\n    }\n\n    // Save updated stats\n    this.saveUserStats(stats);\n\n    return {\n      success: true,\n      pointsAwarded,\n      newAchievements,\n      userLevel: this.getUserLevel(stats.totalPoints),\n      stats\n    };\n  }\n\n  // Update login streak\n  updateLoginStreak(stats) {\n    const today = new Date().toDateString();\n    const lastLogin = stats.lastLoginDate ? new Date(stats.lastLoginDate).toDateString() : null;\n    \n    if (lastLogin === today) {\n      return; // Already logged in today\n    }\n\n    const yesterday = new Date();\n    yesterday.setDate(yesterday.getDate() - 1);\n    const yesterdayStr = yesterday.toDateString();\n\n    if (lastLogin === yesterdayStr) {\n      // Consecutive day login\n      stats.currentStreak = (stats.currentStreak || 0) + 1;\n    } else {\n      // Streak broken or first login\n      stats.currentStreak = 1;\n    }\n\n    stats.lastLoginDate = new Date().toISOString();\n  }\n\n  // Get user level based on points\n  getUserLevel(points = null) {\n    const userPoints = points !== null ? points : (this.getUserStats()?.totalPoints || 0);\n    \n    for (let i = LEVELS.length - 1; i >= 0; i--) {\n      if (userPoints >= LEVELS[i].minPoints) {\n        return LEVELS[i];\n      }\n    }\n    \n    return LEVELS[0];\n  }\n\n  // Get progress to next level\n  getProgressToNextLevel(points = null) {\n    const userPoints = points !== null ? points : (this.getUserStats()?.totalPoints || 0);\n    const currentLevel = this.getUserLevel(userPoints);\n    const nextLevel = LEVELS.find(level => level.minPoints > userPoints);\n    \n    if (!nextLevel) {\n      return { progress: 100, pointsToNext: 0, nextLevel: null };\n    }\n    \n    const pointsInCurrentLevel = userPoints - currentLevel.minPoints;\n    const pointsNeededForNext = nextLevel.minPoints - currentLevel.minPoints;\n    const progress = Math.round((pointsInCurrentLevel / pointsNeededForNext) * 100);\n    \n    return {\n      progress,\n      pointsToNext: nextLevel.minPoints - userPoints,\n      nextLevel\n    };\n  }\n\n  // Get user achievements\n  getUserAchievements() {\n    const stats = this.getUserStats();\n    if (!stats || !stats.achievements) return [];\n\n    return stats.achievements.map(achievementId => \n      Object.values(CORE_ACHIEVEMENTS).find(a => a.id === achievementId)\n    ).filter(Boolean);\n  }\n\n  // Create minimalist gamification UI\n  createGamificationUI(container) {\n    if (!container) return null;\n\n    const stats = this.getUserStats() || {};\n    const level = this.getUserLevel();\n    const progress = this.getProgressToNextLevel();\n\n    const ui = document.createElement('div');\n    ui.className = 'gamification-ui';\n    ui.innerHTML = `\n      <div class=\"gamification-compact\">\n        <div class=\"level-badge\" style=\"background: ${level.color}\">\n          <span class=\"level-icon\">${level.icon}</span>\n          <span class=\"level-info\">\n            <div class=\"level-name\">${level.name}</div>\n            <div class=\"level-points\">${stats.totalPoints || 0} pts</div>\n          </span>\n        </div>\n        <div class=\"progress-bar\">\n          <div class=\"progress-fill\" style=\"width: ${progress.progress}%\"></div>\n        </div>\n        <div class=\"achievements-count\">\n          🏆 ${stats.achievements?.length || 0}\n        </div>\n      </div>\n    `;\n\n    // Add CSS styles\n    const style = document.createElement('style');\n    style.textContent = `\n      .gamification-compact {\n        display: flex;\n        align-items: center;\n        gap: 12px;\n        padding: 8px 12px;\n        background: rgba(255, 255, 255, 0.95);\n        border-radius: 8px;\n        border: 1px solid #e2e8f0;\n        font-size: 12px;\n        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n      }\n      .level-badge {\n        display: flex;\n        align-items: center;\n        gap: 6px;\n        padding: 4px 8px;\n        border-radius: 6px;\n        color: white;\n        font-weight: 600;\n      }\n      .level-icon {\n        font-size: 16px;\n      }\n      .level-info {\n        display: flex;\n        flex-direction: column;\n        line-height: 1.2;\n      }\n      .level-name {\n        font-size: 11px;\n        opacity: 0.9;\n      }\n      .level-points {\n        font-size: 10px;\n        opacity: 0.8;\n      }\n      .progress-bar {\n        flex: 1;\n        height: 4px;\n        background: #e2e8f0;\n        border-radius: 2px;\n        overflow: hidden;\n      }\n      .progress-fill {\n        height: 100%;\n        background: ${level.color};\n        transition: width 0.3s ease;\n      }\n      .achievements-count {\n        font-size: 11px;\n        color: #64748b;\n        font-weight: 500;\n      }\n    `;\n    document.head.appendChild(style);\n\n    container.appendChild(ui);\n    return ui;\n  }\n\n  // Show achievement notification\n  showAchievementNotification(achievement) {\n    const notification = document.createElement('div');\n    notification.className = 'achievement-notification';\n    notification.innerHTML = `\n      <div class=\"achievement-content\">\n        <div class=\"achievement-icon\">${achievement.icon}</div>\n        <div class=\"achievement-text\">\n          <div class=\"achievement-title\">Achievement Unlocked!</div>\n          <div class=\"achievement-name\">${achievement.name}</div>\n          <div class=\"achievement-points\">+${achievement.points} points</div>\n        </div>\n      </div>\n    `;\n\n    notification.style.cssText = `\n      position: fixed;\n      top: 20px;\n      right: 20px;\n      background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n      color: white;\n      padding: 16px;\n      border-radius: 12px;\n      box-shadow: 0 8px 32px rgba(16, 185, 129, 0.3);\n      z-index: 10000;\n      animation: achievementSlideIn 0.5s ease-out;\n      max-width: 300px;\n    `;\n\n    // Add CSS animation\n    const style = document.createElement('style');\n    style.textContent = `\n      @keyframes achievementSlideIn {\n        from {\n          transform: translateX(100%);\n          opacity: 0;\n        }\n        to {\n          transform: translateX(0);\n          opacity: 1;\n        }\n      }\n      .achievement-content {\n        display: flex;\n        align-items: center;\n        gap: 12px;\n      }\n      .achievement-icon {\n        font-size: 32px;\n      }\n      .achievement-text {\n        flex: 1;\n      }\n      .achievement-title {\n        font-size: 12px;\n        opacity: 0.9;\n        margin-bottom: 2px;\n      }\n      .achievement-name {\n        font-size: 16px;\n        font-weight: 600;\n        margin-bottom: 2px;\n      }\n      .achievement-points {\n        font-size: 12px;\n        opacity: 0.8;\n      }\n    `;\n    document.head.appendChild(style);\n\n    document.body.appendChild(notification);\n\n    // Auto-remove after 4 seconds\n    setTimeout(() => {\n      if (notification && notification.parentNode) {\n        notification.style.animation = 'achievementSlideIn 0.3s ease-in reverse';\n        setTimeout(() => {\n          if (notification.parentNode) {\n            notification.parentNode.removeChild(notification);\n          }\n        }, 300);\n      }\n    }, 4000);\n  }\n\n  // Reset user statistics (for testing)\n  resetUserStats() {\n    localStorage.removeItem(STORAGE_KEYS.USER_STATS);\n    this.initializeUserStats();\n    return true;\n  }\n}\n\n// Create singleton instance\nconst optimizedGamificationService = new OptimizedGamificationService();\n\nexport default optimizedGamificationService;\n"], "mappings": "AAAA;AACA;;AAEA,MAAMA,YAAY,GAAG;EACnBC,UAAU,EAAE,yBAAyB;EACrCC,YAAY,EAAE;AAChB,CAAC;;AAED;AACA,MAAMC,iBAAiB,GAAG;EACxBC,UAAU,EAAE;IAAEC,EAAE,EAAE,YAAY;IAAEC,IAAI,EAAE,UAAU;IAAEC,WAAW,EAAE,qCAAqC;IAAEC,MAAM,EAAE,EAAE;IAAEC,IAAI,EAAE;EAAK,CAAC;EAC9HC,WAAW,EAAE;IAAEL,EAAE,EAAE,aAAa;IAAEC,IAAI,EAAE,aAAa;IAAEC,WAAW,EAAE,8BAA8B;IAAEC,MAAM,EAAE,EAAE;IAAEC,IAAI,EAAE;EAAK,CAAC;EAC5HE,cAAc,EAAE;IAAEN,EAAE,EAAE,gBAAgB;IAAEC,IAAI,EAAE,QAAQ;IAAEC,WAAW,EAAE,8BAA8B;IAAEC,MAAM,EAAE,EAAE;IAAEC,IAAI,EAAE;EAAK,CAAC;EAC7HG,gBAAgB,EAAE;IAAEP,EAAE,EAAE,kBAAkB;IAAEC,IAAI,EAAE,kBAAkB;IAAEC,WAAW,EAAE,uBAAuB;IAAEC,MAAM,EAAE,GAAG;IAAEC,IAAI,EAAE;EAAK,CAAC;EACrII,UAAU,EAAE;IAAER,EAAE,EAAE,YAAY;IAAEC,IAAI,EAAE,eAAe;IAAEC,WAAW,EAAE,qCAAqC;IAAEC,MAAM,EAAE,EAAE;IAAEC,IAAI,EAAE;EAAK,CAAC;EACnIK,YAAY,EAAE;IAAET,EAAE,EAAE,cAAc;IAAEC,IAAI,EAAE,cAAc;IAAEC,WAAW,EAAE,6BAA6B;IAAEC,MAAM,EAAE,EAAE;IAAEC,IAAI,EAAE;EAAM,CAAC;EAC/HM,MAAM,EAAE;IAAEV,EAAE,EAAE,QAAQ;IAAEC,IAAI,EAAE,QAAQ;IAAEC,WAAW,EAAE,6BAA6B;IAAEC,MAAM,EAAE,EAAE;IAAEC,IAAI,EAAE;EAAK;AAC7G,CAAC;;AAED;AACA,MAAMO,MAAM,GAAG,CACb;EAAEC,KAAK,EAAE,CAAC;EAAEX,IAAI,EAAE,UAAU;EAAEY,SAAS,EAAE,CAAC;EAAET,IAAI,EAAE,IAAI;EAAEU,KAAK,EAAE;AAAU,CAAC,EAC1E;EAAEF,KAAK,EAAE,CAAC;EAAEX,IAAI,EAAE,UAAU;EAAEY,SAAS,EAAE,GAAG;EAAET,IAAI,EAAE,IAAI;EAAEU,KAAK,EAAE;AAAU,CAAC,EAC5E;EAAEF,KAAK,EAAE,CAAC;EAAEX,IAAI,EAAE,SAAS;EAAEY,SAAS,EAAE,GAAG;EAAET,IAAI,EAAE,IAAI;EAAEU,KAAK,EAAE;AAAU,CAAC,EAC3E;EAAEF,KAAK,EAAE,CAAC;EAAEX,IAAI,EAAE,QAAQ;EAAEY,SAAS,EAAE,GAAG;EAAET,IAAI,EAAE,IAAI;EAAEU,KAAK,EAAE;AAAU,CAAC,EAC1E;EAAEF,KAAK,EAAE,CAAC;EAAEX,IAAI,EAAE,QAAQ;EAAEY,SAAS,EAAE,IAAI;EAAET,IAAI,EAAE,IAAI;EAAEU,KAAK,EAAE;AAAU,CAAC,EAC3E;EAAEF,KAAK,EAAE,CAAC;EAAEX,IAAI,EAAE,aAAa;EAAEY,SAAS,EAAE,IAAI;EAAET,IAAI,EAAE,IAAI;EAAEU,KAAK,EAAE;AAAU,CAAC,EAChF;EAAEF,KAAK,EAAE,CAAC;EAAEX,IAAI,EAAE,QAAQ;EAAEY,SAAS,EAAE,IAAI;EAAET,IAAI,EAAE,GAAG;EAAEU,KAAK,EAAE;AAAU,CAAC,EAC1E;EAAEF,KAAK,EAAE,CAAC;EAAEX,IAAI,EAAE,QAAQ;EAAEY,SAAS,EAAE,KAAK;EAAET,IAAI,EAAE,IAAI;EAAEU,KAAK,EAAE;AAAU,CAAC,EAC5E;EAAEF,KAAK,EAAE,CAAC;EAAEX,IAAI,EAAE,UAAU;EAAEY,SAAS,EAAE,KAAK;EAAET,IAAI,EAAE,GAAG;EAAEU,KAAK,EAAE;AAAU,CAAC,EAC7E;EAAEF,KAAK,EAAE,EAAE;EAAEX,IAAI,EAAE,YAAY;EAAEY,SAAS,EAAE,KAAK;EAAET,IAAI,EAAE,IAAI;EAAEU,KAAK,EAAE;AAAU,CAAC,CAClF;;AAED;AACA,MAAMC,MAAM,GAAG;EACbC,cAAc,EAAE,CAAC;EACjBC,iBAAiB,EAAE,CAAC;EACpBC,WAAW,EAAE,CAAC;EACdC,WAAW,EAAE,CAAC;EACdC,WAAW,EAAE,CAAC;EACdC,YAAY,EAAE,CAAC;EACfC,eAAe,EAAE,CAAC;EAAE;EACpBC,SAAS,EAAE;AACb,CAAC;AAED,MAAMC,4BAA4B,CAAC;EACjCC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,mBAAmB,CAAC,CAAC;EAC5B;;EAEA;EACAA,mBAAmBA,CAAA,EAAG;IACpB,MAAMC,YAAY,GAAG;MACnBC,WAAW,EAAE,CAAC;MACdC,cAAc,EAAE,CAAC;MACjBC,iBAAiB,EAAE,CAAC;MACpBC,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC;MACdC,aAAa,EAAE,CAAC;MAChBC,aAAa,EAAE,IAAI;MACnBC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC;IAED,MAAMC,aAAa,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IACzC,IAAI,CAACD,aAAa,EAAE;MAClB,IAAI,CAACE,aAAa,CAACd,YAAY,CAAC;IAClC;EACF;;EAEA;EACAa,YAAYA,CAAA,EAAG;IACb,IAAI;MACF,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAACjD,YAAY,CAACC,UAAU,CAAC;MAC3D,OAAO8C,KAAK,GAAGG,IAAI,CAACC,KAAK,CAACJ,KAAK,CAAC,GAAG,IAAI;IACzC,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAO,IAAI;IACb;EACF;;EAEA;EACAN,aAAaA,CAACC,KAAK,EAAE;IACnB,IAAI;MACFA,KAAK,CAACO,WAAW,GAAG,IAAIZ,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAC5CK,YAAY,CAACO,OAAO,CAACvD,YAAY,CAACC,UAAU,EAAEiD,IAAI,CAACM,SAAS,CAACT,KAAK,CAAC,CAAC;MACpE,OAAO,IAAI;IACb,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO,KAAK;IACd;EACF;;EAEA;EACAK,WAAWA,CAACC,MAAM,EAAEC,cAAc,GAAG,CAAC,CAAC,EAAE;IACvC,MAAMZ,KAAK,GAAG,IAAI,CAACF,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC;IACvC,IAAIe,aAAa,GAAG,CAAC;IACrB,IAAIC,eAAe,GAAG,EAAE;;IAExB;IACA,QAAQH,MAAM;MACZ,KAAK,gBAAgB;QACnBX,KAAK,CAACb,cAAc,GAAG,CAACa,KAAK,CAACb,cAAc,IAAI,CAAC,IAAI,CAAC;QACtD0B,aAAa,GAAGxC,MAAM,CAACC,cAAc;;QAErC;QACA,IAAI0B,KAAK,CAACb,cAAc,KAAK,CAAC,EAAE;UAC9B2B,eAAe,CAACC,IAAI,CAAC3D,iBAAiB,CAACC,UAAU,CAAC;QACpD,CAAC,MAAM,IAAI2C,KAAK,CAACb,cAAc,KAAK,EAAE,EAAE;UACtC2B,eAAe,CAACC,IAAI,CAAC3D,iBAAiB,CAACO,WAAW,CAAC;QACrD;QACA;MAEF,KAAK,mBAAmB;QACtBqC,KAAK,CAACZ,iBAAiB,GAAG,CAACY,KAAK,CAACZ,iBAAiB,IAAI,CAAC,IAAI,CAAC;QAC5DyB,aAAa,GAAGxC,MAAM,CAACE,iBAAiB;;QAExC;QACA,IAAIyB,KAAK,CAACZ,iBAAiB,KAAK,CAAC,EAAE;UACjC0B,eAAe,CAACC,IAAI,CAAC3D,iBAAiB,CAACQ,cAAc,CAAC;QACxD,CAAC,MAAM,IAAIoC,KAAK,CAACZ,iBAAiB,KAAK,EAAE,EAAE;UACzC0B,eAAe,CAACC,IAAI,CAAC3D,iBAAiB,CAACS,gBAAgB,CAAC;QAC1D;QACA;MAEF,KAAK,aAAa;QAChBmC,KAAK,CAACX,UAAU,GAAG,CAACW,KAAK,CAACX,UAAU,IAAI,CAAC,IAAI,CAAC;QAC9CwB,aAAa,GAAGxC,MAAM,CAACG,WAAW;;QAElC;QACA,IAAIwB,KAAK,CAACX,UAAU,KAAK,CAAC,EAAE;UAC1ByB,eAAe,CAACC,IAAI,CAAC3D,iBAAiB,CAACW,YAAY,CAAC;QACtD;QACA;MAEF,KAAK,aAAa;QAChBiC,KAAK,CAACV,WAAW,GAAG,CAACU,KAAK,CAACV,WAAW,IAAI,CAAC,IAAI,CAAC;QAChDuB,aAAa,GAAGxC,MAAM,CAACI,WAAW;;QAElC;QACA,IAAIuB,KAAK,CAACV,WAAW,KAAK,CAAC,EAAE;UAC3BwB,eAAe,CAACC,IAAI,CAAC3D,iBAAiB,CAACY,MAAM,CAAC;QAChD;QACA;MAEF,KAAK,aAAa;QAChB6C,aAAa,GAAGxC,MAAM,CAACK,WAAW;QAClC,IAAI,CAACsC,iBAAiB,CAAChB,KAAK,CAAC;;QAE7B;QACA,IAAIA,KAAK,CAACT,aAAa,GAAG,CAAC,EAAE;UAC3BsB,aAAa,IAAI,CAACb,KAAK,CAACT,aAAa,GAAG,CAAC,IAAIlB,MAAM,CAACM,YAAY;QAClE;;QAEA;QACA,IAAIqB,KAAK,CAACT,aAAa,KAAK,CAAC,EAAE;UAC7BuB,eAAe,CAACC,IAAI,CAAC3D,iBAAiB,CAACU,UAAU,CAAC;QACpD;QACA;MAEF;QACEwC,OAAO,CAACW,IAAI,CAAC,iBAAiB,EAAEN,MAAM,CAAC;QACvC,OAAO;UAAEO,OAAO,EAAE,KAAK;UAAEL,aAAa,EAAE;QAAE,CAAC;IAC/C;;IAEA;IACAb,KAAK,CAACd,WAAW,GAAG,CAACc,KAAK,CAACd,WAAW,IAAI,CAAC,IAAI2B,aAAa;;IAE5D;IACA,IAAIC,eAAe,CAACK,MAAM,GAAG,CAAC,EAAE;MAC9B,MAAMC,oBAAoB,GAAGpB,KAAK,CAACP,YAAY,IAAI,EAAE;MACrD,MAAM4B,iBAAiB,GAAGP,eAAe,CAACQ,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACjE,EAAE,CAAC;MACxD,MAAMkE,kBAAkB,GAAG,CAAC,GAAGJ,oBAAoB,EAAE,GAAGC,iBAAiB,CAACI,MAAM,CAACnE,EAAE,IAAI,CAAC8D,oBAAoB,CAACM,QAAQ,CAACpE,EAAE,CAAC,CAAC,CAAC;MAE3H0C,KAAK,CAACP,YAAY,GAAG+B,kBAAkB;;MAEvC;MACA,MAAMG,iBAAiB,GAAGb,eAAe,CAACc,MAAM,CAAC,CAACC,KAAK,EAAEC,WAAW,KAAKD,KAAK,GAAGC,WAAW,CAACrE,MAAM,EAAE,CAAC,CAAC;MACvGuC,KAAK,CAACd,WAAW,IAAIyC,iBAAiB;MACtCd,aAAa,IAAIc,iBAAiB;IACpC;;IAEA;IACA,IAAI,CAAC5B,aAAa,CAACC,KAAK,CAAC;IAEzB,OAAO;MACLkB,OAAO,EAAE,IAAI;MACbL,aAAa;MACbC,eAAe;MACfiB,SAAS,EAAE,IAAI,CAACC,YAAY,CAAChC,KAAK,CAACd,WAAW,CAAC;MAC/Cc;IACF,CAAC;EACH;;EAEA;EACAgB,iBAAiBA,CAAChB,KAAK,EAAE;IACvB,MAAMiC,KAAK,GAAG,IAAItC,IAAI,CAAC,CAAC,CAACuC,YAAY,CAAC,CAAC;IACvC,MAAMC,SAAS,GAAGnC,KAAK,CAACR,aAAa,GAAG,IAAIG,IAAI,CAACK,KAAK,CAACR,aAAa,CAAC,CAAC0C,YAAY,CAAC,CAAC,GAAG,IAAI;IAE3F,IAAIC,SAAS,KAAKF,KAAK,EAAE;MACvB,OAAO,CAAC;IACV;IAEA,MAAMG,SAAS,GAAG,IAAIzC,IAAI,CAAC,CAAC;IAC5ByC,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;IAC1C,MAAMC,YAAY,GAAGH,SAAS,CAACF,YAAY,CAAC,CAAC;IAE7C,IAAIC,SAAS,KAAKI,YAAY,EAAE;MAC9B;MACAvC,KAAK,CAACT,aAAa,GAAG,CAACS,KAAK,CAACT,aAAa,IAAI,CAAC,IAAI,CAAC;IACtD,CAAC,MAAM;MACL;MACAS,KAAK,CAACT,aAAa,GAAG,CAAC;IACzB;IAEAS,KAAK,CAACR,aAAa,GAAG,IAAIG,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAChD;;EAEA;EACAoC,YAAYA,CAACvE,MAAM,GAAG,IAAI,EAAE;IAAA,IAAA+E,kBAAA;IAC1B,MAAMC,UAAU,GAAGhF,MAAM,KAAK,IAAI,GAAGA,MAAM,GAAI,EAAA+E,kBAAA,OAAI,CAAC1C,YAAY,CAAC,CAAC,cAAA0C,kBAAA,uBAAnBA,kBAAA,CAAqBtD,WAAW,KAAI,CAAE;IAErF,KAAK,IAAIwD,CAAC,GAAGzE,MAAM,CAACkD,MAAM,GAAG,CAAC,EAAEuB,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC3C,IAAID,UAAU,IAAIxE,MAAM,CAACyE,CAAC,CAAC,CAACvE,SAAS,EAAE;QACrC,OAAOF,MAAM,CAACyE,CAAC,CAAC;MAClB;IACF;IAEA,OAAOzE,MAAM,CAAC,CAAC,CAAC;EAClB;;EAEA;EACA0E,sBAAsBA,CAAClF,MAAM,GAAG,IAAI,EAAE;IAAA,IAAAmF,mBAAA;IACpC,MAAMH,UAAU,GAAGhF,MAAM,KAAK,IAAI,GAAGA,MAAM,GAAI,EAAAmF,mBAAA,OAAI,CAAC9C,YAAY,CAAC,CAAC,cAAA8C,mBAAA,uBAAnBA,mBAAA,CAAqB1D,WAAW,KAAI,CAAE;IACrF,MAAM2D,YAAY,GAAG,IAAI,CAACb,YAAY,CAACS,UAAU,CAAC;IAClD,MAAMK,SAAS,GAAG7E,MAAM,CAAC8E,IAAI,CAAC7E,KAAK,IAAIA,KAAK,CAACC,SAAS,GAAGsE,UAAU,CAAC;IAEpE,IAAI,CAACK,SAAS,EAAE;MACd,OAAO;QAAEE,QAAQ,EAAE,GAAG;QAAEC,YAAY,EAAE,CAAC;QAAEH,SAAS,EAAE;MAAK,CAAC;IAC5D;IAEA,MAAMI,oBAAoB,GAAGT,UAAU,GAAGI,YAAY,CAAC1E,SAAS;IAChE,MAAMgF,mBAAmB,GAAGL,SAAS,CAAC3E,SAAS,GAAG0E,YAAY,CAAC1E,SAAS;IACxE,MAAM6E,QAAQ,GAAGI,IAAI,CAACC,KAAK,CAAEH,oBAAoB,GAAGC,mBAAmB,GAAI,GAAG,CAAC;IAE/E,OAAO;MACLH,QAAQ;MACRC,YAAY,EAAEH,SAAS,CAAC3E,SAAS,GAAGsE,UAAU;MAC9CK;IACF,CAAC;EACH;;EAEA;EACAQ,mBAAmBA,CAAA,EAAG;IACpB,MAAMtD,KAAK,GAAG,IAAI,CAACF,YAAY,CAAC,CAAC;IACjC,IAAI,CAACE,KAAK,IAAI,CAACA,KAAK,CAACP,YAAY,EAAE,OAAO,EAAE;IAE5C,OAAOO,KAAK,CAACP,YAAY,CAAC6B,GAAG,CAACiC,aAAa,IACzCC,MAAM,CAACC,MAAM,CAACrG,iBAAiB,CAAC,CAAC2F,IAAI,CAACxB,CAAC,IAAIA,CAAC,CAACjE,EAAE,KAAKiG,aAAa,CACnE,CAAC,CAAC9B,MAAM,CAACiC,OAAO,CAAC;EACnB;;EAEA;EACAC,oBAAoBA,CAACC,SAAS,EAAE;IAAA,IAAAC,mBAAA;IAC9B,IAAI,CAACD,SAAS,EAAE,OAAO,IAAI;IAE3B,MAAM5D,KAAK,GAAG,IAAI,CAACF,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC;IACvC,MAAM5B,KAAK,GAAG,IAAI,CAAC8D,YAAY,CAAC,CAAC;IACjC,MAAMgB,QAAQ,GAAG,IAAI,CAACL,sBAAsB,CAAC,CAAC;IAE9C,MAAMmB,EAAE,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACxCF,EAAE,CAACG,SAAS,GAAG,iBAAiB;IAChCH,EAAE,CAACI,SAAS,GAAG;AACnB;AACA,sDAAsDhG,KAAK,CAACE,KAAK;AACjE,qCAAqCF,KAAK,CAACR,IAAI;AAC/C;AACA,sCAAsCQ,KAAK,CAACX,IAAI;AAChD,wCAAwCyC,KAAK,CAACd,WAAW,IAAI,CAAC;AAC9D;AACA;AACA;AACA,qDAAqD8D,QAAQ,CAACA,QAAQ;AACtE;AACA;AACA,eAAe,EAAAa,mBAAA,GAAA7D,KAAK,CAACP,YAAY,cAAAoE,mBAAA,uBAAlBA,mBAAA,CAAoB1C,MAAM,KAAI,CAAC;AAC9C;AACA;AACA,KAAK;;IAED;IACA,MAAMgD,KAAK,GAAGJ,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC7CG,KAAK,CAACC,WAAW,GAAG;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsBlG,KAAK,CAACE,KAAK;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;IACD2F,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACH,KAAK,CAAC;IAEhCP,SAAS,CAACU,WAAW,CAACR,EAAE,CAAC;IACzB,OAAOA,EAAE;EACX;;EAEA;EACAS,2BAA2BA,CAACzC,WAAW,EAAE;IACvC,MAAM0C,YAAY,GAAGT,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAClDQ,YAAY,CAACP,SAAS,GAAG,0BAA0B;IACnDO,YAAY,CAACN,SAAS,GAAG;AAC7B;AACA,wCAAwCpC,WAAW,CAACpE,IAAI;AACxD;AACA;AACA,0CAA0CoE,WAAW,CAACvE,IAAI;AAC1D,6CAA6CuE,WAAW,CAACrE,MAAM;AAC/D;AACA;AACA,KAAK;IAED+G,YAAY,CAACL,KAAK,CAACM,OAAO,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;IAED;IACA,MAAMN,KAAK,GAAGJ,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC7CG,KAAK,CAACC,WAAW,GAAG;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;IACDL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACH,KAAK,CAAC;IAEhCJ,QAAQ,CAACW,IAAI,CAACJ,WAAW,CAACE,YAAY,CAAC;;IAEvC;IACAG,UAAU,CAAC,MAAM;MACf,IAAIH,YAAY,IAAIA,YAAY,CAACI,UAAU,EAAE;QAC3CJ,YAAY,CAACL,KAAK,CAACU,SAAS,GAAG,yCAAyC;QACxEF,UAAU,CAAC,MAAM;UACf,IAAIH,YAAY,CAACI,UAAU,EAAE;YAC3BJ,YAAY,CAACI,UAAU,CAACE,WAAW,CAACN,YAAY,CAAC;UACnD;QACF,CAAC,EAAE,GAAG,CAAC;MACT;IACF,CAAC,EAAE,IAAI,CAAC;EACV;;EAEA;EACAO,cAAcA,CAAA,EAAG;IACf9E,YAAY,CAAC+E,UAAU,CAAC/H,YAAY,CAACC,UAAU,CAAC;IAChD,IAAI,CAAC8B,mBAAmB,CAAC,CAAC;IAC1B,OAAO,IAAI;EACb;AACF;;AAEA;AACA,MAAMiG,4BAA4B,GAAG,IAAInG,4BAA4B,CAAC,CAAC;AAEvE,eAAemG,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}