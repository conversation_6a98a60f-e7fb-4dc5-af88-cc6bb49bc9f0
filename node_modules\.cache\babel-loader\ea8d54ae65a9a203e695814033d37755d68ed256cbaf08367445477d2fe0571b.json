{"ast": null, "code": "var _s = $RefreshSig$();\n// 🌍 Pareto 80/20 Localization System\n// Only translates the 20% of text that represents 80% of user experience\n\nimport React from 'react';\nconst translations = {\n  en: {\n    // Header & Navigation (High Impact)\n    appTitle: \"🌳 Knowledge Tree Explorer\",\n    quickLogin: \"🚀 Quick Login\",\n    welcome: \"Welcome\",\n    backToTree: \"← Back to Tree\",\n    // Main Actions (Critical)\n    exploreKnowledge: \"Explore Knowledge 🚀\",\n    generating: \"Generating...\",\n    loading: \"Loading...\",\n    generate: \"🚀 Generate\",\n    // Input & Forms (Essential)\n    topicPlaceholder: \"e.g., Quantum Physics, Machine Learning, History of Art...\",\n    loginRequired: \"Please log in to access the knowledge tree generator.\",\n    quickLoginDev: \"🚀 Quick Login (Development)\",\n    // Article & Content (User-facing)\n    partOf: \"Part of:\",\n    flags: \"Flags:\",\n    topics: \"Topics:\",\n    // Gestures & Hints (User Guidance)\n    gestureHint: \"💡 Double-tap for flags • Long-press for quick article\",\n    // Export & Actions (Functional)\n    copyToClipboard: \"📋 Copy\",\n    exportPDF: \"📄 PDF\",\n    exportWord: \"📝 Word\",\n    // Speech Controls (Interactive)\n    play: \"▶️\",\n    pause: \"⏸️\",\n    stop: \"⏹️\",\n    speed: \"Speed\",\n    // Gamification (Engagement)\n    level: \"Level\",\n    points: \"Points\",\n    achievements: \"Achievements\",\n    leaderboard: \"🏆 Leaderboard\",\n    // Error Messages (Critical)\n    error: \"⚠️ Error\",\n    tryAgain: \"Try Again\",\n    // Additional UI Elements\n    selectBranch: \"Select a branch to explore in detail\",\n    failedToExpand: \"Failed to expand branch. Please try again.\",\n    sources: \"Sources & Further Reading\",\n    noSources: \"No sources available\",\n    readAloud: \"Read Aloud\",\n    stopReading: \"Stop Reading\",\n    // Flag Descriptions (Content Quality)\n    flagArticle: \"Comprehensive article format\",\n    flagExamples: \"Include practical examples\",\n    flagQuiz: \"Interactive quiz questions\",\n    flagVisual: \"Include diagrams and visualizations\",\n    flagPath: \"Structured learning progression\",\n    flagCase: \"Real-world case studies\",\n    flagRomanian: \"Adapted for Romanian context\"\n  },\n  ro: {\n    // Header & Navigation (High Impact)\n    appTitle: \"🌳 Explorator Arbore Cunoștințe\",\n    quickLogin: \"🚀 Login Rapid\",\n    welcome: \"Bun venit\",\n    backToTree: \"← Înapoi la Arbore\",\n    // Main Actions (Critical)\n    exploreKnowledge: \"Explorează Cunoștințe 🚀\",\n    generating: \"Se generează...\",\n    loading: \"Se încarcă...\",\n    generate: \"🚀 Generează\",\n    // Input & Forms (Essential)\n    topicPlaceholder: \"ex: Fizica Cuantică, Machine Learning, Istoria Artei...\",\n    loginRequired: \"Te rugăm să te conectezi pentru a accesa generatorul de arbori de cunoștințe.\",\n    quickLoginDev: \"🚀 Login Rapid (Dezvoltare)\",\n    // Article & Content (User-facing)\n    partOf: \"Parte din:\",\n    flags: \"Flag-uri:\",\n    topics: \"Subiecte:\",\n    // Gestures & Hints (User Guidance)\n    gestureHint: \"💡 Dublu-tap pentru flag-uri • Apăsare lungă pentru articol rapid\",\n    // Export & Actions (Functional)\n    copyToClipboard: \"📋 Copiază\",\n    exportPDF: \"📄 PDF\",\n    exportWord: \"📝 Word\",\n    // Speech Controls (Interactive)\n    play: \"▶️\",\n    pause: \"⏸️\",\n    stop: \"⏹️\",\n    speed: \"Viteză\",\n    // Gamification (Engagement)\n    level: \"Nivel\",\n    points: \"Puncte\",\n    achievements: \"Realizări\",\n    leaderboard: \"🏆 Clasament\",\n    // Error Messages (Critical)\n    error: \"⚠️ Eroare\",\n    tryAgain: \"Încearcă din nou\",\n    // Additional UI Elements\n    selectBranch: \"Selectează o ramură pentru a explora în detaliu\",\n    failedToExpand: \"Nu s-a putut extinde ramura. Te rugăm să încerci din nou.\",\n    sources: \"Surse și Lectură Suplimentară\",\n    noSources: \"Nu sunt surse disponibile\",\n    readAloud: \"Citește cu Voce Tare\",\n    stopReading: \"Oprește Citirea\",\n    // Flag Descriptions (Content Quality)\n    flagArticle: \"Format articol comprehensiv\",\n    flagExamples: \"Include exemple practice\",\n    flagQuiz: \"Întrebări interactive de quiz\",\n    flagVisual: \"Include diagrame și vizualizări\",\n    flagPath: \"Progresie structurată de învățare\",\n    flagCase: \"Studii de caz din lumea reală\",\n    flagRomanian: \"Adaptat pentru contextul românesc\"\n  }\n};\n\n// Current language state\nlet currentLanguage = localStorage.getItem('language') || 'en';\n\n// Translation function - Simple and efficient\nexport const t = key => {\n  var _translations$current;\n  return ((_translations$current = translations[currentLanguage]) === null || _translations$current === void 0 ? void 0 : _translations$current[key]) || translations.en[key] || key;\n};\n\n// Language switcher\nexport const setLanguage = lang => {\n  if (translations[lang]) {\n    currentLanguage = lang;\n    localStorage.setItem('language', lang);\n    // Trigger re-render by dispatching custom event\n    window.dispatchEvent(new CustomEvent('languageChanged', {\n      detail: lang\n    }));\n    return true;\n  }\n  return false;\n};\n\n// Get current language\nexport const getCurrentLanguage = () => currentLanguage;\n\n// Get available languages\nexport const getAvailableLanguages = () => [{\n  code: 'en',\n  name: 'English',\n  flag: 'EN'\n}, {\n  code: 'ro',\n  name: 'Română',\n  flag: 'RO'\n}];\n\n// Hook for React components to re-render on language change\nexport const useTranslation = () => {\n  _s();\n  const [, forceUpdate] = React.useReducer(x => x + 1, 0);\n  React.useEffect(() => {\n    const handleLanguageChange = () => forceUpdate();\n    window.addEventListener('languageChanged', handleLanguageChange);\n    return () => window.removeEventListener('languageChanged', handleLanguageChange);\n  }, []);\n  return {\n    t,\n    setLanguage,\n    currentLanguage: getCurrentLanguage()\n  };\n};\n\n// Default export\n_s(useTranslation, \"BVjwI9c9ZHx95mBYS0XqYwQKDwk=\");\nconst i18nService = {\n  t,\n  setLanguage,\n  getCurrentLanguage,\n  getAvailableLanguages,\n  useTranslation\n};\nexport default i18nService;", "map": {"version": 3, "names": ["React", "translations", "en", "appTitle", "quickLogin", "welcome", "backToTree", "exploreKnowledge", "generating", "loading", "generate", "topicPlaceholder", "loginRequired", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "partOf", "flags", "topics", "gestureHint", "copyToClipboard", "exportPDF", "exportWord", "play", "pause", "stop", "speed", "level", "points", "achievements", "leaderboard", "error", "try<PERSON><PERSON>n", "selectBranch", "failedToExpand", "sources", "noSources", "readAloud", "stopReading", "flagArticle", "flag<PERSON>xa<PERSON>s", "flagQuiz", "flagVisual", "flagPath", "flagCase", "flagRomanian", "ro", "currentLanguage", "localStorage", "getItem", "t", "key", "_translations$current", "setLanguage", "lang", "setItem", "window", "dispatchEvent", "CustomEvent", "detail", "getCurrentLanguage", "getAvailableLanguages", "code", "name", "flag", "useTranslation", "_s", "forceUpdate", "useReducer", "x", "useEffect", "handleLanguageChange", "addEventListener", "removeEventListener", "i18nService"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Appv1/src/utils/i18n.js"], "sourcesContent": ["// 🌍 Pareto 80/20 Localization System\n// Only translates the 20% of text that represents 80% of user experience\n\nimport React from 'react';\n\nconst translations = {\n  en: {\n    // Header & Navigation (High Impact)\n    appTitle: \"🌳 Knowledge Tree Explorer\",\n    quickLogin: \"🚀 Quick Login\",\n    welcome: \"Welcome\",\n    backToTree: \"← Back to Tree\",\n    \n    // Main Actions (Critical)\n    exploreKnowledge: \"Explore Knowledge 🚀\",\n    generating: \"Generating...\",\n    loading: \"Loading...\",\n    generate: \"🚀 Generate\",\n    \n    // Input & Forms (Essential)\n    topicPlaceholder: \"e.g., Quantum Physics, Machine Learning, History of Art...\",\n    loginRequired: \"Please log in to access the knowledge tree generator.\",\n    quickLoginDev: \"🚀 Quick Login (Development)\",\n    \n    // Article & Content (User-facing)\n    partOf: \"Part of:\",\n    flags: \"Flags:\",\n    topics: \"Topics:\",\n    \n    // Gestures & Hints (User Guidance)\n    gestureHint: \"💡 Double-tap for flags • Long-press for quick article\",\n    \n    // Export & Actions (Functional)\n    copyToClipboard: \"📋 Copy\",\n    exportPDF: \"📄 PDF\", \n    exportWord: \"📝 Word\",\n    \n    // Speech Controls (Interactive)\n    play: \"▶️\",\n    pause: \"⏸️\",\n    stop: \"⏹️\",\n    speed: \"Speed\",\n    \n    // Gamification (Engagement)\n    level: \"Level\",\n    points: \"Points\",\n    achievements: \"Achievements\",\n    leaderboard: \"🏆 Leaderboard\",\n    \n    // Error Messages (Critical)\n    error: \"⚠️ Error\",\n    tryAgain: \"Try Again\",\n    \n    // Additional UI Elements\n    selectBranch: \"Select a branch to explore in detail\",\n    failedToExpand: \"Failed to expand branch. Please try again.\",\n    sources: \"Sources & Further Reading\",\n    noSources: \"No sources available\",\n    readAloud: \"Read Aloud\",\n    stopReading: \"Stop Reading\",\n\n    // Flag Descriptions (Content Quality)\n    flagArticle: \"Comprehensive article format\",\n    flagExamples: \"Include practical examples\",\n    flagQuiz: \"Interactive quiz questions\",\n    flagVisual: \"Include diagrams and visualizations\",\n    flagPath: \"Structured learning progression\",\n    flagCase: \"Real-world case studies\",\n    flagRomanian: \"Adapted for Romanian context\"\n  },\n  \n  ro: {\n    // Header & Navigation (High Impact)\n    appTitle: \"🌳 Explorator Arbore Cunoștințe\",\n    quickLogin: \"🚀 Login Rapid\",\n    welcome: \"Bun venit\",\n    backToTree: \"← Înapoi la Arbore\",\n    \n    // Main Actions (Critical)\n    exploreKnowledge: \"Explorează Cunoștințe 🚀\",\n    generating: \"Se generează...\",\n    loading: \"Se încarcă...\",\n    generate: \"🚀 Generează\",\n    \n    // Input & Forms (Essential)\n    topicPlaceholder: \"ex: Fizica Cuantică, Machine Learning, Istoria Artei...\",\n    loginRequired: \"Te rugăm să te conectezi pentru a accesa generatorul de arbori de cunoștințe.\",\n    quickLoginDev: \"🚀 Login Rapid (Dezvoltare)\",\n    \n    // Article & Content (User-facing)\n    partOf: \"Parte din:\",\n    flags: \"Flag-uri:\",\n    topics: \"Subiecte:\",\n    \n    // Gestures & Hints (User Guidance)\n    gestureHint: \"💡 Dublu-tap pentru flag-uri • Apăsare lungă pentru articol rapid\",\n    \n    // Export & Actions (Functional)\n    copyToClipboard: \"📋 Copiază\",\n    exportPDF: \"📄 PDF\",\n    exportWord: \"📝 Word\",\n    \n    // Speech Controls (Interactive)\n    play: \"▶️\",\n    pause: \"⏸️\", \n    stop: \"⏹️\",\n    speed: \"Viteză\",\n    \n    // Gamification (Engagement)\n    level: \"Nivel\",\n    points: \"Puncte\",\n    achievements: \"Realizări\",\n    leaderboard: \"🏆 Clasament\",\n    \n    // Error Messages (Critical)\n    error: \"⚠️ Eroare\",\n    tryAgain: \"Încearcă din nou\",\n    \n    // Additional UI Elements\n    selectBranch: \"Selectează o ramură pentru a explora în detaliu\",\n    failedToExpand: \"Nu s-a putut extinde ramura. Te rugăm să încerci din nou.\",\n    sources: \"Surse și Lectură Suplimentară\",\n    noSources: \"Nu sunt surse disponibile\",\n    readAloud: \"Citește cu Voce Tare\",\n    stopReading: \"Oprește Citirea\",\n\n    // Flag Descriptions (Content Quality)\n    flagArticle: \"Format articol comprehensiv\",\n    flagExamples: \"Include exemple practice\",\n    flagQuiz: \"Întrebări interactive de quiz\",\n    flagVisual: \"Include diagrame și vizualizări\",\n    flagPath: \"Progresie structurată de învățare\",\n    flagCase: \"Studii de caz din lumea reală\",\n    flagRomanian: \"Adaptat pentru contextul românesc\"\n  }\n};\n\n// Current language state\nlet currentLanguage = localStorage.getItem('language') || 'en';\n\n// Translation function - Simple and efficient\nexport const t = (key) => {\n  return translations[currentLanguage]?.[key] || translations.en[key] || key;\n};\n\n// Language switcher\nexport const setLanguage = (lang) => {\n  if (translations[lang]) {\n    currentLanguage = lang;\n    localStorage.setItem('language', lang);\n    // Trigger re-render by dispatching custom event\n    window.dispatchEvent(new CustomEvent('languageChanged', { detail: lang }));\n    return true;\n  }\n  return false;\n};\n\n// Get current language\nexport const getCurrentLanguage = () => currentLanguage;\n\n// Get available languages\nexport const getAvailableLanguages = () => [\n  { code: 'en', name: 'English', flag: 'EN' },\n  { code: 'ro', name: 'Română', flag: 'RO' }\n];\n\n// Hook for React components to re-render on language change\nexport const useTranslation = () => {\n  const [, forceUpdate] = React.useReducer(x => x + 1, 0);\n\n  React.useEffect(() => {\n    const handleLanguageChange = () => forceUpdate();\n    window.addEventListener('languageChanged', handleLanguageChange);\n    return () => window.removeEventListener('languageChanged', handleLanguageChange);\n  }, []);\n\n  return { t, setLanguage, currentLanguage: getCurrentLanguage() };\n};\n\n// Default export\nconst i18nService = { t, setLanguage, getCurrentLanguage, getAvailableLanguages, useTranslation };\nexport default i18nService;\n"], "mappings": ";AAAA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAG;EACnBC,EAAE,EAAE;IACF;IACAC,QAAQ,EAAE,4BAA4B;IACtCC,UAAU,EAAE,gBAAgB;IAC5BC,OAAO,EAAE,SAAS;IAClBC,UAAU,EAAE,gBAAgB;IAE5B;IACAC,gBAAgB,EAAE,sBAAsB;IACxCC,UAAU,EAAE,eAAe;IAC3BC,OAAO,EAAE,YAAY;IACrBC,QAAQ,EAAE,aAAa;IAEvB;IACAC,gBAAgB,EAAE,4DAA4D;IAC9EC,aAAa,EAAE,uDAAuD;IACtEC,aAAa,EAAE,8BAA8B;IAE7C;IACAC,MAAM,EAAE,UAAU;IAClBC,KAAK,EAAE,QAAQ;IACfC,MAAM,EAAE,SAAS;IAEjB;IACAC,WAAW,EAAE,wDAAwD;IAErE;IACAC,eAAe,EAAE,SAAS;IAC1BC,SAAS,EAAE,QAAQ;IACnBC,UAAU,EAAE,SAAS;IAErB;IACAC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,OAAO;IAEd;IACAC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,QAAQ;IAChBC,YAAY,EAAE,cAAc;IAC5BC,WAAW,EAAE,gBAAgB;IAE7B;IACAC,KAAK,EAAE,UAAU;IACjBC,QAAQ,EAAE,WAAW;IAErB;IACAC,YAAY,EAAE,sCAAsC;IACpDC,cAAc,EAAE,4CAA4C;IAC5DC,OAAO,EAAE,2BAA2B;IACpCC,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE,YAAY;IACvBC,WAAW,EAAE,cAAc;IAE3B;IACAC,WAAW,EAAE,8BAA8B;IAC3CC,YAAY,EAAE,4BAA4B;IAC1CC,QAAQ,EAAE,4BAA4B;IACtCC,UAAU,EAAE,qCAAqC;IACjDC,QAAQ,EAAE,iCAAiC;IAC3CC,QAAQ,EAAE,yBAAyB;IACnCC,YAAY,EAAE;EAChB,CAAC;EAEDC,EAAE,EAAE;IACF;IACAzC,QAAQ,EAAE,iCAAiC;IAC3CC,UAAU,EAAE,gBAAgB;IAC5BC,OAAO,EAAE,WAAW;IACpBC,UAAU,EAAE,oBAAoB;IAEhC;IACAC,gBAAgB,EAAE,0BAA0B;IAC5CC,UAAU,EAAE,iBAAiB;IAC7BC,OAAO,EAAE,eAAe;IACxBC,QAAQ,EAAE,cAAc;IAExB;IACAC,gBAAgB,EAAE,yDAAyD;IAC3EC,aAAa,EAAE,+EAA+E;IAC9FC,aAAa,EAAE,6BAA6B;IAE5C;IACAC,MAAM,EAAE,YAAY;IACpBC,KAAK,EAAE,WAAW;IAClBC,MAAM,EAAE,WAAW;IAEnB;IACAC,WAAW,EAAE,mEAAmE;IAEhF;IACAC,eAAe,EAAE,YAAY;IAC7BC,SAAS,EAAE,QAAQ;IACnBC,UAAU,EAAE,SAAS;IAErB;IACAC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,QAAQ;IAEf;IACAC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,QAAQ;IAChBC,YAAY,EAAE,WAAW;IACzBC,WAAW,EAAE,cAAc;IAE3B;IACAC,KAAK,EAAE,WAAW;IAClBC,QAAQ,EAAE,kBAAkB;IAE5B;IACAC,YAAY,EAAE,iDAAiD;IAC/DC,cAAc,EAAE,2DAA2D;IAC3EC,OAAO,EAAE,+BAA+B;IACxCC,SAAS,EAAE,2BAA2B;IACtCC,SAAS,EAAE,sBAAsB;IACjCC,WAAW,EAAE,iBAAiB;IAE9B;IACAC,WAAW,EAAE,6BAA6B;IAC1CC,YAAY,EAAE,0BAA0B;IACxCC,QAAQ,EAAE,+BAA+B;IACzCC,UAAU,EAAE,iCAAiC;IAC7CC,QAAQ,EAAE,mCAAmC;IAC7CC,QAAQ,EAAE,+BAA+B;IACzCC,YAAY,EAAE;EAChB;AACF,CAAC;;AAED;AACA,IAAIE,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAI,IAAI;;AAE9D;AACA,OAAO,MAAMC,CAAC,GAAIC,GAAG,IAAK;EAAA,IAAAC,qBAAA;EACxB,OAAO,EAAAA,qBAAA,GAAAjD,YAAY,CAAC4C,eAAe,CAAC,cAAAK,qBAAA,uBAA7BA,qBAAA,CAAgCD,GAAG,CAAC,KAAIhD,YAAY,CAACC,EAAE,CAAC+C,GAAG,CAAC,IAAIA,GAAG;AAC5E,CAAC;;AAED;AACA,OAAO,MAAME,WAAW,GAAIC,IAAI,IAAK;EACnC,IAAInD,YAAY,CAACmD,IAAI,CAAC,EAAE;IACtBP,eAAe,GAAGO,IAAI;IACtBN,YAAY,CAACO,OAAO,CAAC,UAAU,EAAED,IAAI,CAAC;IACtC;IACAE,MAAM,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,iBAAiB,EAAE;MAAEC,MAAM,EAAEL;IAAK,CAAC,CAAC,CAAC;IAC1E,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd,CAAC;;AAED;AACA,OAAO,MAAMM,kBAAkB,GAAGA,CAAA,KAAMb,eAAe;;AAEvD;AACA,OAAO,MAAMc,qBAAqB,GAAGA,CAAA,KAAM,CACzC;EAAEC,IAAI,EAAE,IAAI;EAAEC,IAAI,EAAE,SAAS;EAAEC,IAAI,EAAE;AAAK,CAAC,EAC3C;EAAEF,IAAI,EAAE,IAAI;EAAEC,IAAI,EAAE,QAAQ;EAAEC,IAAI,EAAE;AAAK,CAAC,CAC3C;;AAED;AACA,OAAO,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM,GAAGC,WAAW,CAAC,GAAGjE,KAAK,CAACkE,UAAU,CAACC,CAAC,IAAIA,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;EAEvDnE,KAAK,CAACoE,SAAS,CAAC,MAAM;IACpB,MAAMC,oBAAoB,GAAGA,CAAA,KAAMJ,WAAW,CAAC,CAAC;IAChDX,MAAM,CAACgB,gBAAgB,CAAC,iBAAiB,EAAED,oBAAoB,CAAC;IAChE,OAAO,MAAMf,MAAM,CAACiB,mBAAmB,CAAC,iBAAiB,EAAEF,oBAAoB,CAAC;EAClF,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IAAErB,CAAC;IAAEG,WAAW;IAAEN,eAAe,EAAEa,kBAAkB,CAAC;EAAE,CAAC;AAClE,CAAC;;AAED;AAAAM,EAAA,CAZaD,cAAc;AAa3B,MAAMS,WAAW,GAAG;EAAExB,CAAC;EAAEG,WAAW;EAAEO,kBAAkB;EAAEC,qBAAqB;EAAEI;AAAe,CAAC;AACjG,eAAeS,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}