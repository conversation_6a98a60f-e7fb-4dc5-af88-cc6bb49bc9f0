[{"C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\App.jsx": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\storageService.js": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\openRouterService.js": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\gamificationService.js": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\auth\\AuthModal.jsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\user\\UserDashboard.jsx": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\subscription\\SubscriptionPlans.jsx": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\auth\\RegisterForm.jsx": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\auth\\LoginForm.jsx": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\gamification\\Leaderboard.jsx": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\gamification\\UserStats.jsx": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\gamification\\AchievementBadges.jsx": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\auth\\SocialLoginButtons.jsx": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\OptimizedApp.jsx": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\exportService.js": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\gestureService.js": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\speechService.js": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\optimizedGamificationService.js": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\LanguageSwitcher.jsx": "20", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\utils\\i18n.js": "21", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\webSearchService.js": "22", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\tabService.js": "23", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\TabManager.jsx": "24"}, {"size": 254, "mtime": 1749767918369, "results": "25", "hashOfConfig": "26"}, {"size": 366, "mtime": 1749810318779, "results": "27", "hashOfConfig": "26"}, {"size": 5127, "mtime": 1749796765840, "results": "28", "hashOfConfig": "26"}, {"size": 22135, "mtime": 1749822744347, "results": "29", "hashOfConfig": "26"}, {"size": 9388, "mtime": 1749796318089, "results": "30", "hashOfConfig": "26"}, {"size": 1848, "mtime": 1749770265422, "results": "31", "hashOfConfig": "26"}, {"size": 11871, "mtime": 1749796753284, "results": "32", "hashOfConfig": "26"}, {"size": 7910, "mtime": 1749770381376, "results": "33", "hashOfConfig": "26"}, {"size": 10485, "mtime": 1749770142474, "results": "34", "hashOfConfig": "26"}, {"size": 5374, "mtime": 1749770106823, "results": "35", "hashOfConfig": "26"}, {"size": 7673, "mtime": 1749796347435, "results": "36", "hashOfConfig": "26"}, {"size": 8820, "mtime": 1749796419889, "results": "37", "hashOfConfig": "26"}, {"size": 8956, "mtime": 1749796490173, "results": "38", "hashOfConfig": "26"}, {"size": 7126, "mtime": 1749770190872, "results": "39", "hashOfConfig": "26"}, {"size": 39891, "mtime": 1749846920205, "results": "40", "hashOfConfig": "26"}, {"size": 12724, "mtime": 1749811062481, "results": "41", "hashOfConfig": "26"}, {"size": 11297, "mtime": 1749816102649, "results": "42", "hashOfConfig": "26"}, {"size": 11102, "mtime": 1749813915409, "results": "43", "hashOfConfig": "26"}, {"size": 20828, "mtime": 1749817458093, "results": "44", "hashOfConfig": "26"}, {"size": 2378, "mtime": 1749812244876, "results": "45", "hashOfConfig": "26"}, {"size": 5872, "mtime": 1749814893635, "results": "46", "hashOfConfig": "26"}, {"size": 16402, "mtime": 1749816197824, "results": "47", "hashOfConfig": "26"}, {"size": 4294, "mtime": 1749820459025, "results": "48", "hashOfConfig": "26"}, {"size": 4000, "mtime": 1749820369882, "results": "49", "hashOfConfig": "26"}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "x402fp", {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\App.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\storageService.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\openRouterService.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\gamificationService.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\auth\\AuthModal.jsx", ["122"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\user\\UserDashboard.jsx", ["123"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\subscription\\SubscriptionPlans.jsx", ["124", "125", "126"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\auth\\RegisterForm.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\auth\\LoginForm.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\gamification\\Leaderboard.jsx", ["127"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\gamification\\UserStats.jsx", ["128"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\gamification\\AchievementBadges.jsx", ["129"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\auth\\SocialLoginButtons.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\OptimizedApp.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\exportService.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\gestureService.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\speechService.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\optimizedGamificationService.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\LanguageSwitcher.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\utils\\i18n.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\webSearchService.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\tabService.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\TabManager.jsx", [], [], {"ruleId": "130", "severity": 1, "message": "131", "line": 45, "column": 6, "nodeType": "132", "endLine": 45, "endColumn": 14, "suggestions": "133"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 3, "column": 30, "nodeType": "136", "messageId": "137", "endLine": 3, "endColumn": 52}, {"ruleId": "134", "severity": 1, "message": "138", "line": 9, "column": 10, "nodeType": "136", "messageId": "137", "endLine": 9, "endColumn": 22}, {"ruleId": "134", "severity": 1, "message": "139", "line": 10, "column": 10, "nodeType": "136", "messageId": "137", "endLine": 10, "endColumn": 26}, {"ruleId": "134", "severity": 1, "message": "140", "line": 28, "column": 9, "nodeType": "136", "messageId": "137", "endLine": 28, "endColumn": 29}, {"ruleId": "130", "severity": 1, "message": "141", "line": 17, "column": 6, "nodeType": "132", "endLine": 17, "endColumn": 24, "suggestions": "142"}, {"ruleId": "134", "severity": 1, "message": "143", "line": 8, "column": 11, "nodeType": "136", "messageId": "137", "endLine": 8, "endColumn": 15}, {"ruleId": "134", "severity": 1, "message": "144", "line": 19, "column": 13, "nodeType": "136", "messageId": "137", "endLine": 19, "endColumn": 22}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'handleKeyDown'. Either include it or remove the dependency array.", "ArrayExpression", ["145"], "no-unused-vars", "'getUserPermissionLevel' is defined but never used.", "Identifier", "unusedVar", "'selectedPlan' is assigned a value but never used.", "'showPaymentModal' is assigned a value but never used.", "'handlePaymentSuccess' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadLeaderboardData'. Either include it or remove the dependency array.", ["146"], "'user' is assigned a value but never used.", "'userStats' is assigned a value but never used.", {"desc": "147", "fix": "148"}, {"desc": "149", "fix": "150"}, "Update the dependencies array to be: [handleKeyDown, isOpen]", {"range": "151", "text": "152"}, "Update the dependencies array to be: [loadLeaderboardData, selectedCategory]", {"range": "153", "text": "154"}, [1034, 1042], "[handleKeyDown, isOpen]", [688, 706], "[loadLeaderboardData, selectedCategory]"]