{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Appv1\\\\src\\\\components\\\\OptimizedApp.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport '../styles/optimized.css';\nimport gestureService, { createFlagWheel } from '../services/gestureService';\nimport speechService from '../services/speechService';\nimport exportService from '../services/exportService';\nimport gamificationService from '../services/optimizedGamificationService';\nimport { generateKnowledgeTree as generateTreeAPI, generateArticle as generateArticleAPI, testConnection } from '../services/openRouterService';\nimport tabService from '../services/tabService';\nimport TabManager from './TabManager';\nimport LanguageSwitcher from './LanguageSwitcher';\nimport { useTranslation } from '../utils/i18n';\n\n// Optimized Knowledge Tree Explorer - Pareto 80/20 Implementation\n// Focus on core functionality with maximum impact\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst OptimizedApp = () => {\n  _s();\n  // Core state - now managed by tabs\n  const [currentView, setCurrentView] = useState('input'); // 'input', 'tree', 'article'\n  const [topic, setTopic] = useState('');\n  const [activeTab, setActiveTab] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [user, setUser] = useState(null);\n  const appRef = useRef(null);\n\n  // Get current tab data\n  const tree = (activeTab === null || activeTab === void 0 ? void 0 : activeTab.tree) || null;\n  const selectedBranch = (activeTab === null || activeTab === void 0 ? void 0 : activeTab.selectedBranch) || null;\n  const article = (activeTab === null || activeTab === void 0 ? void 0 : activeTab.article) || null;\n\n  // Translation hook\n  const {\n    t\n  } = useTranslation();\n\n  // Available flags for the optimized version\n  const availableFlags = React.useMemo(() => [{\n    code: '-a',\n    name: 'Article',\n    description: t('flagArticle')\n  }, {\n    code: '-ex',\n    name: 'Examples',\n    description: t('flagExamples')\n  }, {\n    code: '-q',\n    name: 'Quiz',\n    description: t('flagQuiz')\n  }, {\n    code: '-vis',\n    name: 'Visual',\n    description: t('flagVisual')\n  }, {\n    code: '-path',\n    name: 'Learning Path',\n    description: t('flagPath')\n  }, {\n    code: '-case',\n    name: 'Case Study',\n    description: t('flagCase')\n  }, {\n    code: '-ro',\n    name: 'Romanian',\n    description: t('flagRomanian')\n  }], [t]);\n\n  // Generate article for selected branch with flags\n  const generateArticle = React.useCallback(async (branch, flags = ['-a']) => {\n    setIsLoading(true);\n    setError(null);\n    try {\n      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${process.env.REACT_APP_OPENROUTER_API_KEY}`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': window.location.origin,\n          'X-Title': 'Knowledge Tree Explorer'\n        },\n        body: JSON.stringify({\n          model: 'deepseek/deepseek-r1-0528:free',\n          messages: [{\n            role: 'user',\n            content: `Write a comprehensive article about \"${branch.nume}\" in the context of \"${tree.tema}\".\n\n            Apply these flags: ${flags.join(', ')}\n\n            Flag meanings:\n            - \"-a\": Standard comprehensive article format\n            - \"-ex\": Include 3 practical examples\n            - \"-q\": Add 5 interactive quiz questions at the end\n            - \"-vis\": Describe visual elements and diagrams\n            - \"-path\": Structure as a learning path with steps\n            - \"-case\": Include real-world case studies\n            - \"-ro\": Adapt content for Romanian context and examples\n\n            Make it educational and engaging. Length: 800-1200 words.`\n          }],\n          temperature: 0.7,\n          max_tokens: 3000\n        })\n      });\n      if (!response.ok) {\n        throw new Error(`API Error: ${response.status}`);\n      }\n      const data = await response.json();\n      const content = data.choices[0].message.content;\n      const articleData = {\n        title: branch.nume,\n        content: content,\n        topic: tree.tema,\n        flags: flags\n      };\n      setArticle(articleData);\n      setCurrentView('article');\n\n      // Award points for article generation\n      const result = gamificationService.awardPoints('ARTICLE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (err) {\n      console.error('Error generating article:', err);\n      setError('Failed to generate article. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  }, [tree]);\n\n  // Gesture handlers\n  const handleDoubleTap = React.useCallback((event, targetInfo) => {\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      // Show flag wheel on double tap of branch\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        createFlagWheel(targetInfo.position, availableFlags, selectedFlags => {\n          console.log('Selected flags:', selectedFlags);\n        }, selectedFlags => {\n          generateArticleForBranch(branch, selectedFlags);\n        });\n      }\n    }\n  }, [tree, availableFlags, generateArticleForBranch]);\n  const handleSingleTap = React.useCallback((event, targetInfo) => {\n    // Single tap for normal selection\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        handleBranchSelect(branch);\n      }\n    }\n  }, [tree, handleBranchSelect]);\n  const handleLongPress = React.useCallback(async (event, targetInfo) => {\n    // Long press to expand branch and create tree effect\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        await expandBranch(branch, targetInfo.branchData.index);\n      }\n    }\n  }, [tree]);\n\n  // Initialize services and authentication\n  useEffect(() => {\n    const storedUser = localStorage.getItem('user');\n    const bypassSecurity = localStorage.getItem('bypassSecurity');\n    if (storedUser || bypassSecurity) {\n      const userData = {\n        id: 'user-1',\n        name: 'User',\n        subscriptionTier: 'premium'\n      };\n      setUser(userData);\n\n      // Award daily login points\n      const result = gamificationService.awardPoints('DAILY_LOGIN');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n\n    // Initialize gesture service\n    if (appRef.current) {\n      gestureService.init(appRef.current, {\n        doubleTap: handleDoubleTap,\n        singleTap: handleSingleTap,\n        longPress: handleLongPress\n      });\n    }\n    return () => {\n      gestureService.destroy();\n    };\n  }, [handleDoubleTap, handleSingleTap, handleLongPress]);\n\n  // Initialize gamification UI when user is logged in\n  useEffect(() => {\n    if (user) {\n      const container = document.getElementById('gamification-container');\n      if (container) {\n        // Clear existing content\n        container.innerHTML = '';\n        // Create gamification UI\n        gamificationService.createGamificationUI(container);\n      }\n\n      // Test API connection\n      testConnection().then(isConnected => {\n        console.log('🔌 API Connection Status:', isConnected ? '✅ Connected' : '❌ Failed');\n        if (!isConnected) {\n          console.warn('⚠️ API connection failed. Check your API key and internet connection.');\n        }\n      }).catch(error => {\n        console.error('❌ API connection test error:', error);\n      });\n    }\n  }, [user]);\n\n  // Core API call - using optimized service with tabs\n  const generateKnowledgeTree = async (topicInput, tabId = null) => {\n    let currentTabId = tabId;\n\n    // Create new tab if none provided\n    if (!currentTabId) {\n      try {\n        const newTab = tabService.createTab(topicInput);\n        currentTabId = newTab.id;\n        setActiveTab(newTab);\n        setCurrentView('tree');\n      } catch (error) {\n        setError(error.message);\n        return;\n      }\n    }\n\n    // Update tab status to generating\n    tabService.updateTabStatus(currentTabId, 'generating', {\n      progress: 10\n    });\n    setIsLoading(true);\n    setError(null);\n    try {\n      console.log('🌳 Generating knowledge tree for:', topicInput, 'in tab:', currentTabId);\n\n      // Update progress\n      tabService.updateTabStatus(currentTabId, 'generating', {\n        progress: 30\n      });\n      const treeData = await generateTreeAPI(topicInput);\n      console.log('✅ Generated tree data:', treeData);\n\n      // Update tab with completed tree\n      tabService.updateTabStatus(currentTabId, 'completed', {\n        tree: treeData,\n        progress: 100\n      });\n\n      // Update active tab if this is the current one\n      if (currentTabId === (activeTab === null || activeTab === void 0 ? void 0 : activeTab.id)) {\n        setActiveTab(tabService.getTab(currentTabId));\n      }\n\n      // Award points for tree generation\n      const result = gamificationService.awardPoints('TREE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (err) {\n      console.error('❌ Error generating tree:', err);\n      tabService.updateTabStatus(currentTabId, 'error');\n      setError(`Failed to generate knowledge tree: ${err.message}. Please try again.`);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Handle form submission\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (topic.trim()) {\n      generateKnowledgeTree(topic.trim());\n      setTopic(''); // Clear input for next topic\n    }\n  };\n\n  // Handle tab changes\n  const handleTabChange = tab => {\n    setActiveTab(tab);\n    if (tab !== null && tab !== void 0 && tab.tree) {\n      setCurrentView('tree');\n    } else {\n      setCurrentView('input');\n    }\n  };\n\n  // Handle new tab creation\n  const handleNewTab = () => {\n    setCurrentView('input');\n    setActiveTab(null);\n    setTopic('');\n  };\n\n  // Handle branch selection (single tap)\n  const handleBranchSelect = branch => {\n    if (activeTab) {\n      tabService.updateTabStatus(activeTab.id, activeTab.status, {\n        selectedBranch: branch\n      });\n      setActiveTab(tabService.getTab(activeTab.id));\n    }\n  };\n\n  // Speech functions\n  const handleSpeechToggle = () => {\n    if (!article) return;\n    if (speechService.getStatus().isPlaying) {\n      speechService.toggle();\n    } else {\n      speechService.speak(article.content);\n      // Award points for using speech\n      const result = gamificationService.awardPoints('SPEECH_USED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleSpeechStop = () => {\n    speechService.stop();\n  };\n  const handleSpeechRateChange = rate => {\n    speechService.setRate(rate);\n  };\n\n  // Export functions\n  const handleExportPDF = () => {\n    if (!article) return;\n    const result = exportService.exportAsPDF(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleExportWord = () => {\n    if (!article) return;\n    const result = exportService.exportAsWord(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleCopyToClipboard = async () => {\n    if (!article) return;\n    const result = await exportService.copyToClipboard(article.content);\n    exportService.showMessage(result.message, result.success ? 'success' : 'error');\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  // Generate article with tabs support\n  const generateArticleForBranch = async (branch, flags = ['-a']) => {\n    if (!activeTab) return;\n    setIsLoading(true);\n    try {\n      console.log('📄 Generating article for branch:', branch.nume);\n      const articleData = await generateArticle(activeTab.topic, branch, flags);\n\n      // Update tab with article\n      tabService.updateTabStatus(activeTab.id, 'completed', {\n        article: articleData\n      });\n      setActiveTab(tabService.getTab(activeTab.id));\n      setCurrentView('article');\n\n      // Award points for article generation\n      const result = gamificationService.awardPoints('ARTICLE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (error) {\n      console.error('❌ Error generating article:', error);\n      setError('Failed to generate article. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Navigation functions\n  const goBack = () => {\n    if (currentView === 'article') {\n      setCurrentView('tree');\n      if (activeTab) {\n        tabService.updateTabStatus(activeTab.id, activeTab.status, {\n          article: null\n        });\n        setActiveTab(tabService.getTab(activeTab.id));\n      }\n    } else if (currentView === 'tree') {\n      setCurrentView('input');\n    }\n  };\n\n  // Expand branch to create sub-branches (tree effect)\n  const expandBranch = async (branch, branchIndex) => {\n    setIsLoading(true);\n    setError(null);\n    try {\n      var _data$choices$, _data$choices$$messag;\n      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${process.env.REACT_APP_OPENROUTER_API_KEY}`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': window.location.origin,\n          'X-Title': 'Knowledge Tree Explorer'\n        },\n        body: JSON.stringify({\n          model: 'deepseek/deepseek-r1-0528:free',\n          messages: [{\n            role: 'user',\n            content: `Expand the topic \"${branch.nume}\" from the context of \"${tree.tema}\". Create 4-6 sub-branches that dive deeper into this specific area. Return JSON with:\n            {\n              \"ramuri\": [\n                {\n                  \"nume\": \"Sub-branch Name\",\n                  \"descriere\": \"Brief description\",\n                  \"emoji\": \"📚\",\n                  \"subcategorii\": [\"Detail1\", \"Detail2\", \"Detail3\"]\n                }\n              ]\n            }\n            Focus on specific, actionable sub-topics within \"${branch.nume}\".`\n          }],\n          temperature: 0.7,\n          max_tokens: 1500\n        })\n      });\n      if (!response.ok) {\n        throw new Error(`API Error: ${response.status}`);\n      }\n      const data = await response.json();\n      const content = (_data$choices$ = data.choices[0]) === null || _data$choices$ === void 0 ? void 0 : (_data$choices$$messag = _data$choices$.message) === null || _data$choices$$messag === void 0 ? void 0 : _data$choices$$messag.content;\n      if (!content) {\n        throw new Error('No content received from API');\n      }\n\n      // Parse JSON response\n      const jsonMatch = content.match(/\\{[\\s\\S]*\\}/);\n      if (!jsonMatch) {\n        throw new Error('Invalid JSON format in response');\n      }\n      const expandedData = JSON.parse(jsonMatch[0]);\n\n      // Update tree with expanded branches\n      const newTree = {\n        ...tree\n      };\n      newTree.ramuri = [...newTree.ramuri.slice(0, branchIndex + 1), ...expandedData.ramuri.map(subBranch => ({\n        ...subBranch,\n        isSubBranch: true,\n        parentBranch: branch.nume,\n        level: (branch.level || 0) + 1\n      })), ...newTree.ramuri.slice(branchIndex + 1)];\n\n      // Update tab with expanded tree\n      if (activeTab) {\n        tabService.updateTabStatus(activeTab.id, 'completed', {\n          tree: newTree\n        });\n        setActiveTab(tabService.getTab(activeTab.id));\n      }\n\n      // Award points for branch expansion\n      const result = gamificationService.awardPoints('BRANCH_EXPANDED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (error) {\n      console.error('Error expanding branch:', error);\n      setError(t('failedToExpand'));\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const goHome = () => {\n    setCurrentView('input');\n    setActiveTab(null);\n    setTopic('');\n    // Clear all tabs\n    tabService.clearAllTabs();\n  };\n\n  // Quick login for development\n  const quickLogin = () => {\n    localStorage.setItem('bypassSecurity', 'true');\n    setUser({\n      id: 'dev-1',\n      name: 'Developer',\n      subscriptionTier: 'premium'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    ref: appRef,\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"app-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: goHome,\n          className: \"logo-text\",\n          children: t('appTitle')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-right\",\n          children: [user && /*#__PURE__*/_jsxDEV(\"div\", {\n            id: \"gamification-container\",\n            style: {\n              marginRight: '16px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(LanguageSwitcher, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 541,\n            columnNumber: 13\n          }, this), !user ? /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: quickLogin,\n            className: \"btn btn-primary\",\n            style: {\n              marginLeft: '12px'\n            },\n            children: t('quickLogin')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginLeft: '12px'\n            },\n            children: [t('welcome'), \", \", user.name, \"!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 531,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 530,\n      columnNumber: 7\n    }, this), user && /*#__PURE__*/_jsxDEV(TabManager, {\n      onTabChange: handleTabChange,\n      onNewTab: handleNewTab\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 555,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"main-content\",\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error\",\n        children: [\"\\u26A0\\uFE0F \", error, /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setError(null),\n          style: {\n            marginLeft: 'auto',\n            background: 'none',\n            border: 'none',\n            color: 'white',\n            cursor: 'pointer'\n          },\n          children: \"\\u2715\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 566,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 564,\n        columnNumber: 11\n      }, this), currentView === 'input' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"title\",\n          children: t('appTitle')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"subtitle\",\n          children: \"Enter any topic to generate an interactive knowledge tree with AI-powered content.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 576,\n          columnNumber: 13\n        }, this), !user ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#f1f5f9',\n            padding: '1rem',\n            borderRadius: '0.5rem',\n            marginBottom: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#334155',\n              marginBottom: '1rem'\n            },\n            children: t('loginRequired')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 582,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: quickLogin,\n            className: \"btn btn-primary\",\n            children: t('quickLoginDev')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 585,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 581,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: topic,\n              onChange: e => setTopic(e.target.value),\n              placeholder: t('topicPlaceholder'),\n              className: \"form-input\",\n              disabled: isLoading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 592,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 591,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: isLoading || !topic.trim(),\n            className: \"btn btn-primary\",\n            children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 608,\n                columnNumber: 23\n              }, this), t('generating')]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: t('exploreKnowledge')\n            }, void 0, false)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 601,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 590,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 574,\n        columnNumber: 11\n      }, this), currentView === 'tree' && tree && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tree-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tree-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: tree.tema\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 626,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: t('selectBranch')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 627,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goBack,\n            className: \"btn btn-secondary\",\n            style: {\n              marginTop: '1rem'\n            },\n            children: t('backToTree')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 628,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 625,\n          columnNumber: 13\n        }, this), isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: t('loading')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 636,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 634,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"branches-grid\",\n          children: tree.ramuri.map((branch, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `branch-item ${selectedBranch === branch ? 'selected' : ''}`,\n            \"data-index\": index,\n            \"data-name\": branch.nume,\n            \"data-description\": branch.descriere,\n            \"data-is-sub-branch\": branch.isSubBranch || false,\n            \"data-level\": branch.level || 0,\n            onClick: () => handleBranchSelect(branch),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"branch-emoji\",\n              children: branch.emoji\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 651,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"branch-name\",\n              children: branch.nume\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 652,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"branch-description\",\n              children: branch.descriere\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 653,\n              columnNumber: 21\n            }, this), branch.subcategorii && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.875rem',\n                color: '#475569',\n                marginTop: '0.5rem'\n              },\n              children: [t('topics'), \": \", branch.subcategorii.slice(0, 3).join(', '), branch.subcategorii.length > 3 && '...']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 655,\n              columnNumber: 23\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"gesture-hint\",\n              style: {\n                fontSize: '0.75rem',\n                color: '#64748b',\n                marginTop: '0.5rem',\n                fontStyle: 'italic'\n              },\n              children: t('gestureHint')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 660,\n              columnNumber: 21\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 641,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 639,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 624,\n        columnNumber: 11\n      }, this), currentView === 'article' && article && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tree-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"article-header\",\n            style: {\n              marginBottom: '2rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: goBack,\n              className: \"btn btn-secondary\",\n              children: t('backToTree')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 680,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"article-controls\",\n              style: {\n                display: 'flex',\n                gap: '8px',\n                marginTop: '1rem',\n                flexWrap: 'wrap'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"speech-controls-compact\",\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '8px',\n                  padding: '8px 12px',\n                  background: '#f1f5f9',\n                  borderRadius: '6px',\n                  border: '1px solid #e2e8f0'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleSpeechToggle,\n                  className: \"btn-icon\",\n                  title: \"Play/Pause Speech\",\n                  style: {\n                    background: 'none',\n                    border: 'none',\n                    fontSize: '16px',\n                    cursor: 'pointer',\n                    padding: '4px'\n                  },\n                  children: speechService.getStatus().isPlaying ? '⏸️' : '▶️'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 701,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleSpeechStop,\n                  className: \"btn-icon\",\n                  title: \"Stop Speech\",\n                  style: {\n                    background: 'none',\n                    border: 'none',\n                    fontSize: '16px',\n                    cursor: 'pointer',\n                    padding: '4px'\n                  },\n                  children: \"\\u23F9\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 715,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"range\",\n                  min: \"0.5\",\n                  max: \"2\",\n                  step: \"0.1\",\n                  defaultValue: \"1\",\n                  onChange: e => handleSpeechRateChange(parseFloat(e.target.value)),\n                  style: {\n                    width: '60px'\n                  },\n                  title: \"Speech Speed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 729,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '12px',\n                    color: '#64748b'\n                  },\n                  children: \"\\uD83D\\uDDE3\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 739,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 692,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"export-controls-compact\",\n                style: {\n                  display: 'flex',\n                  gap: '4px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleCopyToClipboard,\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '6px 12px',\n                    fontSize: '12px'\n                  },\n                  title: \"Copy to Clipboard\",\n                  children: \"\\uD83D\\uDCCB Copy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 747,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleExportPDF,\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '6px 12px',\n                    fontSize: '12px'\n                  },\n                  title: \"Export as PDF\",\n                  children: \"\\uD83D\\uDCC4 PDF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 755,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleExportWord,\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '6px 12px',\n                    fontSize: '12px'\n                  },\n                  title: \"Export as Word\",\n                  children: \"\\uD83D\\uDCDD Word\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 763,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 743,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 679,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"title\",\n            children: article.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 775,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#475569',\n              marginBottom: '2rem',\n              fontSize: '0.9rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: [t('partOf'), \": \", article.topic]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 777,\n              columnNumber: 17\n            }, this), article.flags && article.flags.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                marginLeft: '16px'\n              },\n              children: [t('flags'), \": \", article.flags.join(', ')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 779,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 776,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"article-content\",\n            style: {\n              lineHeight: '1.8',\n              fontSize: '1.1rem'\n            },\n            children: article.content.split('\\n').map((paragraph, index) => paragraph.trim() && /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                marginBottom: '1rem'\n              },\n              children: paragraph\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 788,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 785,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 678,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 677,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 562,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 528,\n    columnNumber: 5\n  }, this);\n};\n_s(OptimizedApp, \"19OlsyR8YMT7irnycAPnsBHZi8s=\", false, function () {\n  return [useTranslation];\n});\n_c = OptimizedApp;\nexport default OptimizedApp;\nvar _c;\n$RefreshReg$(_c, \"OptimizedApp\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "gestureService", "createFlagWheel", "speechService", "exportService", "gamificationService", "generateKnowledgeTree", "generateTreeAPI", "generateArticle", "generateArticleAPI", "testConnection", "tabService", "TabManager", "LanguageSwitcher", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "OptimizedApp", "_s", "current<PERSON>iew", "set<PERSON><PERSON><PERSON>View", "topic", "setTopic", "activeTab", "setActiveTab", "isLoading", "setIsLoading", "error", "setError", "user", "setUser", "appRef", "tree", "<PERSON><PERSON><PERSON><PERSON>", "article", "t", "availableFlags", "useMemo", "code", "name", "description", "useCallback", "branch", "flags", "response", "fetch", "method", "headers", "process", "env", "REACT_APP_OPENROUTER_API_KEY", "window", "location", "origin", "body", "JSON", "stringify", "model", "messages", "role", "content", "nume", "tema", "join", "temperature", "max_tokens", "ok", "Error", "status", "data", "json", "choices", "message", "articleData", "title", "setArticle", "result", "awardPoints", "newAchievements", "length", "for<PERSON>ach", "achievement", "showAchievementNotification", "err", "console", "handleDoubleTap", "event", "targetInfo", "isBranchItem", "branchData", "<PERSON><PERSON>", "index", "position", "selected<PERSON><PERSON><PERSON>", "log", "generateArticleForBranch", "handleSingleTap", "handleBranchSelect", "handleLongPress", "expandBranch", "storedUser", "localStorage", "getItem", "bypassSecurity", "userData", "id", "subscriptionTier", "current", "init", "doubleTap", "singleTap", "longPress", "destroy", "container", "document", "getElementById", "innerHTML", "createGamificationUI", "then", "isConnected", "warn", "catch", "topicInput", "tabId", "currentTabId", "newTab", "createTab", "updateTabStatus", "progress", "treeData", "getTab", "handleSubmit", "e", "preventDefault", "trim", "handleTabChange", "tab", "handleNewTab", "handleSpeechToggle", "getStatus", "isPlaying", "toggle", "speak", "handleSpeechStop", "stop", "handleSpeechRateChange", "rate", "setRate", "handleExportPDF", "exportAsPDF", "replace", "success", "gamResult", "handleExportWord", "exportAsWord", "handleCopyToClipboard", "copyToClipboard", "showMessage", "goBack", "branchIndex", "_data$choices$", "_data$choices$$messag", "jsonMatch", "match", "expandedData", "parse", "newTree", "slice", "map", "subBranch", "isSubBranch", "parentBranch", "level", "goHome", "clearAllTabs", "quickLogin", "setItem", "className", "ref", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginRight", "marginLeft", "onTabChange", "onNewTab", "background", "border", "color", "cursor", "padding", "borderRadius", "marginBottom", "onSubmit", "type", "value", "onChange", "target", "placeholder", "disabled", "marginTop", "desc<PERSON><PERSON>", "emoji", "subcategorii", "fontSize", "fontStyle", "display", "gap", "flexWrap", "alignItems", "min", "max", "step", "defaultValue", "parseFloat", "width", "lineHeight", "split", "paragraph", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Appv1/src/components/OptimizedApp.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport '../styles/optimized.css';\nimport gestureService, { createFlagWheel } from '../services/gestureService';\nimport speechService from '../services/speechService';\nimport exportService from '../services/exportService';\nimport gamificationService from '../services/optimizedGamificationService';\nimport { generateKnowledgeTree as generateTreeAPI, generateArticle as generateArticleAPI, testConnection } from '../services/openRouterService';\nimport tabService from '../services/tabService';\nimport TabManager from './TabManager';\nimport LanguageSwitcher from './LanguageSwitcher';\nimport { useTranslation } from '../utils/i18n';\n\n// Optimized Knowledge Tree Explorer - Pareto 80/20 Implementation\n// Focus on core functionality with maximum impact\n\nconst OptimizedApp = () => {\n  // Core state - now managed by tabs\n  const [currentView, setCurrentView] = useState('input'); // 'input', 'tree', 'article'\n  const [topic, setTopic] = useState('');\n  const [activeTab, setActiveTab] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [user, setUser] = useState(null);\n  const appRef = useRef(null);\n\n  // Get current tab data\n  const tree = activeTab?.tree || null;\n  const selectedBranch = activeTab?.selectedBranch || null;\n  const article = activeTab?.article || null;\n\n  // Translation hook\n  const { t } = useTranslation();\n\n  // Available flags for the optimized version\n  const availableFlags = React.useMemo(() => [\n    { code: '-a', name: 'Article', description: t('flagArticle') },\n    { code: '-ex', name: 'Examples', description: t('flagExamples') },\n    { code: '-q', name: 'Quiz', description: t('flagQuiz') },\n    { code: '-vis', name: 'Visual', description: t('flagVisual') },\n    { code: '-path', name: 'Learning Path', description: t('flagPath') },\n    { code: '-case', name: 'Case Study', description: t('flagCase') },\n    { code: '-ro', name: 'Romanian', description: t('flagRomanian') }\n  ], [t]);\n\n  // Generate article for selected branch with flags\n  const generateArticle = React.useCallback(async (branch, flags = ['-a']) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${process.env.REACT_APP_OPENROUTER_API_KEY}`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': window.location.origin,\n          'X-Title': 'Knowledge Tree Explorer'\n        },\n        body: JSON.stringify({\n          model: 'deepseek/deepseek-r1-0528:free',\n          messages: [{\n            role: 'user',\n            content: `Write a comprehensive article about \"${branch.nume}\" in the context of \"${tree.tema}\".\n\n            Apply these flags: ${flags.join(', ')}\n\n            Flag meanings:\n            - \"-a\": Standard comprehensive article format\n            - \"-ex\": Include 3 practical examples\n            - \"-q\": Add 5 interactive quiz questions at the end\n            - \"-vis\": Describe visual elements and diagrams\n            - \"-path\": Structure as a learning path with steps\n            - \"-case\": Include real-world case studies\n            - \"-ro\": Adapt content for Romanian context and examples\n\n            Make it educational and engaging. Length: 800-1200 words.`\n          }],\n          temperature: 0.7,\n          max_tokens: 3000\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error(`API Error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      const content = data.choices[0].message.content;\n\n      const articleData = {\n        title: branch.nume,\n        content: content,\n        topic: tree.tema,\n        flags: flags\n      };\n\n      setArticle(articleData);\n      setCurrentView('article');\n\n      // Award points for article generation\n      const result = gamificationService.awardPoints('ARTICLE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (err) {\n      console.error('Error generating article:', err);\n      setError('Failed to generate article. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  }, [tree]);\n\n  // Gesture handlers\n  const handleDoubleTap = React.useCallback((event, targetInfo) => {\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      // Show flag wheel on double tap of branch\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        createFlagWheel(\n          targetInfo.position,\n          availableFlags,\n          (selectedFlags) => {\n            console.log('Selected flags:', selectedFlags);\n          },\n          (selectedFlags) => {\n            generateArticleForBranch(branch, selectedFlags);\n          }\n        );\n      }\n    }\n  }, [tree, availableFlags, generateArticleForBranch]);\n\n  const handleSingleTap = React.useCallback((event, targetInfo) => {\n    // Single tap for normal selection\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        handleBranchSelect(branch);\n      }\n    }\n  }, [tree, handleBranchSelect]);\n\n  const handleLongPress = React.useCallback(async (event, targetInfo) => {\n    // Long press to expand branch and create tree effect\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        await expandBranch(branch, targetInfo.branchData.index);\n      }\n    }\n  }, [tree]);\n\n  // Initialize services and authentication\n  useEffect(() => {\n    const storedUser = localStorage.getItem('user');\n    const bypassSecurity = localStorage.getItem('bypassSecurity');\n\n    if (storedUser || bypassSecurity) {\n      const userData = {\n        id: 'user-1',\n        name: 'User',\n        subscriptionTier: 'premium'\n      };\n      setUser(userData);\n\n      // Award daily login points\n      const result = gamificationService.awardPoints('DAILY_LOGIN');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n\n    // Initialize gesture service\n    if (appRef.current) {\n      gestureService.init(appRef.current, {\n        doubleTap: handleDoubleTap,\n        singleTap: handleSingleTap,\n        longPress: handleLongPress\n      });\n    }\n\n    return () => {\n      gestureService.destroy();\n    };\n  }, [handleDoubleTap, handleSingleTap, handleLongPress]);\n\n  // Initialize gamification UI when user is logged in\n  useEffect(() => {\n    if (user) {\n      const container = document.getElementById('gamification-container');\n      if (container) {\n        // Clear existing content\n        container.innerHTML = '';\n        // Create gamification UI\n        gamificationService.createGamificationUI(container);\n      }\n\n      // Test API connection\n      testConnection().then(isConnected => {\n        console.log('🔌 API Connection Status:', isConnected ? '✅ Connected' : '❌ Failed');\n        if (!isConnected) {\n          console.warn('⚠️ API connection failed. Check your API key and internet connection.');\n        }\n      }).catch(error => {\n        console.error('❌ API connection test error:', error);\n      });\n    }\n  }, [user]);\n\n  // Core API call - using optimized service with tabs\n  const generateKnowledgeTree = async (topicInput, tabId = null) => {\n    let currentTabId = tabId;\n\n    // Create new tab if none provided\n    if (!currentTabId) {\n      try {\n        const newTab = tabService.createTab(topicInput);\n        currentTabId = newTab.id;\n        setActiveTab(newTab);\n        setCurrentView('tree');\n      } catch (error) {\n        setError(error.message);\n        return;\n      }\n    }\n\n    // Update tab status to generating\n    tabService.updateTabStatus(currentTabId, 'generating', { progress: 10 });\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      console.log('🌳 Generating knowledge tree for:', topicInput, 'in tab:', currentTabId);\n\n      // Update progress\n      tabService.updateTabStatus(currentTabId, 'generating', { progress: 30 });\n\n      const treeData = await generateTreeAPI(topicInput);\n      console.log('✅ Generated tree data:', treeData);\n\n      // Update tab with completed tree\n      tabService.updateTabStatus(currentTabId, 'completed', {\n        tree: treeData,\n        progress: 100\n      });\n\n      // Update active tab if this is the current one\n      if (currentTabId === activeTab?.id) {\n        setActiveTab(tabService.getTab(currentTabId));\n      }\n\n      // Award points for tree generation\n      const result = gamificationService.awardPoints('TREE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (err) {\n      console.error('❌ Error generating tree:', err);\n      tabService.updateTabStatus(currentTabId, 'error');\n      setError(`Failed to generate knowledge tree: ${err.message}. Please try again.`);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n\n\n  // Handle form submission\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    if (topic.trim()) {\n      generateKnowledgeTree(topic.trim());\n      setTopic(''); // Clear input for next topic\n    }\n  };\n\n  // Handle tab changes\n  const handleTabChange = (tab) => {\n    setActiveTab(tab);\n    if (tab?.tree) {\n      setCurrentView('tree');\n    } else {\n      setCurrentView('input');\n    }\n  };\n\n  // Handle new tab creation\n  const handleNewTab = () => {\n    setCurrentView('input');\n    setActiveTab(null);\n    setTopic('');\n  };\n\n  // Handle branch selection (single tap)\n  const handleBranchSelect = (branch) => {\n    if (activeTab) {\n      tabService.updateTabStatus(activeTab.id, activeTab.status, { selectedBranch: branch });\n      setActiveTab(tabService.getTab(activeTab.id));\n    }\n  };\n\n  // Speech functions\n  const handleSpeechToggle = () => {\n    if (!article) return;\n\n    if (speechService.getStatus().isPlaying) {\n      speechService.toggle();\n    } else {\n      speechService.speak(article.content);\n      // Award points for using speech\n      const result = gamificationService.awardPoints('SPEECH_USED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleSpeechStop = () => {\n    speechService.stop();\n  };\n\n  const handleSpeechRateChange = (rate) => {\n    speechService.setRate(rate);\n  };\n\n  // Export functions\n  const handleExportPDF = () => {\n    if (!article) return;\n    const result = exportService.exportAsPDF(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleExportWord = () => {\n    if (!article) return;\n    const result = exportService.exportAsWord(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleCopyToClipboard = async () => {\n    if (!article) return;\n    const result = await exportService.copyToClipboard(article.content);\n    exportService.showMessage(result.message, result.success ? 'success' : 'error');\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  // Generate article with tabs support\n  const generateArticleForBranch = async (branch, flags = ['-a']) => {\n    if (!activeTab) return;\n\n    setIsLoading(true);\n    try {\n      console.log('📄 Generating article for branch:', branch.nume);\n      const articleData = await generateArticle(activeTab.topic, branch, flags);\n\n      // Update tab with article\n      tabService.updateTabStatus(activeTab.id, 'completed', {\n        article: articleData\n      });\n\n      setActiveTab(tabService.getTab(activeTab.id));\n      setCurrentView('article');\n\n      // Award points for article generation\n      const result = gamificationService.awardPoints('ARTICLE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (error) {\n      console.error('❌ Error generating article:', error);\n      setError('Failed to generate article. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Navigation functions\n  const goBack = () => {\n    if (currentView === 'article') {\n      setCurrentView('tree');\n      if (activeTab) {\n        tabService.updateTabStatus(activeTab.id, activeTab.status, { article: null });\n        setActiveTab(tabService.getTab(activeTab.id));\n      }\n    } else if (currentView === 'tree') {\n      setCurrentView('input');\n    }\n  };\n\n  // Expand branch to create sub-branches (tree effect)\n  const expandBranch = async (branch, branchIndex) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${process.env.REACT_APP_OPENROUTER_API_KEY}`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': window.location.origin,\n          'X-Title': 'Knowledge Tree Explorer'\n        },\n        body: JSON.stringify({\n          model: 'deepseek/deepseek-r1-0528:free',\n          messages: [{\n            role: 'user',\n            content: `Expand the topic \"${branch.nume}\" from the context of \"${tree.tema}\". Create 4-6 sub-branches that dive deeper into this specific area. Return JSON with:\n            {\n              \"ramuri\": [\n                {\n                  \"nume\": \"Sub-branch Name\",\n                  \"descriere\": \"Brief description\",\n                  \"emoji\": \"📚\",\n                  \"subcategorii\": [\"Detail1\", \"Detail2\", \"Detail3\"]\n                }\n              ]\n            }\n            Focus on specific, actionable sub-topics within \"${branch.nume}\".`\n          }],\n          temperature: 0.7,\n          max_tokens: 1500\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error(`API Error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      const content = data.choices[0]?.message?.content;\n\n      if (!content) {\n        throw new Error('No content received from API');\n      }\n\n      // Parse JSON response\n      const jsonMatch = content.match(/\\{[\\s\\S]*\\}/);\n      if (!jsonMatch) {\n        throw new Error('Invalid JSON format in response');\n      }\n\n      const expandedData = JSON.parse(jsonMatch[0]);\n\n      // Update tree with expanded branches\n      const newTree = { ...tree };\n      newTree.ramuri = [\n        ...newTree.ramuri.slice(0, branchIndex + 1),\n        ...expandedData.ramuri.map(subBranch => ({\n          ...subBranch,\n          isSubBranch: true,\n          parentBranch: branch.nume,\n          level: (branch.level || 0) + 1\n        })),\n        ...newTree.ramuri.slice(branchIndex + 1)\n      ];\n\n      // Update tab with expanded tree\n      if (activeTab) {\n        tabService.updateTabStatus(activeTab.id, 'completed', { tree: newTree });\n        setActiveTab(tabService.getTab(activeTab.id));\n      }\n\n      // Award points for branch expansion\n      const result = gamificationService.awardPoints('BRANCH_EXPANDED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n\n    } catch (error) {\n      console.error('Error expanding branch:', error);\n      setError(t('failedToExpand'));\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const goHome = () => {\n    setCurrentView('input');\n    setActiveTab(null);\n    setTopic('');\n    // Clear all tabs\n    tabService.clearAllTabs();\n  };\n\n  // Quick login for development\n  const quickLogin = () => {\n    localStorage.setItem('bypassSecurity', 'true');\n    setUser({ id: 'dev-1', name: 'Developer', subscriptionTier: 'premium' });\n  };\n\n  return (\n    <div className=\"app\" ref={appRef}>\n      {/* Header */}\n      <header className=\"app-header\">\n        <div className=\"header-content\">\n          <button onClick={goHome} className=\"logo-text\">\n            {t('appTitle')}\n          </button>\n          <div className=\"header-right\">\n            {user && (\n              <div id=\"gamification-container\" style={{ marginRight: '16px' }}>\n                {/* Gamification UI will be inserted here */}\n              </div>\n            )}\n            <LanguageSwitcher />\n            {!user ? (\n              <button onClick={quickLogin} className=\"btn btn-primary\" style={{ marginLeft: '12px' }}>\n                {t('quickLogin')}\n              </button>\n            ) : (\n              <span style={{ marginLeft: '12px' }}>{t('welcome')}, {user.name}!</span>\n            )}\n          </div>\n        </div>\n      </header>\n\n      {/* Tab Manager */}\n      {user && (\n        <TabManager\n          onTabChange={handleTabChange}\n          onNewTab={handleNewTab}\n        />\n      )}\n\n      {/* Main Content */}\n      <main className=\"main-content\">\n        {error && (\n          <div className=\"error\">\n            ⚠️ {error}\n            <button onClick={() => setError(null)} style={{marginLeft: 'auto', background: 'none', border: 'none', color: 'white', cursor: 'pointer'}}>\n              ✕\n            </button>\n          </div>\n        )}\n\n        {/* Topic Input View */}\n        {currentView === 'input' && (\n          <div className=\"card text-center\">\n            <h1 className=\"title\">{t('appTitle')}</h1>\n            <p className=\"subtitle\">\n              Enter any topic to generate an interactive knowledge tree with AI-powered content.\n            </p>\n\n            {!user ? (\n              <div style={{background: '#f1f5f9', padding: '1rem', borderRadius: '0.5rem', marginBottom: '2rem'}}>\n                <p style={{color: '#334155', marginBottom: '1rem'}}>\n                  {t('loginRequired')}\n                </p>\n                <button onClick={quickLogin} className=\"btn btn-primary\">\n                  {t('quickLoginDev')}\n                </button>\n              </div>\n            ) : (\n              <form onSubmit={handleSubmit}>\n                <div className=\"form-group\">\n                  <input\n                    type=\"text\"\n                    value={topic}\n                    onChange={(e) => setTopic(e.target.value)}\n                    placeholder={t('topicPlaceholder')}\n                    className=\"form-input\"\n                    disabled={isLoading}\n                  />\n                </div>\n                <button\n                  type=\"submit\"\n                  disabled={isLoading || !topic.trim()}\n                  className=\"btn btn-primary\"\n                >\n                  {isLoading ? (\n                    <>\n                      <span className=\"spinner\"></span>\n                      {t('generating')}\n                    </>\n                  ) : (\n                    <>\n                      {t('exploreKnowledge')}\n                    </>\n                  )}\n                </button>\n              </form>\n            )}\n          </div>\n        )}\n\n        {/* Tree View */}\n        {currentView === 'tree' && tree && (\n          <div className=\"tree-container\">\n            <div className=\"tree-header\">\n              <h1>{tree.tema}</h1>\n              <p>{t('selectBranch')}</p>\n              <button onClick={goBack} className=\"btn btn-secondary\" style={{marginTop: '1rem'}}>\n                {t('backToTree')}\n              </button>\n            </div>\n\n            {isLoading ? (\n              <div className=\"loading\">\n                <span className=\"spinner\"></span>\n                <span>{t('loading')}</span>\n              </div>\n            ) : (\n              <div className=\"branches-grid\">\n                {tree.ramuri.map((branch, index) => (\n                  <div\n                    key={index}\n                    className={`branch-item ${selectedBranch === branch ? 'selected' : ''}`}\n                    data-index={index}\n                    data-name={branch.nume}\n                    data-description={branch.descriere}\n                    data-is-sub-branch={branch.isSubBranch || false}\n                    data-level={branch.level || 0}\n                    onClick={() => handleBranchSelect(branch)}\n                  >\n                    <div className=\"branch-emoji\">{branch.emoji}</div>\n                    <h3 className=\"branch-name\">{branch.nume}</h3>\n                    <p className=\"branch-description\">{branch.descriere}</p>\n                    {branch.subcategorii && (\n                      <div style={{fontSize: '0.875rem', color: '#475569', marginTop: '0.5rem'}}>\n                        {t('topics')}: {branch.subcategorii.slice(0, 3).join(', ')}\n                        {branch.subcategorii.length > 3 && '...'}\n                      </div>\n                    )}\n                    <div className=\"gesture-hint\" style={{\n                      fontSize: '0.75rem',\n                      color: '#64748b',\n                      marginTop: '0.5rem',\n                      fontStyle: 'italic'\n                    }}>\n                      {t('gestureHint')}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Article View */}\n        {currentView === 'article' && article && (\n          <div className=\"tree-container\">\n            <div className=\"card\">\n              <div className=\"article-header\" style={{marginBottom: '2rem'}}>\n                <button onClick={goBack} className=\"btn btn-secondary\">\n                  {t('backToTree')}\n                </button>\n\n                {/* Article Controls */}\n                <div className=\"article-controls\" style={{\n                  display: 'flex',\n                  gap: '8px',\n                  marginTop: '1rem',\n                  flexWrap: 'wrap'\n                }}>\n                  {/* Speech Controls */}\n                  <div className=\"speech-controls-compact\" style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '8px',\n                    padding: '8px 12px',\n                    background: '#f1f5f9',\n                    borderRadius: '6px',\n                    border: '1px solid #e2e8f0'\n                  }}>\n                    <button\n                      onClick={handleSpeechToggle}\n                      className=\"btn-icon\"\n                      title=\"Play/Pause Speech\"\n                      style={{\n                        background: 'none',\n                        border: 'none',\n                        fontSize: '16px',\n                        cursor: 'pointer',\n                        padding: '4px'\n                      }}\n                    >\n                      {speechService.getStatus().isPlaying ? '⏸️' : '▶️'}\n                    </button>\n                    <button\n                      onClick={handleSpeechStop}\n                      className=\"btn-icon\"\n                      title=\"Stop Speech\"\n                      style={{\n                        background: 'none',\n                        border: 'none',\n                        fontSize: '16px',\n                        cursor: 'pointer',\n                        padding: '4px'\n                      }}\n                    >\n                      ⏹️\n                    </button>\n                    <input\n                      type=\"range\"\n                      min=\"0.5\"\n                      max=\"2\"\n                      step=\"0.1\"\n                      defaultValue=\"1\"\n                      onChange={(e) => handleSpeechRateChange(parseFloat(e.target.value))}\n                      style={{width: '60px'}}\n                      title=\"Speech Speed\"\n                    />\n                    <span style={{fontSize: '12px', color: '#64748b'}}>🗣️</span>\n                  </div>\n\n                  {/* Export Controls */}\n                  <div className=\"export-controls-compact\" style={{\n                    display: 'flex',\n                    gap: '4px'\n                  }}>\n                    <button\n                      onClick={handleCopyToClipboard}\n                      className=\"btn btn-secondary\"\n                      style={{padding: '6px 12px', fontSize: '12px'}}\n                      title=\"Copy to Clipboard\"\n                    >\n                      📋 Copy\n                    </button>\n                    <button\n                      onClick={handleExportPDF}\n                      className=\"btn btn-secondary\"\n                      style={{padding: '6px 12px', fontSize: '12px'}}\n                      title=\"Export as PDF\"\n                    >\n                      📄 PDF\n                    </button>\n                    <button\n                      onClick={handleExportWord}\n                      className=\"btn btn-secondary\"\n                      style={{padding: '6px 12px', fontSize: '12px'}}\n                      title=\"Export as Word\"\n                    >\n                      📝 Word\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              <h1 className=\"title\">{article.title}</h1>\n              <div style={{color: '#475569', marginBottom: '2rem', fontSize: '0.9rem'}}>\n                <span>{t('partOf')}: {article.topic}</span>\n                {article.flags && article.flags.length > 0 && (\n                  <span style={{marginLeft: '16px'}}>\n                    {t('flags')}: {article.flags.join(', ')}\n                  </span>\n                )}\n              </div>\n\n              <div className=\"article-content\" style={{lineHeight: '1.8', fontSize: '1.1rem'}}>\n                {article.content.split('\\n').map((paragraph, index) => (\n                  paragraph.trim() && (\n                    <p key={index} style={{marginBottom: '1rem'}}>\n                      {paragraph}\n                    </p>\n                  )\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n      </main>\n    </div>\n  );\n};\n\nexport default OptimizedApp;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,yBAAyB;AAChC,OAAOC,cAAc,IAAIC,eAAe,QAAQ,4BAA4B;AAC5E,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,mBAAmB,MAAM,0CAA0C;AAC1E,SAASC,qBAAqB,IAAIC,eAAe,EAAEC,eAAe,IAAIC,kBAAkB,EAAEC,cAAc,QAAQ,+BAA+B;AAC/I,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,cAAc,QAAQ,eAAe;;AAE9C;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEA,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACzD,MAAM,CAACyB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiC,IAAI,EAAEC,OAAO,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAMmC,MAAM,GAAGjC,MAAM,CAAC,IAAI,CAAC;;EAE3B;EACA,MAAMkC,IAAI,GAAG,CAAAT,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAES,IAAI,KAAI,IAAI;EACpC,MAAMC,cAAc,GAAG,CAAAV,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEU,cAAc,KAAI,IAAI;EACxD,MAAMC,OAAO,GAAG,CAAAX,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEW,OAAO,KAAI,IAAI;;EAE1C;EACA,MAAM;IAAEC;EAAE,CAAC,GAAGvB,cAAc,CAAC,CAAC;;EAE9B;EACA,MAAMwB,cAAc,GAAGzC,KAAK,CAAC0C,OAAO,CAAC,MAAM,CACzC;IAAEC,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,SAAS;IAAEC,WAAW,EAAEL,CAAC,CAAC,aAAa;EAAE,CAAC,EAC9D;IAAEG,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAE,UAAU;IAAEC,WAAW,EAAEL,CAAC,CAAC,cAAc;EAAE,CAAC,EACjE;IAAEG,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,MAAM;IAAEC,WAAW,EAAEL,CAAC,CAAC,UAAU;EAAE,CAAC,EACxD;IAAEG,IAAI,EAAE,MAAM;IAAEC,IAAI,EAAE,QAAQ;IAAEC,WAAW,EAAEL,CAAC,CAAC,YAAY;EAAE,CAAC,EAC9D;IAAEG,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,eAAe;IAAEC,WAAW,EAAEL,CAAC,CAAC,UAAU;EAAE,CAAC,EACpE;IAAEG,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,YAAY;IAAEC,WAAW,EAAEL,CAAC,CAAC,UAAU;EAAE,CAAC,EACjE;IAAEG,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAE,UAAU;IAAEC,WAAW,EAAEL,CAAC,CAAC,cAAc;EAAE,CAAC,CAClE,EAAE,CAACA,CAAC,CAAC,CAAC;;EAEP;EACA,MAAM7B,eAAe,GAAGX,KAAK,CAAC8C,WAAW,CAAC,OAAOC,MAAM,EAAEC,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK;IAC1EjB,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMgB,QAAQ,GAAG,MAAMC,KAAK,CAAC,+CAA+C,EAAE;QAC5EC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,OAAO,CAACC,GAAG,CAACC,4BAA4B,EAAE;UACrE,cAAc,EAAE,kBAAkB;UAClC,cAAc,EAAEC,MAAM,CAACC,QAAQ,CAACC,MAAM;UACtC,SAAS,EAAE;QACb,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,KAAK,EAAE,gCAAgC;UACvCC,QAAQ,EAAE,CAAC;YACTC,IAAI,EAAE,MAAM;YACZC,OAAO,EAAE,wCAAwClB,MAAM,CAACmB,IAAI,wBAAwB7B,IAAI,CAAC8B,IAAI;AACzG;AACA,iCAAiCnB,KAAK,CAACoB,IAAI,CAAC,IAAI,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACU,CAAC,CAAC;UACFC,WAAW,EAAE,GAAG;UAChBC,UAAU,EAAE;QACd,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACrB,QAAQ,CAACsB,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,cAAcvB,QAAQ,CAACwB,MAAM,EAAE,CAAC;MAClD;MAEA,MAAMC,IAAI,GAAG,MAAMzB,QAAQ,CAAC0B,IAAI,CAAC,CAAC;MAClC,MAAMV,OAAO,GAAGS,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO,CAACZ,OAAO;MAE/C,MAAMa,WAAW,GAAG;QAClBC,KAAK,EAAEhC,MAAM,CAACmB,IAAI;QAClBD,OAAO,EAAEA,OAAO;QAChBvC,KAAK,EAAEW,IAAI,CAAC8B,IAAI;QAChBnB,KAAK,EAAEA;MACT,CAAC;MAEDgC,UAAU,CAACF,WAAW,CAAC;MACvBrD,cAAc,CAAC,SAAS,CAAC;;MAEzB;MACA,MAAMwD,MAAM,GAAGzE,mBAAmB,CAAC0E,WAAW,CAAC,mBAAmB,CAAC;MACnE,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5C9E,mBAAmB,CAAC+E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZC,OAAO,CAACzD,KAAK,CAAC,2BAA2B,EAAEwD,GAAG,CAAC;MAC/CvD,QAAQ,CAAC,+CAA+C,CAAC;IAC3D,CAAC,SAAS;MACRF,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACM,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMqD,eAAe,GAAG1F,KAAK,CAAC8C,WAAW,CAAC,CAAC6C,KAAK,EAAEC,UAAU,KAAK;IAC/D,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,IAAIzD,IAAI,EAAE;MAC5D;MACA,MAAMU,MAAM,GAAGV,IAAI,CAAC0D,MAAM,CAACH,UAAU,CAACE,UAAU,CAACE,KAAK,CAAC;MACvD,IAAIjD,MAAM,EAAE;QACV1C,eAAe,CACbuF,UAAU,CAACK,QAAQ,EACnBxD,cAAc,EACbyD,aAAa,IAAK;UACjBT,OAAO,CAACU,GAAG,CAAC,iBAAiB,EAAED,aAAa,CAAC;QAC/C,CAAC,EACAA,aAAa,IAAK;UACjBE,wBAAwB,CAACrD,MAAM,EAAEmD,aAAa,CAAC;QACjD,CACF,CAAC;MACH;IACF;EACF,CAAC,EAAE,CAAC7D,IAAI,EAAEI,cAAc,EAAE2D,wBAAwB,CAAC,CAAC;EAEpD,MAAMC,eAAe,GAAGrG,KAAK,CAAC8C,WAAW,CAAC,CAAC6C,KAAK,EAAEC,UAAU,KAAK;IAC/D;IACA,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,IAAIzD,IAAI,EAAE;MAC5D,MAAMU,MAAM,GAAGV,IAAI,CAAC0D,MAAM,CAACH,UAAU,CAACE,UAAU,CAACE,KAAK,CAAC;MACvD,IAAIjD,MAAM,EAAE;QACVuD,kBAAkB,CAACvD,MAAM,CAAC;MAC5B;IACF;EACF,CAAC,EAAE,CAACV,IAAI,EAAEiE,kBAAkB,CAAC,CAAC;EAE9B,MAAMC,eAAe,GAAGvG,KAAK,CAAC8C,WAAW,CAAC,OAAO6C,KAAK,EAAEC,UAAU,KAAK;IACrE;IACA,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,IAAIzD,IAAI,EAAE;MAC5D,MAAMU,MAAM,GAAGV,IAAI,CAAC0D,MAAM,CAACH,UAAU,CAACE,UAAU,CAACE,KAAK,CAAC;MACvD,IAAIjD,MAAM,EAAE;QACV,MAAMyD,YAAY,CAACzD,MAAM,EAAE6C,UAAU,CAACE,UAAU,CAACE,KAAK,CAAC;MACzD;IACF;EACF,CAAC,EAAE,CAAC3D,IAAI,CAAC,CAAC;;EAEV;EACAnC,SAAS,CAAC,MAAM;IACd,MAAMuG,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAC/C,MAAMC,cAAc,GAAGF,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAE7D,IAAIF,UAAU,IAAIG,cAAc,EAAE;MAChC,MAAMC,QAAQ,GAAG;QACfC,EAAE,EAAE,QAAQ;QACZlE,IAAI,EAAE,MAAM;QACZmE,gBAAgB,EAAE;MACpB,CAAC;MACD5E,OAAO,CAAC0E,QAAQ,CAAC;;MAEjB;MACA,MAAM5B,MAAM,GAAGzE,mBAAmB,CAAC0E,WAAW,CAAC,aAAa,CAAC;MAC7D,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5C9E,mBAAmB,CAAC+E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;;IAEA;IACA,IAAIlD,MAAM,CAAC4E,OAAO,EAAE;MAClB5G,cAAc,CAAC6G,IAAI,CAAC7E,MAAM,CAAC4E,OAAO,EAAE;QAClCE,SAAS,EAAExB,eAAe;QAC1ByB,SAAS,EAAEd,eAAe;QAC1Be,SAAS,EAAEb;MACb,CAAC,CAAC;IACJ;IAEA,OAAO,MAAM;MACXnG,cAAc,CAACiH,OAAO,CAAC,CAAC;IAC1B,CAAC;EACH,CAAC,EAAE,CAAC3B,eAAe,EAAEW,eAAe,EAAEE,eAAe,CAAC,CAAC;;EAEvD;EACArG,SAAS,CAAC,MAAM;IACd,IAAIgC,IAAI,EAAE;MACR,MAAMoF,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,wBAAwB,CAAC;MACnE,IAAIF,SAAS,EAAE;QACb;QACAA,SAAS,CAACG,SAAS,GAAG,EAAE;QACxB;QACAjH,mBAAmB,CAACkH,oBAAoB,CAACJ,SAAS,CAAC;MACrD;;MAEA;MACAzG,cAAc,CAAC,CAAC,CAAC8G,IAAI,CAACC,WAAW,IAAI;QACnCnC,OAAO,CAACU,GAAG,CAAC,2BAA2B,EAAEyB,WAAW,GAAG,aAAa,GAAG,UAAU,CAAC;QAClF,IAAI,CAACA,WAAW,EAAE;UAChBnC,OAAO,CAACoC,IAAI,CAAC,uEAAuE,CAAC;QACvF;MACF,CAAC,CAAC,CAACC,KAAK,CAAC9F,KAAK,IAAI;QAChByD,OAAO,CAACzD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACE,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMzB,qBAAqB,GAAG,MAAAA,CAAOsH,UAAU,EAAEC,KAAK,GAAG,IAAI,KAAK;IAChE,IAAIC,YAAY,GAAGD,KAAK;;IAExB;IACA,IAAI,CAACC,YAAY,EAAE;MACjB,IAAI;QACF,MAAMC,MAAM,GAAGpH,UAAU,CAACqH,SAAS,CAACJ,UAAU,CAAC;QAC/CE,YAAY,GAAGC,MAAM,CAACpB,EAAE;QACxBjF,YAAY,CAACqG,MAAM,CAAC;QACpBzG,cAAc,CAAC,MAAM,CAAC;MACxB,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,QAAQ,CAACD,KAAK,CAAC6C,OAAO,CAAC;QACvB;MACF;IACF;;IAEA;IACA/D,UAAU,CAACsH,eAAe,CAACH,YAAY,EAAE,YAAY,EAAE;MAAEI,QAAQ,EAAE;IAAG,CAAC,CAAC;IACxEtG,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACFwD,OAAO,CAACU,GAAG,CAAC,mCAAmC,EAAE4B,UAAU,EAAE,SAAS,EAAEE,YAAY,CAAC;;MAErF;MACAnH,UAAU,CAACsH,eAAe,CAACH,YAAY,EAAE,YAAY,EAAE;QAAEI,QAAQ,EAAE;MAAG,CAAC,CAAC;MAExE,MAAMC,QAAQ,GAAG,MAAM5H,eAAe,CAACqH,UAAU,CAAC;MAClDtC,OAAO,CAACU,GAAG,CAAC,wBAAwB,EAAEmC,QAAQ,CAAC;;MAE/C;MACAxH,UAAU,CAACsH,eAAe,CAACH,YAAY,EAAE,WAAW,EAAE;QACpD5F,IAAI,EAAEiG,QAAQ;QACdD,QAAQ,EAAE;MACZ,CAAC,CAAC;;MAEF;MACA,IAAIJ,YAAY,MAAKrG,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEkF,EAAE,GAAE;QAClCjF,YAAY,CAACf,UAAU,CAACyH,MAAM,CAACN,YAAY,CAAC,CAAC;MAC/C;;MAEA;MACA,MAAMhD,MAAM,GAAGzE,mBAAmB,CAAC0E,WAAW,CAAC,gBAAgB,CAAC;MAChE,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5C9E,mBAAmB,CAAC+E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZC,OAAO,CAACzD,KAAK,CAAC,0BAA0B,EAAEwD,GAAG,CAAC;MAC9C1E,UAAU,CAACsH,eAAe,CAACH,YAAY,EAAE,OAAO,CAAC;MACjDhG,QAAQ,CAAC,sCAAsCuD,GAAG,CAACX,OAAO,qBAAqB,CAAC;IAClF,CAAC,SAAS;MACR9C,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAID;EACA,MAAMyG,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIhH,KAAK,CAACiH,IAAI,CAAC,CAAC,EAAE;MAChBlI,qBAAqB,CAACiB,KAAK,CAACiH,IAAI,CAAC,CAAC,CAAC;MACnChH,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB;EACF,CAAC;;EAED;EACA,MAAMiH,eAAe,GAAIC,GAAG,IAAK;IAC/BhH,YAAY,CAACgH,GAAG,CAAC;IACjB,IAAIA,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAExG,IAAI,EAAE;MACbZ,cAAc,CAAC,MAAM,CAAC;IACxB,CAAC,MAAM;MACLA,cAAc,CAAC,OAAO,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMqH,YAAY,GAAGA,CAAA,KAAM;IACzBrH,cAAc,CAAC,OAAO,CAAC;IACvBI,YAAY,CAAC,IAAI,CAAC;IAClBF,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;;EAED;EACA,MAAM2E,kBAAkB,GAAIvD,MAAM,IAAK;IACrC,IAAInB,SAAS,EAAE;MACbd,UAAU,CAACsH,eAAe,CAACxG,SAAS,CAACkF,EAAE,EAAElF,SAAS,CAAC6C,MAAM,EAAE;QAAEnC,cAAc,EAAES;MAAO,CAAC,CAAC;MACtFlB,YAAY,CAACf,UAAU,CAACyH,MAAM,CAAC3G,SAAS,CAACkF,EAAE,CAAC,CAAC;IAC/C;EACF,CAAC;;EAED;EACA,MAAMiC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI,CAACxG,OAAO,EAAE;IAEd,IAAIjC,aAAa,CAAC0I,SAAS,CAAC,CAAC,CAACC,SAAS,EAAE;MACvC3I,aAAa,CAAC4I,MAAM,CAAC,CAAC;IACxB,CAAC,MAAM;MACL5I,aAAa,CAAC6I,KAAK,CAAC5G,OAAO,CAAC0B,OAAO,CAAC;MACpC;MACA,MAAMgB,MAAM,GAAGzE,mBAAmB,CAAC0E,WAAW,CAAC,aAAa,CAAC;MAC7D,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5C9E,mBAAmB,CAAC+E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAM8D,gBAAgB,GAAGA,CAAA,KAAM;IAC7B9I,aAAa,CAAC+I,IAAI,CAAC,CAAC;EACtB,CAAC;EAED,MAAMC,sBAAsB,GAAIC,IAAI,IAAK;IACvCjJ,aAAa,CAACkJ,OAAO,CAACD,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAAClH,OAAO,EAAE;IACd,MAAM0C,MAAM,GAAG1E,aAAa,CAACmJ,WAAW,CAACnH,OAAO,EAAE,GAAGA,OAAO,CAACwC,KAAK,CAAC4E,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,EAAE,CAAC;IACnG,IAAI1E,MAAM,CAAC2E,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAGrJ,mBAAmB,CAAC0E,WAAW,CAAC,aAAa,CAAC;MAChE,IAAI2E,SAAS,CAAC1E,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxCyE,SAAS,CAAC1E,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/C9E,mBAAmB,CAAC+E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAMwE,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAACvH,OAAO,EAAE;IACd,MAAM0C,MAAM,GAAG1E,aAAa,CAACwJ,YAAY,CAACxH,OAAO,EAAE,GAAGA,OAAO,CAACwC,KAAK,CAAC4E,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,EAAE,CAAC;IACpG,IAAI1E,MAAM,CAAC2E,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAGrJ,mBAAmB,CAAC0E,WAAW,CAAC,aAAa,CAAC;MAChE,IAAI2E,SAAS,CAAC1E,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxCyE,SAAS,CAAC1E,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/C9E,mBAAmB,CAAC+E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAM0E,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI,CAACzH,OAAO,EAAE;IACd,MAAM0C,MAAM,GAAG,MAAM1E,aAAa,CAAC0J,eAAe,CAAC1H,OAAO,CAAC0B,OAAO,CAAC;IACnE1D,aAAa,CAAC2J,WAAW,CAACjF,MAAM,CAACJ,OAAO,EAAEI,MAAM,CAAC2E,OAAO,GAAG,SAAS,GAAG,OAAO,CAAC;IAC/E,IAAI3E,MAAM,CAAC2E,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAGrJ,mBAAmB,CAAC0E,WAAW,CAAC,aAAa,CAAC;MAChE,IAAI2E,SAAS,CAAC1E,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxCyE,SAAS,CAAC1E,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/C9E,mBAAmB,CAAC+E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;;EAED;EACA,MAAMc,wBAAwB,GAAG,MAAAA,CAAOrD,MAAM,EAAEC,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK;IACjE,IAAI,CAACpB,SAAS,EAAE;IAEhBG,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF0D,OAAO,CAACU,GAAG,CAAC,mCAAmC,EAAEpD,MAAM,CAACmB,IAAI,CAAC;MAC7D,MAAMY,WAAW,GAAG,MAAMnE,eAAe,CAACiB,SAAS,CAACF,KAAK,EAAEqB,MAAM,EAAEC,KAAK,CAAC;;MAEzE;MACAlC,UAAU,CAACsH,eAAe,CAACxG,SAAS,CAACkF,EAAE,EAAE,WAAW,EAAE;QACpDvE,OAAO,EAAEuC;MACX,CAAC,CAAC;MAEFjD,YAAY,CAACf,UAAU,CAACyH,MAAM,CAAC3G,SAAS,CAACkF,EAAE,CAAC,CAAC;MAC7CrF,cAAc,CAAC,SAAS,CAAC;;MAEzB;MACA,MAAMwD,MAAM,GAAGzE,mBAAmB,CAAC0E,WAAW,CAAC,mBAAmB,CAAC;MACnE,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5C9E,mBAAmB,CAAC+E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOtD,KAAK,EAAE;MACdyD,OAAO,CAACzD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDC,QAAQ,CAAC,+CAA+C,CAAC;IAC3D,CAAC,SAAS;MACRF,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMoI,MAAM,GAAGA,CAAA,KAAM;IACnB,IAAI3I,WAAW,KAAK,SAAS,EAAE;MAC7BC,cAAc,CAAC,MAAM,CAAC;MACtB,IAAIG,SAAS,EAAE;QACbd,UAAU,CAACsH,eAAe,CAACxG,SAAS,CAACkF,EAAE,EAAElF,SAAS,CAAC6C,MAAM,EAAE;UAAElC,OAAO,EAAE;QAAK,CAAC,CAAC;QAC7EV,YAAY,CAACf,UAAU,CAACyH,MAAM,CAAC3G,SAAS,CAACkF,EAAE,CAAC,CAAC;MAC/C;IACF,CAAC,MAAM,IAAItF,WAAW,KAAK,MAAM,EAAE;MACjCC,cAAc,CAAC,OAAO,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAM+E,YAAY,GAAG,MAAAA,CAAOzD,MAAM,EAAEqH,WAAW,KAAK;IAClDrI,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MAAA,IAAAoI,cAAA,EAAAC,qBAAA;MACF,MAAMrH,QAAQ,GAAG,MAAMC,KAAK,CAAC,+CAA+C,EAAE;QAC5EC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,OAAO,CAACC,GAAG,CAACC,4BAA4B,EAAE;UACrE,cAAc,EAAE,kBAAkB;UAClC,cAAc,EAAEC,MAAM,CAACC,QAAQ,CAACC,MAAM;UACtC,SAAS,EAAE;QACb,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,KAAK,EAAE,gCAAgC;UACvCC,QAAQ,EAAE,CAAC;YACTC,IAAI,EAAE,MAAM;YACZC,OAAO,EAAE,qBAAqBlB,MAAM,CAACmB,IAAI,0BAA0B7B,IAAI,CAAC8B,IAAI;AACxF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+DAA+DpB,MAAM,CAACmB,IAAI;UAChE,CAAC,CAAC;UACFG,WAAW,EAAE,GAAG;UAChBC,UAAU,EAAE;QACd,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACrB,QAAQ,CAACsB,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,cAAcvB,QAAQ,CAACwB,MAAM,EAAE,CAAC;MAClD;MAEA,MAAMC,IAAI,GAAG,MAAMzB,QAAQ,CAAC0B,IAAI,CAAC,CAAC;MAClC,MAAMV,OAAO,IAAAoG,cAAA,GAAG3F,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,cAAAyF,cAAA,wBAAAC,qBAAA,GAAfD,cAAA,CAAiBxF,OAAO,cAAAyF,qBAAA,uBAAxBA,qBAAA,CAA0BrG,OAAO;MAEjD,IAAI,CAACA,OAAO,EAAE;QACZ,MAAM,IAAIO,KAAK,CAAC,8BAA8B,CAAC;MACjD;;MAEA;MACA,MAAM+F,SAAS,GAAGtG,OAAO,CAACuG,KAAK,CAAC,aAAa,CAAC;MAC9C,IAAI,CAACD,SAAS,EAAE;QACd,MAAM,IAAI/F,KAAK,CAAC,iCAAiC,CAAC;MACpD;MAEA,MAAMiG,YAAY,GAAG7G,IAAI,CAAC8G,KAAK,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC;;MAE7C;MACA,MAAMI,OAAO,GAAG;QAAE,GAAGtI;MAAK,CAAC;MAC3BsI,OAAO,CAAC5E,MAAM,GAAG,CACf,GAAG4E,OAAO,CAAC5E,MAAM,CAAC6E,KAAK,CAAC,CAAC,EAAER,WAAW,GAAG,CAAC,CAAC,EAC3C,GAAGK,YAAY,CAAC1E,MAAM,CAAC8E,GAAG,CAACC,SAAS,KAAK;QACvC,GAAGA,SAAS;QACZC,WAAW,EAAE,IAAI;QACjBC,YAAY,EAAEjI,MAAM,CAACmB,IAAI;QACzB+G,KAAK,EAAE,CAAClI,MAAM,CAACkI,KAAK,IAAI,CAAC,IAAI;MAC/B,CAAC,CAAC,CAAC,EACH,GAAGN,OAAO,CAAC5E,MAAM,CAAC6E,KAAK,CAACR,WAAW,GAAG,CAAC,CAAC,CACzC;;MAED;MACA,IAAIxI,SAAS,EAAE;QACbd,UAAU,CAACsH,eAAe,CAACxG,SAAS,CAACkF,EAAE,EAAE,WAAW,EAAE;UAAEzE,IAAI,EAAEsI;QAAQ,CAAC,CAAC;QACxE9I,YAAY,CAACf,UAAU,CAACyH,MAAM,CAAC3G,SAAS,CAACkF,EAAE,CAAC,CAAC;MAC/C;;MAEA;MACA,MAAM7B,MAAM,GAAGzE,mBAAmB,CAAC0E,WAAW,CAAC,iBAAiB,CAAC;MACjE,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5C9E,mBAAmB,CAAC+E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IAEF,CAAC,CAAC,OAAOtD,KAAK,EAAE;MACdyD,OAAO,CAACzD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAACO,CAAC,CAAC,gBAAgB,CAAC,CAAC;IAC/B,CAAC,SAAS;MACRT,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMmJ,MAAM,GAAGA,CAAA,KAAM;IACnBzJ,cAAc,CAAC,OAAO,CAAC;IACvBI,YAAY,CAAC,IAAI,CAAC;IAClBF,QAAQ,CAAC,EAAE,CAAC;IACZ;IACAb,UAAU,CAACqK,YAAY,CAAC,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB1E,YAAY,CAAC2E,OAAO,CAAC,gBAAgB,EAAE,MAAM,CAAC;IAC9ClJ,OAAO,CAAC;MAAE2E,EAAE,EAAE,OAAO;MAAElE,IAAI,EAAE,WAAW;MAAEmE,gBAAgB,EAAE;IAAU,CAAC,CAAC;EAC1E,CAAC;EAED,oBACE5F,OAAA;IAAKmK,SAAS,EAAC,KAAK;IAACC,GAAG,EAAEnJ,MAAO;IAAAoJ,QAAA,gBAE/BrK,OAAA;MAAQmK,SAAS,EAAC,YAAY;MAAAE,QAAA,eAC5BrK,OAAA;QAAKmK,SAAS,EAAC,gBAAgB;QAAAE,QAAA,gBAC7BrK,OAAA;UAAQsK,OAAO,EAAEP,MAAO;UAACI,SAAS,EAAC,WAAW;UAAAE,QAAA,EAC3ChJ,CAAC,CAAC,UAAU;QAAC;UAAAkJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACT1K,OAAA;UAAKmK,SAAS,EAAC,cAAc;UAAAE,QAAA,GAC1BtJ,IAAI,iBACHf,OAAA;YAAK2F,EAAE,EAAC,wBAAwB;YAACgF,KAAK,EAAE;cAAEC,WAAW,EAAE;YAAO;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE3D,CACN,eACD1K,OAAA,CAACH,gBAAgB;YAAA0K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACnB,CAAC3J,IAAI,gBACJf,OAAA;YAAQsK,OAAO,EAAEL,UAAW;YAACE,SAAS,EAAC,iBAAiB;YAACQ,KAAK,EAAE;cAAEE,UAAU,EAAE;YAAO,CAAE;YAAAR,QAAA,EACpFhJ,CAAC,CAAC,YAAY;UAAC;YAAAkJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,gBAET1K,OAAA;YAAM2K,KAAK,EAAE;cAAEE,UAAU,EAAE;YAAO,CAAE;YAAAR,QAAA,GAAEhJ,CAAC,CAAC,SAAS,CAAC,EAAC,IAAE,EAACN,IAAI,CAACU,IAAI,EAAC,GAAC;UAAA;YAAA8I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACxE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAGR3J,IAAI,iBACHf,OAAA,CAACJ,UAAU;MACTkL,WAAW,EAAErD,eAAgB;MAC7BsD,QAAQ,EAAEpD;IAAa;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CACF,eAGD1K,OAAA;MAAMmK,SAAS,EAAC,cAAc;MAAAE,QAAA,GAC3BxJ,KAAK,iBACJb,OAAA;QAAKmK,SAAS,EAAC,OAAO;QAAAE,QAAA,GAAC,eAClB,EAACxJ,KAAK,eACTb,OAAA;UAAQsK,OAAO,EAAEA,CAAA,KAAMxJ,QAAQ,CAAC,IAAI,CAAE;UAAC6J,KAAK,EAAE;YAACE,UAAU,EAAE,MAAM;YAAEG,UAAU,EAAE,MAAM;YAAEC,MAAM,EAAE,MAAM;YAAEC,KAAK,EAAE,OAAO;YAAEC,MAAM,EAAE;UAAS,CAAE;UAAAd,QAAA,EAAC;QAE3I;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAGArK,WAAW,KAAK,OAAO,iBACtBL,OAAA;QAAKmK,SAAS,EAAC,kBAAkB;QAAAE,QAAA,gBAC/BrK,OAAA;UAAImK,SAAS,EAAC,OAAO;UAAAE,QAAA,EAAEhJ,CAAC,CAAC,UAAU;QAAC;UAAAkJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1C1K,OAAA;UAAGmK,SAAS,EAAC,UAAU;UAAAE,QAAA,EAAC;QAExB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAEH,CAAC3J,IAAI,gBACJf,OAAA;UAAK2K,KAAK,EAAE;YAACK,UAAU,EAAE,SAAS;YAAEI,OAAO,EAAE,MAAM;YAAEC,YAAY,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAM,CAAE;UAAAjB,QAAA,gBACjGrK,OAAA;YAAG2K,KAAK,EAAE;cAACO,KAAK,EAAE,SAAS;cAAEI,YAAY,EAAE;YAAM,CAAE;YAAAjB,QAAA,EAChDhJ,CAAC,CAAC,eAAe;UAAC;YAAAkJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACJ1K,OAAA;YAAQsK,OAAO,EAAEL,UAAW;YAACE,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EACrDhJ,CAAC,CAAC,eAAe;UAAC;YAAAkJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAEN1K,OAAA;UAAMuL,QAAQ,EAAElE,YAAa;UAAAgD,QAAA,gBAC3BrK,OAAA;YAAKmK,SAAS,EAAC,YAAY;YAAAE,QAAA,eACzBrK,OAAA;cACEwL,IAAI,EAAC,MAAM;cACXC,KAAK,EAAElL,KAAM;cACbmL,QAAQ,EAAGpE,CAAC,IAAK9G,QAAQ,CAAC8G,CAAC,CAACqE,MAAM,CAACF,KAAK,CAAE;cAC1CG,WAAW,EAAEvK,CAAC,CAAC,kBAAkB,CAAE;cACnC8I,SAAS,EAAC,YAAY;cACtB0B,QAAQ,EAAElL;YAAU;cAAA4J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN1K,OAAA;YACEwL,IAAI,EAAC,QAAQ;YACbK,QAAQ,EAAElL,SAAS,IAAI,CAACJ,KAAK,CAACiH,IAAI,CAAC,CAAE;YACrC2C,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EAE1B1J,SAAS,gBACRX,OAAA,CAAAE,SAAA;cAAAmK,QAAA,gBACErK,OAAA;gBAAMmK,SAAS,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAChCrJ,CAAC,CAAC,YAAY,CAAC;YAAA,eAChB,CAAC,gBAEHrB,OAAA,CAAAE,SAAA;cAAAmK,QAAA,EACGhJ,CAAC,CAAC,kBAAkB;YAAC,gBACtB;UACH;YAAAkJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGArK,WAAW,KAAK,MAAM,IAAIa,IAAI,iBAC7BlB,OAAA;QAAKmK,SAAS,EAAC,gBAAgB;QAAAE,QAAA,gBAC7BrK,OAAA;UAAKmK,SAAS,EAAC,aAAa;UAAAE,QAAA,gBAC1BrK,OAAA;YAAAqK,QAAA,EAAKnJ,IAAI,CAAC8B;UAAI;YAAAuH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpB1K,OAAA;YAAAqK,QAAA,EAAIhJ,CAAC,CAAC,cAAc;UAAC;YAAAkJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1B1K,OAAA;YAAQsK,OAAO,EAAEtB,MAAO;YAACmB,SAAS,EAAC,mBAAmB;YAACQ,KAAK,EAAE;cAACmB,SAAS,EAAE;YAAM,CAAE;YAAAzB,QAAA,EAC/EhJ,CAAC,CAAC,YAAY;UAAC;YAAAkJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAEL/J,SAAS,gBACRX,OAAA;UAAKmK,SAAS,EAAC,SAAS;UAAAE,QAAA,gBACtBrK,OAAA;YAAMmK,SAAS,EAAC;UAAS;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjC1K,OAAA;YAAAqK,QAAA,EAAOhJ,CAAC,CAAC,SAAS;UAAC;YAAAkJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,gBAEN1K,OAAA;UAAKmK,SAAS,EAAC,eAAe;UAAAE,QAAA,EAC3BnJ,IAAI,CAAC0D,MAAM,CAAC8E,GAAG,CAAC,CAAC9H,MAAM,EAAEiD,KAAK,kBAC7B7E,OAAA;YAEEmK,SAAS,EAAE,eAAehJ,cAAc,KAAKS,MAAM,GAAG,UAAU,GAAG,EAAE,EAAG;YACxE,cAAYiD,KAAM;YAClB,aAAWjD,MAAM,CAACmB,IAAK;YACvB,oBAAkBnB,MAAM,CAACmK,SAAU;YACnC,sBAAoBnK,MAAM,CAACgI,WAAW,IAAI,KAAM;YAChD,cAAYhI,MAAM,CAACkI,KAAK,IAAI,CAAE;YAC9BQ,OAAO,EAAEA,CAAA,KAAMnF,kBAAkB,CAACvD,MAAM,CAAE;YAAAyI,QAAA,gBAE1CrK,OAAA;cAAKmK,SAAS,EAAC,cAAc;cAAAE,QAAA,EAAEzI,MAAM,CAACoK;YAAK;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClD1K,OAAA;cAAImK,SAAS,EAAC,aAAa;cAAAE,QAAA,EAAEzI,MAAM,CAACmB;YAAI;cAAAwH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9C1K,OAAA;cAAGmK,SAAS,EAAC,oBAAoB;cAAAE,QAAA,EAAEzI,MAAM,CAACmK;YAAS;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACvD9I,MAAM,CAACqK,YAAY,iBAClBjM,OAAA;cAAK2K,KAAK,EAAE;gBAACuB,QAAQ,EAAE,UAAU;gBAAEhB,KAAK,EAAE,SAAS;gBAAEY,SAAS,EAAE;cAAQ,CAAE;cAAAzB,QAAA,GACvEhJ,CAAC,CAAC,QAAQ,CAAC,EAAC,IAAE,EAACO,MAAM,CAACqK,YAAY,CAACxC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACxG,IAAI,CAAC,IAAI,CAAC,EACzDrB,MAAM,CAACqK,YAAY,CAAChI,MAAM,GAAG,CAAC,IAAI,KAAK;YAAA;cAAAsG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CACN,eACD1K,OAAA;cAAKmK,SAAS,EAAC,cAAc;cAACQ,KAAK,EAAE;gBACnCuB,QAAQ,EAAE,SAAS;gBACnBhB,KAAK,EAAE,SAAS;gBAChBY,SAAS,EAAE,QAAQ;gBACnBK,SAAS,EAAE;cACb,CAAE;cAAA9B,QAAA,EACChJ,CAAC,CAAC,aAAa;YAAC;cAAAkJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC;UAAA,GAzBD7F,KAAK;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0BP,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGArK,WAAW,KAAK,SAAS,IAAIe,OAAO,iBACnCpB,OAAA;QAAKmK,SAAS,EAAC,gBAAgB;QAAAE,QAAA,eAC7BrK,OAAA;UAAKmK,SAAS,EAAC,MAAM;UAAAE,QAAA,gBACnBrK,OAAA;YAAKmK,SAAS,EAAC,gBAAgB;YAACQ,KAAK,EAAE;cAACW,YAAY,EAAE;YAAM,CAAE;YAAAjB,QAAA,gBAC5DrK,OAAA;cAAQsK,OAAO,EAAEtB,MAAO;cAACmB,SAAS,EAAC,mBAAmB;cAAAE,QAAA,EACnDhJ,CAAC,CAAC,YAAY;YAAC;cAAAkJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGT1K,OAAA;cAAKmK,SAAS,EAAC,kBAAkB;cAACQ,KAAK,EAAE;gBACvCyB,OAAO,EAAE,MAAM;gBACfC,GAAG,EAAE,KAAK;gBACVP,SAAS,EAAE,MAAM;gBACjBQ,QAAQ,EAAE;cACZ,CAAE;cAAAjC,QAAA,gBAEArK,OAAA;gBAAKmK,SAAS,EAAC,yBAAyB;gBAACQ,KAAK,EAAE;kBAC9CyB,OAAO,EAAE,MAAM;kBACfG,UAAU,EAAE,QAAQ;kBACpBF,GAAG,EAAE,KAAK;kBACVjB,OAAO,EAAE,UAAU;kBACnBJ,UAAU,EAAE,SAAS;kBACrBK,YAAY,EAAE,KAAK;kBACnBJ,MAAM,EAAE;gBACV,CAAE;gBAAAZ,QAAA,gBACArK,OAAA;kBACEsK,OAAO,EAAE1C,kBAAmB;kBAC5BuC,SAAS,EAAC,UAAU;kBACpBvG,KAAK,EAAC,mBAAmB;kBACzB+G,KAAK,EAAE;oBACLK,UAAU,EAAE,MAAM;oBAClBC,MAAM,EAAE,MAAM;oBACdiB,QAAQ,EAAE,MAAM;oBAChBf,MAAM,EAAE,SAAS;oBACjBC,OAAO,EAAE;kBACX,CAAE;kBAAAf,QAAA,EAEDlL,aAAa,CAAC0I,SAAS,CAAC,CAAC,CAACC,SAAS,GAAG,IAAI,GAAG;gBAAI;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,eACT1K,OAAA;kBACEsK,OAAO,EAAErC,gBAAiB;kBAC1BkC,SAAS,EAAC,UAAU;kBACpBvG,KAAK,EAAC,aAAa;kBACnB+G,KAAK,EAAE;oBACLK,UAAU,EAAE,MAAM;oBAClBC,MAAM,EAAE,MAAM;oBACdiB,QAAQ,EAAE,MAAM;oBAChBf,MAAM,EAAE,SAAS;oBACjBC,OAAO,EAAE;kBACX,CAAE;kBAAAf,QAAA,EACH;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT1K,OAAA;kBACEwL,IAAI,EAAC,OAAO;kBACZgB,GAAG,EAAC,KAAK;kBACTC,GAAG,EAAC,GAAG;kBACPC,IAAI,EAAC,KAAK;kBACVC,YAAY,EAAC,GAAG;kBAChBjB,QAAQ,EAAGpE,CAAC,IAAKa,sBAAsB,CAACyE,UAAU,CAACtF,CAAC,CAACqE,MAAM,CAACF,KAAK,CAAC,CAAE;kBACpEd,KAAK,EAAE;oBAACkC,KAAK,EAAE;kBAAM,CAAE;kBACvBjJ,KAAK,EAAC;gBAAc;kBAAA2G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACF1K,OAAA;kBAAM2K,KAAK,EAAE;oBAACuB,QAAQ,EAAE,MAAM;oBAAEhB,KAAK,EAAE;kBAAS,CAAE;kBAAAb,QAAA,EAAC;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eAGN1K,OAAA;gBAAKmK,SAAS,EAAC,yBAAyB;gBAACQ,KAAK,EAAE;kBAC9CyB,OAAO,EAAE,MAAM;kBACfC,GAAG,EAAE;gBACP,CAAE;gBAAAhC,QAAA,gBACArK,OAAA;kBACEsK,OAAO,EAAEzB,qBAAsB;kBAC/BsB,SAAS,EAAC,mBAAmB;kBAC7BQ,KAAK,EAAE;oBAACS,OAAO,EAAE,UAAU;oBAAEc,QAAQ,EAAE;kBAAM,CAAE;kBAC/CtI,KAAK,EAAC,mBAAmB;kBAAAyG,QAAA,EAC1B;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT1K,OAAA;kBACEsK,OAAO,EAAEhC,eAAgB;kBACzB6B,SAAS,EAAC,mBAAmB;kBAC7BQ,KAAK,EAAE;oBAACS,OAAO,EAAE,UAAU;oBAAEc,QAAQ,EAAE;kBAAM,CAAE;kBAC/CtI,KAAK,EAAC,eAAe;kBAAAyG,QAAA,EACtB;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT1K,OAAA;kBACEsK,OAAO,EAAE3B,gBAAiB;kBAC1BwB,SAAS,EAAC,mBAAmB;kBAC7BQ,KAAK,EAAE;oBAACS,OAAO,EAAE,UAAU;oBAAEc,QAAQ,EAAE;kBAAM,CAAE;kBAC/CtI,KAAK,EAAC,gBAAgB;kBAAAyG,QAAA,EACvB;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1K,OAAA;YAAImK,SAAS,EAAC,OAAO;YAAAE,QAAA,EAAEjJ,OAAO,CAACwC;UAAK;YAAA2G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1C1K,OAAA;YAAK2K,KAAK,EAAE;cAACO,KAAK,EAAE,SAAS;cAAEI,YAAY,EAAE,MAAM;cAAEY,QAAQ,EAAE;YAAQ,CAAE;YAAA7B,QAAA,gBACvErK,OAAA;cAAAqK,QAAA,GAAOhJ,CAAC,CAAC,QAAQ,CAAC,EAAC,IAAE,EAACD,OAAO,CAACb,KAAK;YAAA;cAAAgK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAC1CtJ,OAAO,CAACS,KAAK,IAAIT,OAAO,CAACS,KAAK,CAACoC,MAAM,GAAG,CAAC,iBACxCjE,OAAA;cAAM2K,KAAK,EAAE;gBAACE,UAAU,EAAE;cAAM,CAAE;cAAAR,QAAA,GAC/BhJ,CAAC,CAAC,OAAO,CAAC,EAAC,IAAE,EAACD,OAAO,CAACS,KAAK,CAACoB,IAAI,CAAC,IAAI,CAAC;YAAA;cAAAsH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN1K,OAAA;YAAKmK,SAAS,EAAC,iBAAiB;YAACQ,KAAK,EAAE;cAACmC,UAAU,EAAE,KAAK;cAAEZ,QAAQ,EAAE;YAAQ,CAAE;YAAA7B,QAAA,EAC7EjJ,OAAO,CAAC0B,OAAO,CAACiK,KAAK,CAAC,IAAI,CAAC,CAACrD,GAAG,CAAC,CAACsD,SAAS,EAAEnI,KAAK,KAChDmI,SAAS,CAACxF,IAAI,CAAC,CAAC,iBACdxH,OAAA;cAAe2K,KAAK,EAAE;gBAACW,YAAY,EAAE;cAAM,CAAE;cAAAjB,QAAA,EAC1C2C;YAAS,GADJnI,KAAK;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CAEN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACtK,EAAA,CAhxBID,YAAY;EAAA,QAgBFL,cAAc;AAAA;AAAAmN,EAAA,GAhBxB9M,YAAY;AAkxBlB,eAAeA,YAAY;AAAC,IAAA8M,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}