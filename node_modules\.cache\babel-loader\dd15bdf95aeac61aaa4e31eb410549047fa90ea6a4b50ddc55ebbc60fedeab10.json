{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Appv1\\\\src\\\\components\\\\OptimizedApp.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport '../styles/optimized.css';\nimport gestureService, { createFlagWheel } from '../services/gestureService';\nimport speechService from '../services/speechService';\nimport exportService from '../services/exportService';\nimport gamificationService from '../services/optimizedGamificationService';\nimport { generateKnowledgeTree as generateTreeAPI, generateArticle as generateArticleAPI, testConnection } from '../services/openRouterService';\nimport tabService from '../services/tabService';\nimport TabManager from './TabManager';\nimport LanguageSwitcher from './LanguageSwitcher';\nimport { useTranslation, getCurrentLanguage } from '../utils/i18n';\n\n// Optimized Knowledge Tree Explorer - Pareto 80/20 Implementation\n// Focus on core functionality with maximum impact\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst OptimizedApp = () => {\n  _s();\n  // Core state - now managed by tabs\n  const [currentView, setCurrentView] = useState('input'); // 'input', 'tree', 'article'\n  const [topic, setTopic] = useState('');\n  const [activeTab, setActiveTab] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [user, setUser] = useState(null);\n  const appRef = useRef(null);\n\n  // Get current tab data\n  const tree = (activeTab === null || activeTab === void 0 ? void 0 : activeTab.tree) || null;\n  const selectedBranch = (activeTab === null || activeTab === void 0 ? void 0 : activeTab.selectedBranch) || null;\n  const article = (activeTab === null || activeTab === void 0 ? void 0 : activeTab.article) || null;\n\n  // Translation hook\n  const {\n    t\n  } = useTranslation();\n\n  // Available flags for the optimized version\n  const availableFlags = React.useMemo(() => [{\n    code: '-a',\n    name: 'Article',\n    description: t('flagArticle')\n  }, {\n    code: '-ex',\n    name: 'Examples',\n    description: t('flagExamples')\n  }, {\n    code: '-q',\n    name: 'Quiz',\n    description: t('flagQuiz')\n  }, {\n    code: '-vis',\n    name: 'Visual',\n    description: t('flagVisual')\n  }, {\n    code: '-path',\n    name: 'Learning Path',\n    description: t('flagPath')\n  }, {\n    code: '-case',\n    name: 'Case Study',\n    description: t('flagCase')\n  }, {\n    code: '-ro',\n    name: 'Romanian',\n    description: t('flagRomanian')\n  }], [t]);\n\n  // Generate article with tabs support\n  const generateArticleForBranch = React.useCallback(async (branch, flags = ['-a']) => {\n    if (!activeTab) return;\n    setIsLoading(true);\n\n    // Set tab to loading state (yellow)\n    tabService.updateTabStatus(activeTab.id, 'loading', {\n      selectedBranch: branch,\n      article: null\n    });\n    setActiveTab(tabService.getTab(activeTab.id));\n    try {\n      console.log('📄 Generating article for branch:', branch.nume);\n      const articleData = await generateArticleAPI(activeTab.topic, branch, flags);\n\n      // Update tab with article and set to completed (green)\n      tabService.updateTabStatus(activeTab.id, 'completed', {\n        selectedBranch: branch,\n        article: articleData\n      });\n      const updatedTab = tabService.getTab(activeTab.id);\n      setActiveTab(updatedTab);\n      setCurrentView('article');\n\n      // Award points for article generation\n      const result = gamificationService.awardPoints('ARTICLE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (error) {\n      console.error('❌ Error generating article:', error);\n      setError('Failed to generate article. Please try again.');\n\n      // Set tab back to pending on error\n      tabService.updateTabStatus(activeTab.id, 'pending', {\n        selectedBranch: branch,\n        article: null\n      });\n      setActiveTab(tabService.getTab(activeTab.id));\n    } finally {\n      setIsLoading(false);\n    }\n  }, [activeTab]);\n\n  // Handle branch selection (single tap)\n  const handleBranchSelect = React.useCallback(branch => {\n    if (activeTab) {\n      tabService.updateTabStatus(activeTab.id, activeTab.status, {\n        selectedBranch: branch\n      });\n      setActiveTab(tabService.getTab(activeTab.id));\n    }\n  }, [activeTab]);\n\n  // Gesture handlers\n  const handleDoubleTap = React.useCallback((event, targetInfo) => {\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      // Show flag wheel on double tap of branch\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        createFlagWheel(targetInfo.position, availableFlags, selectedFlags => {\n          console.log('Selected flags:', selectedFlags);\n        }, selectedFlags => {\n          generateArticleForBranch(branch, selectedFlags);\n        });\n      }\n    }\n  }, [tree, availableFlags, generateArticleForBranch]);\n  const handleSingleTap = React.useCallback((event, targetInfo) => {\n    // Single tap for normal selection\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        handleBranchSelect(branch);\n      }\n    }\n  }, [tree, handleBranchSelect]);\n\n  // Expand branch to create sub-branches (tree effect)\n  const expandBranch = React.useCallback(async (branch, branchIndex) => {\n    if (!activeTab || !tree) {\n      setError(t('noActiveTab') || 'No active tab or tree available');\n      return;\n    }\n    setIsLoading(true);\n    setError(null);\n    try {\n      console.log('🌿 Expanding branch:', branch.nume);\n\n      // Create a mini knowledge tree for the branch expansion\n      const expandedTree = await generateTreeAPI(`${branch.nume} - ${tree.tema}`, getCurrentLanguage());\n      console.log('✅ Generated expanded tree:', expandedTree);\n\n      // Extract the branches from the expanded tree\n      const expandedData = {\n        ramuri: expandedTree.ramuri.slice(0, 4) // Take first 4 branches as sub-branches\n      };\n\n      // Update tree with expanded branches\n      const newTree = {\n        ...tree\n      };\n      newTree.ramuri = [...newTree.ramuri.slice(0, branchIndex + 1), ...expandedData.ramuri.map(subBranch => ({\n        ...subBranch,\n        isSubBranch: true,\n        parentBranch: branch.nume,\n        level: (branch.level || 0) + 1\n      })), ...newTree.ramuri.slice(branchIndex + 1)];\n\n      // Update tab with expanded tree\n      tabService.updateTabStatus(activeTab.id, 'completed', {\n        tree: newTree\n      });\n      setActiveTab(tabService.getTab(activeTab.id));\n      console.log('🌳 Tree expanded successfully');\n\n      // Award points for branch expansion\n      const result = gamificationService.awardPoints('BRANCH_EXPANDED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (error) {\n      console.error('❌ Error expanding branch:', error);\n      setError(t('failedToExpand') || 'Nu s-a putut extinde ramura. Te rugăm să încerci din nou.');\n    } finally {\n      setIsLoading(false);\n    }\n  }, [tree, activeTab, t]);\n  const handleLongPress = React.useCallback(async (event, targetInfo) => {\n    // Long press to expand branch and create tree effect\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        await expandBranch(branch, targetInfo.branchData.index);\n      }\n    }\n  }, [tree, expandBranch]);\n\n  // Initialize services and authentication\n  useEffect(() => {\n    const storedUser = localStorage.getItem('user');\n    const bypassSecurity = localStorage.getItem('bypassSecurity');\n    if (storedUser || bypassSecurity) {\n      const userData = {\n        id: 'user-1',\n        name: 'User',\n        subscriptionTier: 'premium'\n      };\n      setUser(userData);\n\n      // Award daily login points\n      const result = gamificationService.awardPoints('DAILY_LOGIN');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n\n    // Initialize gesture service\n    if (appRef.current) {\n      gestureService.init(appRef.current, {\n        doubleTap: handleDoubleTap,\n        singleTap: handleSingleTap,\n        longPress: handleLongPress\n      });\n    }\n    return () => {\n      gestureService.destroy();\n    };\n  }, [handleDoubleTap, handleSingleTap, handleLongPress]);\n\n  // Initialize gamification UI when user is logged in\n  useEffect(() => {\n    if (user) {\n      const container = document.getElementById('gamification-container');\n      if (container) {\n        // Clear existing content\n        container.innerHTML = '';\n        // Create gamification UI\n        gamificationService.createGamificationUI(container);\n      }\n\n      // Test API connection\n      testConnection().then(isConnected => {\n        console.log('🔌 API Connection Status:', isConnected ? '✅ Connected' : '❌ Failed');\n        if (!isConnected) {\n          console.warn('⚠️ API connection failed. Check your API key and internet connection.');\n        }\n      }).catch(error => {\n        console.error('❌ API connection test error:', error);\n      });\n    }\n  }, [user]);\n\n  // Core API call - using optimized service with tabs\n  const generateKnowledgeTree = async (topicInput, tabId = null) => {\n    let currentTabId = tabId;\n\n    // Create new tab if none provided\n    if (!currentTabId) {\n      try {\n        const newTab = tabService.createTab(topicInput);\n        currentTabId = newTab.id;\n        setActiveTab(newTab);\n        setCurrentView('tree');\n      } catch (error) {\n        setError(error.message);\n        return;\n      }\n    }\n\n    // Update tab status to generating\n    tabService.updateTabStatus(currentTabId, 'generating', {\n      progress: 10\n    });\n    setIsLoading(true);\n    setError(null);\n    try {\n      console.log('🌳 Generating knowledge tree for:', topicInput, 'in tab:', currentTabId);\n\n      // Update progress\n      tabService.updateTabStatus(currentTabId, 'generating', {\n        progress: 30\n      });\n      const treeData = await generateTreeAPI(topicInput, getCurrentLanguage());\n      console.log('✅ Generated tree data:', treeData);\n\n      // Update tab with completed tree\n      tabService.updateTabStatus(currentTabId, 'completed', {\n        tree: treeData,\n        progress: 100\n      });\n\n      // Update active tab if this is the current one\n      if (currentTabId === (activeTab === null || activeTab === void 0 ? void 0 : activeTab.id)) {\n        setActiveTab(tabService.getTab(currentTabId));\n      }\n\n      // Award points for tree generation\n      const result = gamificationService.awardPoints('TREE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (err) {\n      console.error('❌ Error generating tree:', err);\n      tabService.updateTabStatus(currentTabId, 'error');\n      setError(`Failed to generate knowledge tree: ${err.message}. Please try again.`);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Handle form submission\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (topic.trim()) {\n      generateKnowledgeTree(topic.trim());\n      setTopic(''); // Clear input for next topic\n    }\n  };\n\n  // Handle tab changes\n  const handleTabChange = tab => {\n    // Clear any existing errors when switching tabs\n    setError(null);\n    setIsLoading(false);\n    setActiveTab(tab);\n    if (tab !== null && tab !== void 0 && tab.tree) {\n      setCurrentView('tree');\n    } else {\n      setCurrentView('input');\n    }\n  };\n\n  // Handle new tab creation\n  const handleNewTab = () => {\n    // Clear any existing errors and loading states\n    setError(null);\n    setIsLoading(false);\n    setCurrentView('input');\n    setActiveTab(null);\n    setTopic('');\n  };\n  const handleTabArticleAccess = tab => {\n    // Clear any existing errors when accessing article\n    setError(null);\n    setIsLoading(false);\n    setActiveTab(tab);\n    setCurrentView('article');\n  };\n\n  // Speech functions\n  const handleSpeechToggle = () => {\n    if (!(article !== null && article !== void 0 && article.content)) return;\n    if (speechService.getStatus().isPlaying) {\n      speechService.toggle();\n    } else {\n      speechService.speak(article.content);\n      // Award points for using speech\n      const result = gamificationService.awardPoints('SPEECH_USED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleSpeechStop = () => {\n    speechService.stop();\n  };\n  const handleSpeechRateChange = rate => {\n    speechService.setRate(rate);\n  };\n\n  // Export functions\n  const handleExportPDF = () => {\n    if (!(article !== null && article !== void 0 && article.title) || !(article !== null && article !== void 0 && article.content)) return;\n    const result = exportService.exportAsPDF(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleExportWord = () => {\n    if (!(article !== null && article !== void 0 && article.title) || !(article !== null && article !== void 0 && article.content)) return;\n    const result = exportService.exportAsWord(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleCopyToClipboard = async () => {\n    if (!(article !== null && article !== void 0 && article.content)) return;\n    const result = await exportService.copyToClipboard(article.content);\n    exportService.showMessage(result.message, result.success ? 'success' : 'error');\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  // Navigation functions\n  const goBack = () => {\n    if (currentView === 'article') {\n      setCurrentView('tree');\n      if (activeTab) {\n        tabService.updateTabStatus(activeTab.id, activeTab.status, {\n          article: null\n        });\n        setActiveTab(tabService.getTab(activeTab.id));\n      }\n    } else if (currentView === 'tree') {\n      setCurrentView('input');\n    }\n  };\n  const goHome = () => {\n    setCurrentView('input');\n    setActiveTab(null);\n    setTopic('');\n    // Clear all tabs\n    tabService.clearAllTabs();\n  };\n\n  // Quick login for development\n  const quickLogin = () => {\n    localStorage.setItem('bypassSecurity', 'true');\n    setUser({\n      id: 'dev-1',\n      name: 'Developer',\n      subscriptionTier: 'premium'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    ref: appRef,\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"app-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: goHome,\n          className: \"logo-text\",\n          children: t('appTitle')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 470,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-right\",\n          children: [user && /*#__PURE__*/_jsxDEV(\"div\", {\n            id: \"gamification-container\",\n            style: {\n              marginRight: '16px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(LanguageSwitcher, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 13\n          }, this), !user ? /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: quickLogin,\n            className: \"btn btn-primary\",\n            style: {\n              marginLeft: '12px'\n            },\n            children: t('quickLogin')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 481,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginLeft: '12px'\n            },\n            children: [t('welcome'), \", \", user.name, \"!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 469,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 468,\n      columnNumber: 7\n    }, this), user && /*#__PURE__*/_jsxDEV(TabManager, {\n      onTabChange: handleTabChange,\n      onNewTab: handleNewTab,\n      onTabArticleAccess: handleTabArticleAccess\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 493,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"main-content\",\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error\",\n        children: [\"\\u26A0\\uFE0F \", error, /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setError(null),\n          style: {\n            marginLeft: 'auto',\n            background: 'none',\n            border: 'none',\n            color: 'white',\n            cursor: 'pointer'\n          },\n          children: \"\\u2715\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 505,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 503,\n        columnNumber: 11\n      }, this), currentView === 'input' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"title\",\n          children: t('appTitle')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"subtitle\",\n          children: \"Enter any topic to generate an interactive knowledge tree with AI-powered content.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 515,\n          columnNumber: 13\n        }, this), !user ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#f1f5f9',\n            padding: '1rem',\n            borderRadius: '0.5rem',\n            marginBottom: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#334155',\n              marginBottom: '1rem'\n            },\n            children: t('loginRequired')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: quickLogin,\n            className: \"btn btn-primary\",\n            children: t('quickLoginDev')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 524,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 520,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: topic,\n              onChange: e => setTopic(e.target.value),\n              placeholder: t('topicPlaceholder'),\n              className: \"form-input\",\n              disabled: isLoading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 531,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: isLoading || !topic.trim(),\n            className: \"btn btn-primary\",\n            children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 547,\n                columnNumber: 23\n              }, this), t('generating')]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: t('exploreKnowledge')\n            }, void 0, false)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 529,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 513,\n        columnNumber: 11\n      }, this), currentView === 'tree' && tree && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tree-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tree-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: tree.tema\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: t('selectBranch')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 566,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goBack,\n            className: \"btn btn-secondary\",\n            style: {\n              marginTop: '1rem'\n            },\n            children: t('backToTree')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 567,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 564,\n          columnNumber: 13\n        }, this), isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 574,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: t('loading')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 573,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"branches-grid\",\n          children: tree.ramuri.map((branch, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `branch-item ${selectedBranch === branch ? 'selected' : ''}`,\n            \"data-index\": index,\n            \"data-name\": branch.nume,\n            \"data-description\": branch.descriere,\n            \"data-is-sub-branch\": branch.isSubBranch || false,\n            \"data-level\": branch.level || 0,\n            onClick: () => handleBranchSelect(branch),\n            onDoubleClick: () => generateArticleForBranch(branch),\n            onTouchStart: e => {\n              const touch = e.touches[0];\n              e.currentTarget.touchStartTime = Date.now();\n              e.currentTarget.touchStartX = touch.clientX;\n              e.currentTarget.touchStartY = touch.clientY;\n            },\n            onTouchEnd: e => {\n              const touch = e.changedTouches[0];\n              const deltaTime = Date.now() - (e.currentTarget.touchStartTime || 0);\n              const deltaX = Math.abs(touch.clientX - (e.currentTarget.touchStartX || 0));\n              const deltaY = Math.abs(touch.clientY - (e.currentTarget.touchStartY || 0));\n\n              // Long press detection (500ms+, minimal movement)\n              if (deltaTime > 500 && deltaX < 10 && deltaY < 10) {\n                e.preventDefault();\n                expandBranch(branch, index);\n              }\n              // Double tap detection\n              else if (deltaTime < 300 && deltaX < 10 && deltaY < 10) {\n                const now = Date.now();\n                const lastTap = e.currentTarget.lastTapTime || 0;\n                if (now - lastTap < 300) {\n                  e.preventDefault();\n                  generateArticleForBranch(branch);\n                }\n                e.currentTarget.lastTapTime = now;\n              }\n            },\n            children: [(branch.level || 0) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"branch-level-indicator\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"level-dots\",\n                children: Array.from({\n                  length: branch.level || 0\n                }, (_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"level-dot\"\n                }, i, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 624,\n                  columnNumber: 29\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 622,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"level-text\",\n                children: [\"Nivel \", branch.level]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 627,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 621,\n              columnNumber: 23\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"branch-emoji\",\n              children: branch.emoji\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 631,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"branch-name\",\n              children: branch.nume\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 632,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"branch-description\",\n              children: branch.descriere\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 21\n            }, this), branch.subcategorii && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"branch-subcategories\",\n              children: [branch.subcategorii.slice(0, 3).map((sub, subIndex) => /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"subcategory-tag\",\n                children: sub\n              }, subIndex, false, {\n                fileName: _jsxFileName,\n                lineNumber: 638,\n                columnNumber: 27\n              }, this)), branch.subcategorii.length > 3 && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"subcategory-tag more\",\n                children: [\"+\", branch.subcategorii.length - 3]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 643,\n                columnNumber: 27\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 636,\n              columnNumber: 23\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"gesture-hint\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"action-hint\",\n                children: \"\\uD83D\\uDCD6 Double-tap pentru articol\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 651,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"action-hint\",\n                children: \"\\uD83C\\uDF3F Long-press pentru expansiune\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 650,\n              columnNumber: 21\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 580,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 563,\n        columnNumber: 11\n      }, this), currentView === 'article' && article && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tree-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"article-header\",\n            style: {\n              marginBottom: '2rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: goBack,\n              className: \"btn btn-secondary\",\n              children: t('backToTree')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 666,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"article-controls\",\n              style: {\n                display: 'flex',\n                gap: '8px',\n                marginTop: '1rem',\n                flexWrap: 'wrap'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"speech-controls-compact\",\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '8px',\n                  padding: '8px 12px',\n                  background: '#f1f5f9',\n                  borderRadius: '6px',\n                  border: '1px solid #e2e8f0'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleSpeechToggle,\n                  className: \"btn-icon\",\n                  title: \"Play/Pause Speech\",\n                  style: {\n                    background: 'none',\n                    border: 'none',\n                    fontSize: '16px',\n                    cursor: 'pointer',\n                    padding: '4px'\n                  },\n                  children: speechService.getStatus().isPlaying ? '⏸️' : '▶️'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 687,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleSpeechStop,\n                  className: \"btn-icon\",\n                  title: \"Stop Speech\",\n                  style: {\n                    background: 'none',\n                    border: 'none',\n                    fontSize: '16px',\n                    cursor: 'pointer',\n                    padding: '4px'\n                  },\n                  children: \"\\u23F9\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 701,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"range\",\n                  min: \"0.5\",\n                  max: \"2\",\n                  step: \"0.1\",\n                  defaultValue: \"1\",\n                  onChange: e => handleSpeechRateChange(parseFloat(e.target.value)),\n                  style: {\n                    width: '60px'\n                  },\n                  title: \"Speech Speed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 715,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '12px',\n                    color: '#64748b'\n                  },\n                  children: \"\\uD83D\\uDDE3\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 725,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 678,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"export-controls-compact\",\n                style: {\n                  display: 'flex',\n                  gap: '4px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleCopyToClipboard,\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '6px 12px',\n                    fontSize: '12px'\n                  },\n                  title: \"Copy to Clipboard\",\n                  children: \"\\uD83D\\uDCCB Copy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 733,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleExportPDF,\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '6px 12px',\n                    fontSize: '12px'\n                  },\n                  title: \"Export as PDF\",\n                  children: \"\\uD83D\\uDCC4 PDF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 741,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleExportWord,\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '6px 12px',\n                    fontSize: '12px'\n                  },\n                  title: \"Export as Word\",\n                  children: \"\\uD83D\\uDCDD Word\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 749,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 729,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 671,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 665,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"title\",\n            children: (article === null || article === void 0 ? void 0 : article.title) || 'Loading...'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 761,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#475569',\n              marginBottom: '2rem',\n              fontSize: '0.9rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: [t('partOf'), \": \", (article === null || article === void 0 ? void 0 : article.topic) || 'Unknown']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 763,\n              columnNumber: 17\n            }, this), (article === null || article === void 0 ? void 0 : article.flags) && article.flags.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                marginLeft: '16px'\n              },\n              children: [t('flags'), \": \", article.flags.join(', ')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 765,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 762,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"article-content\",\n            style: {\n              lineHeight: '1.8',\n              fontSize: '1.1rem'\n            },\n            children: article !== null && article !== void 0 && article.content ? article.content.split('\\n').map((paragraph, index) => paragraph.trim() && /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                marginBottom: '1rem'\n              },\n              children: paragraph\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 774,\n              columnNumber: 21\n            }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Loading article content...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 779,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 771,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 664,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 663,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 501,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 466,\n    columnNumber: 5\n  }, this);\n};\n_s(OptimizedApp, \"B+LB35z6DkvdLV7SxB4V8E3FRsg=\", false, function () {\n  return [useTranslation];\n});\n_c = OptimizedApp;\nexport default OptimizedApp;\nvar _c;\n$RefreshReg$(_c, \"OptimizedApp\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "gestureService", "createFlagWheel", "speechService", "exportService", "gamificationService", "generateKnowledgeTree", "generateTreeAPI", "generateArticle", "generateArticleAPI", "testConnection", "tabService", "TabManager", "LanguageSwitcher", "useTranslation", "getCurrentLanguage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "OptimizedApp", "_s", "current<PERSON>iew", "set<PERSON><PERSON><PERSON>View", "topic", "setTopic", "activeTab", "setActiveTab", "isLoading", "setIsLoading", "error", "setError", "user", "setUser", "appRef", "tree", "<PERSON><PERSON><PERSON><PERSON>", "article", "t", "availableFlags", "useMemo", "code", "name", "description", "generateArticleForBranch", "useCallback", "branch", "flags", "updateTabStatus", "id", "getTab", "console", "log", "nume", "articleData", "updatedTab", "result", "awardPoints", "newAchievements", "length", "for<PERSON>ach", "achievement", "showAchievementNotification", "handleBranchSelect", "status", "handleDoubleTap", "event", "targetInfo", "isBranchItem", "branchData", "<PERSON><PERSON>", "index", "position", "selected<PERSON><PERSON><PERSON>", "handleSingleTap", "expandBranch", "branchIndex", "expandedTree", "tema", "expandedData", "slice", "newTree", "map", "subBranch", "isSubBranch", "parentBranch", "level", "handleLongPress", "storedUser", "localStorage", "getItem", "bypassSecurity", "userData", "subscriptionTier", "current", "init", "doubleTap", "singleTap", "longPress", "destroy", "container", "document", "getElementById", "innerHTML", "createGamificationUI", "then", "isConnected", "warn", "catch", "topicInput", "tabId", "currentTabId", "newTab", "createTab", "message", "progress", "treeData", "err", "handleSubmit", "e", "preventDefault", "trim", "handleTabChange", "tab", "handleNewTab", "handleTabArticleAccess", "handleSpeechToggle", "content", "getStatus", "isPlaying", "toggle", "speak", "handleSpeechStop", "stop", "handleSpeechRateChange", "rate", "setRate", "handleExportPDF", "title", "exportAsPDF", "replace", "success", "gamResult", "handleExportWord", "exportAsWord", "handleCopyToClipboard", "copyToClipboard", "showMessage", "goBack", "goHome", "clearAllTabs", "quickLogin", "setItem", "className", "ref", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginRight", "marginLeft", "onTabChange", "onNewTab", "onTabArticleAccess", "background", "border", "color", "cursor", "padding", "borderRadius", "marginBottom", "onSubmit", "type", "value", "onChange", "target", "placeholder", "disabled", "marginTop", "desc<PERSON><PERSON>", "onDoubleClick", "onTouchStart", "touch", "touches", "currentTarget", "touchStartTime", "Date", "now", "touchStartX", "clientX", "touchStartY", "clientY", "onTouchEnd", "changedTouches", "deltaTime", "deltaX", "Math", "abs", "deltaY", "lastTap", "lastTapTime", "Array", "from", "_", "i", "emoji", "subcategorii", "sub", "subIndex", "display", "gap", "flexWrap", "alignItems", "fontSize", "min", "max", "step", "defaultValue", "parseFloat", "width", "join", "lineHeight", "split", "paragraph", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Appv1/src/components/OptimizedApp.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport '../styles/optimized.css';\nimport gestureService, { createFlagWheel } from '../services/gestureService';\nimport speechService from '../services/speechService';\nimport exportService from '../services/exportService';\nimport gamificationService from '../services/optimizedGamificationService';\nimport { generateKnowledgeTree as generateTreeAPI, generateArticle as generateArticleAPI, testConnection } from '../services/openRouterService';\nimport tabService from '../services/tabService';\nimport TabManager from './TabManager';\nimport LanguageSwitcher from './LanguageSwitcher';\nimport { useTranslation, getCurrentLanguage } from '../utils/i18n';\n\n// Optimized Knowledge Tree Explorer - Pareto 80/20 Implementation\n// Focus on core functionality with maximum impact\n\nconst OptimizedApp = () => {\n  // Core state - now managed by tabs\n  const [currentView, setCurrentView] = useState('input'); // 'input', 'tree', 'article'\n  const [topic, setTopic] = useState('');\n  const [activeTab, setActiveTab] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [user, setUser] = useState(null);\n  const appRef = useRef(null);\n\n  // Get current tab data\n  const tree = activeTab?.tree || null;\n  const selectedBranch = activeTab?.selectedBranch || null;\n  const article = activeTab?.article || null;\n\n  // Translation hook\n  const { t } = useTranslation();\n\n  // Available flags for the optimized version\n  const availableFlags = React.useMemo(() => [\n    { code: '-a', name: 'Article', description: t('flagArticle') },\n    { code: '-ex', name: 'Examples', description: t('flagExamples') },\n    { code: '-q', name: 'Quiz', description: t('flagQuiz') },\n    { code: '-vis', name: 'Visual', description: t('flagVisual') },\n    { code: '-path', name: 'Learning Path', description: t('flagPath') },\n    { code: '-case', name: 'Case Study', description: t('flagCase') },\n    { code: '-ro', name: 'Romanian', description: t('flagRomanian') }\n  ], [t]);\n\n  // Generate article with tabs support\n  const generateArticleForBranch = React.useCallback(async (branch, flags = ['-a']) => {\n    if (!activeTab) return;\n\n    setIsLoading(true);\n\n    // Set tab to loading state (yellow)\n    tabService.updateTabStatus(activeTab.id, 'loading', {\n      selectedBranch: branch,\n      article: null\n    });\n    setActiveTab(tabService.getTab(activeTab.id));\n\n    try {\n      console.log('📄 Generating article for branch:', branch.nume);\n      const articleData = await generateArticleAPI(activeTab.topic, branch, flags);\n\n      // Update tab with article and set to completed (green)\n      tabService.updateTabStatus(activeTab.id, 'completed', {\n        selectedBranch: branch,\n        article: articleData\n      });\n\n      const updatedTab = tabService.getTab(activeTab.id);\n      setActiveTab(updatedTab);\n      setCurrentView('article');\n\n      // Award points for article generation\n      const result = gamificationService.awardPoints('ARTICLE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (error) {\n      console.error('❌ Error generating article:', error);\n      setError('Failed to generate article. Please try again.');\n\n      // Set tab back to pending on error\n      tabService.updateTabStatus(activeTab.id, 'pending', {\n        selectedBranch: branch,\n        article: null\n      });\n      setActiveTab(tabService.getTab(activeTab.id));\n    } finally {\n      setIsLoading(false);\n    }\n  }, [activeTab]);\n\n  // Handle branch selection (single tap)\n  const handleBranchSelect = React.useCallback((branch) => {\n    if (activeTab) {\n      tabService.updateTabStatus(activeTab.id, activeTab.status, { selectedBranch: branch });\n      setActiveTab(tabService.getTab(activeTab.id));\n    }\n  }, [activeTab]);\n\n  // Gesture handlers\n  const handleDoubleTap = React.useCallback((event, targetInfo) => {\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      // Show flag wheel on double tap of branch\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        createFlagWheel(\n          targetInfo.position,\n          availableFlags,\n          (selectedFlags) => {\n            console.log('Selected flags:', selectedFlags);\n          },\n          (selectedFlags) => {\n            generateArticleForBranch(branch, selectedFlags);\n          }\n        );\n      }\n    }\n  }, [tree, availableFlags, generateArticleForBranch]);\n\n  const handleSingleTap = React.useCallback((event, targetInfo) => {\n    // Single tap for normal selection\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        handleBranchSelect(branch);\n      }\n    }\n  }, [tree, handleBranchSelect]);\n\n  // Expand branch to create sub-branches (tree effect)\n  const expandBranch = React.useCallback(async (branch, branchIndex) => {\n    if (!activeTab || !tree) {\n      setError(t('noActiveTab') || 'No active tab or tree available');\n      return;\n    }\n\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      console.log('🌿 Expanding branch:', branch.nume);\n\n      // Create a mini knowledge tree for the branch expansion\n      const expandedTree = await generateTreeAPI(`${branch.nume} - ${tree.tema}`, getCurrentLanguage());\n\n      console.log('✅ Generated expanded tree:', expandedTree);\n\n      // Extract the branches from the expanded tree\n      const expandedData = {\n        ramuri: expandedTree.ramuri.slice(0, 4) // Take first 4 branches as sub-branches\n      };\n\n      // Update tree with expanded branches\n      const newTree = { ...tree };\n      newTree.ramuri = [\n        ...newTree.ramuri.slice(0, branchIndex + 1),\n        ...expandedData.ramuri.map(subBranch => ({\n          ...subBranch,\n          isSubBranch: true,\n          parentBranch: branch.nume,\n          level: (branch.level || 0) + 1\n        })),\n        ...newTree.ramuri.slice(branchIndex + 1)\n      ];\n\n      // Update tab with expanded tree\n      tabService.updateTabStatus(activeTab.id, 'completed', { tree: newTree });\n      setActiveTab(tabService.getTab(activeTab.id));\n\n      console.log('🌳 Tree expanded successfully');\n\n      // Award points for branch expansion\n      const result = gamificationService.awardPoints('BRANCH_EXPANDED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n\n    } catch (error) {\n      console.error('❌ Error expanding branch:', error);\n      setError(t('failedToExpand') || 'Nu s-a putut extinde ramura. Te rugăm să încerci din nou.');\n    } finally {\n      setIsLoading(false);\n    }\n  }, [tree, activeTab, t]);\n\n  const handleLongPress = React.useCallback(async (event, targetInfo) => {\n    // Long press to expand branch and create tree effect\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        await expandBranch(branch, targetInfo.branchData.index);\n      }\n    }\n  }, [tree, expandBranch]);\n\n  // Initialize services and authentication\n  useEffect(() => {\n    const storedUser = localStorage.getItem('user');\n    const bypassSecurity = localStorage.getItem('bypassSecurity');\n\n    if (storedUser || bypassSecurity) {\n      const userData = {\n        id: 'user-1',\n        name: 'User',\n        subscriptionTier: 'premium'\n      };\n      setUser(userData);\n\n      // Award daily login points\n      const result = gamificationService.awardPoints('DAILY_LOGIN');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n\n    // Initialize gesture service\n    if (appRef.current) {\n      gestureService.init(appRef.current, {\n        doubleTap: handleDoubleTap,\n        singleTap: handleSingleTap,\n        longPress: handleLongPress\n      });\n    }\n\n    return () => {\n      gestureService.destroy();\n    };\n  }, [handleDoubleTap, handleSingleTap, handleLongPress]);\n\n  // Initialize gamification UI when user is logged in\n  useEffect(() => {\n    if (user) {\n      const container = document.getElementById('gamification-container');\n      if (container) {\n        // Clear existing content\n        container.innerHTML = '';\n        // Create gamification UI\n        gamificationService.createGamificationUI(container);\n      }\n\n      // Test API connection\n      testConnection().then(isConnected => {\n        console.log('🔌 API Connection Status:', isConnected ? '✅ Connected' : '❌ Failed');\n        if (!isConnected) {\n          console.warn('⚠️ API connection failed. Check your API key and internet connection.');\n        }\n      }).catch(error => {\n        console.error('❌ API connection test error:', error);\n      });\n    }\n  }, [user]);\n\n  // Core API call - using optimized service with tabs\n  const generateKnowledgeTree = async (topicInput, tabId = null) => {\n    let currentTabId = tabId;\n\n    // Create new tab if none provided\n    if (!currentTabId) {\n      try {\n        const newTab = tabService.createTab(topicInput);\n        currentTabId = newTab.id;\n        setActiveTab(newTab);\n        setCurrentView('tree');\n      } catch (error) {\n        setError(error.message);\n        return;\n      }\n    }\n\n    // Update tab status to generating\n    tabService.updateTabStatus(currentTabId, 'generating', { progress: 10 });\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      console.log('🌳 Generating knowledge tree for:', topicInput, 'in tab:', currentTabId);\n\n      // Update progress\n      tabService.updateTabStatus(currentTabId, 'generating', { progress: 30 });\n\n      const treeData = await generateTreeAPI(topicInput, getCurrentLanguage());\n      console.log('✅ Generated tree data:', treeData);\n\n      // Update tab with completed tree\n      tabService.updateTabStatus(currentTabId, 'completed', {\n        tree: treeData,\n        progress: 100\n      });\n\n      // Update active tab if this is the current one\n      if (currentTabId === activeTab?.id) {\n        setActiveTab(tabService.getTab(currentTabId));\n      }\n\n      // Award points for tree generation\n      const result = gamificationService.awardPoints('TREE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (err) {\n      console.error('❌ Error generating tree:', err);\n      tabService.updateTabStatus(currentTabId, 'error');\n      setError(`Failed to generate knowledge tree: ${err.message}. Please try again.`);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n\n\n  // Handle form submission\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    if (topic.trim()) {\n      generateKnowledgeTree(topic.trim());\n      setTopic(''); // Clear input for next topic\n    }\n  };\n\n  // Handle tab changes\n  const handleTabChange = (tab) => {\n    // Clear any existing errors when switching tabs\n    setError(null);\n    setIsLoading(false);\n\n    setActiveTab(tab);\n    if (tab?.tree) {\n      setCurrentView('tree');\n    } else {\n      setCurrentView('input');\n    }\n  };\n\n  // Handle new tab creation\n  const handleNewTab = () => {\n    // Clear any existing errors and loading states\n    setError(null);\n    setIsLoading(false);\n\n    setCurrentView('input');\n    setActiveTab(null);\n    setTopic('');\n  };\n\n  const handleTabArticleAccess = (tab) => {\n    // Clear any existing errors when accessing article\n    setError(null);\n    setIsLoading(false);\n\n    setActiveTab(tab);\n    setCurrentView('article');\n  };\n\n\n\n  // Speech functions\n  const handleSpeechToggle = () => {\n    if (!article?.content) return;\n\n    if (speechService.getStatus().isPlaying) {\n      speechService.toggle();\n    } else {\n      speechService.speak(article.content);\n      // Award points for using speech\n      const result = gamificationService.awardPoints('SPEECH_USED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleSpeechStop = () => {\n    speechService.stop();\n  };\n\n  const handleSpeechRateChange = (rate) => {\n    speechService.setRate(rate);\n  };\n\n  // Export functions\n  const handleExportPDF = () => {\n    if (!article?.title || !article?.content) return;\n    const result = exportService.exportAsPDF(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleExportWord = () => {\n    if (!article?.title || !article?.content) return;\n    const result = exportService.exportAsWord(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleCopyToClipboard = async () => {\n    if (!article?.content) return;\n    const result = await exportService.copyToClipboard(article.content);\n    exportService.showMessage(result.message, result.success ? 'success' : 'error');\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n\n\n  // Navigation functions\n  const goBack = () => {\n    if (currentView === 'article') {\n      setCurrentView('tree');\n      if (activeTab) {\n        tabService.updateTabStatus(activeTab.id, activeTab.status, { article: null });\n        setActiveTab(tabService.getTab(activeTab.id));\n      }\n    } else if (currentView === 'tree') {\n      setCurrentView('input');\n    }\n  };\n\n\n\n  const goHome = () => {\n    setCurrentView('input');\n    setActiveTab(null);\n    setTopic('');\n    // Clear all tabs\n    tabService.clearAllTabs();\n  };\n\n  // Quick login for development\n  const quickLogin = () => {\n    localStorage.setItem('bypassSecurity', 'true');\n    setUser({ id: 'dev-1', name: 'Developer', subscriptionTier: 'premium' });\n  };\n\n  return (\n    <div className=\"app\" ref={appRef}>\n      {/* Header */}\n      <header className=\"app-header\">\n        <div className=\"header-content\">\n          <button onClick={goHome} className=\"logo-text\">\n            {t('appTitle')}\n          </button>\n          <div className=\"header-right\">\n            {user && (\n              <div id=\"gamification-container\" style={{ marginRight: '16px' }}>\n                {/* Gamification UI will be inserted here */}\n              </div>\n            )}\n            <LanguageSwitcher />\n            {!user ? (\n              <button onClick={quickLogin} className=\"btn btn-primary\" style={{ marginLeft: '12px' }}>\n                {t('quickLogin')}\n              </button>\n            ) : (\n              <span style={{ marginLeft: '12px' }}>{t('welcome')}, {user.name}!</span>\n            )}\n          </div>\n        </div>\n      </header>\n\n      {/* Tab Manager */}\n      {user && (\n        <TabManager\n          onTabChange={handleTabChange}\n          onNewTab={handleNewTab}\n          onTabArticleAccess={handleTabArticleAccess}\n        />\n      )}\n\n      {/* Main Content */}\n      <main className=\"main-content\">\n        {error && (\n          <div className=\"error\">\n            ⚠️ {error}\n            <button onClick={() => setError(null)} style={{marginLeft: 'auto', background: 'none', border: 'none', color: 'white', cursor: 'pointer'}}>\n              ✕\n            </button>\n          </div>\n        )}\n\n        {/* Topic Input View */}\n        {currentView === 'input' && (\n          <div className=\"card text-center\">\n            <h1 className=\"title\">{t('appTitle')}</h1>\n            <p className=\"subtitle\">\n              Enter any topic to generate an interactive knowledge tree with AI-powered content.\n            </p>\n\n            {!user ? (\n              <div style={{background: '#f1f5f9', padding: '1rem', borderRadius: '0.5rem', marginBottom: '2rem'}}>\n                <p style={{color: '#334155', marginBottom: '1rem'}}>\n                  {t('loginRequired')}\n                </p>\n                <button onClick={quickLogin} className=\"btn btn-primary\">\n                  {t('quickLoginDev')}\n                </button>\n              </div>\n            ) : (\n              <form onSubmit={handleSubmit}>\n                <div className=\"form-group\">\n                  <input\n                    type=\"text\"\n                    value={topic}\n                    onChange={(e) => setTopic(e.target.value)}\n                    placeholder={t('topicPlaceholder')}\n                    className=\"form-input\"\n                    disabled={isLoading}\n                  />\n                </div>\n                <button\n                  type=\"submit\"\n                  disabled={isLoading || !topic.trim()}\n                  className=\"btn btn-primary\"\n                >\n                  {isLoading ? (\n                    <>\n                      <span className=\"spinner\"></span>\n                      {t('generating')}\n                    </>\n                  ) : (\n                    <>\n                      {t('exploreKnowledge')}\n                    </>\n                  )}\n                </button>\n              </form>\n            )}\n          </div>\n        )}\n\n        {/* Tree View */}\n        {currentView === 'tree' && tree && (\n          <div className=\"tree-container\">\n            <div className=\"tree-header\">\n              <h1>{tree.tema}</h1>\n              <p>{t('selectBranch')}</p>\n              <button onClick={goBack} className=\"btn btn-secondary\" style={{marginTop: '1rem'}}>\n                {t('backToTree')}\n              </button>\n            </div>\n\n            {isLoading ? (\n              <div className=\"loading\">\n                <span className=\"spinner\"></span>\n                <span>{t('loading')}</span>\n              </div>\n            ) : (\n              <div className=\"branches-grid\">\n                {tree.ramuri.map((branch, index) => (\n                  <div\n                    key={index}\n                    className={`branch-item ${selectedBranch === branch ? 'selected' : ''}`}\n                    data-index={index}\n                    data-name={branch.nume}\n                    data-description={branch.descriere}\n                    data-is-sub-branch={branch.isSubBranch || false}\n                    data-level={branch.level || 0}\n                    onClick={() => handleBranchSelect(branch)}\n                    onDoubleClick={() => generateArticleForBranch(branch)}\n                    onTouchStart={(e) => {\n                      const touch = e.touches[0];\n                      e.currentTarget.touchStartTime = Date.now();\n                      e.currentTarget.touchStartX = touch.clientX;\n                      e.currentTarget.touchStartY = touch.clientY;\n                    }}\n                    onTouchEnd={(e) => {\n                      const touch = e.changedTouches[0];\n                      const deltaTime = Date.now() - (e.currentTarget.touchStartTime || 0);\n                      const deltaX = Math.abs(touch.clientX - (e.currentTarget.touchStartX || 0));\n                      const deltaY = Math.abs(touch.clientY - (e.currentTarget.touchStartY || 0));\n\n                      // Long press detection (500ms+, minimal movement)\n                      if (deltaTime > 500 && deltaX < 10 && deltaY < 10) {\n                        e.preventDefault();\n                        expandBranch(branch, index);\n                      }\n                      // Double tap detection\n                      else if (deltaTime < 300 && deltaX < 10 && deltaY < 10) {\n                        const now = Date.now();\n                        const lastTap = e.currentTarget.lastTapTime || 0;\n                        if (now - lastTap < 300) {\n                          e.preventDefault();\n                          generateArticleForBranch(branch);\n                        }\n                        e.currentTarget.lastTapTime = now;\n                      }\n                    }}\n                  >\n                    {/* Branch Level Indicator */}\n                    {(branch.level || 0) > 0 && (\n                      <div className=\"branch-level-indicator\">\n                        <div className=\"level-dots\">\n                          {Array.from({ length: branch.level || 0 }, (_, i) => (\n                            <div key={i} className=\"level-dot\"></div>\n                          ))}\n                        </div>\n                        <div className=\"level-text\">Nivel {branch.level}</div>\n                      </div>\n                    )}\n\n                    <div className=\"branch-emoji\">{branch.emoji}</div>\n                    <h3 className=\"branch-name\">{branch.nume}</h3>\n                    <p className=\"branch-description\">{branch.descriere}</p>\n\n                    {branch.subcategorii && (\n                      <div className=\"branch-subcategories\">\n                        {branch.subcategorii.slice(0, 3).map((sub, subIndex) => (\n                          <span key={subIndex} className=\"subcategory-tag\">\n                            {sub}\n                          </span>\n                        ))}\n                        {branch.subcategorii.length > 3 && (\n                          <span className=\"subcategory-tag more\">\n                            +{branch.subcategorii.length - 3}\n                          </span>\n                        )}\n                      </div>\n                    )}\n\n                    <div className=\"gesture-hint\">\n                      <span className=\"action-hint\">📖 Double-tap pentru articol</span>\n                      <span className=\"action-hint\">🌿 Long-press pentru expansiune</span>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Article View */}\n        {currentView === 'article' && article && (\n          <div className=\"tree-container\">\n            <div className=\"card\">\n              <div className=\"article-header\" style={{marginBottom: '2rem'}}>\n                <button onClick={goBack} className=\"btn btn-secondary\">\n                  {t('backToTree')}\n                </button>\n\n                {/* Article Controls */}\n                <div className=\"article-controls\" style={{\n                  display: 'flex',\n                  gap: '8px',\n                  marginTop: '1rem',\n                  flexWrap: 'wrap'\n                }}>\n                  {/* Speech Controls */}\n                  <div className=\"speech-controls-compact\" style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '8px',\n                    padding: '8px 12px',\n                    background: '#f1f5f9',\n                    borderRadius: '6px',\n                    border: '1px solid #e2e8f0'\n                  }}>\n                    <button\n                      onClick={handleSpeechToggle}\n                      className=\"btn-icon\"\n                      title=\"Play/Pause Speech\"\n                      style={{\n                        background: 'none',\n                        border: 'none',\n                        fontSize: '16px',\n                        cursor: 'pointer',\n                        padding: '4px'\n                      }}\n                    >\n                      {speechService.getStatus().isPlaying ? '⏸️' : '▶️'}\n                    </button>\n                    <button\n                      onClick={handleSpeechStop}\n                      className=\"btn-icon\"\n                      title=\"Stop Speech\"\n                      style={{\n                        background: 'none',\n                        border: 'none',\n                        fontSize: '16px',\n                        cursor: 'pointer',\n                        padding: '4px'\n                      }}\n                    >\n                      ⏹️\n                    </button>\n                    <input\n                      type=\"range\"\n                      min=\"0.5\"\n                      max=\"2\"\n                      step=\"0.1\"\n                      defaultValue=\"1\"\n                      onChange={(e) => handleSpeechRateChange(parseFloat(e.target.value))}\n                      style={{width: '60px'}}\n                      title=\"Speech Speed\"\n                    />\n                    <span style={{fontSize: '12px', color: '#64748b'}}>🗣️</span>\n                  </div>\n\n                  {/* Export Controls */}\n                  <div className=\"export-controls-compact\" style={{\n                    display: 'flex',\n                    gap: '4px'\n                  }}>\n                    <button\n                      onClick={handleCopyToClipboard}\n                      className=\"btn btn-secondary\"\n                      style={{padding: '6px 12px', fontSize: '12px'}}\n                      title=\"Copy to Clipboard\"\n                    >\n                      📋 Copy\n                    </button>\n                    <button\n                      onClick={handleExportPDF}\n                      className=\"btn btn-secondary\"\n                      style={{padding: '6px 12px', fontSize: '12px'}}\n                      title=\"Export as PDF\"\n                    >\n                      📄 PDF\n                    </button>\n                    <button\n                      onClick={handleExportWord}\n                      className=\"btn btn-secondary\"\n                      style={{padding: '6px 12px', fontSize: '12px'}}\n                      title=\"Export as Word\"\n                    >\n                      📝 Word\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              <h1 className=\"title\">{article?.title || 'Loading...'}</h1>\n              <div style={{color: '#475569', marginBottom: '2rem', fontSize: '0.9rem'}}>\n                <span>{t('partOf')}: {article?.topic || 'Unknown'}</span>\n                {article?.flags && article.flags.length > 0 && (\n                  <span style={{marginLeft: '16px'}}>\n                    {t('flags')}: {article.flags.join(', ')}\n                  </span>\n                )}\n              </div>\n\n              <div className=\"article-content\" style={{lineHeight: '1.8', fontSize: '1.1rem'}}>\n                {article?.content ? article.content.split('\\n').map((paragraph, index) => (\n                  paragraph.trim() && (\n                    <p key={index} style={{marginBottom: '1rem'}}>\n                      {paragraph}\n                    </p>\n                  )\n                )) : (\n                  <p>Loading article content...</p>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n      </main>\n    </div>\n  );\n};\n\nexport default OptimizedApp;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,yBAAyB;AAChC,OAAOC,cAAc,IAAIC,eAAe,QAAQ,4BAA4B;AAC5E,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,mBAAmB,MAAM,0CAA0C;AAC1E,SAASC,qBAAqB,IAAIC,eAAe,EAAEC,eAAe,IAAIC,kBAAkB,EAAEC,cAAc,QAAQ,+BAA+B;AAC/I,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,cAAc,EAAEC,kBAAkB,QAAQ,eAAe;;AAElE;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEA,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACzD,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgC,KAAK,EAAEC,QAAQ,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACkC,IAAI,EAAEC,OAAO,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAMoC,MAAM,GAAGlC,MAAM,CAAC,IAAI,CAAC;;EAE3B;EACA,MAAMmC,IAAI,GAAG,CAAAT,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAES,IAAI,KAAI,IAAI;EACpC,MAAMC,cAAc,GAAG,CAAAV,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEU,cAAc,KAAI,IAAI;EACxD,MAAMC,OAAO,GAAG,CAAAX,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEW,OAAO,KAAI,IAAI;;EAE1C;EACA,MAAM;IAAEC;EAAE,CAAC,GAAGxB,cAAc,CAAC,CAAC;;EAE9B;EACA,MAAMyB,cAAc,GAAG1C,KAAK,CAAC2C,OAAO,CAAC,MAAM,CACzC;IAAEC,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,SAAS;IAAEC,WAAW,EAAEL,CAAC,CAAC,aAAa;EAAE,CAAC,EAC9D;IAAEG,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAE,UAAU;IAAEC,WAAW,EAAEL,CAAC,CAAC,cAAc;EAAE,CAAC,EACjE;IAAEG,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,MAAM;IAAEC,WAAW,EAAEL,CAAC,CAAC,UAAU;EAAE,CAAC,EACxD;IAAEG,IAAI,EAAE,MAAM;IAAEC,IAAI,EAAE,QAAQ;IAAEC,WAAW,EAAEL,CAAC,CAAC,YAAY;EAAE,CAAC,EAC9D;IAAEG,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,eAAe;IAAEC,WAAW,EAAEL,CAAC,CAAC,UAAU;EAAE,CAAC,EACpE;IAAEG,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,YAAY;IAAEC,WAAW,EAAEL,CAAC,CAAC,UAAU;EAAE,CAAC,EACjE;IAAEG,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAE,UAAU;IAAEC,WAAW,EAAEL,CAAC,CAAC,cAAc;EAAE,CAAC,CAClE,EAAE,CAACA,CAAC,CAAC,CAAC;;EAEP;EACA,MAAMM,wBAAwB,GAAG/C,KAAK,CAACgD,WAAW,CAAC,OAAOC,MAAM,EAAEC,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK;IACnF,IAAI,CAACrB,SAAS,EAAE;IAEhBG,YAAY,CAAC,IAAI,CAAC;;IAElB;IACAlB,UAAU,CAACqC,eAAe,CAACtB,SAAS,CAACuB,EAAE,EAAE,SAAS,EAAE;MAClDb,cAAc,EAAEU,MAAM;MACtBT,OAAO,EAAE;IACX,CAAC,CAAC;IACFV,YAAY,CAAChB,UAAU,CAACuC,MAAM,CAACxB,SAAS,CAACuB,EAAE,CAAC,CAAC;IAE7C,IAAI;MACFE,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEN,MAAM,CAACO,IAAI,CAAC;MAC7D,MAAMC,WAAW,GAAG,MAAM7C,kBAAkB,CAACiB,SAAS,CAACF,KAAK,EAAEsB,MAAM,EAAEC,KAAK,CAAC;;MAE5E;MACApC,UAAU,CAACqC,eAAe,CAACtB,SAAS,CAACuB,EAAE,EAAE,WAAW,EAAE;QACpDb,cAAc,EAAEU,MAAM;QACtBT,OAAO,EAAEiB;MACX,CAAC,CAAC;MAEF,MAAMC,UAAU,GAAG5C,UAAU,CAACuC,MAAM,CAACxB,SAAS,CAACuB,EAAE,CAAC;MAClDtB,YAAY,CAAC4B,UAAU,CAAC;MACxBhC,cAAc,CAAC,SAAS,CAAC;;MAEzB;MACA,MAAMiC,MAAM,GAAGnD,mBAAmB,CAACoD,WAAW,CAAC,mBAAmB,CAAC;MACnE,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5CxD,mBAAmB,CAACyD,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAO/B,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDC,QAAQ,CAAC,+CAA+C,CAAC;;MAEzD;MACApB,UAAU,CAACqC,eAAe,CAACtB,SAAS,CAACuB,EAAE,EAAE,SAAS,EAAE;QAClDb,cAAc,EAAEU,MAAM;QACtBT,OAAO,EAAE;MACX,CAAC,CAAC;MACFV,YAAY,CAAChB,UAAU,CAACuC,MAAM,CAACxB,SAAS,CAACuB,EAAE,CAAC,CAAC;IAC/C,CAAC,SAAS;MACRpB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACH,SAAS,CAAC,CAAC;;EAEf;EACA,MAAMqC,kBAAkB,GAAGlE,KAAK,CAACgD,WAAW,CAAEC,MAAM,IAAK;IACvD,IAAIpB,SAAS,EAAE;MACbf,UAAU,CAACqC,eAAe,CAACtB,SAAS,CAACuB,EAAE,EAAEvB,SAAS,CAACsC,MAAM,EAAE;QAAE5B,cAAc,EAAEU;MAAO,CAAC,CAAC;MACtFnB,YAAY,CAAChB,UAAU,CAACuC,MAAM,CAACxB,SAAS,CAACuB,EAAE,CAAC,CAAC;IAC/C;EACF,CAAC,EAAE,CAACvB,SAAS,CAAC,CAAC;;EAEf;EACA,MAAMuC,eAAe,GAAGpE,KAAK,CAACgD,WAAW,CAAC,CAACqB,KAAK,EAAEC,UAAU,KAAK;IAC/D,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,IAAIlC,IAAI,EAAE;MAC5D;MACA,MAAMW,MAAM,GAAGX,IAAI,CAACmC,MAAM,CAACH,UAAU,CAACE,UAAU,CAACE,KAAK,CAAC;MACvD,IAAIzB,MAAM,EAAE;QACV5C,eAAe,CACbiE,UAAU,CAACK,QAAQ,EACnBjC,cAAc,EACbkC,aAAa,IAAK;UACjBtB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEqB,aAAa,CAAC;QAC/C,CAAC,EACAA,aAAa,IAAK;UACjB7B,wBAAwB,CAACE,MAAM,EAAE2B,aAAa,CAAC;QACjD,CACF,CAAC;MACH;IACF;EACF,CAAC,EAAE,CAACtC,IAAI,EAAEI,cAAc,EAAEK,wBAAwB,CAAC,CAAC;EAEpD,MAAM8B,eAAe,GAAG7E,KAAK,CAACgD,WAAW,CAAC,CAACqB,KAAK,EAAEC,UAAU,KAAK;IAC/D;IACA,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,IAAIlC,IAAI,EAAE;MAC5D,MAAMW,MAAM,GAAGX,IAAI,CAACmC,MAAM,CAACH,UAAU,CAACE,UAAU,CAACE,KAAK,CAAC;MACvD,IAAIzB,MAAM,EAAE;QACViB,kBAAkB,CAACjB,MAAM,CAAC;MAC5B;IACF;EACF,CAAC,EAAE,CAACX,IAAI,EAAE4B,kBAAkB,CAAC,CAAC;;EAE9B;EACA,MAAMY,YAAY,GAAG9E,KAAK,CAACgD,WAAW,CAAC,OAAOC,MAAM,EAAE8B,WAAW,KAAK;IACpE,IAAI,CAAClD,SAAS,IAAI,CAACS,IAAI,EAAE;MACvBJ,QAAQ,CAACO,CAAC,CAAC,aAAa,CAAC,IAAI,iCAAiC,CAAC;MAC/D;IACF;IAEAT,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACFoB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEN,MAAM,CAACO,IAAI,CAAC;;MAEhD;MACA,MAAMwB,YAAY,GAAG,MAAMtE,eAAe,CAAC,GAAGuC,MAAM,CAACO,IAAI,MAAMlB,IAAI,CAAC2C,IAAI,EAAE,EAAE/D,kBAAkB,CAAC,CAAC,CAAC;MAEjGoC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEyB,YAAY,CAAC;;MAEvD;MACA,MAAME,YAAY,GAAG;QACnBT,MAAM,EAAEO,YAAY,CAACP,MAAM,CAACU,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC1C,CAAC;;MAED;MACA,MAAMC,OAAO,GAAG;QAAE,GAAG9C;MAAK,CAAC;MAC3B8C,OAAO,CAACX,MAAM,GAAG,CACf,GAAGW,OAAO,CAACX,MAAM,CAACU,KAAK,CAAC,CAAC,EAAEJ,WAAW,GAAG,CAAC,CAAC,EAC3C,GAAGG,YAAY,CAACT,MAAM,CAACY,GAAG,CAACC,SAAS,KAAK;QACvC,GAAGA,SAAS;QACZC,WAAW,EAAE,IAAI;QACjBC,YAAY,EAAEvC,MAAM,CAACO,IAAI;QACzBiC,KAAK,EAAE,CAACxC,MAAM,CAACwC,KAAK,IAAI,CAAC,IAAI;MAC/B,CAAC,CAAC,CAAC,EACH,GAAGL,OAAO,CAACX,MAAM,CAACU,KAAK,CAACJ,WAAW,GAAG,CAAC,CAAC,CACzC;;MAED;MACAjE,UAAU,CAACqC,eAAe,CAACtB,SAAS,CAACuB,EAAE,EAAE,WAAW,EAAE;QAAEd,IAAI,EAAE8C;MAAQ,CAAC,CAAC;MACxEtD,YAAY,CAAChB,UAAU,CAACuC,MAAM,CAACxB,SAAS,CAACuB,EAAE,CAAC,CAAC;MAE7CE,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;;MAE5C;MACA,MAAMI,MAAM,GAAGnD,mBAAmB,CAACoD,WAAW,CAAC,iBAAiB,CAAC;MACjE,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5CxD,mBAAmB,CAACyD,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IAEF,CAAC,CAAC,OAAO/B,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDC,QAAQ,CAACO,CAAC,CAAC,gBAAgB,CAAC,IAAI,2DAA2D,CAAC;IAC9F,CAAC,SAAS;MACRT,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACM,IAAI,EAAET,SAAS,EAAEY,CAAC,CAAC,CAAC;EAExB,MAAMiD,eAAe,GAAG1F,KAAK,CAACgD,WAAW,CAAC,OAAOqB,KAAK,EAAEC,UAAU,KAAK;IACrE;IACA,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,IAAIlC,IAAI,EAAE;MAC5D,MAAMW,MAAM,GAAGX,IAAI,CAACmC,MAAM,CAACH,UAAU,CAACE,UAAU,CAACE,KAAK,CAAC;MACvD,IAAIzB,MAAM,EAAE;QACV,MAAM6B,YAAY,CAAC7B,MAAM,EAAEqB,UAAU,CAACE,UAAU,CAACE,KAAK,CAAC;MACzD;IACF;EACF,CAAC,EAAE,CAACpC,IAAI,EAAEwC,YAAY,CAAC,CAAC;;EAExB;EACA5E,SAAS,CAAC,MAAM;IACd,MAAMyF,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAC/C,MAAMC,cAAc,GAAGF,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAE7D,IAAIF,UAAU,IAAIG,cAAc,EAAE;MAChC,MAAMC,QAAQ,GAAG;QACf3C,EAAE,EAAE,QAAQ;QACZP,IAAI,EAAE,MAAM;QACZmD,gBAAgB,EAAE;MACpB,CAAC;MACD5D,OAAO,CAAC2D,QAAQ,CAAC;;MAEjB;MACA,MAAMpC,MAAM,GAAGnD,mBAAmB,CAACoD,WAAW,CAAC,aAAa,CAAC;MAC7D,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5CxD,mBAAmB,CAACyD,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;;IAEA;IACA,IAAI3B,MAAM,CAAC4D,OAAO,EAAE;MAClB7F,cAAc,CAAC8F,IAAI,CAAC7D,MAAM,CAAC4D,OAAO,EAAE;QAClCE,SAAS,EAAE/B,eAAe;QAC1BgC,SAAS,EAAEvB,eAAe;QAC1BwB,SAAS,EAAEX;MACb,CAAC,CAAC;IACJ;IAEA,OAAO,MAAM;MACXtF,cAAc,CAACkG,OAAO,CAAC,CAAC;IAC1B,CAAC;EACH,CAAC,EAAE,CAAClC,eAAe,EAAES,eAAe,EAAEa,eAAe,CAAC,CAAC;;EAEvD;EACAxF,SAAS,CAAC,MAAM;IACd,IAAIiC,IAAI,EAAE;MACR,MAAMoE,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,wBAAwB,CAAC;MACnE,IAAIF,SAAS,EAAE;QACb;QACAA,SAAS,CAACG,SAAS,GAAG,EAAE;QACxB;QACAlG,mBAAmB,CAACmG,oBAAoB,CAACJ,SAAS,CAAC;MACrD;;MAEA;MACA1F,cAAc,CAAC,CAAC,CAAC+F,IAAI,CAACC,WAAW,IAAI;QACnCvD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEsD,WAAW,GAAG,aAAa,GAAG,UAAU,CAAC;QAClF,IAAI,CAACA,WAAW,EAAE;UAChBvD,OAAO,CAACwD,IAAI,CAAC,uEAAuE,CAAC;QACvF;MACF,CAAC,CAAC,CAACC,KAAK,CAAC9E,KAAK,IAAI;QAChBqB,OAAO,CAACrB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACE,IAAI,CAAC,CAAC;;EAEV;EACA,MAAM1B,qBAAqB,GAAG,MAAAA,CAAOuG,UAAU,EAAEC,KAAK,GAAG,IAAI,KAAK;IAChE,IAAIC,YAAY,GAAGD,KAAK;;IAExB;IACA,IAAI,CAACC,YAAY,EAAE;MACjB,IAAI;QACF,MAAMC,MAAM,GAAGrG,UAAU,CAACsG,SAAS,CAACJ,UAAU,CAAC;QAC/CE,YAAY,GAAGC,MAAM,CAAC/D,EAAE;QACxBtB,YAAY,CAACqF,MAAM,CAAC;QACpBzF,cAAc,CAAC,MAAM,CAAC;MACxB,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,QAAQ,CAACD,KAAK,CAACoF,OAAO,CAAC;QACvB;MACF;IACF;;IAEA;IACAvG,UAAU,CAACqC,eAAe,CAAC+D,YAAY,EAAE,YAAY,EAAE;MAAEI,QAAQ,EAAE;IAAG,CAAC,CAAC;IACxEtF,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACFoB,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEyD,UAAU,EAAE,SAAS,EAAEE,YAAY,CAAC;;MAErF;MACApG,UAAU,CAACqC,eAAe,CAAC+D,YAAY,EAAE,YAAY,EAAE;QAAEI,QAAQ,EAAE;MAAG,CAAC,CAAC;MAExE,MAAMC,QAAQ,GAAG,MAAM7G,eAAe,CAACsG,UAAU,EAAE9F,kBAAkB,CAAC,CAAC,CAAC;MACxEoC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEgE,QAAQ,CAAC;;MAE/C;MACAzG,UAAU,CAACqC,eAAe,CAAC+D,YAAY,EAAE,WAAW,EAAE;QACpD5E,IAAI,EAAEiF,QAAQ;QACdD,QAAQ,EAAE;MACZ,CAAC,CAAC;;MAEF;MACA,IAAIJ,YAAY,MAAKrF,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEuB,EAAE,GAAE;QAClCtB,YAAY,CAAChB,UAAU,CAACuC,MAAM,CAAC6D,YAAY,CAAC,CAAC;MAC/C;;MAEA;MACA,MAAMvD,MAAM,GAAGnD,mBAAmB,CAACoD,WAAW,CAAC,gBAAgB,CAAC;MAChE,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5CxD,mBAAmB,CAACyD,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOwD,GAAG,EAAE;MACZlE,OAAO,CAACrB,KAAK,CAAC,0BAA0B,EAAEuF,GAAG,CAAC;MAC9C1G,UAAU,CAACqC,eAAe,CAAC+D,YAAY,EAAE,OAAO,CAAC;MACjDhF,QAAQ,CAAC,sCAAsCsF,GAAG,CAACH,OAAO,qBAAqB,CAAC;IAClF,CAAC,SAAS;MACRrF,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAID;EACA,MAAMyF,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIhG,KAAK,CAACiG,IAAI,CAAC,CAAC,EAAE;MAChBnH,qBAAqB,CAACkB,KAAK,CAACiG,IAAI,CAAC,CAAC,CAAC;MACnChG,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB;EACF,CAAC;;EAED;EACA,MAAMiG,eAAe,GAAIC,GAAG,IAAK;IAC/B;IACA5F,QAAQ,CAAC,IAAI,CAAC;IACdF,YAAY,CAAC,KAAK,CAAC;IAEnBF,YAAY,CAACgG,GAAG,CAAC;IACjB,IAAIA,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAExF,IAAI,EAAE;MACbZ,cAAc,CAAC,MAAM,CAAC;IACxB,CAAC,MAAM;MACLA,cAAc,CAAC,OAAO,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMqG,YAAY,GAAGA,CAAA,KAAM;IACzB;IACA7F,QAAQ,CAAC,IAAI,CAAC;IACdF,YAAY,CAAC,KAAK,CAAC;IAEnBN,cAAc,CAAC,OAAO,CAAC;IACvBI,YAAY,CAAC,IAAI,CAAC;IAClBF,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAMoG,sBAAsB,GAAIF,GAAG,IAAK;IACtC;IACA5F,QAAQ,CAAC,IAAI,CAAC;IACdF,YAAY,CAAC,KAAK,CAAC;IAEnBF,YAAY,CAACgG,GAAG,CAAC;IACjBpG,cAAc,CAAC,SAAS,CAAC;EAC3B,CAAC;;EAID;EACA,MAAMuG,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI,EAACzF,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAE0F,OAAO,GAAE;IAEvB,IAAI5H,aAAa,CAAC6H,SAAS,CAAC,CAAC,CAACC,SAAS,EAAE;MACvC9H,aAAa,CAAC+H,MAAM,CAAC,CAAC;IACxB,CAAC,MAAM;MACL/H,aAAa,CAACgI,KAAK,CAAC9F,OAAO,CAAC0F,OAAO,CAAC;MACpC;MACA,MAAMvE,MAAM,GAAGnD,mBAAmB,CAACoD,WAAW,CAAC,aAAa,CAAC;MAC7D,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5CxD,mBAAmB,CAACyD,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAMuE,gBAAgB,GAAGA,CAAA,KAAM;IAC7BjI,aAAa,CAACkI,IAAI,CAAC,CAAC;EACtB,CAAC;EAED,MAAMC,sBAAsB,GAAIC,IAAI,IAAK;IACvCpI,aAAa,CAACqI,OAAO,CAACD,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,EAACpG,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEqG,KAAK,KAAI,EAACrG,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAE0F,OAAO,GAAE;IAC1C,MAAMvE,MAAM,GAAGpD,aAAa,CAACuI,WAAW,CAACtG,OAAO,EAAE,GAAGA,OAAO,CAACqG,KAAK,CAACE,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,EAAE,CAAC;IACnG,IAAIpF,MAAM,CAACqF,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAGzI,mBAAmB,CAACoD,WAAW,CAAC,aAAa,CAAC;MAChE,IAAIqF,SAAS,CAACpF,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxCmF,SAAS,CAACpF,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/CxD,mBAAmB,CAACyD,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAMkF,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,EAAC1G,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEqG,KAAK,KAAI,EAACrG,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAE0F,OAAO,GAAE;IAC1C,MAAMvE,MAAM,GAAGpD,aAAa,CAAC4I,YAAY,CAAC3G,OAAO,EAAE,GAAGA,OAAO,CAACqG,KAAK,CAACE,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,EAAE,CAAC;IACpG,IAAIpF,MAAM,CAACqF,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAGzI,mBAAmB,CAACoD,WAAW,CAAC,aAAa,CAAC;MAChE,IAAIqF,SAAS,CAACpF,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxCmF,SAAS,CAACpF,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/CxD,mBAAmB,CAACyD,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAMoF,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI,EAAC5G,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAE0F,OAAO,GAAE;IACvB,MAAMvE,MAAM,GAAG,MAAMpD,aAAa,CAAC8I,eAAe,CAAC7G,OAAO,CAAC0F,OAAO,CAAC;IACnE3H,aAAa,CAAC+I,WAAW,CAAC3F,MAAM,CAAC0D,OAAO,EAAE1D,MAAM,CAACqF,OAAO,GAAG,SAAS,GAAG,OAAO,CAAC;IAC/E,IAAIrF,MAAM,CAACqF,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAGzI,mBAAmB,CAACoD,WAAW,CAAC,aAAa,CAAC;MAChE,IAAIqF,SAAS,CAACpF,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxCmF,SAAS,CAACpF,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/CxD,mBAAmB,CAACyD,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;;EAID;EACA,MAAMuF,MAAM,GAAGA,CAAA,KAAM;IACnB,IAAI9H,WAAW,KAAK,SAAS,EAAE;MAC7BC,cAAc,CAAC,MAAM,CAAC;MACtB,IAAIG,SAAS,EAAE;QACbf,UAAU,CAACqC,eAAe,CAACtB,SAAS,CAACuB,EAAE,EAAEvB,SAAS,CAACsC,MAAM,EAAE;UAAE3B,OAAO,EAAE;QAAK,CAAC,CAAC;QAC7EV,YAAY,CAAChB,UAAU,CAACuC,MAAM,CAACxB,SAAS,CAACuB,EAAE,CAAC,CAAC;MAC/C;IACF,CAAC,MAAM,IAAI3B,WAAW,KAAK,MAAM,EAAE;MACjCC,cAAc,CAAC,OAAO,CAAC;IACzB;EACF,CAAC;EAID,MAAM8H,MAAM,GAAGA,CAAA,KAAM;IACnB9H,cAAc,CAAC,OAAO,CAAC;IACvBI,YAAY,CAAC,IAAI,CAAC;IAClBF,QAAQ,CAAC,EAAE,CAAC;IACZ;IACAd,UAAU,CAAC2I,YAAY,CAAC,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB9D,YAAY,CAAC+D,OAAO,CAAC,gBAAgB,EAAE,MAAM,CAAC;IAC9CvH,OAAO,CAAC;MAAEgB,EAAE,EAAE,OAAO;MAAEP,IAAI,EAAE,WAAW;MAAEmD,gBAAgB,EAAE;IAAU,CAAC,CAAC;EAC1E,CAAC;EAED,oBACE5E,OAAA;IAAKwI,SAAS,EAAC,KAAK;IAACC,GAAG,EAAExH,MAAO;IAAAyH,QAAA,gBAE/B1I,OAAA;MAAQwI,SAAS,EAAC,YAAY;MAAAE,QAAA,eAC5B1I,OAAA;QAAKwI,SAAS,EAAC,gBAAgB;QAAAE,QAAA,gBAC7B1I,OAAA;UAAQ2I,OAAO,EAAEP,MAAO;UAACI,SAAS,EAAC,WAAW;UAAAE,QAAA,EAC3CrH,CAAC,CAAC,UAAU;QAAC;UAAAuH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACT/I,OAAA;UAAKwI,SAAS,EAAC,cAAc;UAAAE,QAAA,GAC1B3H,IAAI,iBACHf,OAAA;YAAKgC,EAAE,EAAC,wBAAwB;YAACgH,KAAK,EAAE;cAAEC,WAAW,EAAE;YAAO;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE3D,CACN,eACD/I,OAAA,CAACJ,gBAAgB;YAAAgJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACnB,CAAChI,IAAI,gBACJf,OAAA;YAAQ2I,OAAO,EAAEL,UAAW;YAACE,SAAS,EAAC,iBAAiB;YAACQ,KAAK,EAAE;cAAEE,UAAU,EAAE;YAAO,CAAE;YAAAR,QAAA,EACpFrH,CAAC,CAAC,YAAY;UAAC;YAAAuH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,gBAET/I,OAAA;YAAMgJ,KAAK,EAAE;cAAEE,UAAU,EAAE;YAAO,CAAE;YAAAR,QAAA,GAAErH,CAAC,CAAC,SAAS,CAAC,EAAC,IAAE,EAACN,IAAI,CAACU,IAAI,EAAC,GAAC;UAAA;YAAAmH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACxE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAGRhI,IAAI,iBACHf,OAAA,CAACL,UAAU;MACTwJ,WAAW,EAAE1C,eAAgB;MAC7B2C,QAAQ,EAAEzC,YAAa;MACvB0C,kBAAkB,EAAEzC;IAAuB;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CACF,eAGD/I,OAAA;MAAMwI,SAAS,EAAC,cAAc;MAAAE,QAAA,GAC3B7H,KAAK,iBACJb,OAAA;QAAKwI,SAAS,EAAC,OAAO;QAAAE,QAAA,GAAC,eAClB,EAAC7H,KAAK,eACTb,OAAA;UAAQ2I,OAAO,EAAEA,CAAA,KAAM7H,QAAQ,CAAC,IAAI,CAAE;UAACkI,KAAK,EAAE;YAACE,UAAU,EAAE,MAAM;YAAEI,UAAU,EAAE,MAAM;YAAEC,MAAM,EAAE,MAAM;YAAEC,KAAK,EAAE,OAAO;YAAEC,MAAM,EAAE;UAAS,CAAE;UAAAf,QAAA,EAAC;QAE3I;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAGA1I,WAAW,KAAK,OAAO,iBACtBL,OAAA;QAAKwI,SAAS,EAAC,kBAAkB;QAAAE,QAAA,gBAC/B1I,OAAA;UAAIwI,SAAS,EAAC,OAAO;UAAAE,QAAA,EAAErH,CAAC,CAAC,UAAU;QAAC;UAAAuH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1C/I,OAAA;UAAGwI,SAAS,EAAC,UAAU;UAAAE,QAAA,EAAC;QAExB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAEH,CAAChI,IAAI,gBACJf,OAAA;UAAKgJ,KAAK,EAAE;YAACM,UAAU,EAAE,SAAS;YAAEI,OAAO,EAAE,MAAM;YAAEC,YAAY,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAM,CAAE;UAAAlB,QAAA,gBACjG1I,OAAA;YAAGgJ,KAAK,EAAE;cAACQ,KAAK,EAAE,SAAS;cAAEI,YAAY,EAAE;YAAM,CAAE;YAAAlB,QAAA,EAChDrH,CAAC,CAAC,eAAe;UAAC;YAAAuH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACJ/I,OAAA;YAAQ2I,OAAO,EAAEL,UAAW;YAACE,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EACrDrH,CAAC,CAAC,eAAe;UAAC;YAAAuH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAEN/I,OAAA;UAAM6J,QAAQ,EAAExD,YAAa;UAAAqC,QAAA,gBAC3B1I,OAAA;YAAKwI,SAAS,EAAC,YAAY;YAAAE,QAAA,eACzB1I,OAAA;cACE8J,IAAI,EAAC,MAAM;cACXC,KAAK,EAAExJ,KAAM;cACbyJ,QAAQ,EAAG1D,CAAC,IAAK9F,QAAQ,CAAC8F,CAAC,CAAC2D,MAAM,CAACF,KAAK,CAAE;cAC1CG,WAAW,EAAE7I,CAAC,CAAC,kBAAkB,CAAE;cACnCmH,SAAS,EAAC,YAAY;cACtB2B,QAAQ,EAAExJ;YAAU;cAAAiI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN/I,OAAA;YACE8J,IAAI,EAAC,QAAQ;YACbK,QAAQ,EAAExJ,SAAS,IAAI,CAACJ,KAAK,CAACiG,IAAI,CAAC,CAAE;YACrCgC,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EAE1B/H,SAAS,gBACRX,OAAA,CAAAE,SAAA;cAAAwI,QAAA,gBACE1I,OAAA;gBAAMwI,SAAS,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAChC1H,CAAC,CAAC,YAAY,CAAC;YAAA,eAChB,CAAC,gBAEHrB,OAAA,CAAAE,SAAA;cAAAwI,QAAA,EACGrH,CAAC,CAAC,kBAAkB;YAAC,gBACtB;UACH;YAAAuH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGA1I,WAAW,KAAK,MAAM,IAAIa,IAAI,iBAC7BlB,OAAA;QAAKwI,SAAS,EAAC,gBAAgB;QAAAE,QAAA,gBAC7B1I,OAAA;UAAKwI,SAAS,EAAC,aAAa;UAAAE,QAAA,gBAC1B1I,OAAA;YAAA0I,QAAA,EAAKxH,IAAI,CAAC2C;UAAI;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpB/I,OAAA;YAAA0I,QAAA,EAAIrH,CAAC,CAAC,cAAc;UAAC;YAAAuH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1B/I,OAAA;YAAQ2I,OAAO,EAAER,MAAO;YAACK,SAAS,EAAC,mBAAmB;YAACQ,KAAK,EAAE;cAACoB,SAAS,EAAE;YAAM,CAAE;YAAA1B,QAAA,EAC/ErH,CAAC,CAAC,YAAY;UAAC;YAAAuH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAELpI,SAAS,gBACRX,OAAA;UAAKwI,SAAS,EAAC,SAAS;UAAAE,QAAA,gBACtB1I,OAAA;YAAMwI,SAAS,EAAC;UAAS;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjC/I,OAAA;YAAA0I,QAAA,EAAOrH,CAAC,CAAC,SAAS;UAAC;YAAAuH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,gBAEN/I,OAAA;UAAKwI,SAAS,EAAC,eAAe;UAAAE,QAAA,EAC3BxH,IAAI,CAACmC,MAAM,CAACY,GAAG,CAAC,CAACpC,MAAM,EAAEyB,KAAK,kBAC7BtD,OAAA;YAEEwI,SAAS,EAAE,eAAerH,cAAc,KAAKU,MAAM,GAAG,UAAU,GAAG,EAAE,EAAG;YACxE,cAAYyB,KAAM;YAClB,aAAWzB,MAAM,CAACO,IAAK;YACvB,oBAAkBP,MAAM,CAACwI,SAAU;YACnC,sBAAoBxI,MAAM,CAACsC,WAAW,IAAI,KAAM;YAChD,cAAYtC,MAAM,CAACwC,KAAK,IAAI,CAAE;YAC9BsE,OAAO,EAAEA,CAAA,KAAM7F,kBAAkB,CAACjB,MAAM,CAAE;YAC1CyI,aAAa,EAAEA,CAAA,KAAM3I,wBAAwB,CAACE,MAAM,CAAE;YACtD0I,YAAY,EAAGjE,CAAC,IAAK;cACnB,MAAMkE,KAAK,GAAGlE,CAAC,CAACmE,OAAO,CAAC,CAAC,CAAC;cAC1BnE,CAAC,CAACoE,aAAa,CAACC,cAAc,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;cAC3CvE,CAAC,CAACoE,aAAa,CAACI,WAAW,GAAGN,KAAK,CAACO,OAAO;cAC3CzE,CAAC,CAACoE,aAAa,CAACM,WAAW,GAAGR,KAAK,CAACS,OAAO;YAC7C,CAAE;YACFC,UAAU,EAAG5E,CAAC,IAAK;cACjB,MAAMkE,KAAK,GAAGlE,CAAC,CAAC6E,cAAc,CAAC,CAAC,CAAC;cACjC,MAAMC,SAAS,GAAGR,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIvE,CAAC,CAACoE,aAAa,CAACC,cAAc,IAAI,CAAC,CAAC;cACpE,MAAMU,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACf,KAAK,CAACO,OAAO,IAAIzE,CAAC,CAACoE,aAAa,CAACI,WAAW,IAAI,CAAC,CAAC,CAAC;cAC3E,MAAMU,MAAM,GAAGF,IAAI,CAACC,GAAG,CAACf,KAAK,CAACS,OAAO,IAAI3E,CAAC,CAACoE,aAAa,CAACM,WAAW,IAAI,CAAC,CAAC,CAAC;;cAE3E;cACA,IAAII,SAAS,GAAG,GAAG,IAAIC,MAAM,GAAG,EAAE,IAAIG,MAAM,GAAG,EAAE,EAAE;gBACjDlF,CAAC,CAACC,cAAc,CAAC,CAAC;gBAClB7C,YAAY,CAAC7B,MAAM,EAAEyB,KAAK,CAAC;cAC7B;cACA;cAAA,KACK,IAAI8H,SAAS,GAAG,GAAG,IAAIC,MAAM,GAAG,EAAE,IAAIG,MAAM,GAAG,EAAE,EAAE;gBACtD,MAAMX,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;gBACtB,MAAMY,OAAO,GAAGnF,CAAC,CAACoE,aAAa,CAACgB,WAAW,IAAI,CAAC;gBAChD,IAAIb,GAAG,GAAGY,OAAO,GAAG,GAAG,EAAE;kBACvBnF,CAAC,CAACC,cAAc,CAAC,CAAC;kBAClB5E,wBAAwB,CAACE,MAAM,CAAC;gBAClC;gBACAyE,CAAC,CAACoE,aAAa,CAACgB,WAAW,GAAGb,GAAG;cACnC;YACF,CAAE;YAAAnC,QAAA,GAGD,CAAC7G,MAAM,CAACwC,KAAK,IAAI,CAAC,IAAI,CAAC,iBACtBrE,OAAA;cAAKwI,SAAS,EAAC,wBAAwB;cAAAE,QAAA,gBACrC1I,OAAA;gBAAKwI,SAAS,EAAC,YAAY;gBAAAE,QAAA,EACxBiD,KAAK,CAACC,IAAI,CAAC;kBAAElJ,MAAM,EAAEb,MAAM,CAACwC,KAAK,IAAI;gBAAE,CAAC,EAAE,CAACwH,CAAC,EAAEC,CAAC,kBAC9C9L,OAAA;kBAAawI,SAAS,EAAC;gBAAW,GAAxBsD,CAAC;kBAAAlD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA6B,CACzC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN/I,OAAA;gBAAKwI,SAAS,EAAC,YAAY;gBAAAE,QAAA,GAAC,QAAM,EAAC7G,MAAM,CAACwC,KAAK;cAAA;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CACN,eAED/I,OAAA;cAAKwI,SAAS,EAAC,cAAc;cAAAE,QAAA,EAAE7G,MAAM,CAACkK;YAAK;cAAAnD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClD/I,OAAA;cAAIwI,SAAS,EAAC,aAAa;cAAAE,QAAA,EAAE7G,MAAM,CAACO;YAAI;cAAAwG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9C/I,OAAA;cAAGwI,SAAS,EAAC,oBAAoB;cAAAE,QAAA,EAAE7G,MAAM,CAACwI;YAAS;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAEvDlH,MAAM,CAACmK,YAAY,iBAClBhM,OAAA;cAAKwI,SAAS,EAAC,sBAAsB;cAAAE,QAAA,GAClC7G,MAAM,CAACmK,YAAY,CAACjI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACE,GAAG,CAAC,CAACgI,GAAG,EAAEC,QAAQ,kBACjDlM,OAAA;gBAAqBwI,SAAS,EAAC,iBAAiB;gBAAAE,QAAA,EAC7CuD;cAAG,GADKC,QAAQ;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEb,CACP,CAAC,EACDlH,MAAM,CAACmK,YAAY,CAACtJ,MAAM,GAAG,CAAC,iBAC7B1C,OAAA;gBAAMwI,SAAS,EAAC,sBAAsB;gBAAAE,QAAA,GAAC,GACpC,EAAC7G,MAAM,CAACmK,YAAY,CAACtJ,MAAM,GAAG,CAAC;cAAA;gBAAAkG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN,eAED/I,OAAA;cAAKwI,SAAS,EAAC,cAAc;cAAAE,QAAA,gBAC3B1I,OAAA;gBAAMwI,SAAS,EAAC,aAAa;gBAAAE,QAAA,EAAC;cAA4B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjE/I,OAAA;gBAAMwI,SAAS,EAAC,aAAa;gBAAAE,QAAA,EAAC;cAA+B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CAAC;UAAA,GAxEDzF,KAAK;YAAAsF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyEP,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGA1I,WAAW,KAAK,SAAS,IAAIe,OAAO,iBACnCpB,OAAA;QAAKwI,SAAS,EAAC,gBAAgB;QAAAE,QAAA,eAC7B1I,OAAA;UAAKwI,SAAS,EAAC,MAAM;UAAAE,QAAA,gBACnB1I,OAAA;YAAKwI,SAAS,EAAC,gBAAgB;YAACQ,KAAK,EAAE;cAACY,YAAY,EAAE;YAAM,CAAE;YAAAlB,QAAA,gBAC5D1I,OAAA;cAAQ2I,OAAO,EAAER,MAAO;cAACK,SAAS,EAAC,mBAAmB;cAAAE,QAAA,EACnDrH,CAAC,CAAC,YAAY;YAAC;cAAAuH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGT/I,OAAA;cAAKwI,SAAS,EAAC,kBAAkB;cAACQ,KAAK,EAAE;gBACvCmD,OAAO,EAAE,MAAM;gBACfC,GAAG,EAAE,KAAK;gBACVhC,SAAS,EAAE,MAAM;gBACjBiC,QAAQ,EAAE;cACZ,CAAE;cAAA3D,QAAA,gBAEA1I,OAAA;gBAAKwI,SAAS,EAAC,yBAAyB;gBAACQ,KAAK,EAAE;kBAC9CmD,OAAO,EAAE,MAAM;kBACfG,UAAU,EAAE,QAAQ;kBACpBF,GAAG,EAAE,KAAK;kBACV1C,OAAO,EAAE,UAAU;kBACnBJ,UAAU,EAAE,SAAS;kBACrBK,YAAY,EAAE,KAAK;kBACnBJ,MAAM,EAAE;gBACV,CAAE;gBAAAb,QAAA,gBACA1I,OAAA;kBACE2I,OAAO,EAAE9B,kBAAmB;kBAC5B2B,SAAS,EAAC,UAAU;kBACpBf,KAAK,EAAC,mBAAmB;kBACzBuB,KAAK,EAAE;oBACLM,UAAU,EAAE,MAAM;oBAClBC,MAAM,EAAE,MAAM;oBACdgD,QAAQ,EAAE,MAAM;oBAChB9C,MAAM,EAAE,SAAS;oBACjBC,OAAO,EAAE;kBACX,CAAE;kBAAAhB,QAAA,EAEDxJ,aAAa,CAAC6H,SAAS,CAAC,CAAC,CAACC,SAAS,GAAG,IAAI,GAAG;gBAAI;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,eACT/I,OAAA;kBACE2I,OAAO,EAAExB,gBAAiB;kBAC1BqB,SAAS,EAAC,UAAU;kBACpBf,KAAK,EAAC,aAAa;kBACnBuB,KAAK,EAAE;oBACLM,UAAU,EAAE,MAAM;oBAClBC,MAAM,EAAE,MAAM;oBACdgD,QAAQ,EAAE,MAAM;oBAChB9C,MAAM,EAAE,SAAS;oBACjBC,OAAO,EAAE;kBACX,CAAE;kBAAAhB,QAAA,EACH;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT/I,OAAA;kBACE8J,IAAI,EAAC,OAAO;kBACZ0C,GAAG,EAAC,KAAK;kBACTC,GAAG,EAAC,GAAG;kBACPC,IAAI,EAAC,KAAK;kBACVC,YAAY,EAAC,GAAG;kBAChB3C,QAAQ,EAAG1D,CAAC,IAAKe,sBAAsB,CAACuF,UAAU,CAACtG,CAAC,CAAC2D,MAAM,CAACF,KAAK,CAAC,CAAE;kBACpEf,KAAK,EAAE;oBAAC6D,KAAK,EAAE;kBAAM,CAAE;kBACvBpF,KAAK,EAAC;gBAAc;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACF/I,OAAA;kBAAMgJ,KAAK,EAAE;oBAACuD,QAAQ,EAAE,MAAM;oBAAE/C,KAAK,EAAE;kBAAS,CAAE;kBAAAd,QAAA,EAAC;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eAGN/I,OAAA;gBAAKwI,SAAS,EAAC,yBAAyB;gBAACQ,KAAK,EAAE;kBAC9CmD,OAAO,EAAE,MAAM;kBACfC,GAAG,EAAE;gBACP,CAAE;gBAAA1D,QAAA,gBACA1I,OAAA;kBACE2I,OAAO,EAAEX,qBAAsB;kBAC/BQ,SAAS,EAAC,mBAAmB;kBAC7BQ,KAAK,EAAE;oBAACU,OAAO,EAAE,UAAU;oBAAE6C,QAAQ,EAAE;kBAAM,CAAE;kBAC/C9E,KAAK,EAAC,mBAAmB;kBAAAiB,QAAA,EAC1B;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT/I,OAAA;kBACE2I,OAAO,EAAEnB,eAAgB;kBACzBgB,SAAS,EAAC,mBAAmB;kBAC7BQ,KAAK,EAAE;oBAACU,OAAO,EAAE,UAAU;oBAAE6C,QAAQ,EAAE;kBAAM,CAAE;kBAC/C9E,KAAK,EAAC,eAAe;kBAAAiB,QAAA,EACtB;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT/I,OAAA;kBACE2I,OAAO,EAAEb,gBAAiB;kBAC1BU,SAAS,EAAC,mBAAmB;kBAC7BQ,KAAK,EAAE;oBAACU,OAAO,EAAE,UAAU;oBAAE6C,QAAQ,EAAE;kBAAM,CAAE;kBAC/C9E,KAAK,EAAC,gBAAgB;kBAAAiB,QAAA,EACvB;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN/I,OAAA;YAAIwI,SAAS,EAAC,OAAO;YAAAE,QAAA,EAAE,CAAAtH,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEqG,KAAK,KAAI;UAAY;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC3D/I,OAAA;YAAKgJ,KAAK,EAAE;cAACQ,KAAK,EAAE,SAAS;cAAEI,YAAY,EAAE,MAAM;cAAE2C,QAAQ,EAAE;YAAQ,CAAE;YAAA7D,QAAA,gBACvE1I,OAAA;cAAA0I,QAAA,GAAOrH,CAAC,CAAC,QAAQ,CAAC,EAAC,IAAE,EAAC,CAAAD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEb,KAAK,KAAI,SAAS;YAAA;cAAAqI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACxD,CAAA3H,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEU,KAAK,KAAIV,OAAO,CAACU,KAAK,CAACY,MAAM,GAAG,CAAC,iBACzC1C,OAAA;cAAMgJ,KAAK,EAAE;gBAACE,UAAU,EAAE;cAAM,CAAE;cAAAR,QAAA,GAC/BrH,CAAC,CAAC,OAAO,CAAC,EAAC,IAAE,EAACD,OAAO,CAACU,KAAK,CAACgL,IAAI,CAAC,IAAI,CAAC;YAAA;cAAAlE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN/I,OAAA;YAAKwI,SAAS,EAAC,iBAAiB;YAACQ,KAAK,EAAE;cAAC+D,UAAU,EAAE,KAAK;cAAER,QAAQ,EAAE;YAAQ,CAAE;YAAA7D,QAAA,EAC7EtH,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAE0F,OAAO,GAAG1F,OAAO,CAAC0F,OAAO,CAACkG,KAAK,CAAC,IAAI,CAAC,CAAC/I,GAAG,CAAC,CAACgJ,SAAS,EAAE3J,KAAK,KACnE2J,SAAS,CAACzG,IAAI,CAAC,CAAC,iBACdxG,OAAA;cAAegJ,KAAK,EAAE;gBAACY,YAAY,EAAE;cAAM,CAAE;cAAAlB,QAAA,EAC1CuE;YAAS,GADJ3J,KAAK;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CAEN,CAAC,gBACA/I,OAAA;cAAA0I,QAAA,EAAG;YAA0B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UACjC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC3I,EAAA,CApwBID,YAAY;EAAA,QAgBFN,cAAc;AAAA;AAAAqN,EAAA,GAhBxB/M,YAAY;AAswBlB,eAAeA,YAAY;AAAC,IAAA+M,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}