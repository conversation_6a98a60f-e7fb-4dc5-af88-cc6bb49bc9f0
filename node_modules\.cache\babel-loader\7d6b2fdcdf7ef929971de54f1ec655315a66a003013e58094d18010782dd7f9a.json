{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Appv1\\\\src\\\\components\\\\OptimizedApp.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport '../styles/optimized.css';\nimport gestureService, { createFlagWheel } from '../services/gestureService';\nimport speechService from '../services/speechService';\nimport exportService from '../services/exportService';\nimport gamificationService from '../services/optimizedGamificationService';\nimport { generateKnowledgeTree as generateTreeAPI, generateArticle as generateArticleAPI, testConnection } from '../services/openRouterService';\nimport tabService from '../services/tabService';\nimport TabManager from './TabManager';\nimport LanguageSwitcher from './LanguageSwitcher';\nimport { useTranslation, getCurrentLanguage } from '../utils/i18n';\n\n// Optimized Knowledge Tree Explorer - Pareto 80/20 Implementation\n// Focus on core functionality with maximum impact\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst OptimizedApp = () => {\n  _s();\n  // Core state - now managed by tabs\n  const [currentView, setCurrentView] = useState('input'); // 'input', 'tree', 'article'\n  const [topic, setTopic] = useState('');\n  const [activeTab, setActiveTab] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [user, setUser] = useState(null);\n  const appRef = useRef(null);\n\n  // Get current tab data\n  const tree = (activeTab === null || activeTab === void 0 ? void 0 : activeTab.tree) || null;\n  const selectedBranch = (activeTab === null || activeTab === void 0 ? void 0 : activeTab.selectedBranch) || null;\n  const article = (activeTab === null || activeTab === void 0 ? void 0 : activeTab.article) || null;\n\n  // Translation hook\n  const {\n    t\n  } = useTranslation();\n\n  // Available flags for the optimized version\n  const availableFlags = React.useMemo(() => [{\n    code: '-a',\n    name: 'Article',\n    description: t('flagArticle')\n  }, {\n    code: '-ex',\n    name: 'Examples',\n    description: t('flagExamples')\n  }, {\n    code: '-q',\n    name: 'Quiz',\n    description: t('flagQuiz')\n  }, {\n    code: '-vis',\n    name: 'Visual',\n    description: t('flagVisual')\n  }, {\n    code: '-path',\n    name: 'Learning Path',\n    description: t('flagPath')\n  }, {\n    code: '-case',\n    name: 'Case Study',\n    description: t('flagCase')\n  }, {\n    code: '-ro',\n    name: 'Romanian',\n    description: t('flagRomanian')\n  }], [t]);\n\n  // Generate article with tabs support\n  const generateArticleForBranch = React.useCallback(async (branch, flags = ['-a']) => {\n    if (!activeTab) return;\n    setIsLoading(true);\n\n    // Set tab to loading state (yellow)\n    tabService.updateTabStatus(activeTab.id, 'loading', {\n      selectedBranch: branch,\n      article: null\n    });\n    setActiveTab(tabService.getTab(activeTab.id));\n    try {\n      console.log('📄 Generating article for branch:', branch.nume);\n      const articleData = await generateArticleAPI(activeTab.topic, branch, flags);\n\n      // Update tab with article and set to completed (green)\n      tabService.updateTabStatus(activeTab.id, 'completed', {\n        selectedBranch: branch,\n        article: articleData\n      });\n      const updatedTab = tabService.getTab(activeTab.id);\n      setActiveTab(updatedTab);\n      setCurrentView('article');\n\n      // Award points for article generation\n      const result = gamificationService.awardPoints('ARTICLE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (error) {\n      console.error('❌ Error generating article:', error);\n      setError('Failed to generate article. Please try again.');\n\n      // Set tab back to pending on error\n      tabService.updateTabStatus(activeTab.id, 'pending', {\n        selectedBranch: branch,\n        article: null\n      });\n      setActiveTab(tabService.getTab(activeTab.id));\n    } finally {\n      setIsLoading(false);\n    }\n  }, [activeTab]);\n\n  // Handle branch selection (single tap)\n  const handleBranchSelect = React.useCallback(branch => {\n    if (activeTab) {\n      tabService.updateTabStatus(activeTab.id, activeTab.status, {\n        selectedBranch: branch\n      });\n      setActiveTab(tabService.getTab(activeTab.id));\n    }\n  }, [activeTab]);\n\n  // Gesture handlers\n  const handleDoubleTap = React.useCallback((event, targetInfo) => {\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      // Show flag wheel on double tap of branch\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        createFlagWheel(targetInfo.position, availableFlags, selectedFlags => {\n          console.log('Selected flags:', selectedFlags);\n        }, selectedFlags => {\n          generateArticleForBranch(branch, selectedFlags);\n        });\n      }\n    }\n  }, [tree, availableFlags, generateArticleForBranch]);\n  const handleSingleTap = React.useCallback((event, targetInfo) => {\n    // Single tap for normal selection\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        handleBranchSelect(branch);\n      }\n    }\n  }, [tree, handleBranchSelect]);\n\n  // Expand branch to create sub-branches (tree effect)\n  const expandBranch = React.useCallback(async (branch, branchIndex) => {\n    if (!activeTab || !tree) {\n      setError(t('noActiveTab') || 'No active tab or tree available');\n      return;\n    }\n    setIsLoading(true);\n    setError(null);\n    try {\n      var _data$choices$, _data$choices$$messag;\n      console.log('🌿 Expanding branch:', branch.nume);\n\n      // Use the OpenRouter service for consistency\n      const expandPrompt = `Expand the topic \"${branch.nume}\" from the context of \"${tree.tema || activeTab.topic}\". Create 4-6 sub-branches that dive deeper into this specific area.\n\nReturn ONLY a valid JSON object with this structure:\n{\n  \"ramuri\": [\n    {\n      \"nume\": \"Sub-branch Name\",\n      \"descriere\": \"Brief description\",\n      \"emoji\": \"📚\",\n      \"subcategorii\": [\"Detail1\", \"Detail2\", \"Detail3\"]\n    }\n  ]\n}\n\nFocus on specific, actionable sub-topics within \"${branch.nume}\". Use ${getCurrentLanguage() === 'ro' ? 'Romanian' : 'English'} language.`;\n      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer 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`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': window.location.origin,\n          'X-Title': 'Knowledge Tree Explorer'\n        },\n        body: JSON.stringify({\n          model: 'deepseek/deepseek-r1-0528:free',\n          messages: [{\n            role: 'system',\n            content: 'You are an expert knowledge organizer. Generate specific sub-branches in valid JSON format only. Do not include any explanatory text outside the JSON.'\n          }, {\n            role: 'user',\n            content: expandPrompt\n          }],\n          temperature: 0.3,\n          max_tokens: 1500\n        })\n      });\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error('API Error Response:', errorText);\n        throw new Error(`API Error: ${response.status} - ${errorText}`);\n      }\n      const data = await response.json();\n      const content = (_data$choices$ = data.choices[0]) === null || _data$choices$ === void 0 ? void 0 : (_data$choices$$messag = _data$choices$.message) === null || _data$choices$$messag === void 0 ? void 0 : _data$choices$$messag.content;\n      if (!content) {\n        throw new Error('No content received from API');\n      }\n      console.log('📝 Raw API response:', content);\n\n      // Parse JSON response\n      const jsonMatch = content.match(/\\{[\\s\\S]*\\}/);\n      if (!jsonMatch) {\n        throw new Error('Invalid JSON format in response');\n      }\n      const expandedData = JSON.parse(jsonMatch[0]);\n      if (!expandedData.ramuri || !Array.isArray(expandedData.ramuri)) {\n        throw new Error('Invalid expanded data structure');\n      }\n      console.log('✅ Parsed expanded data:', expandedData);\n\n      // Update tree with expanded branches\n      const newTree = {\n        ...tree\n      };\n      newTree.ramuri = [...newTree.ramuri.slice(0, branchIndex + 1), ...expandedData.ramuri.map(subBranch => ({\n        ...subBranch,\n        isSubBranch: true,\n        parentBranch: branch.nume,\n        level: (branch.level || 0) + 1\n      })), ...newTree.ramuri.slice(branchIndex + 1)];\n\n      // Update tab with expanded tree\n      tabService.updateTabStatus(activeTab.id, 'completed', {\n        tree: newTree\n      });\n      setActiveTab(tabService.getTab(activeTab.id));\n      console.log('🌳 Tree expanded successfully');\n\n      // Award points for branch expansion\n      const result = gamificationService.awardPoints('BRANCH_EXPANDED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (error) {\n      console.error('❌ Error expanding branch:', error);\n      setError(t('failedToExpand') || 'Nu s-a putut extinde ramura. Te rugăm să încerci din nou.');\n    } finally {\n      setIsLoading(false);\n    }\n  }, [tree, activeTab, t]);\n  const handleLongPress = React.useCallback(async (event, targetInfo) => {\n    // Long press to expand branch and create tree effect\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        await expandBranch(branch, targetInfo.branchData.index);\n      }\n    }\n  }, [tree, expandBranch]);\n\n  // Initialize services and authentication\n  useEffect(() => {\n    const storedUser = localStorage.getItem('user');\n    const bypassSecurity = localStorage.getItem('bypassSecurity');\n    if (storedUser || bypassSecurity) {\n      const userData = {\n        id: 'user-1',\n        name: 'User',\n        subscriptionTier: 'premium'\n      };\n      setUser(userData);\n\n      // Award daily login points\n      const result = gamificationService.awardPoints('DAILY_LOGIN');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n\n    // Initialize gesture service\n    if (appRef.current) {\n      gestureService.init(appRef.current, {\n        doubleTap: handleDoubleTap,\n        singleTap: handleSingleTap,\n        longPress: handleLongPress\n      });\n    }\n    return () => {\n      gestureService.destroy();\n    };\n  }, [handleDoubleTap, handleSingleTap, handleLongPress]);\n\n  // Initialize gamification UI when user is logged in\n  useEffect(() => {\n    if (user) {\n      const container = document.getElementById('gamification-container');\n      if (container) {\n        // Clear existing content\n        container.innerHTML = '';\n        // Create gamification UI\n        gamificationService.createGamificationUI(container);\n      }\n\n      // Test API connection\n      testConnection().then(isConnected => {\n        console.log('🔌 API Connection Status:', isConnected ? '✅ Connected' : '❌ Failed');\n        if (!isConnected) {\n          console.warn('⚠️ API connection failed. Check your API key and internet connection.');\n        }\n      }).catch(error => {\n        console.error('❌ API connection test error:', error);\n      });\n    }\n  }, [user]);\n\n  // Core API call - using optimized service with tabs\n  const generateKnowledgeTree = async (topicInput, tabId = null) => {\n    let currentTabId = tabId;\n\n    // Create new tab if none provided\n    if (!currentTabId) {\n      try {\n        const newTab = tabService.createTab(topicInput);\n        currentTabId = newTab.id;\n        setActiveTab(newTab);\n        setCurrentView('tree');\n      } catch (error) {\n        setError(error.message);\n        return;\n      }\n    }\n\n    // Update tab status to generating\n    tabService.updateTabStatus(currentTabId, 'generating', {\n      progress: 10\n    });\n    setIsLoading(true);\n    setError(null);\n    try {\n      console.log('🌳 Generating knowledge tree for:', topicInput, 'in tab:', currentTabId);\n\n      // Update progress\n      tabService.updateTabStatus(currentTabId, 'generating', {\n        progress: 30\n      });\n      const treeData = await generateTreeAPI(topicInput, getCurrentLanguage());\n      console.log('✅ Generated tree data:', treeData);\n\n      // Update tab with completed tree\n      tabService.updateTabStatus(currentTabId, 'completed', {\n        tree: treeData,\n        progress: 100\n      });\n\n      // Update active tab if this is the current one\n      if (currentTabId === (activeTab === null || activeTab === void 0 ? void 0 : activeTab.id)) {\n        setActiveTab(tabService.getTab(currentTabId));\n      }\n\n      // Award points for tree generation\n      const result = gamificationService.awardPoints('TREE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (err) {\n      console.error('❌ Error generating tree:', err);\n      tabService.updateTabStatus(currentTabId, 'error');\n      setError(`Failed to generate knowledge tree: ${err.message}. Please try again.`);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Handle form submission\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (topic.trim()) {\n      generateKnowledgeTree(topic.trim());\n      setTopic(''); // Clear input for next topic\n    }\n  };\n\n  // Handle tab changes\n  const handleTabChange = tab => {\n    // Clear any existing errors when switching tabs\n    setError(null);\n    setIsLoading(false);\n    setActiveTab(tab);\n    if (tab !== null && tab !== void 0 && tab.tree) {\n      setCurrentView('tree');\n    } else {\n      setCurrentView('input');\n    }\n  };\n\n  // Handle new tab creation\n  const handleNewTab = () => {\n    // Clear any existing errors and loading states\n    setError(null);\n    setIsLoading(false);\n    setCurrentView('input');\n    setActiveTab(null);\n    setTopic('');\n  };\n  const handleTabArticleAccess = tab => {\n    // Clear any existing errors when accessing article\n    setError(null);\n    setIsLoading(false);\n    setActiveTab(tab);\n    setCurrentView('article');\n  };\n\n  // Speech functions\n  const handleSpeechToggle = () => {\n    if (!(article !== null && article !== void 0 && article.content)) return;\n    if (speechService.getStatus().isPlaying) {\n      speechService.toggle();\n    } else {\n      speechService.speak(article.content);\n      // Award points for using speech\n      const result = gamificationService.awardPoints('SPEECH_USED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleSpeechStop = () => {\n    speechService.stop();\n  };\n  const handleSpeechRateChange = rate => {\n    speechService.setRate(rate);\n  };\n\n  // Export functions\n  const handleExportPDF = () => {\n    if (!(article !== null && article !== void 0 && article.title) || !(article !== null && article !== void 0 && article.content)) return;\n    const result = exportService.exportAsPDF(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleExportWord = () => {\n    if (!(article !== null && article !== void 0 && article.title) || !(article !== null && article !== void 0 && article.content)) return;\n    const result = exportService.exportAsWord(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleCopyToClipboard = async () => {\n    if (!(article !== null && article !== void 0 && article.content)) return;\n    const result = await exportService.copyToClipboard(article.content);\n    exportService.showMessage(result.message, result.success ? 'success' : 'error');\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  // Navigation functions\n  const goBack = () => {\n    if (currentView === 'article') {\n      setCurrentView('tree');\n      if (activeTab) {\n        tabService.updateTabStatus(activeTab.id, activeTab.status, {\n          article: null\n        });\n        setActiveTab(tabService.getTab(activeTab.id));\n      }\n    } else if (currentView === 'tree') {\n      setCurrentView('input');\n    }\n  };\n  const goHome = () => {\n    setCurrentView('input');\n    setActiveTab(null);\n    setTopic('');\n    // Clear all tabs\n    tabService.clearAllTabs();\n  };\n\n  // Quick login for development\n  const quickLogin = () => {\n    localStorage.setItem('bypassSecurity', 'true');\n    setUser({\n      id: 'dev-1',\n      name: 'Developer',\n      subscriptionTier: 'premium'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    ref: appRef,\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"app-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: goHome,\n          className: \"logo-text\",\n          children: t('appTitle')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 531,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-right\",\n          children: [user && /*#__PURE__*/_jsxDEV(\"div\", {\n            id: \"gamification-container\",\n            style: {\n              marginRight: '16px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 536,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(LanguageSwitcher, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 13\n          }, this), !user ? /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: quickLogin,\n            className: \"btn btn-primary\",\n            style: {\n              marginLeft: '12px'\n            },\n            children: t('quickLogin')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 542,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginLeft: '12px'\n            },\n            children: [t('welcome'), \", \", user.name, \"!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 546,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 534,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 530,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 529,\n      columnNumber: 7\n    }, this), user && /*#__PURE__*/_jsxDEV(TabManager, {\n      onTabChange: handleTabChange,\n      onNewTab: handleNewTab,\n      onTabArticleAccess: handleTabArticleAccess\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 554,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"main-content\",\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error\",\n        children: [\"\\u26A0\\uFE0F \", error, /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setError(null),\n          style: {\n            marginLeft: 'auto',\n            background: 'none',\n            border: 'none',\n            color: 'white',\n            cursor: 'pointer'\n          },\n          children: \"\\u2715\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 566,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 564,\n        columnNumber: 11\n      }, this), currentView === 'input' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"title\",\n          children: t('appTitle')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"subtitle\",\n          children: \"Enter any topic to generate an interactive knowledge tree with AI-powered content.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 576,\n          columnNumber: 13\n        }, this), !user ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#f1f5f9',\n            padding: '1rem',\n            borderRadius: '0.5rem',\n            marginBottom: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#334155',\n              marginBottom: '1rem'\n            },\n            children: t('loginRequired')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 582,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: quickLogin,\n            className: \"btn btn-primary\",\n            children: t('quickLoginDev')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 585,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 581,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: topic,\n              onChange: e => setTopic(e.target.value),\n              placeholder: t('topicPlaceholder'),\n              className: \"form-input\",\n              disabled: isLoading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 592,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 591,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: isLoading || !topic.trim(),\n            className: \"btn btn-primary\",\n            children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 608,\n                columnNumber: 23\n              }, this), t('generating')]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: t('exploreKnowledge')\n            }, void 0, false)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 601,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 590,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 574,\n        columnNumber: 11\n      }, this), currentView === 'tree' && tree && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tree-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tree-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: tree.tema\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 626,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: t('selectBranch')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 627,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goBack,\n            className: \"btn btn-secondary\",\n            style: {\n              marginTop: '1rem'\n            },\n            children: t('backToTree')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 628,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 625,\n          columnNumber: 13\n        }, this), isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: t('loading')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 636,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 634,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"branches-grid\",\n          children: tree.ramuri.map((branch, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `branch-item ${selectedBranch === branch ? 'selected' : ''}`,\n            \"data-index\": index,\n            \"data-name\": branch.nume,\n            \"data-description\": branch.descriere,\n            \"data-is-sub-branch\": branch.isSubBranch || false,\n            \"data-level\": branch.level || 0,\n            onClick: () => handleBranchSelect(branch),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"branch-emoji\",\n              children: branch.emoji\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 651,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"branch-name\",\n              children: branch.nume\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 652,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"branch-description\",\n              children: branch.descriere\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 653,\n              columnNumber: 21\n            }, this), branch.subcategorii && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.875rem',\n                color: '#475569',\n                marginTop: '0.5rem'\n              },\n              children: [t('topics'), \": \", branch.subcategorii.slice(0, 3).join(', '), branch.subcategorii.length > 3 && '...']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 655,\n              columnNumber: 23\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"gesture-hint\",\n              style: {\n                fontSize: '0.75rem',\n                color: '#64748b',\n                marginTop: '0.5rem',\n                fontStyle: 'italic'\n              },\n              children: t('gestureHint')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 660,\n              columnNumber: 21\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 641,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 639,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 624,\n        columnNumber: 11\n      }, this), currentView === 'article' && article && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tree-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"article-header\",\n            style: {\n              marginBottom: '2rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: goBack,\n              className: \"btn btn-secondary\",\n              children: t('backToTree')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 680,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"article-controls\",\n              style: {\n                display: 'flex',\n                gap: '8px',\n                marginTop: '1rem',\n                flexWrap: 'wrap'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"speech-controls-compact\",\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '8px',\n                  padding: '8px 12px',\n                  background: '#f1f5f9',\n                  borderRadius: '6px',\n                  border: '1px solid #e2e8f0'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleSpeechToggle,\n                  className: \"btn-icon\",\n                  title: \"Play/Pause Speech\",\n                  style: {\n                    background: 'none',\n                    border: 'none',\n                    fontSize: '16px',\n                    cursor: 'pointer',\n                    padding: '4px'\n                  },\n                  children: speechService.getStatus().isPlaying ? '⏸️' : '▶️'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 701,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleSpeechStop,\n                  className: \"btn-icon\",\n                  title: \"Stop Speech\",\n                  style: {\n                    background: 'none',\n                    border: 'none',\n                    fontSize: '16px',\n                    cursor: 'pointer',\n                    padding: '4px'\n                  },\n                  children: \"\\u23F9\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 715,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"range\",\n                  min: \"0.5\",\n                  max: \"2\",\n                  step: \"0.1\",\n                  defaultValue: \"1\",\n                  onChange: e => handleSpeechRateChange(parseFloat(e.target.value)),\n                  style: {\n                    width: '60px'\n                  },\n                  title: \"Speech Speed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 729,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '12px',\n                    color: '#64748b'\n                  },\n                  children: \"\\uD83D\\uDDE3\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 739,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 692,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"export-controls-compact\",\n                style: {\n                  display: 'flex',\n                  gap: '4px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleCopyToClipboard,\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '6px 12px',\n                    fontSize: '12px'\n                  },\n                  title: \"Copy to Clipboard\",\n                  children: \"\\uD83D\\uDCCB Copy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 747,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleExportPDF,\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '6px 12px',\n                    fontSize: '12px'\n                  },\n                  title: \"Export as PDF\",\n                  children: \"\\uD83D\\uDCC4 PDF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 755,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleExportWord,\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '6px 12px',\n                    fontSize: '12px'\n                  },\n                  title: \"Export as Word\",\n                  children: \"\\uD83D\\uDCDD Word\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 763,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 743,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 679,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"title\",\n            children: (article === null || article === void 0 ? void 0 : article.title) || 'Loading...'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 775,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#475569',\n              marginBottom: '2rem',\n              fontSize: '0.9rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: [t('partOf'), \": \", (article === null || article === void 0 ? void 0 : article.topic) || 'Unknown']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 777,\n              columnNumber: 17\n            }, this), (article === null || article === void 0 ? void 0 : article.flags) && article.flags.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                marginLeft: '16px'\n              },\n              children: [t('flags'), \": \", article.flags.join(', ')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 779,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 776,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"article-content\",\n            style: {\n              lineHeight: '1.8',\n              fontSize: '1.1rem'\n            },\n            children: article !== null && article !== void 0 && article.content ? article.content.split('\\n').map((paragraph, index) => paragraph.trim() && /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                marginBottom: '1rem'\n              },\n              children: paragraph\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 788,\n              columnNumber: 21\n            }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Loading article content...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 793,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 785,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 678,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 677,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 562,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 527,\n    columnNumber: 5\n  }, this);\n};\n_s(OptimizedApp, \"B+LB35z6DkvdLV7SxB4V8E3FRsg=\", false, function () {\n  return [useTranslation];\n});\n_c = OptimizedApp;\nexport default OptimizedApp;\nvar _c;\n$RefreshReg$(_c, \"OptimizedApp\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "gestureService", "createFlagWheel", "speechService", "exportService", "gamificationService", "generateKnowledgeTree", "generateTreeAPI", "generateArticle", "generateArticleAPI", "testConnection", "tabService", "TabManager", "LanguageSwitcher", "useTranslation", "getCurrentLanguage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "OptimizedApp", "_s", "current<PERSON>iew", "set<PERSON><PERSON><PERSON>View", "topic", "setTopic", "activeTab", "setActiveTab", "isLoading", "setIsLoading", "error", "setError", "user", "setUser", "appRef", "tree", "<PERSON><PERSON><PERSON><PERSON>", "article", "t", "availableFlags", "useMemo", "code", "name", "description", "generateArticleForBranch", "useCallback", "branch", "flags", "updateTabStatus", "id", "getTab", "console", "log", "nume", "articleData", "updatedTab", "result", "awardPoints", "newAchievements", "length", "for<PERSON>ach", "achievement", "showAchievementNotification", "handleBranchSelect", "status", "handleDoubleTap", "event", "targetInfo", "isBranchItem", "branchData", "<PERSON><PERSON>", "index", "position", "selected<PERSON><PERSON><PERSON>", "handleSingleTap", "expandBranch", "branchIndex", "_data$choices$", "_data$choices$$messag", "expandPrompt", "tema", "response", "fetch", "method", "headers", "window", "location", "origin", "body", "JSON", "stringify", "model", "messages", "role", "content", "temperature", "max_tokens", "ok", "errorText", "text", "Error", "data", "json", "choices", "message", "jsonMatch", "match", "expandedData", "parse", "Array", "isArray", "newTree", "slice", "map", "subBranch", "isSubBranch", "parentBranch", "level", "handleLongPress", "storedUser", "localStorage", "getItem", "bypassSecurity", "userData", "subscriptionTier", "current", "init", "doubleTap", "singleTap", "longPress", "destroy", "container", "document", "getElementById", "innerHTML", "createGamificationUI", "then", "isConnected", "warn", "catch", "topicInput", "tabId", "currentTabId", "newTab", "createTab", "progress", "treeData", "err", "handleSubmit", "e", "preventDefault", "trim", "handleTabChange", "tab", "handleNewTab", "handleTabArticleAccess", "handleSpeechToggle", "getStatus", "isPlaying", "toggle", "speak", "handleSpeechStop", "stop", "handleSpeechRateChange", "rate", "setRate", "handleExportPDF", "title", "exportAsPDF", "replace", "success", "gamResult", "handleExportWord", "exportAsWord", "handleCopyToClipboard", "copyToClipboard", "showMessage", "goBack", "goHome", "clearAllTabs", "quickLogin", "setItem", "className", "ref", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginRight", "marginLeft", "onTabChange", "onNewTab", "onTabArticleAccess", "background", "border", "color", "cursor", "padding", "borderRadius", "marginBottom", "onSubmit", "type", "value", "onChange", "target", "placeholder", "disabled", "marginTop", "desc<PERSON><PERSON>", "emoji", "subcategorii", "fontSize", "join", "fontStyle", "display", "gap", "flexWrap", "alignItems", "min", "max", "step", "defaultValue", "parseFloat", "width", "lineHeight", "split", "paragraph", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Appv1/src/components/OptimizedApp.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport '../styles/optimized.css';\nimport gestureService, { createFlagWheel } from '../services/gestureService';\nimport speechService from '../services/speechService';\nimport exportService from '../services/exportService';\nimport gamificationService from '../services/optimizedGamificationService';\nimport { generateKnowledgeTree as generateTreeAPI, generateArticle as generateArticleAPI, testConnection } from '../services/openRouterService';\nimport tabService from '../services/tabService';\nimport TabManager from './TabManager';\nimport LanguageSwitcher from './LanguageSwitcher';\nimport { useTranslation, getCurrentLanguage } from '../utils/i18n';\n\n// Optimized Knowledge Tree Explorer - Pareto 80/20 Implementation\n// Focus on core functionality with maximum impact\n\nconst OptimizedApp = () => {\n  // Core state - now managed by tabs\n  const [currentView, setCurrentView] = useState('input'); // 'input', 'tree', 'article'\n  const [topic, setTopic] = useState('');\n  const [activeTab, setActiveTab] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [user, setUser] = useState(null);\n  const appRef = useRef(null);\n\n  // Get current tab data\n  const tree = activeTab?.tree || null;\n  const selectedBranch = activeTab?.selectedBranch || null;\n  const article = activeTab?.article || null;\n\n  // Translation hook\n  const { t } = useTranslation();\n\n  // Available flags for the optimized version\n  const availableFlags = React.useMemo(() => [\n    { code: '-a', name: 'Article', description: t('flagArticle') },\n    { code: '-ex', name: 'Examples', description: t('flagExamples') },\n    { code: '-q', name: 'Quiz', description: t('flagQuiz') },\n    { code: '-vis', name: 'Visual', description: t('flagVisual') },\n    { code: '-path', name: 'Learning Path', description: t('flagPath') },\n    { code: '-case', name: 'Case Study', description: t('flagCase') },\n    { code: '-ro', name: 'Romanian', description: t('flagRomanian') }\n  ], [t]);\n\n  // Generate article with tabs support\n  const generateArticleForBranch = React.useCallback(async (branch, flags = ['-a']) => {\n    if (!activeTab) return;\n\n    setIsLoading(true);\n\n    // Set tab to loading state (yellow)\n    tabService.updateTabStatus(activeTab.id, 'loading', {\n      selectedBranch: branch,\n      article: null\n    });\n    setActiveTab(tabService.getTab(activeTab.id));\n\n    try {\n      console.log('📄 Generating article for branch:', branch.nume);\n      const articleData = await generateArticleAPI(activeTab.topic, branch, flags);\n\n      // Update tab with article and set to completed (green)\n      tabService.updateTabStatus(activeTab.id, 'completed', {\n        selectedBranch: branch,\n        article: articleData\n      });\n\n      const updatedTab = tabService.getTab(activeTab.id);\n      setActiveTab(updatedTab);\n      setCurrentView('article');\n\n      // Award points for article generation\n      const result = gamificationService.awardPoints('ARTICLE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (error) {\n      console.error('❌ Error generating article:', error);\n      setError('Failed to generate article. Please try again.');\n\n      // Set tab back to pending on error\n      tabService.updateTabStatus(activeTab.id, 'pending', {\n        selectedBranch: branch,\n        article: null\n      });\n      setActiveTab(tabService.getTab(activeTab.id));\n    } finally {\n      setIsLoading(false);\n    }\n  }, [activeTab]);\n\n  // Handle branch selection (single tap)\n  const handleBranchSelect = React.useCallback((branch) => {\n    if (activeTab) {\n      tabService.updateTabStatus(activeTab.id, activeTab.status, { selectedBranch: branch });\n      setActiveTab(tabService.getTab(activeTab.id));\n    }\n  }, [activeTab]);\n\n  // Gesture handlers\n  const handleDoubleTap = React.useCallback((event, targetInfo) => {\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      // Show flag wheel on double tap of branch\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        createFlagWheel(\n          targetInfo.position,\n          availableFlags,\n          (selectedFlags) => {\n            console.log('Selected flags:', selectedFlags);\n          },\n          (selectedFlags) => {\n            generateArticleForBranch(branch, selectedFlags);\n          }\n        );\n      }\n    }\n  }, [tree, availableFlags, generateArticleForBranch]);\n\n  const handleSingleTap = React.useCallback((event, targetInfo) => {\n    // Single tap for normal selection\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        handleBranchSelect(branch);\n      }\n    }\n  }, [tree, handleBranchSelect]);\n\n  // Expand branch to create sub-branches (tree effect)\n  const expandBranch = React.useCallback(async (branch, branchIndex) => {\n    if (!activeTab || !tree) {\n      setError(t('noActiveTab') || 'No active tab or tree available');\n      return;\n    }\n\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      console.log('🌿 Expanding branch:', branch.nume);\n\n      // Use the OpenRouter service for consistency\n      const expandPrompt = `Expand the topic \"${branch.nume}\" from the context of \"${tree.tema || activeTab.topic}\". Create 4-6 sub-branches that dive deeper into this specific area.\n\nReturn ONLY a valid JSON object with this structure:\n{\n  \"ramuri\": [\n    {\n      \"nume\": \"Sub-branch Name\",\n      \"descriere\": \"Brief description\",\n      \"emoji\": \"📚\",\n      \"subcategorii\": [\"Detail1\", \"Detail2\", \"Detail3\"]\n    }\n  ]\n}\n\nFocus on specific, actionable sub-topics within \"${branch.nume}\". Use ${getCurrentLanguage() === 'ro' ? 'Romanian' : 'English'} language.`;\n\n      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer 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`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': window.location.origin,\n          'X-Title': 'Knowledge Tree Explorer'\n        },\n        body: JSON.stringify({\n          model: 'deepseek/deepseek-r1-0528:free',\n          messages: [\n            {\n              role: 'system',\n              content: 'You are an expert knowledge organizer. Generate specific sub-branches in valid JSON format only. Do not include any explanatory text outside the JSON.'\n            },\n            {\n              role: 'user',\n              content: expandPrompt\n            }\n          ],\n          temperature: 0.3,\n          max_tokens: 1500\n        })\n      });\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error('API Error Response:', errorText);\n        throw new Error(`API Error: ${response.status} - ${errorText}`);\n      }\n\n      const data = await response.json();\n      const content = data.choices[0]?.message?.content;\n\n      if (!content) {\n        throw new Error('No content received from API');\n      }\n\n      console.log('📝 Raw API response:', content);\n\n      // Parse JSON response\n      const jsonMatch = content.match(/\\{[\\s\\S]*\\}/);\n      if (!jsonMatch) {\n        throw new Error('Invalid JSON format in response');\n      }\n\n      const expandedData = JSON.parse(jsonMatch[0]);\n\n      if (!expandedData.ramuri || !Array.isArray(expandedData.ramuri)) {\n        throw new Error('Invalid expanded data structure');\n      }\n\n      console.log('✅ Parsed expanded data:', expandedData);\n\n      // Update tree with expanded branches\n      const newTree = { ...tree };\n      newTree.ramuri = [\n        ...newTree.ramuri.slice(0, branchIndex + 1),\n        ...expandedData.ramuri.map(subBranch => ({\n          ...subBranch,\n          isSubBranch: true,\n          parentBranch: branch.nume,\n          level: (branch.level || 0) + 1\n        })),\n        ...newTree.ramuri.slice(branchIndex + 1)\n      ];\n\n      // Update tab with expanded tree\n      tabService.updateTabStatus(activeTab.id, 'completed', { tree: newTree });\n      setActiveTab(tabService.getTab(activeTab.id));\n\n      console.log('🌳 Tree expanded successfully');\n\n      // Award points for branch expansion\n      const result = gamificationService.awardPoints('BRANCH_EXPANDED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n\n    } catch (error) {\n      console.error('❌ Error expanding branch:', error);\n      setError(t('failedToExpand') || 'Nu s-a putut extinde ramura. Te rugăm să încerci din nou.');\n    } finally {\n      setIsLoading(false);\n    }\n  }, [tree, activeTab, t]);\n\n  const handleLongPress = React.useCallback(async (event, targetInfo) => {\n    // Long press to expand branch and create tree effect\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        await expandBranch(branch, targetInfo.branchData.index);\n      }\n    }\n  }, [tree, expandBranch]);\n\n  // Initialize services and authentication\n  useEffect(() => {\n    const storedUser = localStorage.getItem('user');\n    const bypassSecurity = localStorage.getItem('bypassSecurity');\n\n    if (storedUser || bypassSecurity) {\n      const userData = {\n        id: 'user-1',\n        name: 'User',\n        subscriptionTier: 'premium'\n      };\n      setUser(userData);\n\n      // Award daily login points\n      const result = gamificationService.awardPoints('DAILY_LOGIN');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n\n    // Initialize gesture service\n    if (appRef.current) {\n      gestureService.init(appRef.current, {\n        doubleTap: handleDoubleTap,\n        singleTap: handleSingleTap,\n        longPress: handleLongPress\n      });\n    }\n\n    return () => {\n      gestureService.destroy();\n    };\n  }, [handleDoubleTap, handleSingleTap, handleLongPress]);\n\n  // Initialize gamification UI when user is logged in\n  useEffect(() => {\n    if (user) {\n      const container = document.getElementById('gamification-container');\n      if (container) {\n        // Clear existing content\n        container.innerHTML = '';\n        // Create gamification UI\n        gamificationService.createGamificationUI(container);\n      }\n\n      // Test API connection\n      testConnection().then(isConnected => {\n        console.log('🔌 API Connection Status:', isConnected ? '✅ Connected' : '❌ Failed');\n        if (!isConnected) {\n          console.warn('⚠️ API connection failed. Check your API key and internet connection.');\n        }\n      }).catch(error => {\n        console.error('❌ API connection test error:', error);\n      });\n    }\n  }, [user]);\n\n  // Core API call - using optimized service with tabs\n  const generateKnowledgeTree = async (topicInput, tabId = null) => {\n    let currentTabId = tabId;\n\n    // Create new tab if none provided\n    if (!currentTabId) {\n      try {\n        const newTab = tabService.createTab(topicInput);\n        currentTabId = newTab.id;\n        setActiveTab(newTab);\n        setCurrentView('tree');\n      } catch (error) {\n        setError(error.message);\n        return;\n      }\n    }\n\n    // Update tab status to generating\n    tabService.updateTabStatus(currentTabId, 'generating', { progress: 10 });\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      console.log('🌳 Generating knowledge tree for:', topicInput, 'in tab:', currentTabId);\n\n      // Update progress\n      tabService.updateTabStatus(currentTabId, 'generating', { progress: 30 });\n\n      const treeData = await generateTreeAPI(topicInput, getCurrentLanguage());\n      console.log('✅ Generated tree data:', treeData);\n\n      // Update tab with completed tree\n      tabService.updateTabStatus(currentTabId, 'completed', {\n        tree: treeData,\n        progress: 100\n      });\n\n      // Update active tab if this is the current one\n      if (currentTabId === activeTab?.id) {\n        setActiveTab(tabService.getTab(currentTabId));\n      }\n\n      // Award points for tree generation\n      const result = gamificationService.awardPoints('TREE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (err) {\n      console.error('❌ Error generating tree:', err);\n      tabService.updateTabStatus(currentTabId, 'error');\n      setError(`Failed to generate knowledge tree: ${err.message}. Please try again.`);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n\n\n  // Handle form submission\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    if (topic.trim()) {\n      generateKnowledgeTree(topic.trim());\n      setTopic(''); // Clear input for next topic\n    }\n  };\n\n  // Handle tab changes\n  const handleTabChange = (tab) => {\n    // Clear any existing errors when switching tabs\n    setError(null);\n    setIsLoading(false);\n\n    setActiveTab(tab);\n    if (tab?.tree) {\n      setCurrentView('tree');\n    } else {\n      setCurrentView('input');\n    }\n  };\n\n  // Handle new tab creation\n  const handleNewTab = () => {\n    // Clear any existing errors and loading states\n    setError(null);\n    setIsLoading(false);\n\n    setCurrentView('input');\n    setActiveTab(null);\n    setTopic('');\n  };\n\n  const handleTabArticleAccess = (tab) => {\n    // Clear any existing errors when accessing article\n    setError(null);\n    setIsLoading(false);\n\n    setActiveTab(tab);\n    setCurrentView('article');\n  };\n\n\n\n  // Speech functions\n  const handleSpeechToggle = () => {\n    if (!article?.content) return;\n\n    if (speechService.getStatus().isPlaying) {\n      speechService.toggle();\n    } else {\n      speechService.speak(article.content);\n      // Award points for using speech\n      const result = gamificationService.awardPoints('SPEECH_USED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleSpeechStop = () => {\n    speechService.stop();\n  };\n\n  const handleSpeechRateChange = (rate) => {\n    speechService.setRate(rate);\n  };\n\n  // Export functions\n  const handleExportPDF = () => {\n    if (!article?.title || !article?.content) return;\n    const result = exportService.exportAsPDF(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleExportWord = () => {\n    if (!article?.title || !article?.content) return;\n    const result = exportService.exportAsWord(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleCopyToClipboard = async () => {\n    if (!article?.content) return;\n    const result = await exportService.copyToClipboard(article.content);\n    exportService.showMessage(result.message, result.success ? 'success' : 'error');\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n\n\n  // Navigation functions\n  const goBack = () => {\n    if (currentView === 'article') {\n      setCurrentView('tree');\n      if (activeTab) {\n        tabService.updateTabStatus(activeTab.id, activeTab.status, { article: null });\n        setActiveTab(tabService.getTab(activeTab.id));\n      }\n    } else if (currentView === 'tree') {\n      setCurrentView('input');\n    }\n  };\n\n\n\n  const goHome = () => {\n    setCurrentView('input');\n    setActiveTab(null);\n    setTopic('');\n    // Clear all tabs\n    tabService.clearAllTabs();\n  };\n\n  // Quick login for development\n  const quickLogin = () => {\n    localStorage.setItem('bypassSecurity', 'true');\n    setUser({ id: 'dev-1', name: 'Developer', subscriptionTier: 'premium' });\n  };\n\n  return (\n    <div className=\"app\" ref={appRef}>\n      {/* Header */}\n      <header className=\"app-header\">\n        <div className=\"header-content\">\n          <button onClick={goHome} className=\"logo-text\">\n            {t('appTitle')}\n          </button>\n          <div className=\"header-right\">\n            {user && (\n              <div id=\"gamification-container\" style={{ marginRight: '16px' }}>\n                {/* Gamification UI will be inserted here */}\n              </div>\n            )}\n            <LanguageSwitcher />\n            {!user ? (\n              <button onClick={quickLogin} className=\"btn btn-primary\" style={{ marginLeft: '12px' }}>\n                {t('quickLogin')}\n              </button>\n            ) : (\n              <span style={{ marginLeft: '12px' }}>{t('welcome')}, {user.name}!</span>\n            )}\n          </div>\n        </div>\n      </header>\n\n      {/* Tab Manager */}\n      {user && (\n        <TabManager\n          onTabChange={handleTabChange}\n          onNewTab={handleNewTab}\n          onTabArticleAccess={handleTabArticleAccess}\n        />\n      )}\n\n      {/* Main Content */}\n      <main className=\"main-content\">\n        {error && (\n          <div className=\"error\">\n            ⚠️ {error}\n            <button onClick={() => setError(null)} style={{marginLeft: 'auto', background: 'none', border: 'none', color: 'white', cursor: 'pointer'}}>\n              ✕\n            </button>\n          </div>\n        )}\n\n        {/* Topic Input View */}\n        {currentView === 'input' && (\n          <div className=\"card text-center\">\n            <h1 className=\"title\">{t('appTitle')}</h1>\n            <p className=\"subtitle\">\n              Enter any topic to generate an interactive knowledge tree with AI-powered content.\n            </p>\n\n            {!user ? (\n              <div style={{background: '#f1f5f9', padding: '1rem', borderRadius: '0.5rem', marginBottom: '2rem'}}>\n                <p style={{color: '#334155', marginBottom: '1rem'}}>\n                  {t('loginRequired')}\n                </p>\n                <button onClick={quickLogin} className=\"btn btn-primary\">\n                  {t('quickLoginDev')}\n                </button>\n              </div>\n            ) : (\n              <form onSubmit={handleSubmit}>\n                <div className=\"form-group\">\n                  <input\n                    type=\"text\"\n                    value={topic}\n                    onChange={(e) => setTopic(e.target.value)}\n                    placeholder={t('topicPlaceholder')}\n                    className=\"form-input\"\n                    disabled={isLoading}\n                  />\n                </div>\n                <button\n                  type=\"submit\"\n                  disabled={isLoading || !topic.trim()}\n                  className=\"btn btn-primary\"\n                >\n                  {isLoading ? (\n                    <>\n                      <span className=\"spinner\"></span>\n                      {t('generating')}\n                    </>\n                  ) : (\n                    <>\n                      {t('exploreKnowledge')}\n                    </>\n                  )}\n                </button>\n              </form>\n            )}\n          </div>\n        )}\n\n        {/* Tree View */}\n        {currentView === 'tree' && tree && (\n          <div className=\"tree-container\">\n            <div className=\"tree-header\">\n              <h1>{tree.tema}</h1>\n              <p>{t('selectBranch')}</p>\n              <button onClick={goBack} className=\"btn btn-secondary\" style={{marginTop: '1rem'}}>\n                {t('backToTree')}\n              </button>\n            </div>\n\n            {isLoading ? (\n              <div className=\"loading\">\n                <span className=\"spinner\"></span>\n                <span>{t('loading')}</span>\n              </div>\n            ) : (\n              <div className=\"branches-grid\">\n                {tree.ramuri.map((branch, index) => (\n                  <div\n                    key={index}\n                    className={`branch-item ${selectedBranch === branch ? 'selected' : ''}`}\n                    data-index={index}\n                    data-name={branch.nume}\n                    data-description={branch.descriere}\n                    data-is-sub-branch={branch.isSubBranch || false}\n                    data-level={branch.level || 0}\n                    onClick={() => handleBranchSelect(branch)}\n                  >\n                    <div className=\"branch-emoji\">{branch.emoji}</div>\n                    <h3 className=\"branch-name\">{branch.nume}</h3>\n                    <p className=\"branch-description\">{branch.descriere}</p>\n                    {branch.subcategorii && (\n                      <div style={{fontSize: '0.875rem', color: '#475569', marginTop: '0.5rem'}}>\n                        {t('topics')}: {branch.subcategorii.slice(0, 3).join(', ')}\n                        {branch.subcategorii.length > 3 && '...'}\n                      </div>\n                    )}\n                    <div className=\"gesture-hint\" style={{\n                      fontSize: '0.75rem',\n                      color: '#64748b',\n                      marginTop: '0.5rem',\n                      fontStyle: 'italic'\n                    }}>\n                      {t('gestureHint')}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Article View */}\n        {currentView === 'article' && article && (\n          <div className=\"tree-container\">\n            <div className=\"card\">\n              <div className=\"article-header\" style={{marginBottom: '2rem'}}>\n                <button onClick={goBack} className=\"btn btn-secondary\">\n                  {t('backToTree')}\n                </button>\n\n                {/* Article Controls */}\n                <div className=\"article-controls\" style={{\n                  display: 'flex',\n                  gap: '8px',\n                  marginTop: '1rem',\n                  flexWrap: 'wrap'\n                }}>\n                  {/* Speech Controls */}\n                  <div className=\"speech-controls-compact\" style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '8px',\n                    padding: '8px 12px',\n                    background: '#f1f5f9',\n                    borderRadius: '6px',\n                    border: '1px solid #e2e8f0'\n                  }}>\n                    <button\n                      onClick={handleSpeechToggle}\n                      className=\"btn-icon\"\n                      title=\"Play/Pause Speech\"\n                      style={{\n                        background: 'none',\n                        border: 'none',\n                        fontSize: '16px',\n                        cursor: 'pointer',\n                        padding: '4px'\n                      }}\n                    >\n                      {speechService.getStatus().isPlaying ? '⏸️' : '▶️'}\n                    </button>\n                    <button\n                      onClick={handleSpeechStop}\n                      className=\"btn-icon\"\n                      title=\"Stop Speech\"\n                      style={{\n                        background: 'none',\n                        border: 'none',\n                        fontSize: '16px',\n                        cursor: 'pointer',\n                        padding: '4px'\n                      }}\n                    >\n                      ⏹️\n                    </button>\n                    <input\n                      type=\"range\"\n                      min=\"0.5\"\n                      max=\"2\"\n                      step=\"0.1\"\n                      defaultValue=\"1\"\n                      onChange={(e) => handleSpeechRateChange(parseFloat(e.target.value))}\n                      style={{width: '60px'}}\n                      title=\"Speech Speed\"\n                    />\n                    <span style={{fontSize: '12px', color: '#64748b'}}>🗣️</span>\n                  </div>\n\n                  {/* Export Controls */}\n                  <div className=\"export-controls-compact\" style={{\n                    display: 'flex',\n                    gap: '4px'\n                  }}>\n                    <button\n                      onClick={handleCopyToClipboard}\n                      className=\"btn btn-secondary\"\n                      style={{padding: '6px 12px', fontSize: '12px'}}\n                      title=\"Copy to Clipboard\"\n                    >\n                      📋 Copy\n                    </button>\n                    <button\n                      onClick={handleExportPDF}\n                      className=\"btn btn-secondary\"\n                      style={{padding: '6px 12px', fontSize: '12px'}}\n                      title=\"Export as PDF\"\n                    >\n                      📄 PDF\n                    </button>\n                    <button\n                      onClick={handleExportWord}\n                      className=\"btn btn-secondary\"\n                      style={{padding: '6px 12px', fontSize: '12px'}}\n                      title=\"Export as Word\"\n                    >\n                      📝 Word\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              <h1 className=\"title\">{article?.title || 'Loading...'}</h1>\n              <div style={{color: '#475569', marginBottom: '2rem', fontSize: '0.9rem'}}>\n                <span>{t('partOf')}: {article?.topic || 'Unknown'}</span>\n                {article?.flags && article.flags.length > 0 && (\n                  <span style={{marginLeft: '16px'}}>\n                    {t('flags')}: {article.flags.join(', ')}\n                  </span>\n                )}\n              </div>\n\n              <div className=\"article-content\" style={{lineHeight: '1.8', fontSize: '1.1rem'}}>\n                {article?.content ? article.content.split('\\n').map((paragraph, index) => (\n                  paragraph.trim() && (\n                    <p key={index} style={{marginBottom: '1rem'}}>\n                      {paragraph}\n                    </p>\n                  )\n                )) : (\n                  <p>Loading article content...</p>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n      </main>\n    </div>\n  );\n};\n\nexport default OptimizedApp;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,yBAAyB;AAChC,OAAOC,cAAc,IAAIC,eAAe,QAAQ,4BAA4B;AAC5E,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,mBAAmB,MAAM,0CAA0C;AAC1E,SAASC,qBAAqB,IAAIC,eAAe,EAAEC,eAAe,IAAIC,kBAAkB,EAAEC,cAAc,QAAQ,+BAA+B;AAC/I,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,cAAc,EAAEC,kBAAkB,QAAQ,eAAe;;AAElE;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEA,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACzD,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgC,KAAK,EAAEC,QAAQ,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACkC,IAAI,EAAEC,OAAO,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAMoC,MAAM,GAAGlC,MAAM,CAAC,IAAI,CAAC;;EAE3B;EACA,MAAMmC,IAAI,GAAG,CAAAT,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAES,IAAI,KAAI,IAAI;EACpC,MAAMC,cAAc,GAAG,CAAAV,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEU,cAAc,KAAI,IAAI;EACxD,MAAMC,OAAO,GAAG,CAAAX,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEW,OAAO,KAAI,IAAI;;EAE1C;EACA,MAAM;IAAEC;EAAE,CAAC,GAAGxB,cAAc,CAAC,CAAC;;EAE9B;EACA,MAAMyB,cAAc,GAAG1C,KAAK,CAAC2C,OAAO,CAAC,MAAM,CACzC;IAAEC,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,SAAS;IAAEC,WAAW,EAAEL,CAAC,CAAC,aAAa;EAAE,CAAC,EAC9D;IAAEG,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAE,UAAU;IAAEC,WAAW,EAAEL,CAAC,CAAC,cAAc;EAAE,CAAC,EACjE;IAAEG,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,MAAM;IAAEC,WAAW,EAAEL,CAAC,CAAC,UAAU;EAAE,CAAC,EACxD;IAAEG,IAAI,EAAE,MAAM;IAAEC,IAAI,EAAE,QAAQ;IAAEC,WAAW,EAAEL,CAAC,CAAC,YAAY;EAAE,CAAC,EAC9D;IAAEG,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,eAAe;IAAEC,WAAW,EAAEL,CAAC,CAAC,UAAU;EAAE,CAAC,EACpE;IAAEG,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,YAAY;IAAEC,WAAW,EAAEL,CAAC,CAAC,UAAU;EAAE,CAAC,EACjE;IAAEG,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAE,UAAU;IAAEC,WAAW,EAAEL,CAAC,CAAC,cAAc;EAAE,CAAC,CAClE,EAAE,CAACA,CAAC,CAAC,CAAC;;EAEP;EACA,MAAMM,wBAAwB,GAAG/C,KAAK,CAACgD,WAAW,CAAC,OAAOC,MAAM,EAAEC,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK;IACnF,IAAI,CAACrB,SAAS,EAAE;IAEhBG,YAAY,CAAC,IAAI,CAAC;;IAElB;IACAlB,UAAU,CAACqC,eAAe,CAACtB,SAAS,CAACuB,EAAE,EAAE,SAAS,EAAE;MAClDb,cAAc,EAAEU,MAAM;MACtBT,OAAO,EAAE;IACX,CAAC,CAAC;IACFV,YAAY,CAAChB,UAAU,CAACuC,MAAM,CAACxB,SAAS,CAACuB,EAAE,CAAC,CAAC;IAE7C,IAAI;MACFE,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEN,MAAM,CAACO,IAAI,CAAC;MAC7D,MAAMC,WAAW,GAAG,MAAM7C,kBAAkB,CAACiB,SAAS,CAACF,KAAK,EAAEsB,MAAM,EAAEC,KAAK,CAAC;;MAE5E;MACApC,UAAU,CAACqC,eAAe,CAACtB,SAAS,CAACuB,EAAE,EAAE,WAAW,EAAE;QACpDb,cAAc,EAAEU,MAAM;QACtBT,OAAO,EAAEiB;MACX,CAAC,CAAC;MAEF,MAAMC,UAAU,GAAG5C,UAAU,CAACuC,MAAM,CAACxB,SAAS,CAACuB,EAAE,CAAC;MAClDtB,YAAY,CAAC4B,UAAU,CAAC;MACxBhC,cAAc,CAAC,SAAS,CAAC;;MAEzB;MACA,MAAMiC,MAAM,GAAGnD,mBAAmB,CAACoD,WAAW,CAAC,mBAAmB,CAAC;MACnE,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5CxD,mBAAmB,CAACyD,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAO/B,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDC,QAAQ,CAAC,+CAA+C,CAAC;;MAEzD;MACApB,UAAU,CAACqC,eAAe,CAACtB,SAAS,CAACuB,EAAE,EAAE,SAAS,EAAE;QAClDb,cAAc,EAAEU,MAAM;QACtBT,OAAO,EAAE;MACX,CAAC,CAAC;MACFV,YAAY,CAAChB,UAAU,CAACuC,MAAM,CAACxB,SAAS,CAACuB,EAAE,CAAC,CAAC;IAC/C,CAAC,SAAS;MACRpB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACH,SAAS,CAAC,CAAC;;EAEf;EACA,MAAMqC,kBAAkB,GAAGlE,KAAK,CAACgD,WAAW,CAAEC,MAAM,IAAK;IACvD,IAAIpB,SAAS,EAAE;MACbf,UAAU,CAACqC,eAAe,CAACtB,SAAS,CAACuB,EAAE,EAAEvB,SAAS,CAACsC,MAAM,EAAE;QAAE5B,cAAc,EAAEU;MAAO,CAAC,CAAC;MACtFnB,YAAY,CAAChB,UAAU,CAACuC,MAAM,CAACxB,SAAS,CAACuB,EAAE,CAAC,CAAC;IAC/C;EACF,CAAC,EAAE,CAACvB,SAAS,CAAC,CAAC;;EAEf;EACA,MAAMuC,eAAe,GAAGpE,KAAK,CAACgD,WAAW,CAAC,CAACqB,KAAK,EAAEC,UAAU,KAAK;IAC/D,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,IAAIlC,IAAI,EAAE;MAC5D;MACA,MAAMW,MAAM,GAAGX,IAAI,CAACmC,MAAM,CAACH,UAAU,CAACE,UAAU,CAACE,KAAK,CAAC;MACvD,IAAIzB,MAAM,EAAE;QACV5C,eAAe,CACbiE,UAAU,CAACK,QAAQ,EACnBjC,cAAc,EACbkC,aAAa,IAAK;UACjBtB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEqB,aAAa,CAAC;QAC/C,CAAC,EACAA,aAAa,IAAK;UACjB7B,wBAAwB,CAACE,MAAM,EAAE2B,aAAa,CAAC;QACjD,CACF,CAAC;MACH;IACF;EACF,CAAC,EAAE,CAACtC,IAAI,EAAEI,cAAc,EAAEK,wBAAwB,CAAC,CAAC;EAEpD,MAAM8B,eAAe,GAAG7E,KAAK,CAACgD,WAAW,CAAC,CAACqB,KAAK,EAAEC,UAAU,KAAK;IAC/D;IACA,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,IAAIlC,IAAI,EAAE;MAC5D,MAAMW,MAAM,GAAGX,IAAI,CAACmC,MAAM,CAACH,UAAU,CAACE,UAAU,CAACE,KAAK,CAAC;MACvD,IAAIzB,MAAM,EAAE;QACViB,kBAAkB,CAACjB,MAAM,CAAC;MAC5B;IACF;EACF,CAAC,EAAE,CAACX,IAAI,EAAE4B,kBAAkB,CAAC,CAAC;;EAE9B;EACA,MAAMY,YAAY,GAAG9E,KAAK,CAACgD,WAAW,CAAC,OAAOC,MAAM,EAAE8B,WAAW,KAAK;IACpE,IAAI,CAAClD,SAAS,IAAI,CAACS,IAAI,EAAE;MACvBJ,QAAQ,CAACO,CAAC,CAAC,aAAa,CAAC,IAAI,iCAAiC,CAAC;MAC/D;IACF;IAEAT,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MAAA,IAAA8C,cAAA,EAAAC,qBAAA;MACF3B,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEN,MAAM,CAACO,IAAI,CAAC;;MAEhD;MACA,MAAM0B,YAAY,GAAG,qBAAqBjC,MAAM,CAACO,IAAI,0BAA0BlB,IAAI,CAAC6C,IAAI,IAAItD,SAAS,CAACF,KAAK;AACjH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mDAAmDsB,MAAM,CAACO,IAAI,UAAUtC,kBAAkB,CAAC,CAAC,KAAK,IAAI,GAAG,UAAU,GAAG,SAAS,YAAY;MAEpI,MAAMkE,QAAQ,GAAG,MAAMC,KAAK,CAAC,+CAA+C,EAAE;QAC5EC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,eAAe,EAAE,kFAAkF;UACnG,cAAc,EAAE,kBAAkB;UAClC,cAAc,EAAEC,MAAM,CAACC,QAAQ,CAACC,MAAM;UACtC,SAAS,EAAE;QACb,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,KAAK,EAAE,gCAAgC;UACvCC,QAAQ,EAAE,CACR;YACEC,IAAI,EAAE,QAAQ;YACdC,OAAO,EAAE;UACX,CAAC,EACD;YACED,IAAI,EAAE,MAAM;YACZC,OAAO,EAAEf;UACX,CAAC,CACF;UACDgB,WAAW,EAAE,GAAG;UAChBC,UAAU,EAAE;QACd,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACf,QAAQ,CAACgB,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAMjB,QAAQ,CAACkB,IAAI,CAAC,CAAC;QACvChD,OAAO,CAACrB,KAAK,CAAC,qBAAqB,EAAEoE,SAAS,CAAC;QAC/C,MAAM,IAAIE,KAAK,CAAC,cAAcnB,QAAQ,CAACjB,MAAM,MAAMkC,SAAS,EAAE,CAAC;MACjE;MAEA,MAAMG,IAAI,GAAG,MAAMpB,QAAQ,CAACqB,IAAI,CAAC,CAAC;MAClC,MAAMR,OAAO,IAAAjB,cAAA,GAAGwB,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,cAAA1B,cAAA,wBAAAC,qBAAA,GAAfD,cAAA,CAAiB2B,OAAO,cAAA1B,qBAAA,uBAAxBA,qBAAA,CAA0BgB,OAAO;MAEjD,IAAI,CAACA,OAAO,EAAE;QACZ,MAAM,IAAIM,KAAK,CAAC,8BAA8B,CAAC;MACjD;MAEAjD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE0C,OAAO,CAAC;;MAE5C;MACA,MAAMW,SAAS,GAAGX,OAAO,CAACY,KAAK,CAAC,aAAa,CAAC;MAC9C,IAAI,CAACD,SAAS,EAAE;QACd,MAAM,IAAIL,KAAK,CAAC,iCAAiC,CAAC;MACpD;MAEA,MAAMO,YAAY,GAAGlB,IAAI,CAACmB,KAAK,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC;MAE7C,IAAI,CAACE,YAAY,CAACrC,MAAM,IAAI,CAACuC,KAAK,CAACC,OAAO,CAACH,YAAY,CAACrC,MAAM,CAAC,EAAE;QAC/D,MAAM,IAAI8B,KAAK,CAAC,iCAAiC,CAAC;MACpD;MAEAjD,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEuD,YAAY,CAAC;;MAEpD;MACA,MAAMI,OAAO,GAAG;QAAE,GAAG5E;MAAK,CAAC;MAC3B4E,OAAO,CAACzC,MAAM,GAAG,CACf,GAAGyC,OAAO,CAACzC,MAAM,CAAC0C,KAAK,CAAC,CAAC,EAAEpC,WAAW,GAAG,CAAC,CAAC,EAC3C,GAAG+B,YAAY,CAACrC,MAAM,CAAC2C,GAAG,CAACC,SAAS,KAAK;QACvC,GAAGA,SAAS;QACZC,WAAW,EAAE,IAAI;QACjBC,YAAY,EAAEtE,MAAM,CAACO,IAAI;QACzBgE,KAAK,EAAE,CAACvE,MAAM,CAACuE,KAAK,IAAI,CAAC,IAAI;MAC/B,CAAC,CAAC,CAAC,EACH,GAAGN,OAAO,CAACzC,MAAM,CAAC0C,KAAK,CAACpC,WAAW,GAAG,CAAC,CAAC,CACzC;;MAED;MACAjE,UAAU,CAACqC,eAAe,CAACtB,SAAS,CAACuB,EAAE,EAAE,WAAW,EAAE;QAAEd,IAAI,EAAE4E;MAAQ,CAAC,CAAC;MACxEpF,YAAY,CAAChB,UAAU,CAACuC,MAAM,CAACxB,SAAS,CAACuB,EAAE,CAAC,CAAC;MAE7CE,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;;MAE5C;MACA,MAAMI,MAAM,GAAGnD,mBAAmB,CAACoD,WAAW,CAAC,iBAAiB,CAAC;MACjE,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5CxD,mBAAmB,CAACyD,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IAEF,CAAC,CAAC,OAAO/B,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDC,QAAQ,CAACO,CAAC,CAAC,gBAAgB,CAAC,IAAI,2DAA2D,CAAC;IAC9F,CAAC,SAAS;MACRT,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACM,IAAI,EAAET,SAAS,EAAEY,CAAC,CAAC,CAAC;EAExB,MAAMgF,eAAe,GAAGzH,KAAK,CAACgD,WAAW,CAAC,OAAOqB,KAAK,EAAEC,UAAU,KAAK;IACrE;IACA,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,IAAIlC,IAAI,EAAE;MAC5D,MAAMW,MAAM,GAAGX,IAAI,CAACmC,MAAM,CAACH,UAAU,CAACE,UAAU,CAACE,KAAK,CAAC;MACvD,IAAIzB,MAAM,EAAE;QACV,MAAM6B,YAAY,CAAC7B,MAAM,EAAEqB,UAAU,CAACE,UAAU,CAACE,KAAK,CAAC;MACzD;IACF;EACF,CAAC,EAAE,CAACpC,IAAI,EAAEwC,YAAY,CAAC,CAAC;;EAExB;EACA5E,SAAS,CAAC,MAAM;IACd,MAAMwH,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAC/C,MAAMC,cAAc,GAAGF,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAE7D,IAAIF,UAAU,IAAIG,cAAc,EAAE;MAChC,MAAMC,QAAQ,GAAG;QACf1E,EAAE,EAAE,QAAQ;QACZP,IAAI,EAAE,MAAM;QACZkF,gBAAgB,EAAE;MACpB,CAAC;MACD3F,OAAO,CAAC0F,QAAQ,CAAC;;MAEjB;MACA,MAAMnE,MAAM,GAAGnD,mBAAmB,CAACoD,WAAW,CAAC,aAAa,CAAC;MAC7D,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5CxD,mBAAmB,CAACyD,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;;IAEA;IACA,IAAI3B,MAAM,CAAC2F,OAAO,EAAE;MAClB5H,cAAc,CAAC6H,IAAI,CAAC5F,MAAM,CAAC2F,OAAO,EAAE;QAClCE,SAAS,EAAE9D,eAAe;QAC1B+D,SAAS,EAAEtD,eAAe;QAC1BuD,SAAS,EAAEX;MACb,CAAC,CAAC;IACJ;IAEA,OAAO,MAAM;MACXrH,cAAc,CAACiI,OAAO,CAAC,CAAC;IAC1B,CAAC;EACH,CAAC,EAAE,CAACjE,eAAe,EAAES,eAAe,EAAE4C,eAAe,CAAC,CAAC;;EAEvD;EACAvH,SAAS,CAAC,MAAM;IACd,IAAIiC,IAAI,EAAE;MACR,MAAMmG,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,wBAAwB,CAAC;MACnE,IAAIF,SAAS,EAAE;QACb;QACAA,SAAS,CAACG,SAAS,GAAG,EAAE;QACxB;QACAjI,mBAAmB,CAACkI,oBAAoB,CAACJ,SAAS,CAAC;MACrD;;MAEA;MACAzH,cAAc,CAAC,CAAC,CAAC8H,IAAI,CAACC,WAAW,IAAI;QACnCtF,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEqF,WAAW,GAAG,aAAa,GAAG,UAAU,CAAC;QAClF,IAAI,CAACA,WAAW,EAAE;UAChBtF,OAAO,CAACuF,IAAI,CAAC,uEAAuE,CAAC;QACvF;MACF,CAAC,CAAC,CAACC,KAAK,CAAC7G,KAAK,IAAI;QAChBqB,OAAO,CAACrB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACE,IAAI,CAAC,CAAC;;EAEV;EACA,MAAM1B,qBAAqB,GAAG,MAAAA,CAAOsI,UAAU,EAAEC,KAAK,GAAG,IAAI,KAAK;IAChE,IAAIC,YAAY,GAAGD,KAAK;;IAExB;IACA,IAAI,CAACC,YAAY,EAAE;MACjB,IAAI;QACF,MAAMC,MAAM,GAAGpI,UAAU,CAACqI,SAAS,CAACJ,UAAU,CAAC;QAC/CE,YAAY,GAAGC,MAAM,CAAC9F,EAAE;QACxBtB,YAAY,CAACoH,MAAM,CAAC;QACpBxH,cAAc,CAAC,MAAM,CAAC;MACxB,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,QAAQ,CAACD,KAAK,CAAC0E,OAAO,CAAC;QACvB;MACF;IACF;;IAEA;IACA7F,UAAU,CAACqC,eAAe,CAAC8F,YAAY,EAAE,YAAY,EAAE;MAAEG,QAAQ,EAAE;IAAG,CAAC,CAAC;IACxEpH,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACFoB,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEwF,UAAU,EAAE,SAAS,EAAEE,YAAY,CAAC;;MAErF;MACAnI,UAAU,CAACqC,eAAe,CAAC8F,YAAY,EAAE,YAAY,EAAE;QAAEG,QAAQ,EAAE;MAAG,CAAC,CAAC;MAExE,MAAMC,QAAQ,GAAG,MAAM3I,eAAe,CAACqI,UAAU,EAAE7H,kBAAkB,CAAC,CAAC,CAAC;MACxEoC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE8F,QAAQ,CAAC;;MAE/C;MACAvI,UAAU,CAACqC,eAAe,CAAC8F,YAAY,EAAE,WAAW,EAAE;QACpD3G,IAAI,EAAE+G,QAAQ;QACdD,QAAQ,EAAE;MACZ,CAAC,CAAC;;MAEF;MACA,IAAIH,YAAY,MAAKpH,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEuB,EAAE,GAAE;QAClCtB,YAAY,CAAChB,UAAU,CAACuC,MAAM,CAAC4F,YAAY,CAAC,CAAC;MAC/C;;MAEA;MACA,MAAMtF,MAAM,GAAGnD,mBAAmB,CAACoD,WAAW,CAAC,gBAAgB,CAAC;MAChE,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5CxD,mBAAmB,CAACyD,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOsF,GAAG,EAAE;MACZhG,OAAO,CAACrB,KAAK,CAAC,0BAA0B,EAAEqH,GAAG,CAAC;MAC9CxI,UAAU,CAACqC,eAAe,CAAC8F,YAAY,EAAE,OAAO,CAAC;MACjD/G,QAAQ,CAAC,sCAAsCoH,GAAG,CAAC3C,OAAO,qBAAqB,CAAC;IAClF,CAAC,SAAS;MACR3E,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAID;EACA,MAAMuH,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI9H,KAAK,CAAC+H,IAAI,CAAC,CAAC,EAAE;MAChBjJ,qBAAqB,CAACkB,KAAK,CAAC+H,IAAI,CAAC,CAAC,CAAC;MACnC9H,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB;EACF,CAAC;;EAED;EACA,MAAM+H,eAAe,GAAIC,GAAG,IAAK;IAC/B;IACA1H,QAAQ,CAAC,IAAI,CAAC;IACdF,YAAY,CAAC,KAAK,CAAC;IAEnBF,YAAY,CAAC8H,GAAG,CAAC;IACjB,IAAIA,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAEtH,IAAI,EAAE;MACbZ,cAAc,CAAC,MAAM,CAAC;IACxB,CAAC,MAAM;MACLA,cAAc,CAAC,OAAO,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMmI,YAAY,GAAGA,CAAA,KAAM;IACzB;IACA3H,QAAQ,CAAC,IAAI,CAAC;IACdF,YAAY,CAAC,KAAK,CAAC;IAEnBN,cAAc,CAAC,OAAO,CAAC;IACvBI,YAAY,CAAC,IAAI,CAAC;IAClBF,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAMkI,sBAAsB,GAAIF,GAAG,IAAK;IACtC;IACA1H,QAAQ,CAAC,IAAI,CAAC;IACdF,YAAY,CAAC,KAAK,CAAC;IAEnBF,YAAY,CAAC8H,GAAG,CAAC;IACjBlI,cAAc,CAAC,SAAS,CAAC;EAC3B,CAAC;;EAID;EACA,MAAMqI,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI,EAACvH,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEyD,OAAO,GAAE;IAEvB,IAAI3F,aAAa,CAAC0J,SAAS,CAAC,CAAC,CAACC,SAAS,EAAE;MACvC3J,aAAa,CAAC4J,MAAM,CAAC,CAAC;IACxB,CAAC,MAAM;MACL5J,aAAa,CAAC6J,KAAK,CAAC3H,OAAO,CAACyD,OAAO,CAAC;MACpC;MACA,MAAMtC,MAAM,GAAGnD,mBAAmB,CAACoD,WAAW,CAAC,aAAa,CAAC;MAC7D,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5CxD,mBAAmB,CAACyD,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAMoG,gBAAgB,GAAGA,CAAA,KAAM;IAC7B9J,aAAa,CAAC+J,IAAI,CAAC,CAAC;EACtB,CAAC;EAED,MAAMC,sBAAsB,GAAIC,IAAI,IAAK;IACvCjK,aAAa,CAACkK,OAAO,CAACD,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,EAACjI,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEkI,KAAK,KAAI,EAAClI,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEyD,OAAO,GAAE;IAC1C,MAAMtC,MAAM,GAAGpD,aAAa,CAACoK,WAAW,CAACnI,OAAO,EAAE,GAAGA,OAAO,CAACkI,KAAK,CAACE,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,EAAE,CAAC;IACnG,IAAIjH,MAAM,CAACkH,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAGtK,mBAAmB,CAACoD,WAAW,CAAC,aAAa,CAAC;MAChE,IAAIkH,SAAS,CAACjH,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxCgH,SAAS,CAACjH,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/CxD,mBAAmB,CAACyD,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAM+G,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,EAACvI,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEkI,KAAK,KAAI,EAAClI,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEyD,OAAO,GAAE;IAC1C,MAAMtC,MAAM,GAAGpD,aAAa,CAACyK,YAAY,CAACxI,OAAO,EAAE,GAAGA,OAAO,CAACkI,KAAK,CAACE,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,EAAE,CAAC;IACpG,IAAIjH,MAAM,CAACkH,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAGtK,mBAAmB,CAACoD,WAAW,CAAC,aAAa,CAAC;MAChE,IAAIkH,SAAS,CAACjH,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxCgH,SAAS,CAACjH,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/CxD,mBAAmB,CAACyD,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAMiH,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI,EAACzI,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEyD,OAAO,GAAE;IACvB,MAAMtC,MAAM,GAAG,MAAMpD,aAAa,CAAC2K,eAAe,CAAC1I,OAAO,CAACyD,OAAO,CAAC;IACnE1F,aAAa,CAAC4K,WAAW,CAACxH,MAAM,CAACgD,OAAO,EAAEhD,MAAM,CAACkH,OAAO,GAAG,SAAS,GAAG,OAAO,CAAC;IAC/E,IAAIlH,MAAM,CAACkH,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAGtK,mBAAmB,CAACoD,WAAW,CAAC,aAAa,CAAC;MAChE,IAAIkH,SAAS,CAACjH,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxCgH,SAAS,CAACjH,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/CxD,mBAAmB,CAACyD,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;;EAID;EACA,MAAMoH,MAAM,GAAGA,CAAA,KAAM;IACnB,IAAI3J,WAAW,KAAK,SAAS,EAAE;MAC7BC,cAAc,CAAC,MAAM,CAAC;MACtB,IAAIG,SAAS,EAAE;QACbf,UAAU,CAACqC,eAAe,CAACtB,SAAS,CAACuB,EAAE,EAAEvB,SAAS,CAACsC,MAAM,EAAE;UAAE3B,OAAO,EAAE;QAAK,CAAC,CAAC;QAC7EV,YAAY,CAAChB,UAAU,CAACuC,MAAM,CAACxB,SAAS,CAACuB,EAAE,CAAC,CAAC;MAC/C;IACF,CAAC,MAAM,IAAI3B,WAAW,KAAK,MAAM,EAAE;MACjCC,cAAc,CAAC,OAAO,CAAC;IACzB;EACF,CAAC;EAID,MAAM2J,MAAM,GAAGA,CAAA,KAAM;IACnB3J,cAAc,CAAC,OAAO,CAAC;IACvBI,YAAY,CAAC,IAAI,CAAC;IAClBF,QAAQ,CAAC,EAAE,CAAC;IACZ;IACAd,UAAU,CAACwK,YAAY,CAAC,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB5D,YAAY,CAAC6D,OAAO,CAAC,gBAAgB,EAAE,MAAM,CAAC;IAC9CpJ,OAAO,CAAC;MAAEgB,EAAE,EAAE,OAAO;MAAEP,IAAI,EAAE,WAAW;MAAEkF,gBAAgB,EAAE;IAAU,CAAC,CAAC;EAC1E,CAAC;EAED,oBACE3G,OAAA;IAAKqK,SAAS,EAAC,KAAK;IAACC,GAAG,EAAErJ,MAAO;IAAAsJ,QAAA,gBAE/BvK,OAAA;MAAQqK,SAAS,EAAC,YAAY;MAAAE,QAAA,eAC5BvK,OAAA;QAAKqK,SAAS,EAAC,gBAAgB;QAAAE,QAAA,gBAC7BvK,OAAA;UAAQwK,OAAO,EAAEP,MAAO;UAACI,SAAS,EAAC,WAAW;UAAAE,QAAA,EAC3ClJ,CAAC,CAAC,UAAU;QAAC;UAAAoJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACT5K,OAAA;UAAKqK,SAAS,EAAC,cAAc;UAAAE,QAAA,GAC1BxJ,IAAI,iBACHf,OAAA;YAAKgC,EAAE,EAAC,wBAAwB;YAAC6I,KAAK,EAAE;cAAEC,WAAW,EAAE;YAAO;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE3D,CACN,eACD5K,OAAA,CAACJ,gBAAgB;YAAA6K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACnB,CAAC7J,IAAI,gBACJf,OAAA;YAAQwK,OAAO,EAAEL,UAAW;YAACE,SAAS,EAAC,iBAAiB;YAACQ,KAAK,EAAE;cAAEE,UAAU,EAAE;YAAO,CAAE;YAAAR,QAAA,EACpFlJ,CAAC,CAAC,YAAY;UAAC;YAAAoJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,gBAET5K,OAAA;YAAM6K,KAAK,EAAE;cAAEE,UAAU,EAAE;YAAO,CAAE;YAAAR,QAAA,GAAElJ,CAAC,CAAC,SAAS,CAAC,EAAC,IAAE,EAACN,IAAI,CAACU,IAAI,EAAC,GAAC;UAAA;YAAAgJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACxE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAGR7J,IAAI,iBACHf,OAAA,CAACL,UAAU;MACTqL,WAAW,EAAEzC,eAAgB;MAC7B0C,QAAQ,EAAExC,YAAa;MACvByC,kBAAkB,EAAExC;IAAuB;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CACF,eAGD5K,OAAA;MAAMqK,SAAS,EAAC,cAAc;MAAAE,QAAA,GAC3B1J,KAAK,iBACJb,OAAA;QAAKqK,SAAS,EAAC,OAAO;QAAAE,QAAA,GAAC,eAClB,EAAC1J,KAAK,eACTb,OAAA;UAAQwK,OAAO,EAAEA,CAAA,KAAM1J,QAAQ,CAAC,IAAI,CAAE;UAAC+J,KAAK,EAAE;YAACE,UAAU,EAAE,MAAM;YAAEI,UAAU,EAAE,MAAM;YAAEC,MAAM,EAAE,MAAM;YAAEC,KAAK,EAAE,OAAO;YAAEC,MAAM,EAAE;UAAS,CAAE;UAAAf,QAAA,EAAC;QAE3I;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAGAvK,WAAW,KAAK,OAAO,iBACtBL,OAAA;QAAKqK,SAAS,EAAC,kBAAkB;QAAAE,QAAA,gBAC/BvK,OAAA;UAAIqK,SAAS,EAAC,OAAO;UAAAE,QAAA,EAAElJ,CAAC,CAAC,UAAU;QAAC;UAAAoJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1C5K,OAAA;UAAGqK,SAAS,EAAC,UAAU;UAAAE,QAAA,EAAC;QAExB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAEH,CAAC7J,IAAI,gBACJf,OAAA;UAAK6K,KAAK,EAAE;YAACM,UAAU,EAAE,SAAS;YAAEI,OAAO,EAAE,MAAM;YAAEC,YAAY,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAM,CAAE;UAAAlB,QAAA,gBACjGvK,OAAA;YAAG6K,KAAK,EAAE;cAACQ,KAAK,EAAE,SAAS;cAAEI,YAAY,EAAE;YAAM,CAAE;YAAAlB,QAAA,EAChDlJ,CAAC,CAAC,eAAe;UAAC;YAAAoJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACJ5K,OAAA;YAAQwK,OAAO,EAAEL,UAAW;YAACE,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EACrDlJ,CAAC,CAAC,eAAe;UAAC;YAAAoJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAEN5K,OAAA;UAAM0L,QAAQ,EAAEvD,YAAa;UAAAoC,QAAA,gBAC3BvK,OAAA;YAAKqK,SAAS,EAAC,YAAY;YAAAE,QAAA,eACzBvK,OAAA;cACE2L,IAAI,EAAC,MAAM;cACXC,KAAK,EAAErL,KAAM;cACbsL,QAAQ,EAAGzD,CAAC,IAAK5H,QAAQ,CAAC4H,CAAC,CAAC0D,MAAM,CAACF,KAAK,CAAE;cAC1CG,WAAW,EAAE1K,CAAC,CAAC,kBAAkB,CAAE;cACnCgJ,SAAS,EAAC,YAAY;cACtB2B,QAAQ,EAAErL;YAAU;cAAA8J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN5K,OAAA;YACE2L,IAAI,EAAC,QAAQ;YACbK,QAAQ,EAAErL,SAAS,IAAI,CAACJ,KAAK,CAAC+H,IAAI,CAAC,CAAE;YACrC+B,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EAE1B5J,SAAS,gBACRX,OAAA,CAAAE,SAAA;cAAAqK,QAAA,gBACEvK,OAAA;gBAAMqK,SAAS,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAChCvJ,CAAC,CAAC,YAAY,CAAC;YAAA,eAChB,CAAC,gBAEHrB,OAAA,CAAAE,SAAA;cAAAqK,QAAA,EACGlJ,CAAC,CAAC,kBAAkB;YAAC,gBACtB;UACH;YAAAoJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGAvK,WAAW,KAAK,MAAM,IAAIa,IAAI,iBAC7BlB,OAAA;QAAKqK,SAAS,EAAC,gBAAgB;QAAAE,QAAA,gBAC7BvK,OAAA;UAAKqK,SAAS,EAAC,aAAa;UAAAE,QAAA,gBAC1BvK,OAAA;YAAAuK,QAAA,EAAKrJ,IAAI,CAAC6C;UAAI;YAAA0G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpB5K,OAAA;YAAAuK,QAAA,EAAIlJ,CAAC,CAAC,cAAc;UAAC;YAAAoJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1B5K,OAAA;YAAQwK,OAAO,EAAER,MAAO;YAACK,SAAS,EAAC,mBAAmB;YAACQ,KAAK,EAAE;cAACoB,SAAS,EAAE;YAAM,CAAE;YAAA1B,QAAA,EAC/ElJ,CAAC,CAAC,YAAY;UAAC;YAAAoJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAELjK,SAAS,gBACRX,OAAA;UAAKqK,SAAS,EAAC,SAAS;UAAAE,QAAA,gBACtBvK,OAAA;YAAMqK,SAAS,EAAC;UAAS;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjC5K,OAAA;YAAAuK,QAAA,EAAOlJ,CAAC,CAAC,SAAS;UAAC;YAAAoJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,gBAEN5K,OAAA;UAAKqK,SAAS,EAAC,eAAe;UAAAE,QAAA,EAC3BrJ,IAAI,CAACmC,MAAM,CAAC2C,GAAG,CAAC,CAACnE,MAAM,EAAEyB,KAAK,kBAC7BtD,OAAA;YAEEqK,SAAS,EAAE,eAAelJ,cAAc,KAAKU,MAAM,GAAG,UAAU,GAAG,EAAE,EAAG;YACxE,cAAYyB,KAAM;YAClB,aAAWzB,MAAM,CAACO,IAAK;YACvB,oBAAkBP,MAAM,CAACqK,SAAU;YACnC,sBAAoBrK,MAAM,CAACqE,WAAW,IAAI,KAAM;YAChD,cAAYrE,MAAM,CAACuE,KAAK,IAAI,CAAE;YAC9BoE,OAAO,EAAEA,CAAA,KAAM1H,kBAAkB,CAACjB,MAAM,CAAE;YAAA0I,QAAA,gBAE1CvK,OAAA;cAAKqK,SAAS,EAAC,cAAc;cAAAE,QAAA,EAAE1I,MAAM,CAACsK;YAAK;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClD5K,OAAA;cAAIqK,SAAS,EAAC,aAAa;cAAAE,QAAA,EAAE1I,MAAM,CAACO;YAAI;cAAAqI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9C5K,OAAA;cAAGqK,SAAS,EAAC,oBAAoB;cAAAE,QAAA,EAAE1I,MAAM,CAACqK;YAAS;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACvD/I,MAAM,CAACuK,YAAY,iBAClBpM,OAAA;cAAK6K,KAAK,EAAE;gBAACwB,QAAQ,EAAE,UAAU;gBAAEhB,KAAK,EAAE,SAAS;gBAAEY,SAAS,EAAE;cAAQ,CAAE;cAAA1B,QAAA,GACvElJ,CAAC,CAAC,QAAQ,CAAC,EAAC,IAAE,EAACQ,MAAM,CAACuK,YAAY,CAACrG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACuG,IAAI,CAAC,IAAI,CAAC,EACzDzK,MAAM,CAACuK,YAAY,CAAC1J,MAAM,GAAG,CAAC,IAAI,KAAK;YAAA;cAAA+H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CACN,eACD5K,OAAA;cAAKqK,SAAS,EAAC,cAAc;cAACQ,KAAK,EAAE;gBACnCwB,QAAQ,EAAE,SAAS;gBACnBhB,KAAK,EAAE,SAAS;gBAChBY,SAAS,EAAE,QAAQ;gBACnBM,SAAS,EAAE;cACb,CAAE;cAAAhC,QAAA,EACClJ,CAAC,CAAC,aAAa;YAAC;cAAAoJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC;UAAA,GAzBDtH,KAAK;YAAAmH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0BP,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGAvK,WAAW,KAAK,SAAS,IAAIe,OAAO,iBACnCpB,OAAA;QAAKqK,SAAS,EAAC,gBAAgB;QAAAE,QAAA,eAC7BvK,OAAA;UAAKqK,SAAS,EAAC,MAAM;UAAAE,QAAA,gBACnBvK,OAAA;YAAKqK,SAAS,EAAC,gBAAgB;YAACQ,KAAK,EAAE;cAACY,YAAY,EAAE;YAAM,CAAE;YAAAlB,QAAA,gBAC5DvK,OAAA;cAAQwK,OAAO,EAAER,MAAO;cAACK,SAAS,EAAC,mBAAmB;cAAAE,QAAA,EACnDlJ,CAAC,CAAC,YAAY;YAAC;cAAAoJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGT5K,OAAA;cAAKqK,SAAS,EAAC,kBAAkB;cAACQ,KAAK,EAAE;gBACvC2B,OAAO,EAAE,MAAM;gBACfC,GAAG,EAAE,KAAK;gBACVR,SAAS,EAAE,MAAM;gBACjBS,QAAQ,EAAE;cACZ,CAAE;cAAAnC,QAAA,gBAEAvK,OAAA;gBAAKqK,SAAS,EAAC,yBAAyB;gBAACQ,KAAK,EAAE;kBAC9C2B,OAAO,EAAE,MAAM;kBACfG,UAAU,EAAE,QAAQ;kBACpBF,GAAG,EAAE,KAAK;kBACVlB,OAAO,EAAE,UAAU;kBACnBJ,UAAU,EAAE,SAAS;kBACrBK,YAAY,EAAE,KAAK;kBACnBJ,MAAM,EAAE;gBACV,CAAE;gBAAAb,QAAA,gBACAvK,OAAA;kBACEwK,OAAO,EAAE7B,kBAAmB;kBAC5B0B,SAAS,EAAC,UAAU;kBACpBf,KAAK,EAAC,mBAAmB;kBACzBuB,KAAK,EAAE;oBACLM,UAAU,EAAE,MAAM;oBAClBC,MAAM,EAAE,MAAM;oBACdiB,QAAQ,EAAE,MAAM;oBAChBf,MAAM,EAAE,SAAS;oBACjBC,OAAO,EAAE;kBACX,CAAE;kBAAAhB,QAAA,EAEDrL,aAAa,CAAC0J,SAAS,CAAC,CAAC,CAACC,SAAS,GAAG,IAAI,GAAG;gBAAI;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,eACT5K,OAAA;kBACEwK,OAAO,EAAExB,gBAAiB;kBAC1BqB,SAAS,EAAC,UAAU;kBACpBf,KAAK,EAAC,aAAa;kBACnBuB,KAAK,EAAE;oBACLM,UAAU,EAAE,MAAM;oBAClBC,MAAM,EAAE,MAAM;oBACdiB,QAAQ,EAAE,MAAM;oBAChBf,MAAM,EAAE,SAAS;oBACjBC,OAAO,EAAE;kBACX,CAAE;kBAAAhB,QAAA,EACH;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT5K,OAAA;kBACE2L,IAAI,EAAC,OAAO;kBACZiB,GAAG,EAAC,KAAK;kBACTC,GAAG,EAAC,GAAG;kBACPC,IAAI,EAAC,KAAK;kBACVC,YAAY,EAAC,GAAG;kBAChBlB,QAAQ,EAAGzD,CAAC,IAAKc,sBAAsB,CAAC8D,UAAU,CAAC5E,CAAC,CAAC0D,MAAM,CAACF,KAAK,CAAC,CAAE;kBACpEf,KAAK,EAAE;oBAACoC,KAAK,EAAE;kBAAM,CAAE;kBACvB3D,KAAK,EAAC;gBAAc;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACF5K,OAAA;kBAAM6K,KAAK,EAAE;oBAACwB,QAAQ,EAAE,MAAM;oBAAEhB,KAAK,EAAE;kBAAS,CAAE;kBAAAd,QAAA,EAAC;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eAGN5K,OAAA;gBAAKqK,SAAS,EAAC,yBAAyB;gBAACQ,KAAK,EAAE;kBAC9C2B,OAAO,EAAE,MAAM;kBACfC,GAAG,EAAE;gBACP,CAAE;gBAAAlC,QAAA,gBACAvK,OAAA;kBACEwK,OAAO,EAAEX,qBAAsB;kBAC/BQ,SAAS,EAAC,mBAAmB;kBAC7BQ,KAAK,EAAE;oBAACU,OAAO,EAAE,UAAU;oBAAEc,QAAQ,EAAE;kBAAM,CAAE;kBAC/C/C,KAAK,EAAC,mBAAmB;kBAAAiB,QAAA,EAC1B;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT5K,OAAA;kBACEwK,OAAO,EAAEnB,eAAgB;kBACzBgB,SAAS,EAAC,mBAAmB;kBAC7BQ,KAAK,EAAE;oBAACU,OAAO,EAAE,UAAU;oBAAEc,QAAQ,EAAE;kBAAM,CAAE;kBAC/C/C,KAAK,EAAC,eAAe;kBAAAiB,QAAA,EACtB;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT5K,OAAA;kBACEwK,OAAO,EAAEb,gBAAiB;kBAC1BU,SAAS,EAAC,mBAAmB;kBAC7BQ,KAAK,EAAE;oBAACU,OAAO,EAAE,UAAU;oBAAEc,QAAQ,EAAE;kBAAM,CAAE;kBAC/C/C,KAAK,EAAC,gBAAgB;kBAAAiB,QAAA,EACvB;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN5K,OAAA;YAAIqK,SAAS,EAAC,OAAO;YAAAE,QAAA,EAAE,CAAAnJ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEkI,KAAK,KAAI;UAAY;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC3D5K,OAAA;YAAK6K,KAAK,EAAE;cAACQ,KAAK,EAAE,SAAS;cAAEI,YAAY,EAAE,MAAM;cAAEY,QAAQ,EAAE;YAAQ,CAAE;YAAA9B,QAAA,gBACvEvK,OAAA;cAAAuK,QAAA,GAAOlJ,CAAC,CAAC,QAAQ,CAAC,EAAC,IAAE,EAAC,CAAAD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEb,KAAK,KAAI,SAAS;YAAA;cAAAkK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACxD,CAAAxJ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEU,KAAK,KAAIV,OAAO,CAACU,KAAK,CAACY,MAAM,GAAG,CAAC,iBACzC1C,OAAA;cAAM6K,KAAK,EAAE;gBAACE,UAAU,EAAE;cAAM,CAAE;cAAAR,QAAA,GAC/BlJ,CAAC,CAAC,OAAO,CAAC,EAAC,IAAE,EAACD,OAAO,CAACU,KAAK,CAACwK,IAAI,CAAC,IAAI,CAAC;YAAA;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN5K,OAAA;YAAKqK,SAAS,EAAC,iBAAiB;YAACQ,KAAK,EAAE;cAACqC,UAAU,EAAE,KAAK;cAAEb,QAAQ,EAAE;YAAQ,CAAE;YAAA9B,QAAA,EAC7EnJ,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEyD,OAAO,GAAGzD,OAAO,CAACyD,OAAO,CAACsI,KAAK,CAAC,IAAI,CAAC,CAACnH,GAAG,CAAC,CAACoH,SAAS,EAAE9J,KAAK,KACnE8J,SAAS,CAAC9E,IAAI,CAAC,CAAC,iBACdtI,OAAA;cAAe6K,KAAK,EAAE;gBAACY,YAAY,EAAE;cAAM,CAAE;cAAAlB,QAAA,EAC1C6C;YAAS,GADJ9J,KAAK;cAAAmH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CAEN,CAAC,gBACA5K,OAAA;cAAAuK,QAAA,EAAG;YAA0B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UACjC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACxK,EAAA,CAlxBID,YAAY;EAAA,QAgBFN,cAAc;AAAA;AAAAwN,EAAA,GAhBxBlN,YAAY;AAoxBlB,eAAeA,YAAY;AAAC,IAAAkN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}