{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Appv1\\\\src\\\\components\\\\OptimizedApp.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport '../styles/optimized.css';\nimport gestureService, { createFlagWheel } from '../services/gestureService';\nimport speechService from '../services/speechService';\nimport exportService from '../services/exportService';\nimport gamificationService from '../services/optimizedGamificationService';\nimport { generateKnowledgeTree as generateTreeAPI, generateArticle, testConnection } from '../services/openRouterService';\nimport tabService from '../services/tabService';\nimport TabManager from './TabManager';\nimport LanguageSwitcher from './LanguageSwitcher';\nimport { useTranslation } from '../utils/i18n';\n\n// Optimized Knowledge Tree Explorer - Pareto 80/20 Implementation\n// Focus on core functionality with maximum impact\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst OptimizedApp = () => {\n  _s();\n  const [currentView, setCurrentView] = useState('input'); // 'input', 'tree', 'article'\n  const [topic, setTopic] = useState('');\n  const [tree, setTree] = useState(null);\n  const [selectedBranch, setSelectedBranch] = useState(null);\n  const [article, setArticle] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [user, setUser] = useState(null);\n  const appRef = useRef(null);\n\n  // Translation hook\n  const {\n    t\n  } = useTranslation();\n\n  // Available flags for the optimized version\n  const availableFlags = React.useMemo(() => [{\n    code: '-a',\n    name: 'Article',\n    description: t('flagArticle')\n  }, {\n    code: '-ex',\n    name: 'Examples',\n    description: t('flagExamples')\n  }, {\n    code: '-q',\n    name: 'Quiz',\n    description: t('flagQuiz')\n  }, {\n    code: '-vis',\n    name: 'Visual',\n    description: t('flagVisual')\n  }, {\n    code: '-path',\n    name: 'Learning Path',\n    description: t('flagPath')\n  }, {\n    code: '-case',\n    name: 'Case Study',\n    description: t('flagCase')\n  }, {\n    code: '-ro',\n    name: 'Romanian',\n    description: t('flagRomanian')\n  }], [t]);\n\n  // Generate article for selected branch with flags\n  const generateArticle = React.useCallback(async (branch, flags = ['-a']) => {\n    setIsLoading(true);\n    setError(null);\n    try {\n      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${process.env.REACT_APP_OPENROUTER_API_KEY}`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': window.location.origin,\n          'X-Title': 'Knowledge Tree Explorer'\n        },\n        body: JSON.stringify({\n          model: 'deepseek/deepseek-r1-0528:free',\n          messages: [{\n            role: 'user',\n            content: `Write a comprehensive article about \"${branch.nume}\" in the context of \"${tree.tema}\".\n\n            Apply these flags: ${flags.join(', ')}\n\n            Flag meanings:\n            - \"-a\": Standard comprehensive article format\n            - \"-ex\": Include 3 practical examples\n            - \"-q\": Add 5 interactive quiz questions at the end\n            - \"-vis\": Describe visual elements and diagrams\n            - \"-path\": Structure as a learning path with steps\n            - \"-case\": Include real-world case studies\n            - \"-ro\": Adapt content for Romanian context and examples\n\n            Make it educational and engaging. Length: 800-1200 words.`\n          }],\n          temperature: 0.7,\n          max_tokens: 3000\n        })\n      });\n      if (!response.ok) {\n        throw new Error(`API Error: ${response.status}`);\n      }\n      const data = await response.json();\n      const content = data.choices[0].message.content;\n      const articleData = {\n        title: branch.nume,\n        content: content,\n        topic: tree.tema,\n        flags: flags\n      };\n      setArticle(articleData);\n      setCurrentView('article');\n\n      // Award points for article generation\n      const result = gamificationService.awardPoints('ARTICLE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (err) {\n      console.error('Error generating article:', err);\n      setError('Failed to generate article. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  }, [tree]);\n\n  // Gesture handlers\n  const handleDoubleTap = React.useCallback((event, targetInfo) => {\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      // Show flag wheel on double tap of branch\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        createFlagWheel(targetInfo.position, availableFlags, selectedFlags => {\n          console.log('Selected flags:', selectedFlags);\n        }, selectedFlags => {\n          generateArticle(branch, selectedFlags);\n        });\n      }\n    }\n  }, [tree, availableFlags, generateArticle]);\n  const handleSingleTap = React.useCallback((event, targetInfo) => {\n    // Single tap for normal selection\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        setSelectedBranch(branch);\n      }\n    }\n  }, [tree]);\n  const handleLongPress = React.useCallback(async (event, targetInfo) => {\n    // Long press to expand branch and create tree effect\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        await expandBranch(branch, targetInfo.branchData.index);\n      }\n    }\n  }, [tree]);\n\n  // Initialize services and authentication\n  useEffect(() => {\n    const storedUser = localStorage.getItem('user');\n    const bypassSecurity = localStorage.getItem('bypassSecurity');\n    if (storedUser || bypassSecurity) {\n      const userData = {\n        id: 'user-1',\n        name: 'User',\n        subscriptionTier: 'premium'\n      };\n      setUser(userData);\n\n      // Award daily login points\n      const result = gamificationService.awardPoints('DAILY_LOGIN');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n\n    // Initialize gesture service\n    if (appRef.current) {\n      gestureService.init(appRef.current, {\n        doubleTap: handleDoubleTap,\n        singleTap: handleSingleTap,\n        longPress: handleLongPress\n      });\n    }\n    return () => {\n      gestureService.destroy();\n    };\n  }, [handleDoubleTap, handleSingleTap, handleLongPress]);\n\n  // Initialize gamification UI when user is logged in\n  useEffect(() => {\n    if (user) {\n      const container = document.getElementById('gamification-container');\n      if (container) {\n        // Clear existing content\n        container.innerHTML = '';\n        // Create gamification UI\n        gamificationService.createGamificationUI(container);\n      }\n\n      // Test API connection\n      testConnection().then(isConnected => {\n        console.log('🔌 API Connection Status:', isConnected ? '✅ Connected' : '❌ Failed');\n        if (!isConnected) {\n          console.warn('⚠️ API connection failed. Check your API key and internet connection.');\n        }\n      }).catch(error => {\n        console.error('❌ API connection test error:', error);\n      });\n    }\n  }, [user]);\n\n  // Core API call - using optimized service\n  const generateKnowledgeTree = async topicInput => {\n    setIsLoading(true);\n    setError(null);\n    try {\n      console.log('Generating knowledge tree for:', topicInput);\n      const treeData = await generateTreeAPI(topicInput);\n      console.log('Generated tree data:', treeData);\n      setTree(treeData);\n      setCurrentView('tree');\n\n      // Award points for tree generation\n      const result = gamificationService.awardPoints('TREE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (err) {\n      console.error('Error generating tree:', err);\n      setError(`Failed to generate knowledge tree: ${err.message}. Please try again.`);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Handle form submission\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (topic.trim()) {\n      generateKnowledgeTree(topic.trim());\n    }\n  };\n\n  // Handle branch selection (single tap)\n  const handleBranchSelect = branch => {\n    setSelectedBranch(branch);\n    // Don't auto-generate article, wait for double-tap or explicit action\n  };\n\n  // Speech functions\n  const handleSpeechToggle = () => {\n    if (!article) return;\n    if (speechService.getStatus().isPlaying) {\n      speechService.toggle();\n    } else {\n      speechService.speak(article.content);\n      // Award points for using speech\n      const result = gamificationService.awardPoints('SPEECH_USED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleSpeechStop = () => {\n    speechService.stop();\n  };\n  const handleSpeechRateChange = rate => {\n    speechService.setRate(rate);\n  };\n\n  // Export functions\n  const handleExportPDF = () => {\n    if (!article) return;\n    const result = exportService.exportAsPDF(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleExportWord = () => {\n    if (!article) return;\n    const result = exportService.exportAsWord(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleCopyToClipboard = async () => {\n    if (!article) return;\n    const result = await exportService.copyToClipboard(article.content);\n    exportService.showMessage(result.message, result.success ? 'success' : 'error');\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  // Navigation functions\n  const goBack = () => {\n    if (currentView === 'article') {\n      setCurrentView('tree');\n      setArticle(null);\n    } else if (currentView === 'tree') {\n      setCurrentView('input');\n      setTree(null);\n      setSelectedBranch(null);\n    }\n  };\n\n  // Expand branch to create sub-branches (tree effect)\n  const expandBranch = async (branch, branchIndex) => {\n    setIsLoading(true);\n    setError(null);\n    try {\n      var _data$choices$, _data$choices$$messag;\n      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${process.env.REACT_APP_OPENROUTER_API_KEY}`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': window.location.origin,\n          'X-Title': 'Knowledge Tree Explorer'\n        },\n        body: JSON.stringify({\n          model: 'deepseek/deepseek-r1-0528:free',\n          messages: [{\n            role: 'user',\n            content: `Expand the topic \"${branch.nume}\" from the context of \"${tree.tema}\". Create 4-6 sub-branches that dive deeper into this specific area. Return JSON with:\n            {\n              \"ramuri\": [\n                {\n                  \"nume\": \"Sub-branch Name\",\n                  \"descriere\": \"Brief description\",\n                  \"emoji\": \"📚\",\n                  \"subcategorii\": [\"Detail1\", \"Detail2\", \"Detail3\"]\n                }\n              ]\n            }\n            Focus on specific, actionable sub-topics within \"${branch.nume}\".`\n          }],\n          temperature: 0.7,\n          max_tokens: 1500\n        })\n      });\n      if (!response.ok) {\n        throw new Error(`API Error: ${response.status}`);\n      }\n      const data = await response.json();\n      const content = (_data$choices$ = data.choices[0]) === null || _data$choices$ === void 0 ? void 0 : (_data$choices$$messag = _data$choices$.message) === null || _data$choices$$messag === void 0 ? void 0 : _data$choices$$messag.content;\n      if (!content) {\n        throw new Error('No content received from API');\n      }\n\n      // Parse JSON response\n      const jsonMatch = content.match(/\\{[\\s\\S]*\\}/);\n      if (!jsonMatch) {\n        throw new Error('Invalid JSON format in response');\n      }\n      const expandedData = JSON.parse(jsonMatch[0]);\n\n      // Update tree with expanded branches\n      const newTree = {\n        ...tree\n      };\n      newTree.ramuri = [...newTree.ramuri.slice(0, branchIndex + 1), ...expandedData.ramuri.map(subBranch => ({\n        ...subBranch,\n        isSubBranch: true,\n        parentBranch: branch.nume,\n        level: (branch.level || 0) + 1\n      })), ...newTree.ramuri.slice(branchIndex + 1)];\n      setTree(newTree);\n\n      // Award points for branch expansion\n      const result = gamificationService.awardPoints('BRANCH_EXPANDED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (error) {\n      console.error('Error expanding branch:', error);\n      setError(t('failedToExpand'));\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const goHome = () => {\n    setCurrentView('input');\n    setTree(null);\n    setSelectedBranch(null);\n    setArticle(null);\n    setTopic('');\n  };\n\n  // Quick login for development\n  const quickLogin = () => {\n    localStorage.setItem('bypassSecurity', 'true');\n    setUser({\n      id: 'dev-1',\n      name: 'Developer',\n      subscriptionTier: 'premium'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    ref: appRef,\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"app-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: goHome,\n          className: \"logo-text\",\n          children: t('appTitle')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-right\",\n          children: [user && /*#__PURE__*/_jsxDEV(\"div\", {\n            id: \"gamification-container\",\n            style: {\n              marginRight: '16px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(LanguageSwitcher, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 13\n          }, this), !user ? /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: quickLogin,\n            className: \"btn btn-primary\",\n            style: {\n              marginLeft: '12px'\n            },\n            children: t('quickLogin')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginLeft: '12px'\n            },\n            children: [t('welcome'), \", \", user.name, \"!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 456,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 444,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 440,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 439,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"main-content\",\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error\",\n        children: [\"\\u26A0\\uFE0F \", error, /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setError(null),\n          style: {\n            marginLeft: 'auto',\n            background: 'none',\n            border: 'none',\n            color: 'white',\n            cursor: 'pointer'\n          },\n          children: \"\\u2715\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 465,\n        columnNumber: 11\n      }, this), currentView === 'input' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"title\",\n          children: t('appTitle')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 476,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"subtitle\",\n          children: \"Enter any topic to generate an interactive knowledge tree with AI-powered content.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 13\n        }, this), !user ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#f1f5f9',\n            padding: '1rem',\n            borderRadius: '0.5rem',\n            marginBottom: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#334155',\n              marginBottom: '1rem'\n            },\n            children: t('loginRequired')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 483,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: quickLogin,\n            className: \"btn btn-primary\",\n            children: t('quickLoginDev')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 482,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: topic,\n              onChange: e => setTopic(e.target.value),\n              placeholder: t('topicPlaceholder'),\n              className: \"form-input\",\n              disabled: isLoading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: isLoading || !topic.trim(),\n            className: \"btn btn-primary\",\n            children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 23\n              }, this), t('generating')]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: t('exploreKnowledge')\n            }, void 0, false)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 491,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 475,\n        columnNumber: 11\n      }, this), currentView === 'tree' && tree && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tree-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tree-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: tree.tema\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 527,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: t('selectBranch')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goBack,\n            className: \"btn btn-secondary\",\n            style: {\n              marginTop: '1rem'\n            },\n            children: t('backToTree')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 529,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 13\n        }, this), isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 536,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: t('loading')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"branches-grid\",\n          children: tree.ramuri.map((branch, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `branch-item ${selectedBranch === branch ? 'selected' : ''}`,\n            \"data-index\": index,\n            \"data-name\": branch.nume,\n            \"data-description\": branch.descriere,\n            \"data-is-sub-branch\": branch.isSubBranch || false,\n            \"data-level\": branch.level || 0,\n            onClick: () => handleBranchSelect(branch),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"branch-emoji\",\n              children: branch.emoji\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 552,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"branch-name\",\n              children: branch.nume\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"branch-description\",\n              children: branch.descriere\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 554,\n              columnNumber: 21\n            }, this), branch.subcategorii && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.875rem',\n                color: '#475569',\n                marginTop: '0.5rem'\n              },\n              children: [t('topics'), \": \", branch.subcategorii.slice(0, 3).join(', '), branch.subcategorii.length > 3 && '...']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 23\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"gesture-hint\",\n              style: {\n                fontSize: '0.75rem',\n                color: '#64748b',\n                marginTop: '0.5rem',\n                fontStyle: 'italic'\n              },\n              children: t('gestureHint')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 561,\n              columnNumber: 21\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 542,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 540,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 525,\n        columnNumber: 11\n      }, this), currentView === 'article' && article && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tree-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"article-header\",\n            style: {\n              marginBottom: '2rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: goBack,\n              className: \"btn btn-secondary\",\n              children: t('backToTree')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"article-controls\",\n              style: {\n                display: 'flex',\n                gap: '8px',\n                marginTop: '1rem',\n                flexWrap: 'wrap'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"speech-controls-compact\",\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '8px',\n                  padding: '8px 12px',\n                  background: '#f1f5f9',\n                  borderRadius: '6px',\n                  border: '1px solid #e2e8f0'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleSpeechToggle,\n                  className: \"btn-icon\",\n                  title: \"Play/Pause Speech\",\n                  style: {\n                    background: 'none',\n                    border: 'none',\n                    fontSize: '16px',\n                    cursor: 'pointer',\n                    padding: '4px'\n                  },\n                  children: speechService.getStatus().isPlaying ? '⏸️' : '▶️'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 602,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleSpeechStop,\n                  className: \"btn-icon\",\n                  title: \"Stop Speech\",\n                  style: {\n                    background: 'none',\n                    border: 'none',\n                    fontSize: '16px',\n                    cursor: 'pointer',\n                    padding: '4px'\n                  },\n                  children: \"\\u23F9\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 616,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"range\",\n                  min: \"0.5\",\n                  max: \"2\",\n                  step: \"0.1\",\n                  defaultValue: \"1\",\n                  onChange: e => handleSpeechRateChange(parseFloat(e.target.value)),\n                  style: {\n                    width: '60px'\n                  },\n                  title: \"Speech Speed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 630,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '12px',\n                    color: '#64748b'\n                  },\n                  children: \"\\uD83D\\uDDE3\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 640,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 593,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"export-controls-compact\",\n                style: {\n                  display: 'flex',\n                  gap: '4px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleCopyToClipboard,\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '6px 12px',\n                    fontSize: '12px'\n                  },\n                  title: \"Copy to Clipboard\",\n                  children: \"\\uD83D\\uDCCB Copy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 648,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleExportPDF,\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '6px 12px',\n                    fontSize: '12px'\n                  },\n                  title: \"Export as PDF\",\n                  children: \"\\uD83D\\uDCC4 PDF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 656,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleExportWord,\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '6px 12px',\n                    fontSize: '12px'\n                  },\n                  title: \"Export as Word\",\n                  children: \"\\uD83D\\uDCDD Word\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 664,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 644,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 580,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"title\",\n            children: article.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 676,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#475569',\n              marginBottom: '2rem',\n              fontSize: '0.9rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: [t('partOf'), \": \", article.topic]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 678,\n              columnNumber: 17\n            }, this), article.flags && article.flags.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                marginLeft: '16px'\n              },\n              children: [t('flags'), \": \", article.flags.join(', ')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 680,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 677,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"article-content\",\n            style: {\n              lineHeight: '1.8',\n              fontSize: '1.1rem'\n            },\n            children: article.content.split('\\n').map((paragraph, index) => paragraph.trim() && /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                marginBottom: '1rem'\n              },\n              children: paragraph\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 689,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 686,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 579,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 578,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 463,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 437,\n    columnNumber: 5\n  }, this);\n};\n_s(OptimizedApp, \"SBHX/m6km6wP0XZni4rxJXfKIVY=\", false, function () {\n  return [useTranslation];\n});\n_c = OptimizedApp;\nexport default OptimizedApp;\nvar _c;\n$RefreshReg$(_c, \"OptimizedApp\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "gestureService", "createFlagWheel", "speechService", "exportService", "gamificationService", "generateKnowledgeTree", "generateTreeAPI", "generateArticle", "testConnection", "tabService", "TabManager", "LanguageSwitcher", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "OptimizedApp", "_s", "current<PERSON>iew", "set<PERSON><PERSON><PERSON>View", "topic", "setTopic", "tree", "setTree", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedBranch", "article", "setArticle", "isLoading", "setIsLoading", "error", "setError", "user", "setUser", "appRef", "t", "availableFlags", "useMemo", "code", "name", "description", "useCallback", "branch", "flags", "response", "fetch", "method", "headers", "process", "env", "REACT_APP_OPENROUTER_API_KEY", "window", "location", "origin", "body", "JSON", "stringify", "model", "messages", "role", "content", "nume", "tema", "join", "temperature", "max_tokens", "ok", "Error", "status", "data", "json", "choices", "message", "articleData", "title", "result", "awardPoints", "newAchievements", "length", "for<PERSON>ach", "achievement", "showAchievementNotification", "err", "console", "handleDoubleTap", "event", "targetInfo", "isBranchItem", "branchData", "<PERSON><PERSON>", "index", "position", "selected<PERSON><PERSON><PERSON>", "log", "handleSingleTap", "handleLongPress", "expandBranch", "storedUser", "localStorage", "getItem", "bypassSecurity", "userData", "id", "subscriptionTier", "current", "init", "doubleTap", "singleTap", "longPress", "destroy", "container", "document", "getElementById", "innerHTML", "createGamificationUI", "then", "isConnected", "warn", "catch", "topicInput", "treeData", "handleSubmit", "e", "preventDefault", "trim", "handleBranchSelect", "handleSpeechToggle", "getStatus", "isPlaying", "toggle", "speak", "handleSpeechStop", "stop", "handleSpeechRateChange", "rate", "setRate", "handleExportPDF", "exportAsPDF", "replace", "success", "gamResult", "handleExportWord", "exportAsWord", "handleCopyToClipboard", "copyToClipboard", "showMessage", "goBack", "branchIndex", "_data$choices$", "_data$choices$$messag", "jsonMatch", "match", "expandedData", "parse", "newTree", "slice", "map", "subBranch", "isSubBranch", "parentBranch", "level", "goHome", "quickLogin", "setItem", "className", "ref", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginRight", "marginLeft", "background", "border", "color", "cursor", "padding", "borderRadius", "marginBottom", "onSubmit", "type", "value", "onChange", "target", "placeholder", "disabled", "marginTop", "desc<PERSON><PERSON>", "emoji", "subcategorii", "fontSize", "fontStyle", "display", "gap", "flexWrap", "alignItems", "min", "max", "step", "defaultValue", "parseFloat", "width", "lineHeight", "split", "paragraph", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Appv1/src/components/OptimizedApp.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport '../styles/optimized.css';\nimport gestureService, { createFlagWheel } from '../services/gestureService';\nimport speechService from '../services/speechService';\nimport exportService from '../services/exportService';\nimport gamificationService from '../services/optimizedGamificationService';\nimport { generateKnowledgeTree as generateTreeAPI, generateArticle, testConnection } from '../services/openRouterService';\nimport tabService from '../services/tabService';\nimport TabManager from './TabManager';\nimport LanguageSwitcher from './LanguageSwitcher';\nimport { useTranslation } from '../utils/i18n';\n\n// Optimized Knowledge Tree Explorer - Pareto 80/20 Implementation\n// Focus on core functionality with maximum impact\n\nconst OptimizedApp = () => {\n  const [currentView, setCurrentView] = useState('input'); // 'input', 'tree', 'article'\n  const [topic, setTopic] = useState('');\n  const [tree, setTree] = useState(null);\n  const [selectedBranch, setSelectedBranch] = useState(null);\n  const [article, setArticle] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [user, setUser] = useState(null);\n  const appRef = useRef(null);\n\n  // Translation hook\n  const { t } = useTranslation();\n\n  // Available flags for the optimized version\n  const availableFlags = React.useMemo(() => [\n    { code: '-a', name: 'Article', description: t('flagArticle') },\n    { code: '-ex', name: 'Examples', description: t('flagExamples') },\n    { code: '-q', name: 'Quiz', description: t('flagQuiz') },\n    { code: '-vis', name: 'Visual', description: t('flagVisual') },\n    { code: '-path', name: 'Learning Path', description: t('flagPath') },\n    { code: '-case', name: 'Case Study', description: t('flagCase') },\n    { code: '-ro', name: 'Romanian', description: t('flagRomanian') }\n  ], [t]);\n\n  // Generate article for selected branch with flags\n  const generateArticle = React.useCallback(async (branch, flags = ['-a']) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${process.env.REACT_APP_OPENROUTER_API_KEY}`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': window.location.origin,\n          'X-Title': 'Knowledge Tree Explorer'\n        },\n        body: JSON.stringify({\n          model: 'deepseek/deepseek-r1-0528:free',\n          messages: [{\n            role: 'user',\n            content: `Write a comprehensive article about \"${branch.nume}\" in the context of \"${tree.tema}\".\n\n            Apply these flags: ${flags.join(', ')}\n\n            Flag meanings:\n            - \"-a\": Standard comprehensive article format\n            - \"-ex\": Include 3 practical examples\n            - \"-q\": Add 5 interactive quiz questions at the end\n            - \"-vis\": Describe visual elements and diagrams\n            - \"-path\": Structure as a learning path with steps\n            - \"-case\": Include real-world case studies\n            - \"-ro\": Adapt content for Romanian context and examples\n\n            Make it educational and engaging. Length: 800-1200 words.`\n          }],\n          temperature: 0.7,\n          max_tokens: 3000\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error(`API Error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      const content = data.choices[0].message.content;\n\n      const articleData = {\n        title: branch.nume,\n        content: content,\n        topic: tree.tema,\n        flags: flags\n      };\n\n      setArticle(articleData);\n      setCurrentView('article');\n\n      // Award points for article generation\n      const result = gamificationService.awardPoints('ARTICLE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (err) {\n      console.error('Error generating article:', err);\n      setError('Failed to generate article. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  }, [tree]);\n\n  // Gesture handlers\n  const handleDoubleTap = React.useCallback((event, targetInfo) => {\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      // Show flag wheel on double tap of branch\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        createFlagWheel(\n          targetInfo.position,\n          availableFlags,\n          (selectedFlags) => {\n            console.log('Selected flags:', selectedFlags);\n          },\n          (selectedFlags) => {\n            generateArticle(branch, selectedFlags);\n          }\n        );\n      }\n    }\n  }, [tree, availableFlags, generateArticle]);\n\n  const handleSingleTap = React.useCallback((event, targetInfo) => {\n    // Single tap for normal selection\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        setSelectedBranch(branch);\n      }\n    }\n  }, [tree]);\n\n  const handleLongPress = React.useCallback(async (event, targetInfo) => {\n    // Long press to expand branch and create tree effect\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        await expandBranch(branch, targetInfo.branchData.index);\n      }\n    }\n  }, [tree]);\n\n  // Initialize services and authentication\n  useEffect(() => {\n    const storedUser = localStorage.getItem('user');\n    const bypassSecurity = localStorage.getItem('bypassSecurity');\n\n    if (storedUser || bypassSecurity) {\n      const userData = {\n        id: 'user-1',\n        name: 'User',\n        subscriptionTier: 'premium'\n      };\n      setUser(userData);\n\n      // Award daily login points\n      const result = gamificationService.awardPoints('DAILY_LOGIN');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n\n    // Initialize gesture service\n    if (appRef.current) {\n      gestureService.init(appRef.current, {\n        doubleTap: handleDoubleTap,\n        singleTap: handleSingleTap,\n        longPress: handleLongPress\n      });\n    }\n\n    return () => {\n      gestureService.destroy();\n    };\n  }, [handleDoubleTap, handleSingleTap, handleLongPress]);\n\n  // Initialize gamification UI when user is logged in\n  useEffect(() => {\n    if (user) {\n      const container = document.getElementById('gamification-container');\n      if (container) {\n        // Clear existing content\n        container.innerHTML = '';\n        // Create gamification UI\n        gamificationService.createGamificationUI(container);\n      }\n\n      // Test API connection\n      testConnection().then(isConnected => {\n        console.log('🔌 API Connection Status:', isConnected ? '✅ Connected' : '❌ Failed');\n        if (!isConnected) {\n          console.warn('⚠️ API connection failed. Check your API key and internet connection.');\n        }\n      }).catch(error => {\n        console.error('❌ API connection test error:', error);\n      });\n    }\n  }, [user]);\n\n  // Core API call - using optimized service\n  const generateKnowledgeTree = async (topicInput) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      console.log('Generating knowledge tree for:', topicInput);\n      const treeData = await generateTreeAPI(topicInput);\n      console.log('Generated tree data:', treeData);\n\n      setTree(treeData);\n      setCurrentView('tree');\n\n      // Award points for tree generation\n      const result = gamificationService.awardPoints('TREE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (err) {\n      console.error('Error generating tree:', err);\n      setError(`Failed to generate knowledge tree: ${err.message}. Please try again.`);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n\n\n  // Handle form submission\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    if (topic.trim()) {\n      generateKnowledgeTree(topic.trim());\n    }\n  };\n\n  // Handle branch selection (single tap)\n  const handleBranchSelect = (branch) => {\n    setSelectedBranch(branch);\n    // Don't auto-generate article, wait for double-tap or explicit action\n  };\n\n  // Speech functions\n  const handleSpeechToggle = () => {\n    if (!article) return;\n\n    if (speechService.getStatus().isPlaying) {\n      speechService.toggle();\n    } else {\n      speechService.speak(article.content);\n      // Award points for using speech\n      const result = gamificationService.awardPoints('SPEECH_USED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleSpeechStop = () => {\n    speechService.stop();\n  };\n\n  const handleSpeechRateChange = (rate) => {\n    speechService.setRate(rate);\n  };\n\n  // Export functions\n  const handleExportPDF = () => {\n    if (!article) return;\n    const result = exportService.exportAsPDF(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleExportWord = () => {\n    if (!article) return;\n    const result = exportService.exportAsWord(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleCopyToClipboard = async () => {\n    if (!article) return;\n    const result = await exportService.copyToClipboard(article.content);\n    exportService.showMessage(result.message, result.success ? 'success' : 'error');\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  // Navigation functions\n  const goBack = () => {\n    if (currentView === 'article') {\n      setCurrentView('tree');\n      setArticle(null);\n    } else if (currentView === 'tree') {\n      setCurrentView('input');\n      setTree(null);\n      setSelectedBranch(null);\n    }\n  };\n\n  // Expand branch to create sub-branches (tree effect)\n  const expandBranch = async (branch, branchIndex) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${process.env.REACT_APP_OPENROUTER_API_KEY}`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': window.location.origin,\n          'X-Title': 'Knowledge Tree Explorer'\n        },\n        body: JSON.stringify({\n          model: 'deepseek/deepseek-r1-0528:free',\n          messages: [{\n            role: 'user',\n            content: `Expand the topic \"${branch.nume}\" from the context of \"${tree.tema}\". Create 4-6 sub-branches that dive deeper into this specific area. Return JSON with:\n            {\n              \"ramuri\": [\n                {\n                  \"nume\": \"Sub-branch Name\",\n                  \"descriere\": \"Brief description\",\n                  \"emoji\": \"📚\",\n                  \"subcategorii\": [\"Detail1\", \"Detail2\", \"Detail3\"]\n                }\n              ]\n            }\n            Focus on specific, actionable sub-topics within \"${branch.nume}\".`\n          }],\n          temperature: 0.7,\n          max_tokens: 1500\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error(`API Error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      const content = data.choices[0]?.message?.content;\n\n      if (!content) {\n        throw new Error('No content received from API');\n      }\n\n      // Parse JSON response\n      const jsonMatch = content.match(/\\{[\\s\\S]*\\}/);\n      if (!jsonMatch) {\n        throw new Error('Invalid JSON format in response');\n      }\n\n      const expandedData = JSON.parse(jsonMatch[0]);\n\n      // Update tree with expanded branches\n      const newTree = { ...tree };\n      newTree.ramuri = [\n        ...newTree.ramuri.slice(0, branchIndex + 1),\n        ...expandedData.ramuri.map(subBranch => ({\n          ...subBranch,\n          isSubBranch: true,\n          parentBranch: branch.nume,\n          level: (branch.level || 0) + 1\n        })),\n        ...newTree.ramuri.slice(branchIndex + 1)\n      ];\n\n      setTree(newTree);\n\n      // Award points for branch expansion\n      const result = gamificationService.awardPoints('BRANCH_EXPANDED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n\n    } catch (error) {\n      console.error('Error expanding branch:', error);\n      setError(t('failedToExpand'));\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const goHome = () => {\n    setCurrentView('input');\n    setTree(null);\n    setSelectedBranch(null);\n    setArticle(null);\n    setTopic('');\n  };\n\n  // Quick login for development\n  const quickLogin = () => {\n    localStorage.setItem('bypassSecurity', 'true');\n    setUser({ id: 'dev-1', name: 'Developer', subscriptionTier: 'premium' });\n  };\n\n  return (\n    <div className=\"app\" ref={appRef}>\n      {/* Header */}\n      <header className=\"app-header\">\n        <div className=\"header-content\">\n          <button onClick={goHome} className=\"logo-text\">\n            {t('appTitle')}\n          </button>\n          <div className=\"header-right\">\n            {user && (\n              <div id=\"gamification-container\" style={{ marginRight: '16px' }}>\n                {/* Gamification UI will be inserted here */}\n              </div>\n            )}\n            <LanguageSwitcher />\n            {!user ? (\n              <button onClick={quickLogin} className=\"btn btn-primary\" style={{ marginLeft: '12px' }}>\n                {t('quickLogin')}\n              </button>\n            ) : (\n              <span style={{ marginLeft: '12px' }}>{t('welcome')}, {user.name}!</span>\n            )}\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"main-content\">\n        {error && (\n          <div className=\"error\">\n            ⚠️ {error}\n            <button onClick={() => setError(null)} style={{marginLeft: 'auto', background: 'none', border: 'none', color: 'white', cursor: 'pointer'}}>\n              ✕\n            </button>\n          </div>\n        )}\n\n        {/* Topic Input View */}\n        {currentView === 'input' && (\n          <div className=\"card text-center\">\n            <h1 className=\"title\">{t('appTitle')}</h1>\n            <p className=\"subtitle\">\n              Enter any topic to generate an interactive knowledge tree with AI-powered content.\n            </p>\n\n            {!user ? (\n              <div style={{background: '#f1f5f9', padding: '1rem', borderRadius: '0.5rem', marginBottom: '2rem'}}>\n                <p style={{color: '#334155', marginBottom: '1rem'}}>\n                  {t('loginRequired')}\n                </p>\n                <button onClick={quickLogin} className=\"btn btn-primary\">\n                  {t('quickLoginDev')}\n                </button>\n              </div>\n            ) : (\n              <form onSubmit={handleSubmit}>\n                <div className=\"form-group\">\n                  <input\n                    type=\"text\"\n                    value={topic}\n                    onChange={(e) => setTopic(e.target.value)}\n                    placeholder={t('topicPlaceholder')}\n                    className=\"form-input\"\n                    disabled={isLoading}\n                  />\n                </div>\n                <button\n                  type=\"submit\"\n                  disabled={isLoading || !topic.trim()}\n                  className=\"btn btn-primary\"\n                >\n                  {isLoading ? (\n                    <>\n                      <span className=\"spinner\"></span>\n                      {t('generating')}\n                    </>\n                  ) : (\n                    <>\n                      {t('exploreKnowledge')}\n                    </>\n                  )}\n                </button>\n              </form>\n            )}\n          </div>\n        )}\n\n        {/* Tree View */}\n        {currentView === 'tree' && tree && (\n          <div className=\"tree-container\">\n            <div className=\"tree-header\">\n              <h1>{tree.tema}</h1>\n              <p>{t('selectBranch')}</p>\n              <button onClick={goBack} className=\"btn btn-secondary\" style={{marginTop: '1rem'}}>\n                {t('backToTree')}\n              </button>\n            </div>\n\n            {isLoading ? (\n              <div className=\"loading\">\n                <span className=\"spinner\"></span>\n                <span>{t('loading')}</span>\n              </div>\n            ) : (\n              <div className=\"branches-grid\">\n                {tree.ramuri.map((branch, index) => (\n                  <div\n                    key={index}\n                    className={`branch-item ${selectedBranch === branch ? 'selected' : ''}`}\n                    data-index={index}\n                    data-name={branch.nume}\n                    data-description={branch.descriere}\n                    data-is-sub-branch={branch.isSubBranch || false}\n                    data-level={branch.level || 0}\n                    onClick={() => handleBranchSelect(branch)}\n                  >\n                    <div className=\"branch-emoji\">{branch.emoji}</div>\n                    <h3 className=\"branch-name\">{branch.nume}</h3>\n                    <p className=\"branch-description\">{branch.descriere}</p>\n                    {branch.subcategorii && (\n                      <div style={{fontSize: '0.875rem', color: '#475569', marginTop: '0.5rem'}}>\n                        {t('topics')}: {branch.subcategorii.slice(0, 3).join(', ')}\n                        {branch.subcategorii.length > 3 && '...'}\n                      </div>\n                    )}\n                    <div className=\"gesture-hint\" style={{\n                      fontSize: '0.75rem',\n                      color: '#64748b',\n                      marginTop: '0.5rem',\n                      fontStyle: 'italic'\n                    }}>\n                      {t('gestureHint')}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Article View */}\n        {currentView === 'article' && article && (\n          <div className=\"tree-container\">\n            <div className=\"card\">\n              <div className=\"article-header\" style={{marginBottom: '2rem'}}>\n                <button onClick={goBack} className=\"btn btn-secondary\">\n                  {t('backToTree')}\n                </button>\n\n                {/* Article Controls */}\n                <div className=\"article-controls\" style={{\n                  display: 'flex',\n                  gap: '8px',\n                  marginTop: '1rem',\n                  flexWrap: 'wrap'\n                }}>\n                  {/* Speech Controls */}\n                  <div className=\"speech-controls-compact\" style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '8px',\n                    padding: '8px 12px',\n                    background: '#f1f5f9',\n                    borderRadius: '6px',\n                    border: '1px solid #e2e8f0'\n                  }}>\n                    <button\n                      onClick={handleSpeechToggle}\n                      className=\"btn-icon\"\n                      title=\"Play/Pause Speech\"\n                      style={{\n                        background: 'none',\n                        border: 'none',\n                        fontSize: '16px',\n                        cursor: 'pointer',\n                        padding: '4px'\n                      }}\n                    >\n                      {speechService.getStatus().isPlaying ? '⏸️' : '▶️'}\n                    </button>\n                    <button\n                      onClick={handleSpeechStop}\n                      className=\"btn-icon\"\n                      title=\"Stop Speech\"\n                      style={{\n                        background: 'none',\n                        border: 'none',\n                        fontSize: '16px',\n                        cursor: 'pointer',\n                        padding: '4px'\n                      }}\n                    >\n                      ⏹️\n                    </button>\n                    <input\n                      type=\"range\"\n                      min=\"0.5\"\n                      max=\"2\"\n                      step=\"0.1\"\n                      defaultValue=\"1\"\n                      onChange={(e) => handleSpeechRateChange(parseFloat(e.target.value))}\n                      style={{width: '60px'}}\n                      title=\"Speech Speed\"\n                    />\n                    <span style={{fontSize: '12px', color: '#64748b'}}>🗣️</span>\n                  </div>\n\n                  {/* Export Controls */}\n                  <div className=\"export-controls-compact\" style={{\n                    display: 'flex',\n                    gap: '4px'\n                  }}>\n                    <button\n                      onClick={handleCopyToClipboard}\n                      className=\"btn btn-secondary\"\n                      style={{padding: '6px 12px', fontSize: '12px'}}\n                      title=\"Copy to Clipboard\"\n                    >\n                      📋 Copy\n                    </button>\n                    <button\n                      onClick={handleExportPDF}\n                      className=\"btn btn-secondary\"\n                      style={{padding: '6px 12px', fontSize: '12px'}}\n                      title=\"Export as PDF\"\n                    >\n                      📄 PDF\n                    </button>\n                    <button\n                      onClick={handleExportWord}\n                      className=\"btn btn-secondary\"\n                      style={{padding: '6px 12px', fontSize: '12px'}}\n                      title=\"Export as Word\"\n                    >\n                      📝 Word\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              <h1 className=\"title\">{article.title}</h1>\n              <div style={{color: '#475569', marginBottom: '2rem', fontSize: '0.9rem'}}>\n                <span>{t('partOf')}: {article.topic}</span>\n                {article.flags && article.flags.length > 0 && (\n                  <span style={{marginLeft: '16px'}}>\n                    {t('flags')}: {article.flags.join(', ')}\n                  </span>\n                )}\n              </div>\n\n              <div className=\"article-content\" style={{lineHeight: '1.8', fontSize: '1.1rem'}}>\n                {article.content.split('\\n').map((paragraph, index) => (\n                  paragraph.trim() && (\n                    <p key={index} style={{marginBottom: '1rem'}}>\n                      {paragraph}\n                    </p>\n                  )\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n      </main>\n    </div>\n  );\n};\n\nexport default OptimizedApp;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,yBAAyB;AAChC,OAAOC,cAAc,IAAIC,eAAe,QAAQ,4BAA4B;AAC5E,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,mBAAmB,MAAM,0CAA0C;AAC1E,SAASC,qBAAqB,IAAIC,eAAe,EAAEC,eAAe,EAAEC,cAAc,QAAQ,+BAA+B;AACzH,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,cAAc,QAAQ,eAAe;;AAE9C;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEA,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACzD,MAAM,CAACwB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC0B,IAAI,EAAEC,OAAO,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACkC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACoC,IAAI,EAAEC,OAAO,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAMsC,MAAM,GAAGpC,MAAM,CAAC,IAAI,CAAC;;EAE3B;EACA,MAAM;IAAEqC;EAAE,CAAC,GAAGxB,cAAc,CAAC,CAAC;;EAE9B;EACA,MAAMyB,cAAc,GAAGzC,KAAK,CAAC0C,OAAO,CAAC,MAAM,CACzC;IAAEC,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,SAAS;IAAEC,WAAW,EAAEL,CAAC,CAAC,aAAa;EAAE,CAAC,EAC9D;IAAEG,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAE,UAAU;IAAEC,WAAW,EAAEL,CAAC,CAAC,cAAc;EAAE,CAAC,EACjE;IAAEG,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,MAAM;IAAEC,WAAW,EAAEL,CAAC,CAAC,UAAU;EAAE,CAAC,EACxD;IAAEG,IAAI,EAAE,MAAM;IAAEC,IAAI,EAAE,QAAQ;IAAEC,WAAW,EAAEL,CAAC,CAAC,YAAY;EAAE,CAAC,EAC9D;IAAEG,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,eAAe;IAAEC,WAAW,EAAEL,CAAC,CAAC,UAAU;EAAE,CAAC,EACpE;IAAEG,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,YAAY;IAAEC,WAAW,EAAEL,CAAC,CAAC,UAAU;EAAE,CAAC,EACjE;IAAEG,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAE,UAAU;IAAEC,WAAW,EAAEL,CAAC,CAAC,cAAc;EAAE,CAAC,CAClE,EAAE,CAACA,CAAC,CAAC,CAAC;;EAEP;EACA,MAAM7B,eAAe,GAAGX,KAAK,CAAC8C,WAAW,CAAC,OAAOC,MAAM,EAAEC,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK;IAC1Ed,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMa,QAAQ,GAAG,MAAMC,KAAK,CAAC,+CAA+C,EAAE;QAC5EC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,OAAO,CAACC,GAAG,CAACC,4BAA4B,EAAE;UACrE,cAAc,EAAE,kBAAkB;UAClC,cAAc,EAAEC,MAAM,CAACC,QAAQ,CAACC,MAAM;UACtC,SAAS,EAAE;QACb,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,KAAK,EAAE,gCAAgC;UACvCC,QAAQ,EAAE,CAAC;YACTC,IAAI,EAAE,MAAM;YACZC,OAAO,EAAE,wCAAwClB,MAAM,CAACmB,IAAI,wBAAwBvC,IAAI,CAACwC,IAAI;AACzG;AACA,iCAAiCnB,KAAK,CAACoB,IAAI,CAAC,IAAI,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACU,CAAC,CAAC;UACFC,WAAW,EAAE,GAAG;UAChBC,UAAU,EAAE;QACd,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACrB,QAAQ,CAACsB,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,cAAcvB,QAAQ,CAACwB,MAAM,EAAE,CAAC;MAClD;MAEA,MAAMC,IAAI,GAAG,MAAMzB,QAAQ,CAAC0B,IAAI,CAAC,CAAC;MAClC,MAAMV,OAAO,GAAGS,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO,CAACZ,OAAO;MAE/C,MAAMa,WAAW,GAAG;QAClBC,KAAK,EAAEhC,MAAM,CAACmB,IAAI;QAClBD,OAAO,EAAEA,OAAO;QAChBxC,KAAK,EAAEE,IAAI,CAACwC,IAAI;QAChBnB,KAAK,EAAEA;MACT,CAAC;MAEDhB,UAAU,CAAC8C,WAAW,CAAC;MACvBtD,cAAc,CAAC,SAAS,CAAC;;MAEzB;MACA,MAAMwD,MAAM,GAAGxE,mBAAmB,CAACyE,WAAW,CAAC,mBAAmB,CAAC;MACnE,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5C7E,mBAAmB,CAAC8E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZC,OAAO,CAACrD,KAAK,CAAC,2BAA2B,EAAEoD,GAAG,CAAC;MAC/CnD,QAAQ,CAAC,+CAA+C,CAAC;IAC3D,CAAC,SAAS;MACRF,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACP,IAAI,CAAC,CAAC;;EAEV;EACA,MAAM8D,eAAe,GAAGzF,KAAK,CAAC8C,WAAW,CAAC,CAAC4C,KAAK,EAAEC,UAAU,KAAK;IAC/D,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,IAAIlE,IAAI,EAAE;MAC5D;MACA,MAAMoB,MAAM,GAAGpB,IAAI,CAACmE,MAAM,CAACH,UAAU,CAACE,UAAU,CAACE,KAAK,CAAC;MACvD,IAAIhD,MAAM,EAAE;QACV1C,eAAe,CACbsF,UAAU,CAACK,QAAQ,EACnBvD,cAAc,EACbwD,aAAa,IAAK;UACjBT,OAAO,CAACU,GAAG,CAAC,iBAAiB,EAAED,aAAa,CAAC;QAC/C,CAAC,EACAA,aAAa,IAAK;UACjBtF,eAAe,CAACoC,MAAM,EAAEkD,aAAa,CAAC;QACxC,CACF,CAAC;MACH;IACF;EACF,CAAC,EAAE,CAACtE,IAAI,EAAEc,cAAc,EAAE9B,eAAe,CAAC,CAAC;EAE3C,MAAMwF,eAAe,GAAGnG,KAAK,CAAC8C,WAAW,CAAC,CAAC4C,KAAK,EAAEC,UAAU,KAAK;IAC/D;IACA,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,IAAIlE,IAAI,EAAE;MAC5D,MAAMoB,MAAM,GAAGpB,IAAI,CAACmE,MAAM,CAACH,UAAU,CAACE,UAAU,CAACE,KAAK,CAAC;MACvD,IAAIhD,MAAM,EAAE;QACVjB,iBAAiB,CAACiB,MAAM,CAAC;MAC3B;IACF;EACF,CAAC,EAAE,CAACpB,IAAI,CAAC,CAAC;EAEV,MAAMyE,eAAe,GAAGpG,KAAK,CAAC8C,WAAW,CAAC,OAAO4C,KAAK,EAAEC,UAAU,KAAK;IACrE;IACA,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,IAAIlE,IAAI,EAAE;MAC5D,MAAMoB,MAAM,GAAGpB,IAAI,CAACmE,MAAM,CAACH,UAAU,CAACE,UAAU,CAACE,KAAK,CAAC;MACvD,IAAIhD,MAAM,EAAE;QACV,MAAMsD,YAAY,CAACtD,MAAM,EAAE4C,UAAU,CAACE,UAAU,CAACE,KAAK,CAAC;MACzD;IACF;EACF,CAAC,EAAE,CAACpE,IAAI,CAAC,CAAC;;EAEV;EACAzB,SAAS,CAAC,MAAM;IACd,MAAMoG,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAC/C,MAAMC,cAAc,GAAGF,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAE7D,IAAIF,UAAU,IAAIG,cAAc,EAAE;MAChC,MAAMC,QAAQ,GAAG;QACfC,EAAE,EAAE,QAAQ;QACZ/D,IAAI,EAAE,MAAM;QACZgE,gBAAgB,EAAE;MACpB,CAAC;MACDtE,OAAO,CAACoE,QAAQ,CAAC;;MAEjB;MACA,MAAM1B,MAAM,GAAGxE,mBAAmB,CAACyE,WAAW,CAAC,aAAa,CAAC;MAC7D,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5C7E,mBAAmB,CAAC8E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;;IAEA;IACA,IAAI9C,MAAM,CAACsE,OAAO,EAAE;MAClBzG,cAAc,CAAC0G,IAAI,CAACvE,MAAM,CAACsE,OAAO,EAAE;QAClCE,SAAS,EAAEtB,eAAe;QAC1BuB,SAAS,EAAEb,eAAe;QAC1Bc,SAAS,EAAEb;MACb,CAAC,CAAC;IACJ;IAEA,OAAO,MAAM;MACXhG,cAAc,CAAC8G,OAAO,CAAC,CAAC;IAC1B,CAAC;EACH,CAAC,EAAE,CAACzB,eAAe,EAAEU,eAAe,EAAEC,eAAe,CAAC,CAAC;;EAEvD;EACAlG,SAAS,CAAC,MAAM;IACd,IAAImC,IAAI,EAAE;MACR,MAAM8E,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,wBAAwB,CAAC;MACnE,IAAIF,SAAS,EAAE;QACb;QACAA,SAAS,CAACG,SAAS,GAAG,EAAE;QACxB;QACA9G,mBAAmB,CAAC+G,oBAAoB,CAACJ,SAAS,CAAC;MACrD;;MAEA;MACAvG,cAAc,CAAC,CAAC,CAAC4G,IAAI,CAACC,WAAW,IAAI;QACnCjC,OAAO,CAACU,GAAG,CAAC,2BAA2B,EAAEuB,WAAW,GAAG,aAAa,GAAG,UAAU,CAAC;QAClF,IAAI,CAACA,WAAW,EAAE;UAChBjC,OAAO,CAACkC,IAAI,CAAC,uEAAuE,CAAC;QACvF;MACF,CAAC,CAAC,CAACC,KAAK,CAACxF,KAAK,IAAI;QAChBqD,OAAO,CAACrD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACE,IAAI,CAAC,CAAC;;EAEV;EACA,MAAM5B,qBAAqB,GAAG,MAAOmH,UAAU,IAAK;IAClD1F,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACFoD,OAAO,CAACU,GAAG,CAAC,gCAAgC,EAAE0B,UAAU,CAAC;MACzD,MAAMC,QAAQ,GAAG,MAAMnH,eAAe,CAACkH,UAAU,CAAC;MAClDpC,OAAO,CAACU,GAAG,CAAC,sBAAsB,EAAE2B,QAAQ,CAAC;MAE7CjG,OAAO,CAACiG,QAAQ,CAAC;MACjBrG,cAAc,CAAC,MAAM,CAAC;;MAEtB;MACA,MAAMwD,MAAM,GAAGxE,mBAAmB,CAACyE,WAAW,CAAC,gBAAgB,CAAC;MAChE,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5C7E,mBAAmB,CAAC8E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZC,OAAO,CAACrD,KAAK,CAAC,wBAAwB,EAAEoD,GAAG,CAAC;MAC5CnD,QAAQ,CAAC,sCAAsCmD,GAAG,CAACV,OAAO,qBAAqB,CAAC;IAClF,CAAC,SAAS;MACR3C,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAID;EACA,MAAM4F,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIvG,KAAK,CAACwG,IAAI,CAAC,CAAC,EAAE;MAChBxH,qBAAqB,CAACgB,KAAK,CAACwG,IAAI,CAAC,CAAC,CAAC;IACrC;EACF,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAInF,MAAM,IAAK;IACrCjB,iBAAiB,CAACiB,MAAM,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMoF,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI,CAACpG,OAAO,EAAE;IAEd,IAAIzB,aAAa,CAAC8H,SAAS,CAAC,CAAC,CAACC,SAAS,EAAE;MACvC/H,aAAa,CAACgI,MAAM,CAAC,CAAC;IACxB,CAAC,MAAM;MACLhI,aAAa,CAACiI,KAAK,CAACxG,OAAO,CAACkC,OAAO,CAAC;MACpC;MACA,MAAMe,MAAM,GAAGxE,mBAAmB,CAACyE,WAAW,CAAC,aAAa,CAAC;MAC7D,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5C7E,mBAAmB,CAAC8E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAMmD,gBAAgB,GAAGA,CAAA,KAAM;IAC7BlI,aAAa,CAACmI,IAAI,CAAC,CAAC;EACtB,CAAC;EAED,MAAMC,sBAAsB,GAAIC,IAAI,IAAK;IACvCrI,aAAa,CAACsI,OAAO,CAACD,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAAC9G,OAAO,EAAE;IACd,MAAMiD,MAAM,GAAGzE,aAAa,CAACuI,WAAW,CAAC/G,OAAO,EAAE,GAAGA,OAAO,CAACgD,KAAK,CAACgE,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,EAAE,CAAC;IACnG,IAAI/D,MAAM,CAACgE,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAGzI,mBAAmB,CAACyE,WAAW,CAAC,aAAa,CAAC;MAChE,IAAIgE,SAAS,CAAC/D,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxC8D,SAAS,CAAC/D,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/C7E,mBAAmB,CAAC8E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAM6D,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAACnH,OAAO,EAAE;IACd,MAAMiD,MAAM,GAAGzE,aAAa,CAAC4I,YAAY,CAACpH,OAAO,EAAE,GAAGA,OAAO,CAACgD,KAAK,CAACgE,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,EAAE,CAAC;IACpG,IAAI/D,MAAM,CAACgE,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAGzI,mBAAmB,CAACyE,WAAW,CAAC,aAAa,CAAC;MAChE,IAAIgE,SAAS,CAAC/D,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxC8D,SAAS,CAAC/D,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/C7E,mBAAmB,CAAC8E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAM+D,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI,CAACrH,OAAO,EAAE;IACd,MAAMiD,MAAM,GAAG,MAAMzE,aAAa,CAAC8I,eAAe,CAACtH,OAAO,CAACkC,OAAO,CAAC;IACnE1D,aAAa,CAAC+I,WAAW,CAACtE,MAAM,CAACH,OAAO,EAAEG,MAAM,CAACgE,OAAO,GAAG,SAAS,GAAG,OAAO,CAAC;IAC/E,IAAIhE,MAAM,CAACgE,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAGzI,mBAAmB,CAACyE,WAAW,CAAC,aAAa,CAAC;MAChE,IAAIgE,SAAS,CAAC/D,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxC8D,SAAS,CAAC/D,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/C7E,mBAAmB,CAAC8E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;;EAED;EACA,MAAMkE,MAAM,GAAGA,CAAA,KAAM;IACnB,IAAIhI,WAAW,KAAK,SAAS,EAAE;MAC7BC,cAAc,CAAC,MAAM,CAAC;MACtBQ,UAAU,CAAC,IAAI,CAAC;IAClB,CAAC,MAAM,IAAIT,WAAW,KAAK,MAAM,EAAE;MACjCC,cAAc,CAAC,OAAO,CAAC;MACvBI,OAAO,CAAC,IAAI,CAAC;MACbE,iBAAiB,CAAC,IAAI,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMuE,YAAY,GAAG,MAAAA,CAAOtD,MAAM,EAAEyG,WAAW,KAAK;IAClDtH,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MAAA,IAAAqH,cAAA,EAAAC,qBAAA;MACF,MAAMzG,QAAQ,GAAG,MAAMC,KAAK,CAAC,+CAA+C,EAAE;QAC5EC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,OAAO,CAACC,GAAG,CAACC,4BAA4B,EAAE;UACrE,cAAc,EAAE,kBAAkB;UAClC,cAAc,EAAEC,MAAM,CAACC,QAAQ,CAACC,MAAM;UACtC,SAAS,EAAE;QACb,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,KAAK,EAAE,gCAAgC;UACvCC,QAAQ,EAAE,CAAC;YACTC,IAAI,EAAE,MAAM;YACZC,OAAO,EAAE,qBAAqBlB,MAAM,CAACmB,IAAI,0BAA0BvC,IAAI,CAACwC,IAAI;AACxF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+DAA+DpB,MAAM,CAACmB,IAAI;UAChE,CAAC,CAAC;UACFG,WAAW,EAAE,GAAG;UAChBC,UAAU,EAAE;QACd,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACrB,QAAQ,CAACsB,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,cAAcvB,QAAQ,CAACwB,MAAM,EAAE,CAAC;MAClD;MAEA,MAAMC,IAAI,GAAG,MAAMzB,QAAQ,CAAC0B,IAAI,CAAC,CAAC;MAClC,MAAMV,OAAO,IAAAwF,cAAA,GAAG/E,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,cAAA6E,cAAA,wBAAAC,qBAAA,GAAfD,cAAA,CAAiB5E,OAAO,cAAA6E,qBAAA,uBAAxBA,qBAAA,CAA0BzF,OAAO;MAEjD,IAAI,CAACA,OAAO,EAAE;QACZ,MAAM,IAAIO,KAAK,CAAC,8BAA8B,CAAC;MACjD;;MAEA;MACA,MAAMmF,SAAS,GAAG1F,OAAO,CAAC2F,KAAK,CAAC,aAAa,CAAC;MAC9C,IAAI,CAACD,SAAS,EAAE;QACd,MAAM,IAAInF,KAAK,CAAC,iCAAiC,CAAC;MACpD;MAEA,MAAMqF,YAAY,GAAGjG,IAAI,CAACkG,KAAK,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC;;MAE7C;MACA,MAAMI,OAAO,GAAG;QAAE,GAAGpI;MAAK,CAAC;MAC3BoI,OAAO,CAACjE,MAAM,GAAG,CACf,GAAGiE,OAAO,CAACjE,MAAM,CAACkE,KAAK,CAAC,CAAC,EAAER,WAAW,GAAG,CAAC,CAAC,EAC3C,GAAGK,YAAY,CAAC/D,MAAM,CAACmE,GAAG,CAACC,SAAS,KAAK;QACvC,GAAGA,SAAS;QACZC,WAAW,EAAE,IAAI;QACjBC,YAAY,EAAErH,MAAM,CAACmB,IAAI;QACzBmG,KAAK,EAAE,CAACtH,MAAM,CAACsH,KAAK,IAAI,CAAC,IAAI;MAC/B,CAAC,CAAC,CAAC,EACH,GAAGN,OAAO,CAACjE,MAAM,CAACkE,KAAK,CAACR,WAAW,GAAG,CAAC,CAAC,CACzC;MAED5H,OAAO,CAACmI,OAAO,CAAC;;MAEhB;MACA,MAAM/E,MAAM,GAAGxE,mBAAmB,CAACyE,WAAW,CAAC,iBAAiB,CAAC;MACjE,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5C7E,mBAAmB,CAAC8E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IAEF,CAAC,CAAC,OAAOlD,KAAK,EAAE;MACdqD,OAAO,CAACrD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAACI,CAAC,CAAC,gBAAgB,CAAC,CAAC;IAC/B,CAAC,SAAS;MACRN,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMoI,MAAM,GAAGA,CAAA,KAAM;IACnB9I,cAAc,CAAC,OAAO,CAAC;IACvBI,OAAO,CAAC,IAAI,CAAC;IACbE,iBAAiB,CAAC,IAAI,CAAC;IACvBE,UAAU,CAAC,IAAI,CAAC;IAChBN,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;;EAED;EACA,MAAM6I,UAAU,GAAGA,CAAA,KAAM;IACvBhE,YAAY,CAACiE,OAAO,CAAC,gBAAgB,EAAE,MAAM,CAAC;IAC9ClI,OAAO,CAAC;MAAEqE,EAAE,EAAE,OAAO;MAAE/D,IAAI,EAAE,WAAW;MAAEgE,gBAAgB,EAAE;IAAU,CAAC,CAAC;EAC1E,CAAC;EAED,oBACE1F,OAAA;IAAKuJ,SAAS,EAAC,KAAK;IAACC,GAAG,EAAEnI,MAAO;IAAAoI,QAAA,gBAE/BzJ,OAAA;MAAQuJ,SAAS,EAAC,YAAY;MAAAE,QAAA,eAC5BzJ,OAAA;QAAKuJ,SAAS,EAAC,gBAAgB;QAAAE,QAAA,gBAC7BzJ,OAAA;UAAQ0J,OAAO,EAAEN,MAAO;UAACG,SAAS,EAAC,WAAW;UAAAE,QAAA,EAC3CnI,CAAC,CAAC,UAAU;QAAC;UAAAqI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACT9J,OAAA;UAAKuJ,SAAS,EAAC,cAAc;UAAAE,QAAA,GAC1BtI,IAAI,iBACHnB,OAAA;YAAKyF,EAAE,EAAC,wBAAwB;YAACsE,KAAK,EAAE;cAAEC,WAAW,EAAE;YAAO;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE3D,CACN,eACD9J,OAAA,CAACH,gBAAgB;YAAA8J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACnB,CAAC3I,IAAI,gBACJnB,OAAA;YAAQ0J,OAAO,EAAEL,UAAW;YAACE,SAAS,EAAC,iBAAiB;YAACQ,KAAK,EAAE;cAAEE,UAAU,EAAE;YAAO,CAAE;YAAAR,QAAA,EACpFnI,CAAC,CAAC,YAAY;UAAC;YAAAqI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,gBAET9J,OAAA;YAAM+J,KAAK,EAAE;cAAEE,UAAU,EAAE;YAAO,CAAE;YAAAR,QAAA,GAAEnI,CAAC,CAAC,SAAS,CAAC,EAAC,IAAE,EAACH,IAAI,CAACO,IAAI,EAAC,GAAC;UAAA;YAAAiI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACxE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGT9J,OAAA;MAAMuJ,SAAS,EAAC,cAAc;MAAAE,QAAA,GAC3BxI,KAAK,iBACJjB,OAAA;QAAKuJ,SAAS,EAAC,OAAO;QAAAE,QAAA,GAAC,eAClB,EAACxI,KAAK,eACTjB,OAAA;UAAQ0J,OAAO,EAAEA,CAAA,KAAMxI,QAAQ,CAAC,IAAI,CAAE;UAAC6I,KAAK,EAAE;YAACE,UAAU,EAAE,MAAM;YAAEC,UAAU,EAAE,MAAM;YAAEC,MAAM,EAAE,MAAM;YAAEC,KAAK,EAAE,OAAO;YAAEC,MAAM,EAAE;UAAS,CAAE;UAAAZ,QAAA,EAAC;QAE3I;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAGAzJ,WAAW,KAAK,OAAO,iBACtBL,OAAA;QAAKuJ,SAAS,EAAC,kBAAkB;QAAAE,QAAA,gBAC/BzJ,OAAA;UAAIuJ,SAAS,EAAC,OAAO;UAAAE,QAAA,EAAEnI,CAAC,CAAC,UAAU;QAAC;UAAAqI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1C9J,OAAA;UAAGuJ,SAAS,EAAC,UAAU;UAAAE,QAAA,EAAC;QAExB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAEH,CAAC3I,IAAI,gBACJnB,OAAA;UAAK+J,KAAK,EAAE;YAACG,UAAU,EAAE,SAAS;YAAEI,OAAO,EAAE,MAAM;YAAEC,YAAY,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAM,CAAE;UAAAf,QAAA,gBACjGzJ,OAAA;YAAG+J,KAAK,EAAE;cAACK,KAAK,EAAE,SAAS;cAAEI,YAAY,EAAE;YAAM,CAAE;YAAAf,QAAA,EAChDnI,CAAC,CAAC,eAAe;UAAC;YAAAqI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACJ9J,OAAA;YAAQ0J,OAAO,EAAEL,UAAW;YAACE,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EACrDnI,CAAC,CAAC,eAAe;UAAC;YAAAqI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAEN9J,OAAA;UAAMyK,QAAQ,EAAE7D,YAAa;UAAA6C,QAAA,gBAC3BzJ,OAAA;YAAKuJ,SAAS,EAAC,YAAY;YAAAE,QAAA,eACzBzJ,OAAA;cACE0K,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEpK,KAAM;cACbqK,QAAQ,EAAG/D,CAAC,IAAKrG,QAAQ,CAACqG,CAAC,CAACgE,MAAM,CAACF,KAAK,CAAE;cAC1CG,WAAW,EAAExJ,CAAC,CAAC,kBAAkB,CAAE;cACnCiI,SAAS,EAAC,YAAY;cACtBwB,QAAQ,EAAEhK;YAAU;cAAA4I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN9J,OAAA;YACE0K,IAAI,EAAC,QAAQ;YACbK,QAAQ,EAAEhK,SAAS,IAAI,CAACR,KAAK,CAACwG,IAAI,CAAC,CAAE;YACrCwC,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EAE1B1I,SAAS,gBACRf,OAAA,CAAAE,SAAA;cAAAuJ,QAAA,gBACEzJ,OAAA;gBAAMuJ,SAAS,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAChCxI,CAAC,CAAC,YAAY,CAAC;YAAA,eAChB,CAAC,gBAEHtB,OAAA,CAAAE,SAAA;cAAAuJ,QAAA,EACGnI,CAAC,CAAC,kBAAkB;YAAC,gBACtB;UACH;YAAAqI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGAzJ,WAAW,KAAK,MAAM,IAAII,IAAI,iBAC7BT,OAAA;QAAKuJ,SAAS,EAAC,gBAAgB;QAAAE,QAAA,gBAC7BzJ,OAAA;UAAKuJ,SAAS,EAAC,aAAa;UAAAE,QAAA,gBAC1BzJ,OAAA;YAAAyJ,QAAA,EAAKhJ,IAAI,CAACwC;UAAI;YAAA0G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpB9J,OAAA;YAAAyJ,QAAA,EAAInI,CAAC,CAAC,cAAc;UAAC;YAAAqI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1B9J,OAAA;YAAQ0J,OAAO,EAAErB,MAAO;YAACkB,SAAS,EAAC,mBAAmB;YAACQ,KAAK,EAAE;cAACiB,SAAS,EAAE;YAAM,CAAE;YAAAvB,QAAA,EAC/EnI,CAAC,CAAC,YAAY;UAAC;YAAAqI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAEL/I,SAAS,gBACRf,OAAA;UAAKuJ,SAAS,EAAC,SAAS;UAAAE,QAAA,gBACtBzJ,OAAA;YAAMuJ,SAAS,EAAC;UAAS;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjC9J,OAAA;YAAAyJ,QAAA,EAAOnI,CAAC,CAAC,SAAS;UAAC;YAAAqI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,gBAEN9J,OAAA;UAAKuJ,SAAS,EAAC,eAAe;UAAAE,QAAA,EAC3BhJ,IAAI,CAACmE,MAAM,CAACmE,GAAG,CAAC,CAAClH,MAAM,EAAEgD,KAAK,kBAC7B7E,OAAA;YAEEuJ,SAAS,EAAE,eAAe5I,cAAc,KAAKkB,MAAM,GAAG,UAAU,GAAG,EAAE,EAAG;YACxE,cAAYgD,KAAM;YAClB,aAAWhD,MAAM,CAACmB,IAAK;YACvB,oBAAkBnB,MAAM,CAACoJ,SAAU;YACnC,sBAAoBpJ,MAAM,CAACoH,WAAW,IAAI,KAAM;YAChD,cAAYpH,MAAM,CAACsH,KAAK,IAAI,CAAE;YAC9BO,OAAO,EAAEA,CAAA,KAAM1C,kBAAkB,CAACnF,MAAM,CAAE;YAAA4H,QAAA,gBAE1CzJ,OAAA;cAAKuJ,SAAS,EAAC,cAAc;cAAAE,QAAA,EAAE5H,MAAM,CAACqJ;YAAK;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClD9J,OAAA;cAAIuJ,SAAS,EAAC,aAAa;cAAAE,QAAA,EAAE5H,MAAM,CAACmB;YAAI;cAAA2G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9C9J,OAAA;cAAGuJ,SAAS,EAAC,oBAAoB;cAAAE,QAAA,EAAE5H,MAAM,CAACoJ;YAAS;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACvDjI,MAAM,CAACsJ,YAAY,iBAClBnL,OAAA;cAAK+J,KAAK,EAAE;gBAACqB,QAAQ,EAAE,UAAU;gBAAEhB,KAAK,EAAE,SAAS;gBAAEY,SAAS,EAAE;cAAQ,CAAE;cAAAvB,QAAA,GACvEnI,CAAC,CAAC,QAAQ,CAAC,EAAC,IAAE,EAACO,MAAM,CAACsJ,YAAY,CAACrC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC5F,IAAI,CAAC,IAAI,CAAC,EACzDrB,MAAM,CAACsJ,YAAY,CAAClH,MAAM,GAAG,CAAC,IAAI,KAAK;YAAA;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CACN,eACD9J,OAAA;cAAKuJ,SAAS,EAAC,cAAc;cAACQ,KAAK,EAAE;gBACnCqB,QAAQ,EAAE,SAAS;gBACnBhB,KAAK,EAAE,SAAS;gBAChBY,SAAS,EAAE,QAAQ;gBACnBK,SAAS,EAAE;cACb,CAAE;cAAA5B,QAAA,EACCnI,CAAC,CAAC,aAAa;YAAC;cAAAqI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC;UAAA,GAzBDjF,KAAK;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0BP,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGAzJ,WAAW,KAAK,SAAS,IAAIQ,OAAO,iBACnCb,OAAA;QAAKuJ,SAAS,EAAC,gBAAgB;QAAAE,QAAA,eAC7BzJ,OAAA;UAAKuJ,SAAS,EAAC,MAAM;UAAAE,QAAA,gBACnBzJ,OAAA;YAAKuJ,SAAS,EAAC,gBAAgB;YAACQ,KAAK,EAAE;cAACS,YAAY,EAAE;YAAM,CAAE;YAAAf,QAAA,gBAC5DzJ,OAAA;cAAQ0J,OAAO,EAAErB,MAAO;cAACkB,SAAS,EAAC,mBAAmB;cAAAE,QAAA,EACnDnI,CAAC,CAAC,YAAY;YAAC;cAAAqI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGT9J,OAAA;cAAKuJ,SAAS,EAAC,kBAAkB;cAACQ,KAAK,EAAE;gBACvCuB,OAAO,EAAE,MAAM;gBACfC,GAAG,EAAE,KAAK;gBACVP,SAAS,EAAE,MAAM;gBACjBQ,QAAQ,EAAE;cACZ,CAAE;cAAA/B,QAAA,gBAEAzJ,OAAA;gBAAKuJ,SAAS,EAAC,yBAAyB;gBAACQ,KAAK,EAAE;kBAC9CuB,OAAO,EAAE,MAAM;kBACfG,UAAU,EAAE,QAAQ;kBACpBF,GAAG,EAAE,KAAK;kBACVjB,OAAO,EAAE,UAAU;kBACnBJ,UAAU,EAAE,SAAS;kBACrBK,YAAY,EAAE,KAAK;kBACnBJ,MAAM,EAAE;gBACV,CAAE;gBAAAV,QAAA,gBACAzJ,OAAA;kBACE0J,OAAO,EAAEzC,kBAAmB;kBAC5BsC,SAAS,EAAC,UAAU;kBACpB1F,KAAK,EAAC,mBAAmB;kBACzBkG,KAAK,EAAE;oBACLG,UAAU,EAAE,MAAM;oBAClBC,MAAM,EAAE,MAAM;oBACdiB,QAAQ,EAAE,MAAM;oBAChBf,MAAM,EAAE,SAAS;oBACjBC,OAAO,EAAE;kBACX,CAAE;kBAAAb,QAAA,EAEDrK,aAAa,CAAC8H,SAAS,CAAC,CAAC,CAACC,SAAS,GAAG,IAAI,GAAG;gBAAI;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,eACT9J,OAAA;kBACE0J,OAAO,EAAEpC,gBAAiB;kBAC1BiC,SAAS,EAAC,UAAU;kBACpB1F,KAAK,EAAC,aAAa;kBACnBkG,KAAK,EAAE;oBACLG,UAAU,EAAE,MAAM;oBAClBC,MAAM,EAAE,MAAM;oBACdiB,QAAQ,EAAE,MAAM;oBAChBf,MAAM,EAAE,SAAS;oBACjBC,OAAO,EAAE;kBACX,CAAE;kBAAAb,QAAA,EACH;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT9J,OAAA;kBACE0K,IAAI,EAAC,OAAO;kBACZgB,GAAG,EAAC,KAAK;kBACTC,GAAG,EAAC,GAAG;kBACPC,IAAI,EAAC,KAAK;kBACVC,YAAY,EAAC,GAAG;kBAChBjB,QAAQ,EAAG/D,CAAC,IAAKW,sBAAsB,CAACsE,UAAU,CAACjF,CAAC,CAACgE,MAAM,CAACF,KAAK,CAAC,CAAE;kBACpEZ,KAAK,EAAE;oBAACgC,KAAK,EAAE;kBAAM,CAAE;kBACvBlI,KAAK,EAAC;gBAAc;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACF9J,OAAA;kBAAM+J,KAAK,EAAE;oBAACqB,QAAQ,EAAE,MAAM;oBAAEhB,KAAK,EAAE;kBAAS,CAAE;kBAAAX,QAAA,EAAC;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eAGN9J,OAAA;gBAAKuJ,SAAS,EAAC,yBAAyB;gBAACQ,KAAK,EAAE;kBAC9CuB,OAAO,EAAE,MAAM;kBACfC,GAAG,EAAE;gBACP,CAAE;gBAAA9B,QAAA,gBACAzJ,OAAA;kBACE0J,OAAO,EAAExB,qBAAsB;kBAC/BqB,SAAS,EAAC,mBAAmB;kBAC7BQ,KAAK,EAAE;oBAACO,OAAO,EAAE,UAAU;oBAAEc,QAAQ,EAAE;kBAAM,CAAE;kBAC/CvH,KAAK,EAAC,mBAAmB;kBAAA4F,QAAA,EAC1B;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT9J,OAAA;kBACE0J,OAAO,EAAE/B,eAAgB;kBACzB4B,SAAS,EAAC,mBAAmB;kBAC7BQ,KAAK,EAAE;oBAACO,OAAO,EAAE,UAAU;oBAAEc,QAAQ,EAAE;kBAAM,CAAE;kBAC/CvH,KAAK,EAAC,eAAe;kBAAA4F,QAAA,EACtB;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT9J,OAAA;kBACE0J,OAAO,EAAE1B,gBAAiB;kBAC1BuB,SAAS,EAAC,mBAAmB;kBAC7BQ,KAAK,EAAE;oBAACO,OAAO,EAAE,UAAU;oBAAEc,QAAQ,EAAE;kBAAM,CAAE;kBAC/CvH,KAAK,EAAC,gBAAgB;kBAAA4F,QAAA,EACvB;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9J,OAAA;YAAIuJ,SAAS,EAAC,OAAO;YAAAE,QAAA,EAAE5I,OAAO,CAACgD;UAAK;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1C9J,OAAA;YAAK+J,KAAK,EAAE;cAACK,KAAK,EAAE,SAAS;cAAEI,YAAY,EAAE,MAAM;cAAEY,QAAQ,EAAE;YAAQ,CAAE;YAAA3B,QAAA,gBACvEzJ,OAAA;cAAAyJ,QAAA,GAAOnI,CAAC,CAAC,QAAQ,CAAC,EAAC,IAAE,EAACT,OAAO,CAACN,KAAK;YAAA;cAAAoJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAC1CjJ,OAAO,CAACiB,KAAK,IAAIjB,OAAO,CAACiB,KAAK,CAACmC,MAAM,GAAG,CAAC,iBACxCjE,OAAA;cAAM+J,KAAK,EAAE;gBAACE,UAAU,EAAE;cAAM,CAAE;cAAAR,QAAA,GAC/BnI,CAAC,CAAC,OAAO,CAAC,EAAC,IAAE,EAACT,OAAO,CAACiB,KAAK,CAACoB,IAAI,CAAC,IAAI,CAAC;YAAA;cAAAyG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN9J,OAAA;YAAKuJ,SAAS,EAAC,iBAAiB;YAACQ,KAAK,EAAE;cAACiC,UAAU,EAAE,KAAK;cAAEZ,QAAQ,EAAE;YAAQ,CAAE;YAAA3B,QAAA,EAC7E5I,OAAO,CAACkC,OAAO,CAACkJ,KAAK,CAAC,IAAI,CAAC,CAAClD,GAAG,CAAC,CAACmD,SAAS,EAAErH,KAAK,KAChDqH,SAAS,CAACnF,IAAI,CAAC,CAAC,iBACd/G,OAAA;cAAe+J,KAAK,EAAE;gBAACS,YAAY,EAAE;cAAM,CAAE;cAAAf,QAAA,EAC1CyC;YAAS,GADJrH,KAAK;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CAEN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC1J,EAAA,CA7qBID,YAAY;EAAA,QAYFL,cAAc;AAAA;AAAAqM,EAAA,GAZxBhM,YAAY;AA+qBlB,eAAeA,YAAY;AAAC,IAAAgM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}