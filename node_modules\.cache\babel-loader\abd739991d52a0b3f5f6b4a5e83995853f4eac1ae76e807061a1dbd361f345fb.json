{"ast": null, "code": "// Optimized Gesture Service - Pareto 80/20 Implementation\n// Focus on essential gestures that provide maximum value\n\nclass GestureService {\n  constructor() {\n    this.isListening = false;\n    this.lastTap = 0;\n    this.doubleTapDelay = 300; // ms\n    this.callbacks = {\n      doubleTap: null,\n      singleTap: null,\n      longPress: null\n    };\n    this.longPressTimer = null;\n    this.longPressDelay = 500; // ms\n  }\n\n  // Initialize gesture listeners\n  init(element, callbacks = {}) {\n    if (this.isListening) {\n      this.destroy();\n    }\n    this.callbacks = {\n      ...this.callbacks,\n      ...callbacks\n    };\n    this.element = element || document;\n    this.isListening = true;\n\n    // Add event listeners\n    this.element.addEventListener('touchstart', this.handleTouchStart.bind(this), {\n      passive: false\n    });\n    this.element.addEventListener('touchend', this.handleTouchEnd.bind(this), {\n      passive: false\n    });\n    this.element.addEventListener('mousedown', this.handleMouseDown.bind(this));\n    this.element.addEventListener('mouseup', this.handleMouseUp.bind(this));\n    this.element.addEventListener('contextmenu', this.handleContextMenu.bind(this));\n    return this;\n  }\n\n  // Handle touch start\n  handleTouchStart(event) {\n    this.startLongPressTimer(event);\n  }\n\n  // Handle touch end\n  handleTouchEnd(event) {\n    this.clearLongPressTimer();\n    this.handleTap(event);\n  }\n\n  // Handle mouse down\n  handleMouseDown(event) {\n    this.startLongPressTimer(event);\n  }\n\n  // Handle mouse up\n  handleMouseUp(event) {\n    this.clearLongPressTimer();\n    this.handleTap(event);\n  }\n\n  // Handle context menu (prevent on long press)\n  handleContextMenu(event) {\n    event.preventDefault();\n  }\n\n  // Handle tap detection (single/double)\n  handleTap(event) {\n    const now = Date.now();\n    const timeSinceLastTap = now - this.lastTap;\n    if (timeSinceLastTap < this.doubleTapDelay && timeSinceLastTap > 0) {\n      // Double tap detected\n      this.lastTap = 0; // Reset to prevent triple tap\n      if (this.callbacks.doubleTap) {\n        this.callbacks.doubleTap(event, this.getTargetInfo(event));\n      }\n    } else {\n      // Potential single tap - wait to see if double tap follows\n      this.lastTap = now;\n      setTimeout(() => {\n        if (this.lastTap === now && this.callbacks.singleTap) {\n          this.callbacks.singleTap(event, this.getTargetInfo(event));\n        }\n      }, this.doubleTapDelay);\n    }\n  }\n\n  // Start long press timer\n  startLongPressTimer(event) {\n    this.clearLongPressTimer();\n    this.longPressTimer = setTimeout(() => {\n      if (this.callbacks.longPress) {\n        this.callbacks.longPress(event, this.getTargetInfo(event));\n      }\n    }, this.longPressDelay);\n  }\n\n  // Clear long press timer\n  clearLongPressTimer() {\n    if (this.longPressTimer) {\n      clearTimeout(this.longPressTimer);\n      this.longPressTimer = null;\n    }\n  }\n\n  // Get target information\n  getTargetInfo(event) {\n    var _event$touches$, _event$touches$2;\n    const target = event.target;\n    const rect = target.getBoundingClientRect();\n    return {\n      element: target,\n      className: target.className,\n      id: target.id,\n      dataset: target.dataset,\n      position: {\n        x: event.clientX || event.touches && ((_event$touches$ = event.touches[0]) === null || _event$touches$ === void 0 ? void 0 : _event$touches$.clientX) || 0,\n        y: event.clientY || event.touches && ((_event$touches$2 = event.touches[0]) === null || _event$touches$2 === void 0 ? void 0 : _event$touches$2.clientY) || 0\n      },\n      elementRect: rect,\n      isBranchItem: target.closest('.branch-item') !== null,\n      branchData: this.getBranchData(target)\n    };\n  }\n\n  // Extract branch data from target\n  getBranchData(target) {\n    const branchItem = target.closest('.branch-item');\n    if (!branchItem) return null;\n    return {\n      index: branchItem.dataset.index,\n      name: branchItem.dataset.name,\n      description: branchItem.dataset.description,\n      element: branchItem\n    };\n  }\n\n  // Destroy gesture listeners\n  destroy() {\n    if (!this.isListening) return;\n    this.clearLongPressTimer();\n    if (this.element) {\n      this.element.removeEventListener('touchstart', this.handleTouchStart.bind(this));\n      this.element.removeEventListener('touchend', this.handleTouchEnd.bind(this));\n      this.element.removeEventListener('mousedown', this.handleMouseDown.bind(this));\n      this.element.removeEventListener('mouseup', this.handleMouseUp.bind(this));\n      this.element.removeEventListener('contextmenu', this.handleContextMenu.bind(this));\n    }\n    this.isListening = false;\n    this.element = null;\n    this.callbacks = {\n      doubleTap: null,\n      singleTap: null,\n      longPress: null\n    };\n  }\n\n  // Update callbacks\n  updateCallbacks(newCallbacks) {\n    this.callbacks = {\n      ...this.callbacks,\n      ...newCallbacks\n    };\n  }\n\n  // Check if gesture service is active\n  isActive() {\n    return this.isListening;\n  }\n}\n\n// Create singleton instance\nconst gestureService = new GestureService();\nexport default gestureService;\n\n// Helper function to create flag wheel\nexport const createFlagWheel = (position, availableFlags, onFlagSelect, onGenerate) => {\n  // Remove existing wheel\n  const existingWheel = document.querySelector('.flag-wheel');\n  if (existingWheel) {\n    existingWheel.remove();\n  }\n\n  // Create wheel container\n  const wheel = document.createElement('div');\n  wheel.className = 'flag-wheel';\n  wheel.style.cssText = `\n    position: fixed;\n    left: ${position.x - 100}px;\n    top: ${position.y - 100}px;\n    width: 200px;\n    height: 200px;\n    border-radius: 50%;\n    background: rgba(255, 255, 255, 0.95);\n    border: 2px solid #1d4ed8;\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);\n    z-index: 10000;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    backdrop-filter: blur(10px);\n    animation: wheelAppear 0.2s ease-out;\n  `;\n\n  // Create center generate button\n  const centerButton = document.createElement('button');\n  centerButton.className = 'wheel-center-btn';\n  centerButton.textContent = '🚀 Generate';\n  centerButton.style.cssText = `\n    position: absolute;\n    width: 80px;\n    height: 80px;\n    border-radius: 50%;\n    background: #1d4ed8;\n    color: white;\n    border: none;\n    font-size: 12px;\n    font-weight: 600;\n    cursor: pointer;\n    z-index: 10001;\n    transition: all 0.2s ease;\n  `;\n  centerButton.addEventListener('click', () => {\n    const selectedFlags = Array.from(wheel.querySelectorAll('.flag-item.selected')).map(item => item.dataset.flag);\n    onGenerate(selectedFlags);\n    wheel.remove();\n  });\n\n  // Create flag items around the wheel\n  const flagCount = Math.min(availableFlags.length, 8); // Max 8 flags\n  const angleStep = 2 * Math.PI / flagCount;\n  availableFlags.slice(0, flagCount).forEach((flag, index) => {\n    const angle = index * angleStep - Math.PI / 2; // Start from top\n    const radius = 70;\n    const x = Math.cos(angle) * radius;\n    const y = Math.sin(angle) * radius;\n    const flagItem = document.createElement('div');\n    flagItem.className = 'flag-item';\n    flagItem.dataset.flag = flag.code;\n    flagItem.style.cssText = `\n      position: absolute;\n      left: ${100 + x - 15}px;\n      top: ${100 + y - 15}px;\n      width: 30px;\n      height: 30px;\n      border-radius: 50%;\n      background: #f1f5f9;\n      border: 2px solid #e2e8f0;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      cursor: pointer;\n      font-size: 10px;\n      font-weight: 600;\n      color: #1d4ed8;\n      transition: all 0.2s ease;\n      user-select: none;\n    `;\n    flagItem.textContent = flag.code;\n    flagItem.title = flag.name;\n\n    // Add hover tooltip\n    let tooltip = null;\n    flagItem.addEventListener('mouseenter', () => {\n      // Remove existing tooltip\n      const existingTooltip = document.querySelector('.flag-tooltip');\n      if (existingTooltip) existingTooltip.remove();\n\n      // Create tooltip\n      tooltip = document.createElement('div');\n      tooltip.className = 'flag-tooltip';\n      tooltip.innerHTML = `\n        <div class=\"tooltip-content\">\n          <div class=\"tooltip-title\">${flag.name}</div>\n          <div class=\"tooltip-description\">${flag.description}</div>\n        </div>\n      `;\n      tooltip.style.cssText = `\n        position: fixed;\n        left: ${position.x + 120}px;\n        top: ${position.y - 20}px;\n        background: rgba(30, 41, 59, 0.95);\n        color: white;\n        padding: 12px;\n        border-radius: 8px;\n        font-size: 12px;\n        max-width: 200px;\n        z-index: 10002;\n        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\n        backdrop-filter: blur(10px);\n        animation: tooltipFadeIn 0.2s ease;\n      `;\n      document.body.appendChild(tooltip);\n    });\n    flagItem.addEventListener('mouseleave', () => {\n      if (tooltip) {\n        tooltip.remove();\n        tooltip = null;\n      }\n    });\n    flagItem.addEventListener('click', () => {\n      flagItem.classList.toggle('selected');\n      if (flagItem.classList.contains('selected')) {\n        flagItem.style.background = '#1d4ed8';\n        flagItem.style.color = 'white';\n        flagItem.style.borderColor = '#1d4ed8';\n      } else {\n        flagItem.style.background = '#f1f5f9';\n        flagItem.style.color = '#1d4ed8';\n        flagItem.style.borderColor = '#e2e8f0';\n      }\n    });\n    wheel.appendChild(flagItem);\n  });\n  wheel.appendChild(centerButton);\n\n  // Add CSS animation\n  const style = document.createElement('style');\n  style.textContent = `\n    @keyframes wheelAppear {\n      from {\n        opacity: 0;\n        transform: scale(0.5);\n      }\n      to {\n        opacity: 1;\n        transform: scale(1);\n      }\n    }\n  `;\n  document.head.appendChild(style);\n\n  // Add to document\n  document.body.appendChild(wheel);\n\n  // Remove wheel when clicking outside\n  const removeWheel = event => {\n    if (!wheel.contains(event.target)) {\n      wheel.remove();\n      document.removeEventListener('click', removeWheel);\n    }\n  };\n  setTimeout(() => {\n    document.addEventListener('click', removeWheel);\n  }, 100);\n  return wheel;\n};", "map": {"version": 3, "names": ["GestureService", "constructor", "isListening", "lastTap", "doubleTapDelay", "callbacks", "doubleTap", "singleTap", "longPress", "longPressTimer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "init", "element", "destroy", "document", "addEventListener", "handleTouchStart", "bind", "passive", "handleTouchEnd", "handleMouseDown", "handleMouseUp", "handleContextMenu", "event", "startLongPressTimer", "clearLongPressTimer", "handleTap", "preventDefault", "now", "Date", "timeSinceLastTap", "getTargetInfo", "setTimeout", "clearTimeout", "_event$touches$", "_event$touches$2", "target", "rect", "getBoundingClientRect", "className", "id", "dataset", "position", "x", "clientX", "touches", "y", "clientY", "elementRect", "isBranchItem", "closest", "branchData", "getBranchData", "branchItem", "index", "name", "description", "removeEventListener", "updateCallbacks", "newCallbacks", "isActive", "gestureService", "createFlagWheel", "availableFlags", "onFlagSelect", "onGenerate", "existingWheel", "querySelector", "remove", "wheel", "createElement", "style", "cssText", "centerButton", "textContent", "selected<PERSON><PERSON><PERSON>", "Array", "from", "querySelectorAll", "map", "item", "flag", "flagCount", "Math", "min", "length", "angleStep", "PI", "slice", "for<PERSON>ach", "angle", "radius", "cos", "sin", "flagItem", "code", "title", "tooltip", "existingTooltip", "innerHTML", "body", "append<PERSON><PERSON><PERSON>", "classList", "toggle", "contains", "background", "color", "borderColor", "head", "removeW<PERSON>l"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Appv1/src/services/gestureService.js"], "sourcesContent": ["// Optimized Gesture Service - Pareto 80/20 Implementation\n// Focus on essential gestures that provide maximum value\n\nclass GestureService {\n  constructor() {\n    this.isListening = false;\n    this.lastTap = 0;\n    this.doubleTapDelay = 300; // ms\n    this.callbacks = {\n      doubleTap: null,\n      singleTap: null,\n      longPress: null\n    };\n    this.longPressTimer = null;\n    this.longPressDelay = 500; // ms\n  }\n\n  // Initialize gesture listeners\n  init(element, callbacks = {}) {\n    if (this.isListening) {\n      this.destroy();\n    }\n\n    this.callbacks = { ...this.callbacks, ...callbacks };\n    this.element = element || document;\n    this.isListening = true;\n\n    // Add event listeners\n    this.element.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: false });\n    this.element.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: false });\n    this.element.addEventListener('mousedown', this.handleMouseDown.bind(this));\n    this.element.addEventListener('mouseup', this.handleMouseUp.bind(this));\n    this.element.addEventListener('contextmenu', this.handleContextMenu.bind(this));\n\n    return this;\n  }\n\n  // Handle touch start\n  handleTouchStart(event) {\n    this.startLongPressTimer(event);\n  }\n\n  // Handle touch end\n  handleTouchEnd(event) {\n    this.clearLongPressTimer();\n    this.handleTap(event);\n  }\n\n  // Handle mouse down\n  handleMouseDown(event) {\n    this.startLongPressTimer(event);\n  }\n\n  // Handle mouse up\n  handleMouseUp(event) {\n    this.clearLongPressTimer();\n    this.handleTap(event);\n  }\n\n  // Handle context menu (prevent on long press)\n  handleContextMenu(event) {\n    event.preventDefault();\n  }\n\n  // Handle tap detection (single/double)\n  handleTap(event) {\n    const now = Date.now();\n    const timeSinceLastTap = now - this.lastTap;\n\n    if (timeSinceLastTap < this.doubleTapDelay && timeSinceLastTap > 0) {\n      // Double tap detected\n      this.lastTap = 0; // Reset to prevent triple tap\n      if (this.callbacks.doubleTap) {\n        this.callbacks.doubleTap(event, this.getTargetInfo(event));\n      }\n    } else {\n      // Potential single tap - wait to see if double tap follows\n      this.lastTap = now;\n      setTimeout(() => {\n        if (this.lastTap === now && this.callbacks.singleTap) {\n          this.callbacks.singleTap(event, this.getTargetInfo(event));\n        }\n      }, this.doubleTapDelay);\n    }\n  }\n\n  // Start long press timer\n  startLongPressTimer(event) {\n    this.clearLongPressTimer();\n    this.longPressTimer = setTimeout(() => {\n      if (this.callbacks.longPress) {\n        this.callbacks.longPress(event, this.getTargetInfo(event));\n      }\n    }, this.longPressDelay);\n  }\n\n  // Clear long press timer\n  clearLongPressTimer() {\n    if (this.longPressTimer) {\n      clearTimeout(this.longPressTimer);\n      this.longPressTimer = null;\n    }\n  }\n\n  // Get target information\n  getTargetInfo(event) {\n    const target = event.target;\n    const rect = target.getBoundingClientRect();\n    \n    return {\n      element: target,\n      className: target.className,\n      id: target.id,\n      dataset: target.dataset,\n      position: {\n        x: event.clientX || (event.touches && event.touches[0]?.clientX) || 0,\n        y: event.clientY || (event.touches && event.touches[0]?.clientY) || 0\n      },\n      elementRect: rect,\n      isBranchItem: target.closest('.branch-item') !== null,\n      branchData: this.getBranchData(target)\n    };\n  }\n\n  // Extract branch data from target\n  getBranchData(target) {\n    const branchItem = target.closest('.branch-item');\n    if (!branchItem) return null;\n\n    return {\n      index: branchItem.dataset.index,\n      name: branchItem.dataset.name,\n      description: branchItem.dataset.description,\n      element: branchItem\n    };\n  }\n\n  // Destroy gesture listeners\n  destroy() {\n    if (!this.isListening) return;\n\n    this.clearLongPressTimer();\n    \n    if (this.element) {\n      this.element.removeEventListener('touchstart', this.handleTouchStart.bind(this));\n      this.element.removeEventListener('touchend', this.handleTouchEnd.bind(this));\n      this.element.removeEventListener('mousedown', this.handleMouseDown.bind(this));\n      this.element.removeEventListener('mouseup', this.handleMouseUp.bind(this));\n      this.element.removeEventListener('contextmenu', this.handleContextMenu.bind(this));\n    }\n\n    this.isListening = false;\n    this.element = null;\n    this.callbacks = {\n      doubleTap: null,\n      singleTap: null,\n      longPress: null\n    };\n  }\n\n  // Update callbacks\n  updateCallbacks(newCallbacks) {\n    this.callbacks = { ...this.callbacks, ...newCallbacks };\n  }\n\n  // Check if gesture service is active\n  isActive() {\n    return this.isListening;\n  }\n}\n\n// Create singleton instance\nconst gestureService = new GestureService();\n\nexport default gestureService;\n\n// Helper function to create flag wheel\nexport const createFlagWheel = (position, availableFlags, onFlagSelect, onGenerate) => {\n  // Remove existing wheel\n  const existingWheel = document.querySelector('.flag-wheel');\n  if (existingWheel) {\n    existingWheel.remove();\n  }\n\n  // Create wheel container\n  const wheel = document.createElement('div');\n  wheel.className = 'flag-wheel';\n  wheel.style.cssText = `\n    position: fixed;\n    left: ${position.x - 100}px;\n    top: ${position.y - 100}px;\n    width: 200px;\n    height: 200px;\n    border-radius: 50%;\n    background: rgba(255, 255, 255, 0.95);\n    border: 2px solid #1d4ed8;\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);\n    z-index: 10000;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    backdrop-filter: blur(10px);\n    animation: wheelAppear 0.2s ease-out;\n  `;\n\n  // Create center generate button\n  const centerButton = document.createElement('button');\n  centerButton.className = 'wheel-center-btn';\n  centerButton.textContent = '🚀 Generate';\n  centerButton.style.cssText = `\n    position: absolute;\n    width: 80px;\n    height: 80px;\n    border-radius: 50%;\n    background: #1d4ed8;\n    color: white;\n    border: none;\n    font-size: 12px;\n    font-weight: 600;\n    cursor: pointer;\n    z-index: 10001;\n    transition: all 0.2s ease;\n  `;\n\n  centerButton.addEventListener('click', () => {\n    const selectedFlags = Array.from(wheel.querySelectorAll('.flag-item.selected'))\n      .map(item => item.dataset.flag);\n    onGenerate(selectedFlags);\n    wheel.remove();\n  });\n\n  // Create flag items around the wheel\n  const flagCount = Math.min(availableFlags.length, 8); // Max 8 flags\n  const angleStep = (2 * Math.PI) / flagCount;\n\n  availableFlags.slice(0, flagCount).forEach((flag, index) => {\n    const angle = index * angleStep - Math.PI / 2; // Start from top\n    const radius = 70;\n    const x = Math.cos(angle) * radius;\n    const y = Math.sin(angle) * radius;\n\n    const flagItem = document.createElement('div');\n    flagItem.className = 'flag-item';\n    flagItem.dataset.flag = flag.code;\n    flagItem.style.cssText = `\n      position: absolute;\n      left: ${100 + x - 15}px;\n      top: ${100 + y - 15}px;\n      width: 30px;\n      height: 30px;\n      border-radius: 50%;\n      background: #f1f5f9;\n      border: 2px solid #e2e8f0;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      cursor: pointer;\n      font-size: 10px;\n      font-weight: 600;\n      color: #1d4ed8;\n      transition: all 0.2s ease;\n      user-select: none;\n    `;\n    flagItem.textContent = flag.code;\n    flagItem.title = flag.name;\n\n    // Add hover tooltip\n    let tooltip = null;\n\n    flagItem.addEventListener('mouseenter', () => {\n      // Remove existing tooltip\n      const existingTooltip = document.querySelector('.flag-tooltip');\n      if (existingTooltip) existingTooltip.remove();\n\n      // Create tooltip\n      tooltip = document.createElement('div');\n      tooltip.className = 'flag-tooltip';\n      tooltip.innerHTML = `\n        <div class=\"tooltip-content\">\n          <div class=\"tooltip-title\">${flag.name}</div>\n          <div class=\"tooltip-description\">${flag.description}</div>\n        </div>\n      `;\n      tooltip.style.cssText = `\n        position: fixed;\n        left: ${position.x + 120}px;\n        top: ${position.y - 20}px;\n        background: rgba(30, 41, 59, 0.95);\n        color: white;\n        padding: 12px;\n        border-radius: 8px;\n        font-size: 12px;\n        max-width: 200px;\n        z-index: 10002;\n        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\n        backdrop-filter: blur(10px);\n        animation: tooltipFadeIn 0.2s ease;\n      `;\n      document.body.appendChild(tooltip);\n    });\n\n    flagItem.addEventListener('mouseleave', () => {\n      if (tooltip) {\n        tooltip.remove();\n        tooltip = null;\n      }\n    });\n\n    flagItem.addEventListener('click', () => {\n      flagItem.classList.toggle('selected');\n      if (flagItem.classList.contains('selected')) {\n        flagItem.style.background = '#1d4ed8';\n        flagItem.style.color = 'white';\n        flagItem.style.borderColor = '#1d4ed8';\n      } else {\n        flagItem.style.background = '#f1f5f9';\n        flagItem.style.color = '#1d4ed8';\n        flagItem.style.borderColor = '#e2e8f0';\n      }\n    });\n\n    wheel.appendChild(flagItem);\n  });\n\n  wheel.appendChild(centerButton);\n\n  // Add CSS animation\n  const style = document.createElement('style');\n  style.textContent = `\n    @keyframes wheelAppear {\n      from {\n        opacity: 0;\n        transform: scale(0.5);\n      }\n      to {\n        opacity: 1;\n        transform: scale(1);\n      }\n    }\n  `;\n  document.head.appendChild(style);\n\n  // Add to document\n  document.body.appendChild(wheel);\n\n  // Remove wheel when clicking outside\n  const removeWheel = (event) => {\n    if (!wheel.contains(event.target)) {\n      wheel.remove();\n      document.removeEventListener('click', removeWheel);\n    }\n  };\n  \n  setTimeout(() => {\n    document.addEventListener('click', removeWheel);\n  }, 100);\n\n  return wheel;\n};\n"], "mappings": "AAAA;AACA;;AAEA,MAAMA,cAAc,CAAC;EACnBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,OAAO,GAAG,CAAC;IAChB,IAAI,CAACC,cAAc,GAAG,GAAG,CAAC,CAAC;IAC3B,IAAI,CAACC,SAAS,GAAG;MACfC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE;IACb,CAAC;IACD,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,cAAc,GAAG,GAAG,CAAC,CAAC;EAC7B;;EAEA;EACAC,IAAIA,CAACC,OAAO,EAAEP,SAAS,GAAG,CAAC,CAAC,EAAE;IAC5B,IAAI,IAAI,CAACH,WAAW,EAAE;MACpB,IAAI,CAACW,OAAO,CAAC,CAAC;IAChB;IAEA,IAAI,CAACR,SAAS,GAAG;MAAE,GAAG,IAAI,CAACA,SAAS;MAAE,GAAGA;IAAU,CAAC;IACpD,IAAI,CAACO,OAAO,GAAGA,OAAO,IAAIE,QAAQ;IAClC,IAAI,CAACZ,WAAW,GAAG,IAAI;;IAEvB;IACA,IAAI,CAACU,OAAO,CAACG,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAACC,gBAAgB,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE;MAAEC,OAAO,EAAE;IAAM,CAAC,CAAC;IACjG,IAAI,CAACN,OAAO,CAACG,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAACI,cAAc,CAACF,IAAI,CAAC,IAAI,CAAC,EAAE;MAAEC,OAAO,EAAE;IAAM,CAAC,CAAC;IAC7F,IAAI,CAACN,OAAO,CAACG,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACK,eAAe,CAACH,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3E,IAAI,CAACL,OAAO,CAACG,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACM,aAAa,CAACJ,IAAI,CAAC,IAAI,CAAC,CAAC;IACvE,IAAI,CAACL,OAAO,CAACG,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAACO,iBAAiB,CAACL,IAAI,CAAC,IAAI,CAAC,CAAC;IAE/E,OAAO,IAAI;EACb;;EAEA;EACAD,gBAAgBA,CAACO,KAAK,EAAE;IACtB,IAAI,CAACC,mBAAmB,CAACD,KAAK,CAAC;EACjC;;EAEA;EACAJ,cAAcA,CAACI,KAAK,EAAE;IACpB,IAAI,CAACE,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAACC,SAAS,CAACH,KAAK,CAAC;EACvB;;EAEA;EACAH,eAAeA,CAACG,KAAK,EAAE;IACrB,IAAI,CAACC,mBAAmB,CAACD,KAAK,CAAC;EACjC;;EAEA;EACAF,aAAaA,CAACE,KAAK,EAAE;IACnB,IAAI,CAACE,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAACC,SAAS,CAACH,KAAK,CAAC;EACvB;;EAEA;EACAD,iBAAiBA,CAACC,KAAK,EAAE;IACvBA,KAAK,CAACI,cAAc,CAAC,CAAC;EACxB;;EAEA;EACAD,SAASA,CAACH,KAAK,EAAE;IACf,MAAMK,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;IACtB,MAAME,gBAAgB,GAAGF,GAAG,GAAG,IAAI,CAACzB,OAAO;IAE3C,IAAI2B,gBAAgB,GAAG,IAAI,CAAC1B,cAAc,IAAI0B,gBAAgB,GAAG,CAAC,EAAE;MAClE;MACA,IAAI,CAAC3B,OAAO,GAAG,CAAC,CAAC,CAAC;MAClB,IAAI,IAAI,CAACE,SAAS,CAACC,SAAS,EAAE;QAC5B,IAAI,CAACD,SAAS,CAACC,SAAS,CAACiB,KAAK,EAAE,IAAI,CAACQ,aAAa,CAACR,KAAK,CAAC,CAAC;MAC5D;IACF,CAAC,MAAM;MACL;MACA,IAAI,CAACpB,OAAO,GAAGyB,GAAG;MAClBI,UAAU,CAAC,MAAM;QACf,IAAI,IAAI,CAAC7B,OAAO,KAAKyB,GAAG,IAAI,IAAI,CAACvB,SAAS,CAACE,SAAS,EAAE;UACpD,IAAI,CAACF,SAAS,CAACE,SAAS,CAACgB,KAAK,EAAE,IAAI,CAACQ,aAAa,CAACR,KAAK,CAAC,CAAC;QAC5D;MACF,CAAC,EAAE,IAAI,CAACnB,cAAc,CAAC;IACzB;EACF;;EAEA;EACAoB,mBAAmBA,CAACD,KAAK,EAAE;IACzB,IAAI,CAACE,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAAChB,cAAc,GAAGuB,UAAU,CAAC,MAAM;MACrC,IAAI,IAAI,CAAC3B,SAAS,CAACG,SAAS,EAAE;QAC5B,IAAI,CAACH,SAAS,CAACG,SAAS,CAACe,KAAK,EAAE,IAAI,CAACQ,aAAa,CAACR,KAAK,CAAC,CAAC;MAC5D;IACF,CAAC,EAAE,IAAI,CAACb,cAAc,CAAC;EACzB;;EAEA;EACAe,mBAAmBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAAChB,cAAc,EAAE;MACvBwB,YAAY,CAAC,IAAI,CAACxB,cAAc,CAAC;MACjC,IAAI,CAACA,cAAc,GAAG,IAAI;IAC5B;EACF;;EAEA;EACAsB,aAAaA,CAACR,KAAK,EAAE;IAAA,IAAAW,eAAA,EAAAC,gBAAA;IACnB,MAAMC,MAAM,GAAGb,KAAK,CAACa,MAAM;IAC3B,MAAMC,IAAI,GAAGD,MAAM,CAACE,qBAAqB,CAAC,CAAC;IAE3C,OAAO;MACL1B,OAAO,EAAEwB,MAAM;MACfG,SAAS,EAAEH,MAAM,CAACG,SAAS;MAC3BC,EAAE,EAAEJ,MAAM,CAACI,EAAE;MACbC,OAAO,EAAEL,MAAM,CAACK,OAAO;MACvBC,QAAQ,EAAE;QACRC,CAAC,EAAEpB,KAAK,CAACqB,OAAO,IAAKrB,KAAK,CAACsB,OAAO,MAAAX,eAAA,GAAIX,KAAK,CAACsB,OAAO,CAAC,CAAC,CAAC,cAAAX,eAAA,uBAAhBA,eAAA,CAAkBU,OAAO,CAAC,IAAI,CAAC;QACrEE,CAAC,EAAEvB,KAAK,CAACwB,OAAO,IAAKxB,KAAK,CAACsB,OAAO,MAAAV,gBAAA,GAAIZ,KAAK,CAACsB,OAAO,CAAC,CAAC,CAAC,cAAAV,gBAAA,uBAAhBA,gBAAA,CAAkBY,OAAO,CAAC,IAAI;MACtE,CAAC;MACDC,WAAW,EAAEX,IAAI;MACjBY,YAAY,EAAEb,MAAM,CAACc,OAAO,CAAC,cAAc,CAAC,KAAK,IAAI;MACrDC,UAAU,EAAE,IAAI,CAACC,aAAa,CAAChB,MAAM;IACvC,CAAC;EACH;;EAEA;EACAgB,aAAaA,CAAChB,MAAM,EAAE;IACpB,MAAMiB,UAAU,GAAGjB,MAAM,CAACc,OAAO,CAAC,cAAc,CAAC;IACjD,IAAI,CAACG,UAAU,EAAE,OAAO,IAAI;IAE5B,OAAO;MACLC,KAAK,EAAED,UAAU,CAACZ,OAAO,CAACa,KAAK;MAC/BC,IAAI,EAAEF,UAAU,CAACZ,OAAO,CAACc,IAAI;MAC7BC,WAAW,EAAEH,UAAU,CAACZ,OAAO,CAACe,WAAW;MAC3C5C,OAAO,EAAEyC;IACX,CAAC;EACH;;EAEA;EACAxC,OAAOA,CAAA,EAAG;IACR,IAAI,CAAC,IAAI,CAACX,WAAW,EAAE;IAEvB,IAAI,CAACuB,mBAAmB,CAAC,CAAC;IAE1B,IAAI,IAAI,CAACb,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAAC6C,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAACzC,gBAAgB,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MAChF,IAAI,CAACL,OAAO,CAAC6C,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAACtC,cAAc,CAACF,IAAI,CAAC,IAAI,CAAC,CAAC;MAC5E,IAAI,CAACL,OAAO,CAAC6C,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACrC,eAAe,CAACH,IAAI,CAAC,IAAI,CAAC,CAAC;MAC9E,IAAI,CAACL,OAAO,CAAC6C,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACpC,aAAa,CAACJ,IAAI,CAAC,IAAI,CAAC,CAAC;MAC1E,IAAI,CAACL,OAAO,CAAC6C,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAACnC,iBAAiB,CAACL,IAAI,CAAC,IAAI,CAAC,CAAC;IACpF;IAEA,IAAI,CAACf,WAAW,GAAG,KAAK;IACxB,IAAI,CAACU,OAAO,GAAG,IAAI;IACnB,IAAI,CAACP,SAAS,GAAG;MACfC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE;IACb,CAAC;EACH;;EAEA;EACAkD,eAAeA,CAACC,YAAY,EAAE;IAC5B,IAAI,CAACtD,SAAS,GAAG;MAAE,GAAG,IAAI,CAACA,SAAS;MAAE,GAAGsD;IAAa,CAAC;EACzD;;EAEA;EACAC,QAAQA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC1D,WAAW;EACzB;AACF;;AAEA;AACA,MAAM2D,cAAc,GAAG,IAAI7D,cAAc,CAAC,CAAC;AAE3C,eAAe6D,cAAc;;AAE7B;AACA,OAAO,MAAMC,eAAe,GAAGA,CAACpB,QAAQ,EAAEqB,cAAc,EAAEC,YAAY,EAAEC,UAAU,KAAK;EACrF;EACA,MAAMC,aAAa,GAAGpD,QAAQ,CAACqD,aAAa,CAAC,aAAa,CAAC;EAC3D,IAAID,aAAa,EAAE;IACjBA,aAAa,CAACE,MAAM,CAAC,CAAC;EACxB;;EAEA;EACA,MAAMC,KAAK,GAAGvD,QAAQ,CAACwD,aAAa,CAAC,KAAK,CAAC;EAC3CD,KAAK,CAAC9B,SAAS,GAAG,YAAY;EAC9B8B,KAAK,CAACE,KAAK,CAACC,OAAO,GAAG;AACxB;AACA,YAAY9B,QAAQ,CAACC,CAAC,GAAG,GAAG;AAC5B,WAAWD,QAAQ,CAACI,CAAC,GAAG,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;EAED;EACA,MAAM2B,YAAY,GAAG3D,QAAQ,CAACwD,aAAa,CAAC,QAAQ,CAAC;EACrDG,YAAY,CAAClC,SAAS,GAAG,kBAAkB;EAC3CkC,YAAY,CAACC,WAAW,GAAG,aAAa;EACxCD,YAAY,CAACF,KAAK,CAACC,OAAO,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;EAEDC,YAAY,CAAC1D,gBAAgB,CAAC,OAAO,EAAE,MAAM;IAC3C,MAAM4D,aAAa,GAAGC,KAAK,CAACC,IAAI,CAACR,KAAK,CAACS,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,CAC5EC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACvC,OAAO,CAACwC,IAAI,CAAC;IACjChB,UAAU,CAACU,aAAa,CAAC;IACzBN,KAAK,CAACD,MAAM,CAAC,CAAC;EAChB,CAAC,CAAC;;EAEF;EACA,MAAMc,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACrB,cAAc,CAACsB,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;EACtD,MAAMC,SAAS,GAAI,CAAC,GAAGH,IAAI,CAACI,EAAE,GAAIL,SAAS;EAE3CnB,cAAc,CAACyB,KAAK,CAAC,CAAC,EAAEN,SAAS,CAAC,CAACO,OAAO,CAAC,CAACR,IAAI,EAAE3B,KAAK,KAAK;IAC1D,MAAMoC,KAAK,GAAGpC,KAAK,GAAGgC,SAAS,GAAGH,IAAI,CAACI,EAAE,GAAG,CAAC,CAAC,CAAC;IAC/C,MAAMI,MAAM,GAAG,EAAE;IACjB,MAAMhD,CAAC,GAAGwC,IAAI,CAACS,GAAG,CAACF,KAAK,CAAC,GAAGC,MAAM;IAClC,MAAM7C,CAAC,GAAGqC,IAAI,CAACU,GAAG,CAACH,KAAK,CAAC,GAAGC,MAAM;IAElC,MAAMG,QAAQ,GAAGhF,QAAQ,CAACwD,aAAa,CAAC,KAAK,CAAC;IAC9CwB,QAAQ,CAACvD,SAAS,GAAG,WAAW;IAChCuD,QAAQ,CAACrD,OAAO,CAACwC,IAAI,GAAGA,IAAI,CAACc,IAAI;IACjCD,QAAQ,CAACvB,KAAK,CAACC,OAAO,GAAG;AAC7B;AACA,cAAc,GAAG,GAAG7B,CAAC,GAAG,EAAE;AAC1B,aAAa,GAAG,GAAGG,CAAC,GAAG,EAAE;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;IACDgD,QAAQ,CAACpB,WAAW,GAAGO,IAAI,CAACc,IAAI;IAChCD,QAAQ,CAACE,KAAK,GAAGf,IAAI,CAAC1B,IAAI;;IAE1B;IACA,IAAI0C,OAAO,GAAG,IAAI;IAElBH,QAAQ,CAAC/E,gBAAgB,CAAC,YAAY,EAAE,MAAM;MAC5C;MACA,MAAMmF,eAAe,GAAGpF,QAAQ,CAACqD,aAAa,CAAC,eAAe,CAAC;MAC/D,IAAI+B,eAAe,EAAEA,eAAe,CAAC9B,MAAM,CAAC,CAAC;;MAE7C;MACA6B,OAAO,GAAGnF,QAAQ,CAACwD,aAAa,CAAC,KAAK,CAAC;MACvC2B,OAAO,CAAC1D,SAAS,GAAG,cAAc;MAClC0D,OAAO,CAACE,SAAS,GAAG;AAC1B;AACA,uCAAuClB,IAAI,CAAC1B,IAAI;AAChD,6CAA6C0B,IAAI,CAACzB,WAAW;AAC7D;AACA,OAAO;MACDyC,OAAO,CAAC1B,KAAK,CAACC,OAAO,GAAG;AAC9B;AACA,gBAAgB9B,QAAQ,CAACC,CAAC,GAAG,GAAG;AAChC,eAAeD,QAAQ,CAACI,CAAC,GAAG,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;MACDhC,QAAQ,CAACsF,IAAI,CAACC,WAAW,CAACJ,OAAO,CAAC;IACpC,CAAC,CAAC;IAEFH,QAAQ,CAAC/E,gBAAgB,CAAC,YAAY,EAAE,MAAM;MAC5C,IAAIkF,OAAO,EAAE;QACXA,OAAO,CAAC7B,MAAM,CAAC,CAAC;QAChB6B,OAAO,GAAG,IAAI;MAChB;IACF,CAAC,CAAC;IAEFH,QAAQ,CAAC/E,gBAAgB,CAAC,OAAO,EAAE,MAAM;MACvC+E,QAAQ,CAACQ,SAAS,CAACC,MAAM,CAAC,UAAU,CAAC;MACrC,IAAIT,QAAQ,CAACQ,SAAS,CAACE,QAAQ,CAAC,UAAU,CAAC,EAAE;QAC3CV,QAAQ,CAACvB,KAAK,CAACkC,UAAU,GAAG,SAAS;QACrCX,QAAQ,CAACvB,KAAK,CAACmC,KAAK,GAAG,OAAO;QAC9BZ,QAAQ,CAACvB,KAAK,CAACoC,WAAW,GAAG,SAAS;MACxC,CAAC,MAAM;QACLb,QAAQ,CAACvB,KAAK,CAACkC,UAAU,GAAG,SAAS;QACrCX,QAAQ,CAACvB,KAAK,CAACmC,KAAK,GAAG,SAAS;QAChCZ,QAAQ,CAACvB,KAAK,CAACoC,WAAW,GAAG,SAAS;MACxC;IACF,CAAC,CAAC;IAEFtC,KAAK,CAACgC,WAAW,CAACP,QAAQ,CAAC;EAC7B,CAAC,CAAC;EAEFzB,KAAK,CAACgC,WAAW,CAAC5B,YAAY,CAAC;;EAE/B;EACA,MAAMF,KAAK,GAAGzD,QAAQ,CAACwD,aAAa,CAAC,OAAO,CAAC;EAC7CC,KAAK,CAACG,WAAW,GAAG;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;EACD5D,QAAQ,CAAC8F,IAAI,CAACP,WAAW,CAAC9B,KAAK,CAAC;;EAEhC;EACAzD,QAAQ,CAACsF,IAAI,CAACC,WAAW,CAAChC,KAAK,CAAC;;EAEhC;EACA,MAAMwC,WAAW,GAAItF,KAAK,IAAK;IAC7B,IAAI,CAAC8C,KAAK,CAACmC,QAAQ,CAACjF,KAAK,CAACa,MAAM,CAAC,EAAE;MACjCiC,KAAK,CAACD,MAAM,CAAC,CAAC;MACdtD,QAAQ,CAAC2C,mBAAmB,CAAC,OAAO,EAAEoD,WAAW,CAAC;IACpD;EACF,CAAC;EAED7E,UAAU,CAAC,MAAM;IACflB,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAE8F,WAAW,CAAC;EACjD,CAAC,EAAE,GAAG,CAAC;EAEP,OAAOxC,KAAK;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}