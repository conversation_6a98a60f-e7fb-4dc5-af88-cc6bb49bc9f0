{"ast": null, "code": "// OpenRouter API Service for Knowledge Tree Generation\nimport webSearchService from './webSearchService';\nconst OPENROUTER_API_KEY = process.env.REACT_APP_OPENROUTER_API_KEY || '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';\nconst OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';\nconst MODEL = process.env.REACT_APP_OPENROUTER_MODEL || 'deepseek/deepseek-r1-0528:free';\n\n// Site configuration for OpenRouter rankings\nconst SITE_CONFIG = {\n  'HTTP-Referer': process.env.REACT_APP_SITE_URL || 'http://localhost:3000',\n  'X-Title': process.env.REACT_APP_SITE_NAME || 'Knowledge Tree Explorer'\n};\nclass OpenRouterClient {\n  constructor() {\n    this.baseURL = OPENROUTER_BASE_URL;\n    this.apiKey = OPENROUTER_API_KEY;\n\n    // Debug logging - always show for troubleshooting\n    console.log('🔧 OpenRouter Client initialized:');\n    console.log('- Base URL:', this.baseURL);\n    console.log('- Model:', MODEL);\n    console.log('- API Key:', this.apiKey ? `${this.apiKey.substring(0, 10)}...` : 'NOT SET');\n    console.log('- Site Config:', SITE_CONFIG);\n  }\n  async makeRequest(messages, temperature = 0.7) {\n    try {\n      const response = await fetch(`${this.baseURL}/chat/completions`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${this.apiKey}`,\n          'Content-Type': 'application/json',\n          ...SITE_CONFIG\n        },\n        body: JSON.stringify({\n          model: MODEL,\n          messages,\n          temperature,\n          max_tokens: 1500,\n          // Optimizat pentru răspunsuri mai rapide\n          stream: false\n        })\n      });\n      if (!response.ok) {\n        var _errorData$error;\n        const errorData = await response.json().catch(() => ({}));\n        throw new Error(`OpenRouter API Error: ${response.status} - ${((_errorData$error = errorData.error) === null || _errorData$error === void 0 ? void 0 : _errorData$error.message) || 'Unknown error'}`);\n      }\n      const data = await response.json();\n      return data.choices[0].message.content;\n    } catch (error) {\n      console.error('OpenRouter API request failed:', error);\n      throw error;\n    }\n  }\n}\nconst client = new OpenRouterClient();\n\n// Generate Knowledge Tree from topic\nexport async function generateKnowledgeTree(topic, language = 'en') {\n  // Get current language from localStorage if not provided\n  const currentLang = language || localStorage.getItem('language') || 'en';\n  const prompts = {\n    ro: `Analizează cu atenție subiectul \"${topic}\" și creează un arbore de cunoștințe FOARTE SPECIFIC și RELEVANT pentru acest domeniu exact.\n\nIMPORTANT: Generează ramuri care sunt DIRECT LEGATE de \"${topic}\", nu concepte generale!\n\nReturnează DOAR un obiect JSON valid cu această structură exactă:\n{\n  \"tema\": \"${topic}\",\n  \"ramuri\": [\n    {\n      \"nume\": \"Nume Ramură Specifică\",\n      \"descriere\": \"Descriere scurtă și precisă\",\n      \"emoji\": \"📚\",\n      \"subcategorii\": [\"subcategorie1\", \"subcategorie2\", \"subcategorie3\"]\n    }\n  ]\n}\n\nCerințe STRICTE:\n- Generează 6-8 ramuri principale SPECIFICE pentru \"${topic}\"\n- Fiecare ramură TREBUIE să fie direct legată de subiectul principal\n- Emoji-uri relevante pentru fiecare ramură\n- 3-4 subcategorii specifice pentru fiecare ramură\n- Descrieri de maxim 1 propoziție\n- Focalizează-te pe aspectele PRACTICE și APLICABILE\n- Evită conceptele generale sau irelevante\n\nSubiect: ${topic}`,\n    en: `Analyze the subject \"${topic}\" carefully and create a VERY SPECIFIC and RELEVANT knowledge tree for this exact domain.\n\nIMPORTANT: Generate branches that are DIRECTLY RELATED to \"${topic}\", not general concepts!\n\nReturn ONLY a valid JSON object with this exact structure:\n{\n  \"tema\": \"${topic}\",\n  \"ramuri\": [\n    {\n      \"nume\": \"Specific Branch Name\",\n      \"descriere\": \"Short and precise description\",\n      \"emoji\": \"📚\",\n      \"subcategorii\": [\"subcategory1\", \"subcategory2\", \"subcategory3\"]\n    }\n  ]\n}\n\nSTRICT Requirements:\n- Generate 6-8 main branches SPECIFIC to \"${topic}\"\n- Each branch MUST be directly related to the main subject\n- Relevant emojis for each branch\n- 3-4 specific subcategories for each branch\n- Descriptions of maximum 1 sentence\n- Focus on PRACTICAL and APPLICABLE aspects\n- Avoid general or irrelevant concepts\n\nSubject: ${topic}`\n  };\n  const prompt = prompts[currentLang] || prompts.en;\n  try {\n    const response = await client.makeRequest([{\n      role: 'system',\n      content: 'Expert în organizarea cunoștințelor. Generează arbori de cunoștințe specifici în format JSON valid. Răspunde DOAR cu JSON, fără text explicativ.'\n    }, {\n      role: 'user',\n      content: prompt\n    }], 0.3); // Temperatură mai mică pentru răspunsuri mai consistente și rapide\n\n    // Parse and validate the JSON response\n    const cleanResponse = response.trim();\n    const jsonMatch = cleanResponse.match(/\\{[\\s\\S]*\\}/);\n    if (!jsonMatch) {\n      throw new Error('No valid JSON found in response');\n    }\n    const tree = JSON.parse(jsonMatch[0]);\n\n    // Validate structure\n    if (!tree.tema || !Array.isArray(tree.ramuri)) {\n      throw new Error('Invalid tree structure');\n    }\n    return tree;\n  } catch (error) {\n    console.error('Error generating knowledge tree:', error);\n\n    // Fallback tree structure based on language\n    const fallbacks = {\n      ro: {\n        tema: topic,\n        ramuri: [{\n          nume: \"Fundamentele\",\n          descriere: `Concepte de bază și principii ale ${topic}`,\n          emoji: \"📚\",\n          subcategorii: [\"Concepte Esențiale\", \"Principii Cheie\", \"Teoria de Bază\"]\n        }, {\n          nume: \"Aplicații\",\n          descriere: `Aplicații practice și cazuri de utilizare ale ${topic}`,\n          emoji: \"🔧\",\n          subcategorii: [\"Utilizări Reale\", \"Aplicații Industriale\", \"Studii de Caz\"]\n        }, {\n          nume: \"Subiecte Avansate\",\n          descriere: `Aspecte complexe și specializate ale ${topic}`,\n          emoji: \"🎓\",\n          subcategorii: [\"Nivel Expert\", \"Zone de Cercetare\", \"Tehnologii Noi\"]\n        }]\n      },\n      en: {\n        tema: topic,\n        ramuri: [{\n          nume: \"Fundamentals\",\n          descriere: `Basic concepts and principles of ${topic}`,\n          emoji: \"📚\",\n          subcategorii: [\"Core Concepts\", \"Key Principles\", \"Basic Theory\"]\n        }, {\n          nume: \"Applications\",\n          descriere: `Practical applications and use cases of ${topic}`,\n          emoji: \"🔧\",\n          subcategorii: [\"Real-world Uses\", \"Industry Applications\", \"Case Studies\"]\n        }, {\n          nume: \"Advanced Topics\",\n          descriere: `Complex and specialized aspects of ${topic}`,\n          emoji: \"🎓\",\n          subcategorii: [\"Expert Level\", \"Research Areas\", \"Cutting Edge\"]\n        }]\n      }\n    };\n    return fallbacks[currentLang] || fallbacks.en;\n  }\n}\n\n// Generate Article for specific branch\nexport async function generateArticle(topic, branch, flags = ['-a']) {\n  var _branch$subcategorii;\n  // Get current language from localStorage\n  const currentLang = localStorage.getItem('language') || 'ro';\n  const flagInstructions = {\n    ro: {\n      // Basic flags cu prompt-uri FOARTE SPECIFICE în ROMÂNĂ\n      '-a': 'Scrie un articol informativ standard (600-800 cuvinte) cu structură clară: introducere, dezvoltare cu 3-4 secțiuni principale, și concluzie. SCRIE ÎNTREGUL ARTICOL ÎN ROMÂNĂ.',\n      '-t': 'Formatează ÎNTREGUL conținut ca tabele și date structurate. Creează minimum 3 tabele cu informații organizate în coloane și rânduri. Fiecare tabel să aibă titlu și explicații. SCRIE TOT ÎN ROMÂNĂ.',\n      '-ex': 'Include EXACT 3 exemple practice detaliate cu explicații pas cu pas. Fiecare exemplu să aibă: context, implementare, rezultat așteptat. Numerotează exemplele 1, 2, 3. SCRIE TOT ÎN ROMÂNĂ.',\n      '-p': 'Include OBLIGATORIU exemple de cod și demonstrații tehnice. Minimum 2 blocuri de cod cu explicații linie cu linie. Adaugă comentarii în cod. SCRIE TOT ÎN ROMÂNĂ.',\n      '-q': 'Creează EXACT 5 întrebări tip grilă la sfârșitul articolului. Fiecare întrebare să aibă 4 variante (A, B, C, D). Include baremul cu răspunsurile corecte la final. SCRIE TOT ÎN ROMÂNĂ.',\n      '-rap': 'Scrie un raport exhaustiv (800-1200 cuvinte) cu acoperire comprehensivă: rezumat executiv, analiză detaliată, concluzii și recomandări. SCRIE TOT ÎN ROMÂNĂ.',\n      '-def': 'Focalizează-te pe definiții de nivel expert și terminologie tehnică. Include minimum 10 termeni specializați cu definiții precise. SCRIE TOT ÎN ROMÂNĂ.',\n      // Learning & Visualization flags\n      '-path': 'Creează o cale de învățare personalizată cu 5-7 pași concreți, milestone-uri măsurabile și estimări de timp pentru fiecare etapă. SCRIE TOT ÎN ROMÂNĂ.',\n      '-vis': 'Generează descrieri detaliate pentru minimum 3 infografice/diagrame. Pentru fiecare diagramă: titlu, elemente vizuale, culori sugerate, și explicația fiecărui element. SCRIE TOT ÎN ROMÂNĂ.',\n      '-mind': 'Prezintă informația ca o hartă mentală interactivă cu concepte conectate. Descrie structura: nod central, 5-8 ramuri principale, sub-ramuri și conexiuni între concepte. SCRIE TOT ÎN ROMÂNĂ.',\n      '-flow': 'Creează diagrame de flux și procese cu puncte de decizie și rezultate. Include minimum 2 flowchart-uri cu forme geometrice specifice și săgeți directionale. SCRIE TOT ÎN ROMÂNĂ.',\n      // Industry-specific flags\n      '-case': 'Include 2-3 studii de caz reale cu rezultate măsurabile și analiză detaliată. Pentru fiecare caz: context, provocări, soluții implementate, rezultate concrete cu cifre. SCRIE TOT ÎN ROMÂNĂ.',\n      '-calc': 'Include calculatoare și instrumente interactive pentru calcule cu formule. Prezintă minimum 3 formule matematice cu exemple numerice concrete. SCRIE TOT ÎN ROMÂNĂ.',\n      '-game': 'Adaugă elemente de gamificare cu puncte, realizări și competiții. Creează un sistem de punctaj cu 5 nivele și recompense pentru fiecare nivel. SCRIE TOT ÎN ROMÂNĂ.',\n      // Localization flags\n      '-ro': 'Adaptează pentru piața românească și legislația locală cu exemple românești. Include referințe la legi românești, companii românești și practici locale specifice. SCRIE TOT ÎN ROMÂNĂ.',\n      // Advanced features flags\n      '-auto': 'Focalizează-te pe automatizarea proceselor cu instrumente și pași de implementare. Include minimum 3 instrumente software cu tutorial de configurare. SCRIE TOT ÎN ROMÂNĂ.'\n    },\n    en: {\n      // English versions\n      '-a': 'Write a comprehensive informative article (600-800 words) with clear structure: introduction, development with 3-4 main sections, and conclusion. WRITE THE ENTIRE ARTICLE IN ENGLISH.',\n      '-t': 'Format ALL content as tables and structured data. Create minimum 3 tables with organized information in columns and rows. Each table should have title and explanations. WRITE EVERYTHING IN ENGLISH.',\n      '-ex': 'Include EXACTLY 3 detailed practical examples with step-by-step explanations. Each example should have: context, implementation, expected result. Number examples 1, 2, 3. WRITE EVERYTHING IN ENGLISH.',\n      '-p': 'Include MANDATORY code examples and technical demonstrations. Minimum 2 code blocks with line-by-line explanations. Add comments in code. WRITE EVERYTHING IN ENGLISH.',\n      '-q': 'Create EXACTLY 5 multiple choice questions at the end of the article. Each question should have 4 options (A, B, C, D). Include answer key with correct answers at the end. WRITE EVERYTHING IN ENGLISH.',\n      '-rap': 'Write an exhaustive report (800-1200 words) with comprehensive coverage: executive summary, detailed analysis, conclusions and recommendations. WRITE EVERYTHING IN ENGLISH.',\n      '-def': 'Focus on expert-level definitions and technical terminology. Include minimum 10 specialized terms with precise definitions. WRITE EVERYTHING IN ENGLISH.',\n      '-path': 'Create a personalized learning path with 5-7 concrete steps, measurable milestones and time estimates for each stage. WRITE EVERYTHING IN ENGLISH.',\n      '-vis': 'Generate detailed descriptions for minimum 3 infographics/diagrams. For each diagram: title, visual elements, suggested colors, and explanation of each element. WRITE EVERYTHING IN ENGLISH.',\n      '-mind': 'Present information as an interactive mind map with connected concepts. Describe structure: central node, 5-8 main branches, sub-branches and connections between concepts. WRITE EVERYTHING IN ENGLISH.',\n      '-flow': 'Create flow diagrams and processes with decision points and results. Include minimum 2 flowcharts with specific geometric shapes and directional arrows. WRITE EVERYTHING IN ENGLISH.',\n      '-case': 'Include 2-3 real case studies with measurable results and detailed analysis. For each case: context, challenges, implemented solutions, concrete results with figures. WRITE EVERYTHING IN ENGLISH.',\n      '-calc': 'Include calculators and interactive tools for calculations with formulas. Present minimum 3 mathematical formulas with concrete numerical examples. WRITE EVERYTHING IN ENGLISH.',\n      '-game': 'Add gamification elements with points, achievements and competitions. Create a scoring system with 5 levels and rewards for each level. WRITE EVERYTHING IN ENGLISH.',\n      '-ro': 'Adapt for Romanian market and local legislation with Romanian examples. Include references to Romanian laws, Romanian companies and specific local practices. WRITE EVERYTHING IN ENGLISH.',\n      '-auto': 'Focus on process automation with tools and implementation steps. Include minimum 3 software tools with configuration tutorial. WRITE EVERYTHING IN ENGLISH.'\n    }\n  };\n  const currentFlagInstructions = flagInstructions[currentLang] || flagInstructions.ro;\n\n  // Combine selected flag instructions with language support\n  const selectedInstructions = flags.map(flag => currentFlagInstructions[flag] || '').filter(Boolean);\n  const combinedInstructions = selectedInstructions.join(' ');\n\n  // Language-specific prompts\n  const languagePrompts = {\n    ro: {\n      expertRole: `Ești un expert în ${topic}. Creează un articol detaliat despre \"${branch.nume}\" în contextul \"${topic}\".`,\n      description: `Descrierea ramuri: ${branch.descriere}`,\n      subcategories: branch.subcategorii ? `Subcategorii: ${branch.subcategorii.join(', ')}` : '',\n      important: `IMPORTANT:\n- Articolul trebuie să fie în format JSON cu structura: {\"titlu\": \"...\", \"continut\": \"...\"}\n- Conținutul să fie în format markdown\n- Să fie informativ și detaliat (600-800 cuvinte)\n- Să includă exemple practice și aplicații concrete\n- Să respecte EXACT instrucțiunile flag-urilor selectate\n- SCRIE ÎNTREGUL ARTICOL ÎN ROMÂNĂ - nu amesteca cu engleză!\n- Folosește minimum 5 surse web pentru informații actualizate`,\n      response: `Răspunde DOAR cu JSON-ul, fără text suplimentar.`\n    },\n    en: {\n      expertRole: `You are an expert in ${topic}. Create a detailed article about \"${branch.nume}\" in the context of \"${topic}\".`,\n      description: `Branch description: ${branch.descriere}`,\n      subcategories: branch.subcategorii ? `Subcategories: ${branch.subcategorii.join(', ')}` : '',\n      important: `IMPORTANT:\n- The article must be in JSON format with structure: {\"titlu\": \"...\", \"continut\": \"...\"}\n- Content should be in markdown format\n- Should be informative and detailed (600-800 words)\n- Should include practical examples and concrete applications\n- Must follow EXACTLY the selected flag instructions\n- WRITE THE ENTIRE ARTICLE IN ENGLISH - don't mix with other languages!\n- Use minimum 5 web sources for updated information`,\n      response: `Respond ONLY with the JSON, without additional text.`\n    }\n  };\n  const currentPrompts = languagePrompts[currentLang] || languagePrompts.ro;\n\n  // Search for web sources with language-specific queries\n  let webSources = [];\n  let sourcesContext = '';\n  try {\n    console.log('🔍 Searching for web sources for:', branch.nume, 'in language:', currentLang);\n\n    // Language-specific search query\n    const searchQuery = currentLang === 'ro' ? `${topic} ${branch.nume} română` : `${topic} ${branch.nume}`;\n    webSources = await webSearchService.searchSources(searchQuery, 5);\n    console.log('✅ Found sources:', webSources.length, 'sources');\n    if (webSources.length > 0) {\n      const sourcesText = currentLang === 'ro' ? `\\n\\nIMPORTANT: Include aceste surse web în articol:\\n${webSources.map(source => `- ${source.title}: ${source.description} (${source.source})`).join('\\n')}\\n\\nAsigură-te că referențiezi aceste surse în conținut și adaugă o secțiune \"Surse și Lectură Suplimentară\" la final.` : `\\n\\nIMPORTANT: Include these web sources in the article:\\n${webSources.map(source => `- ${source.title}: ${source.description} (${source.source})`).join('\\n')}\\n\\nMake sure to reference these sources in the content and add a \"Sources & Further Reading\" section at the end.`;\n      sourcesContext = sourcesText;\n    }\n  } catch (error) {\n    console.warn('⚠️ Web search failed, continuing without sources:', error);\n  }\n  const prompt = `Generate a comprehensive, well-structured article about \"${branch.nume}\" in the context of \"${topic}\".\n\nArticle Requirements:\n${flagText || flagInstructions['-a']}\n\n${localizationContext ? `Localization Requirements: ${localizationContext}` : ''}\n${advancedContext ? `Advanced Features: ${advancedContext}` : ''}\n${sourcesContext}\n\nCRITICAL FORMATTING RULES:\n- Use CLEAN text without asterisks (*) or special formatting symbols\n- Write in plain, readable markdown format\n- Use proper headings (# ## ###) instead of asterisks\n- Ensure content is EXACTLY 600-800 words for optimal learning\n- Structure in logical sections for easy comprehension\n- Include practical examples and actionable insights\n\nContent Guidelines:\n- Write in an engaging, educational style optimized for rapid learning\n- Use clear headings and structure with clean markdown formatting\n- Include relevant examples and explanations for better understanding\n- Apply proven learning techniques: spaced repetition, active recall, chunking\n- Target length: EXACTLY 600-800 words for optimal learning retention\n- If -rap flag is used, extend to 1000-1200 words with comprehensive coverage\n- Structure content in digestible sections of 150-200 words each\n- Use bullet points and numbered lists for better information processing\n- If visualization flags are used, describe diagrams and charts in detail\n- If interactive flags are used, provide step-by-step instructions\n- If Romanian flag is used, write in Romanian language\n- IMPORTANT: Do NOT use asterisks (*) or other formatting symbols in the text\n- Use clean, readable text without special characters for formatting\n\nReturn ONLY a valid JSON object with this structure:\n{\n  \"titlu\": \"Article Title\",\n  \"continut\": \"Full article content with proper markdown formatting\",\n  \"subcategorii\": [\"related topic 1\", \"related topic 2\", \"related topic 3\"],\n  \"flags\": ${JSON.stringify(flags)},\n  \"pozitie\": \"${topic} → ${branch.nume}\",\n  \"estimatedReadTime\": \"X minutes\",\n  \"difficulty\": \"Beginner|Intermediate|Advanced\",\n  \"practicalValue\": \"High|Medium|Low\"\n}\n\nTopic Context: ${topic}\nBranch: ${branch.nume}\nBranch Description: ${branch.descriere}\nSubcategories: ${((_branch$subcategorii = branch.subcategorii) === null || _branch$subcategorii === void 0 ? void 0 : _branch$subcategorii.join(', ')) || 'None'}\nSelected Flags: ${flags.join(' ')}`;\n  try {\n    const response = await client.makeRequest([{\n      role: 'system',\n      content: 'You are an expert content writer and educator. Generate comprehensive, well-structured articles in valid JSON format only. Do not include any explanatory text outside the JSON.'\n    }, {\n      role: 'user',\n      content: prompt\n    }], 0.8);\n\n    // Parse and validate the JSON response\n    const cleanResponse = response.trim();\n    const jsonMatch = cleanResponse.match(/\\{[\\s\\S]*\\}/);\n    if (!jsonMatch) {\n      throw new Error('No valid JSON found in response');\n    }\n    const article = JSON.parse(jsonMatch[0]);\n\n    // Validate structure\n    if (!article.titlu || !article.continut) {\n      throw new Error('Invalid article structure');\n    }\n\n    // Add web sources to the article\n    if (webSources.length > 0) {\n      const sourcesHTML = webSearchService.formatSourcesHTML(webSources);\n      article.continut += '\\n\\n' + sourcesHTML;\n      article.webSources = webSources;\n    }\n    return article;\n  } catch (error) {\n    console.error('Error generating article:', error);\n\n    // Fallback article\n    return {\n      titlu: `${branch.nume} - ${topic}`,\n      continut: `# ${branch.nume}\n\nThis section explores ${branch.nume} in the context of ${topic}.\n\n## Overview\n${branch.descriere}\n\n## Key Concepts\nUnderstanding ${branch.nume} is essential for mastering ${topic}. This area covers fundamental principles and practical applications.\n\n## Applications\nThe concepts in ${branch.nume} have wide-ranging applications across various domains and industries.\n\n## Further Learning\nTo deepen your understanding, consider exploring related topics and practical exercises.`,\n      subcategorii: branch.subcategorii || [],\n      flags: flags,\n      pozitie: `${topic} → ${branch.nume}`\n    };\n  }\n}\n\n// Test API connection\nexport async function testConnection() {\n  try {\n    const response = await client.makeRequest([{\n      role: 'user',\n      content: 'Hello, please respond with \"API connection successful\"'\n    }]);\n    return response.includes('successful');\n  } catch (error) {\n    console.error('API connection test failed:', error);\n    return false;\n  }\n}", "map": {"version": 3, "names": ["webSearchService", "OPENROUTER_API_KEY", "process", "env", "REACT_APP_OPENROUTER_API_KEY", "OPENROUTER_BASE_URL", "MODEL", "REACT_APP_OPENROUTER_MODEL", "SITE_CONFIG", "REACT_APP_SITE_URL", "REACT_APP_SITE_NAME", "OpenRouterClient", "constructor", "baseURL", "<PERSON><PERSON><PERSON><PERSON>", "console", "log", "substring", "makeRequest", "messages", "temperature", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "model", "max_tokens", "stream", "ok", "_errorData$error", "errorData", "json", "catch", "Error", "status", "error", "message", "data", "choices", "content", "client", "generateKnowledgeTree", "topic", "language", "currentLang", "localStorage", "getItem", "prompts", "ro", "en", "prompt", "role", "cleanResponse", "trim", "jsonMatch", "match", "tree", "parse", "tema", "Array", "isArray", "<PERSON><PERSON>", "fallbacks", "nume", "desc<PERSON><PERSON>", "emoji", "subcategorii", "generateArticle", "branch", "flags", "_branch$subcategorii", "flagInstructions", "currentFlagInstructions", "selectedInstructions", "map", "flag", "filter", "Boolean", "combinedInstructions", "join", "languagePrompts", "expertRole", "description", "subcategories", "important", "currentPrompts", "webSources", "sourcesContext", "searchQuery", "searchSources", "length", "sourcesText", "source", "title", "warn", "flagText", "localizationContext", "advancedContext", "article", "titlu", "continut", "sourcesHTML", "formatSourcesHTML", "pozitie", "testConnection", "includes"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Appv1/src/services/openRouterService.js"], "sourcesContent": ["// OpenRouter API Service for Knowledge Tree Generation\nimport webSearchService from './webSearchService';\n\nconst OPENROUTER_API_KEY = process.env.REACT_APP_OPENROUTER_API_KEY || '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';\nconst OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';\nconst MODEL = process.env.REACT_APP_OPENROUTER_MODEL || 'deepseek/deepseek-r1-0528:free';\n\n// Site configuration for OpenRouter rankings\nconst SITE_CONFIG = {\n  'HTTP-Referer': process.env.REACT_APP_SITE_URL || 'http://localhost:3000',\n  'X-Title': process.env.REACT_APP_SITE_NAME || 'Knowledge Tree Explorer'\n};\n\nclass OpenRouterClient {\n  constructor() {\n    this.baseURL = OPENROUTER_BASE_URL;\n    this.apiKey = OPENROUTER_API_KEY;\n\n    // Debug logging - always show for troubleshooting\n    console.log('🔧 OpenRouter Client initialized:');\n    console.log('- Base URL:', this.baseURL);\n    console.log('- Model:', MODEL);\n    console.log('- API Key:', this.apiKey ? `${this.apiKey.substring(0, 10)}...` : 'NOT SET');\n    console.log('- Site Config:', SITE_CONFIG);\n  }\n\n  async makeRequest(messages, temperature = 0.7) {\n    try {\n      const response = await fetch(`${this.baseURL}/chat/completions`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${this.apiKey}`,\n          'Content-Type': 'application/json',\n          ...SITE_CONFIG\n        },\n        body: JSON.stringify({\n          model: MODEL,\n          messages,\n          temperature,\n          max_tokens: 1500, // Optimizat pentru răspunsuri mai rapide\n          stream: false\n        })\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        throw new Error(`OpenRouter API Error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);\n      }\n\n      const data = await response.json();\n      return data.choices[0].message.content;\n    } catch (error) {\n      console.error('OpenRouter API request failed:', error);\n      throw error;\n    }\n  }\n}\n\nconst client = new OpenRouterClient();\n\n// Generate Knowledge Tree from topic\nexport async function generateKnowledgeTree(topic, language = 'en') {\n  // Get current language from localStorage if not provided\n  const currentLang = language || localStorage.getItem('language') || 'en';\n\n  const prompts = {\n    ro: `Analizează cu atenție subiectul \"${topic}\" și creează un arbore de cunoștințe FOARTE SPECIFIC și RELEVANT pentru acest domeniu exact.\n\nIMPORTANT: Generează ramuri care sunt DIRECT LEGATE de \"${topic}\", nu concepte generale!\n\nReturnează DOAR un obiect JSON valid cu această structură exactă:\n{\n  \"tema\": \"${topic}\",\n  \"ramuri\": [\n    {\n      \"nume\": \"Nume Ramură Specifică\",\n      \"descriere\": \"Descriere scurtă și precisă\",\n      \"emoji\": \"📚\",\n      \"subcategorii\": [\"subcategorie1\", \"subcategorie2\", \"subcategorie3\"]\n    }\n  ]\n}\n\nCerințe STRICTE:\n- Generează 6-8 ramuri principale SPECIFICE pentru \"${topic}\"\n- Fiecare ramură TREBUIE să fie direct legată de subiectul principal\n- Emoji-uri relevante pentru fiecare ramură\n- 3-4 subcategorii specifice pentru fiecare ramură\n- Descrieri de maxim 1 propoziție\n- Focalizează-te pe aspectele PRACTICE și APLICABILE\n- Evită conceptele generale sau irelevante\n\nSubiect: ${topic}`,\n\n    en: `Analyze the subject \"${topic}\" carefully and create a VERY SPECIFIC and RELEVANT knowledge tree for this exact domain.\n\nIMPORTANT: Generate branches that are DIRECTLY RELATED to \"${topic}\", not general concepts!\n\nReturn ONLY a valid JSON object with this exact structure:\n{\n  \"tema\": \"${topic}\",\n  \"ramuri\": [\n    {\n      \"nume\": \"Specific Branch Name\",\n      \"descriere\": \"Short and precise description\",\n      \"emoji\": \"📚\",\n      \"subcategorii\": [\"subcategory1\", \"subcategory2\", \"subcategory3\"]\n    }\n  ]\n}\n\nSTRICT Requirements:\n- Generate 6-8 main branches SPECIFIC to \"${topic}\"\n- Each branch MUST be directly related to the main subject\n- Relevant emojis for each branch\n- 3-4 specific subcategories for each branch\n- Descriptions of maximum 1 sentence\n- Focus on PRACTICAL and APPLICABLE aspects\n- Avoid general or irrelevant concepts\n\nSubject: ${topic}`\n  };\n\n  const prompt = prompts[currentLang] || prompts.en;\n\n  try {\n    const response = await client.makeRequest([\n      {\n        role: 'system',\n        content: 'Expert în organizarea cunoștințelor. Generează arbori de cunoștințe specifici în format JSON valid. Răspunde DOAR cu JSON, fără text explicativ.'\n      },\n      {\n        role: 'user',\n        content: prompt\n      }\n    ], 0.3); // Temperatură mai mică pentru răspunsuri mai consistente și rapide\n\n    // Parse and validate the JSON response\n    const cleanResponse = response.trim();\n    const jsonMatch = cleanResponse.match(/\\{[\\s\\S]*\\}/);\n    \n    if (!jsonMatch) {\n      throw new Error('No valid JSON found in response');\n    }\n\n    const tree = JSON.parse(jsonMatch[0]);\n    \n    // Validate structure\n    if (!tree.tema || !Array.isArray(tree.ramuri)) {\n      throw new Error('Invalid tree structure');\n    }\n\n    return tree;\n  } catch (error) {\n    console.error('Error generating knowledge tree:', error);\n    \n    // Fallback tree structure based on language\n    const fallbacks = {\n      ro: {\n        tema: topic,\n        ramuri: [\n          {\n            nume: \"Fundamentele\",\n            descriere: `Concepte de bază și principii ale ${topic}`,\n            emoji: \"📚\",\n            subcategorii: [\"Concepte Esențiale\", \"Principii Cheie\", \"Teoria de Bază\"]\n          },\n          {\n            nume: \"Aplicații\",\n            descriere: `Aplicații practice și cazuri de utilizare ale ${topic}`,\n            emoji: \"🔧\",\n            subcategorii: [\"Utilizări Reale\", \"Aplicații Industriale\", \"Studii de Caz\"]\n          },\n          {\n            nume: \"Subiecte Avansate\",\n            descriere: `Aspecte complexe și specializate ale ${topic}`,\n            emoji: \"🎓\",\n            subcategorii: [\"Nivel Expert\", \"Zone de Cercetare\", \"Tehnologii Noi\"]\n          }\n        ]\n      },\n      en: {\n        tema: topic,\n        ramuri: [\n          {\n            nume: \"Fundamentals\",\n            descriere: `Basic concepts and principles of ${topic}`,\n            emoji: \"📚\",\n            subcategorii: [\"Core Concepts\", \"Key Principles\", \"Basic Theory\"]\n          },\n          {\n            nume: \"Applications\",\n            descriere: `Practical applications and use cases of ${topic}`,\n            emoji: \"🔧\",\n            subcategorii: [\"Real-world Uses\", \"Industry Applications\", \"Case Studies\"]\n          },\n          {\n            nume: \"Advanced Topics\",\n            descriere: `Complex and specialized aspects of ${topic}`,\n            emoji: \"🎓\",\n            subcategorii: [\"Expert Level\", \"Research Areas\", \"Cutting Edge\"]\n          }\n        ]\n      }\n    };\n\n    return fallbacks[currentLang] || fallbacks.en;\n  }\n}\n\n// Generate Article for specific branch\nexport async function generateArticle(topic, branch, flags = ['-a']) {\n  // Get current language from localStorage\n  const currentLang = localStorage.getItem('language') || 'ro';\n\n  const flagInstructions = {\n    ro: {\n      // Basic flags cu prompt-uri FOARTE SPECIFICE în ROMÂNĂ\n      '-a': 'Scrie un articol informativ standard (600-800 cuvinte) cu structură clară: introducere, dezvoltare cu 3-4 secțiuni principale, și concluzie. SCRIE ÎNTREGUL ARTICOL ÎN ROMÂNĂ.',\n\n      '-t': 'Formatează ÎNTREGUL conținut ca tabele și date structurate. Creează minimum 3 tabele cu informații organizate în coloane și rânduri. Fiecare tabel să aibă titlu și explicații. SCRIE TOT ÎN ROMÂNĂ.',\n\n      '-ex': 'Include EXACT 3 exemple practice detaliate cu explicații pas cu pas. Fiecare exemplu să aibă: context, implementare, rezultat așteptat. Numerotează exemplele 1, 2, 3. SCRIE TOT ÎN ROMÂNĂ.',\n\n      '-p': 'Include OBLIGATORIU exemple de cod și demonstrații tehnice. Minimum 2 blocuri de cod cu explicații linie cu linie. Adaugă comentarii în cod. SCRIE TOT ÎN ROMÂNĂ.',\n\n      '-q': 'Creează EXACT 5 întrebări tip grilă la sfârșitul articolului. Fiecare întrebare să aibă 4 variante (A, B, C, D). Include baremul cu răspunsurile corecte la final. SCRIE TOT ÎN ROMÂNĂ.',\n\n      '-rap': 'Scrie un raport exhaustiv (800-1200 cuvinte) cu acoperire comprehensivă: rezumat executiv, analiză detaliată, concluzii și recomandări. SCRIE TOT ÎN ROMÂNĂ.',\n\n      '-def': 'Focalizează-te pe definiții de nivel expert și terminologie tehnică. Include minimum 10 termeni specializați cu definiții precise. SCRIE TOT ÎN ROMÂNĂ.',\n\n      // Learning & Visualization flags\n      '-path': 'Creează o cale de învățare personalizată cu 5-7 pași concreți, milestone-uri măsurabile și estimări de timp pentru fiecare etapă. SCRIE TOT ÎN ROMÂNĂ.',\n\n      '-vis': 'Generează descrieri detaliate pentru minimum 3 infografice/diagrame. Pentru fiecare diagramă: titlu, elemente vizuale, culori sugerate, și explicația fiecărui element. SCRIE TOT ÎN ROMÂNĂ.',\n\n      '-mind': 'Prezintă informația ca o hartă mentală interactivă cu concepte conectate. Descrie structura: nod central, 5-8 ramuri principale, sub-ramuri și conexiuni între concepte. SCRIE TOT ÎN ROMÂNĂ.',\n\n      '-flow': 'Creează diagrame de flux și procese cu puncte de decizie și rezultate. Include minimum 2 flowchart-uri cu forme geometrice specifice și săgeți directionale. SCRIE TOT ÎN ROMÂNĂ.',\n\n      // Industry-specific flags\n      '-case': 'Include 2-3 studii de caz reale cu rezultate măsurabile și analiză detaliată. Pentru fiecare caz: context, provocări, soluții implementate, rezultate concrete cu cifre. SCRIE TOT ÎN ROMÂNĂ.',\n\n      '-calc': 'Include calculatoare și instrumente interactive pentru calcule cu formule. Prezintă minimum 3 formule matematice cu exemple numerice concrete. SCRIE TOT ÎN ROMÂNĂ.',\n\n      '-game': 'Adaugă elemente de gamificare cu puncte, realizări și competiții. Creează un sistem de punctaj cu 5 nivele și recompense pentru fiecare nivel. SCRIE TOT ÎN ROMÂNĂ.',\n\n      // Localization flags\n      '-ro': 'Adaptează pentru piața românească și legislația locală cu exemple românești. Include referințe la legi românești, companii românești și practici locale specifice. SCRIE TOT ÎN ROMÂNĂ.',\n\n      // Advanced features flags\n      '-auto': 'Focalizează-te pe automatizarea proceselor cu instrumente și pași de implementare. Include minimum 3 instrumente software cu tutorial de configurare. SCRIE TOT ÎN ROMÂNĂ.'\n    },\n    en: {\n      // English versions\n      '-a': 'Write a comprehensive informative article (600-800 words) with clear structure: introduction, development with 3-4 main sections, and conclusion. WRITE THE ENTIRE ARTICLE IN ENGLISH.',\n      '-t': 'Format ALL content as tables and structured data. Create minimum 3 tables with organized information in columns and rows. Each table should have title and explanations. WRITE EVERYTHING IN ENGLISH.',\n      '-ex': 'Include EXACTLY 3 detailed practical examples with step-by-step explanations. Each example should have: context, implementation, expected result. Number examples 1, 2, 3. WRITE EVERYTHING IN ENGLISH.',\n      '-p': 'Include MANDATORY code examples and technical demonstrations. Minimum 2 code blocks with line-by-line explanations. Add comments in code. WRITE EVERYTHING IN ENGLISH.',\n      '-q': 'Create EXACTLY 5 multiple choice questions at the end of the article. Each question should have 4 options (A, B, C, D). Include answer key with correct answers at the end. WRITE EVERYTHING IN ENGLISH.',\n      '-rap': 'Write an exhaustive report (800-1200 words) with comprehensive coverage: executive summary, detailed analysis, conclusions and recommendations. WRITE EVERYTHING IN ENGLISH.',\n      '-def': 'Focus on expert-level definitions and technical terminology. Include minimum 10 specialized terms with precise definitions. WRITE EVERYTHING IN ENGLISH.',\n      '-path': 'Create a personalized learning path with 5-7 concrete steps, measurable milestones and time estimates for each stage. WRITE EVERYTHING IN ENGLISH.',\n      '-vis': 'Generate detailed descriptions for minimum 3 infographics/diagrams. For each diagram: title, visual elements, suggested colors, and explanation of each element. WRITE EVERYTHING IN ENGLISH.',\n      '-mind': 'Present information as an interactive mind map with connected concepts. Describe structure: central node, 5-8 main branches, sub-branches and connections between concepts. WRITE EVERYTHING IN ENGLISH.',\n      '-flow': 'Create flow diagrams and processes with decision points and results. Include minimum 2 flowcharts with specific geometric shapes and directional arrows. WRITE EVERYTHING IN ENGLISH.',\n      '-case': 'Include 2-3 real case studies with measurable results and detailed analysis. For each case: context, challenges, implemented solutions, concrete results with figures. WRITE EVERYTHING IN ENGLISH.',\n      '-calc': 'Include calculators and interactive tools for calculations with formulas. Present minimum 3 mathematical formulas with concrete numerical examples. WRITE EVERYTHING IN ENGLISH.',\n      '-game': 'Add gamification elements with points, achievements and competitions. Create a scoring system with 5 levels and rewards for each level. WRITE EVERYTHING IN ENGLISH.',\n      '-ro': 'Adapt for Romanian market and local legislation with Romanian examples. Include references to Romanian laws, Romanian companies and specific local practices. WRITE EVERYTHING IN ENGLISH.',\n      '-auto': 'Focus on process automation with tools and implementation steps. Include minimum 3 software tools with configuration tutorial. WRITE EVERYTHING IN ENGLISH.'\n    }\n  };\n\n  const currentFlagInstructions = flagInstructions[currentLang] || flagInstructions.ro;\n\n  // Combine selected flag instructions with language support\n  const selectedInstructions = flags.map(flag => currentFlagInstructions[flag] || '').filter(Boolean);\n  const combinedInstructions = selectedInstructions.join(' ');\n\n  // Language-specific prompts\n  const languagePrompts = {\n    ro: {\n      expertRole: `Ești un expert în ${topic}. Creează un articol detaliat despre \"${branch.nume}\" în contextul \"${topic}\".`,\n      description: `Descrierea ramuri: ${branch.descriere}`,\n      subcategories: branch.subcategorii ? `Subcategorii: ${branch.subcategorii.join(', ')}` : '',\n      important: `IMPORTANT:\n- Articolul trebuie să fie în format JSON cu structura: {\"titlu\": \"...\", \"continut\": \"...\"}\n- Conținutul să fie în format markdown\n- Să fie informativ și detaliat (600-800 cuvinte)\n- Să includă exemple practice și aplicații concrete\n- Să respecte EXACT instrucțiunile flag-urilor selectate\n- SCRIE ÎNTREGUL ARTICOL ÎN ROMÂNĂ - nu amesteca cu engleză!\n- Folosește minimum 5 surse web pentru informații actualizate`,\n      response: `Răspunde DOAR cu JSON-ul, fără text suplimentar.`\n    },\n    en: {\n      expertRole: `You are an expert in ${topic}. Create a detailed article about \"${branch.nume}\" in the context of \"${topic}\".`,\n      description: `Branch description: ${branch.descriere}`,\n      subcategories: branch.subcategorii ? `Subcategories: ${branch.subcategorii.join(', ')}` : '',\n      important: `IMPORTANT:\n- The article must be in JSON format with structure: {\"titlu\": \"...\", \"continut\": \"...\"}\n- Content should be in markdown format\n- Should be informative and detailed (600-800 words)\n- Should include practical examples and concrete applications\n- Must follow EXACTLY the selected flag instructions\n- WRITE THE ENTIRE ARTICLE IN ENGLISH - don't mix with other languages!\n- Use minimum 5 web sources for updated information`,\n      response: `Respond ONLY with the JSON, without additional text.`\n    }\n  };\n\n  const currentPrompts = languagePrompts[currentLang] || languagePrompts.ro;\n\n  // Search for web sources with language-specific queries\n  let webSources = [];\n  let sourcesContext = '';\n\n  try {\n    console.log('🔍 Searching for web sources for:', branch.nume, 'in language:', currentLang);\n\n    // Language-specific search query\n    const searchQuery = currentLang === 'ro'\n      ? `${topic} ${branch.nume} română`\n      : `${topic} ${branch.nume}`;\n\n    webSources = await webSearchService.searchSources(searchQuery, 5);\n    console.log('✅ Found sources:', webSources.length, 'sources');\n\n    if (webSources.length > 0) {\n      const sourcesText = currentLang === 'ro'\n        ? `\\n\\nIMPORTANT: Include aceste surse web în articol:\\n${webSources.map(source =>\n            `- ${source.title}: ${source.description} (${source.source})`\n          ).join('\\n')}\\n\\nAsigură-te că referențiezi aceste surse în conținut și adaugă o secțiune \"Surse și Lectură Suplimentară\" la final.`\n        : `\\n\\nIMPORTANT: Include these web sources in the article:\\n${webSources.map(source =>\n            `- ${source.title}: ${source.description} (${source.source})`\n          ).join('\\n')}\\n\\nMake sure to reference these sources in the content and add a \"Sources & Further Reading\" section at the end.`;\n\n      sourcesContext = sourcesText;\n    }\n  } catch (error) {\n    console.warn('⚠️ Web search failed, continuing without sources:', error);\n  }\n\n  const prompt = `Generate a comprehensive, well-structured article about \"${branch.nume}\" in the context of \"${topic}\".\n\nArticle Requirements:\n${flagText || flagInstructions['-a']}\n\n${localizationContext ? `Localization Requirements: ${localizationContext}` : ''}\n${advancedContext ? `Advanced Features: ${advancedContext}` : ''}\n${sourcesContext}\n\nCRITICAL FORMATTING RULES:\n- Use CLEAN text without asterisks (*) or special formatting symbols\n- Write in plain, readable markdown format\n- Use proper headings (# ## ###) instead of asterisks\n- Ensure content is EXACTLY 600-800 words for optimal learning\n- Structure in logical sections for easy comprehension\n- Include practical examples and actionable insights\n\nContent Guidelines:\n- Write in an engaging, educational style optimized for rapid learning\n- Use clear headings and structure with clean markdown formatting\n- Include relevant examples and explanations for better understanding\n- Apply proven learning techniques: spaced repetition, active recall, chunking\n- Target length: EXACTLY 600-800 words for optimal learning retention\n- If -rap flag is used, extend to 1000-1200 words with comprehensive coverage\n- Structure content in digestible sections of 150-200 words each\n- Use bullet points and numbered lists for better information processing\n- If visualization flags are used, describe diagrams and charts in detail\n- If interactive flags are used, provide step-by-step instructions\n- If Romanian flag is used, write in Romanian language\n- IMPORTANT: Do NOT use asterisks (*) or other formatting symbols in the text\n- Use clean, readable text without special characters for formatting\n\nReturn ONLY a valid JSON object with this structure:\n{\n  \"titlu\": \"Article Title\",\n  \"continut\": \"Full article content with proper markdown formatting\",\n  \"subcategorii\": [\"related topic 1\", \"related topic 2\", \"related topic 3\"],\n  \"flags\": ${JSON.stringify(flags)},\n  \"pozitie\": \"${topic} → ${branch.nume}\",\n  \"estimatedReadTime\": \"X minutes\",\n  \"difficulty\": \"Beginner|Intermediate|Advanced\",\n  \"practicalValue\": \"High|Medium|Low\"\n}\n\nTopic Context: ${topic}\nBranch: ${branch.nume}\nBranch Description: ${branch.descriere}\nSubcategories: ${branch.subcategorii?.join(', ') || 'None'}\nSelected Flags: ${flags.join(' ')}`;\n\n  try {\n    const response = await client.makeRequest([\n      {\n        role: 'system',\n        content: 'You are an expert content writer and educator. Generate comprehensive, well-structured articles in valid JSON format only. Do not include any explanatory text outside the JSON.'\n      },\n      {\n        role: 'user',\n        content: prompt\n      }\n    ], 0.8);\n\n    // Parse and validate the JSON response\n    const cleanResponse = response.trim();\n    const jsonMatch = cleanResponse.match(/\\{[\\s\\S]*\\}/);\n    \n    if (!jsonMatch) {\n      throw new Error('No valid JSON found in response');\n    }\n\n    const article = JSON.parse(jsonMatch[0]);\n    \n    // Validate structure\n    if (!article.titlu || !article.continut) {\n      throw new Error('Invalid article structure');\n    }\n\n    // Add web sources to the article\n    if (webSources.length > 0) {\n      const sourcesHTML = webSearchService.formatSourcesHTML(webSources);\n      article.continut += '\\n\\n' + sourcesHTML;\n      article.webSources = webSources;\n    }\n\n    return article;\n  } catch (error) {\n    console.error('Error generating article:', error);\n    \n    // Fallback article\n    return {\n      titlu: `${branch.nume} - ${topic}`,\n      continut: `# ${branch.nume}\n\nThis section explores ${branch.nume} in the context of ${topic}.\n\n## Overview\n${branch.descriere}\n\n## Key Concepts\nUnderstanding ${branch.nume} is essential for mastering ${topic}. This area covers fundamental principles and practical applications.\n\n## Applications\nThe concepts in ${branch.nume} have wide-ranging applications across various domains and industries.\n\n## Further Learning\nTo deepen your understanding, consider exploring related topics and practical exercises.`,\n      subcategorii: branch.subcategorii || [],\n      flags: flags,\n      pozitie: `${topic} → ${branch.nume}`\n    };\n  }\n}\n\n// Test API connection\nexport async function testConnection() {\n  try {\n    const response = await client.makeRequest([\n      {\n        role: 'user',\n        content: 'Hello, please respond with \"API connection successful\"'\n      }\n    ]);\n    \n    return response.includes('successful');\n  } catch (error) {\n    console.error('API connection test failed:', error);\n    return false;\n  }\n}\n"], "mappings": "AAAA;AACA,OAAOA,gBAAgB,MAAM,oBAAoB;AAEjD,MAAMC,kBAAkB,GAAGC,OAAO,CAACC,GAAG,CAACC,4BAA4B,IAAI,2EAA2E;AAClJ,MAAMC,mBAAmB,GAAG,8BAA8B;AAC1D,MAAMC,KAAK,GAAGJ,OAAO,CAACC,GAAG,CAACI,0BAA0B,IAAI,gCAAgC;;AAExF;AACA,MAAMC,WAAW,GAAG;EAClB,cAAc,EAAEN,OAAO,CAACC,GAAG,CAACM,kBAAkB,IAAI,uBAAuB;EACzE,SAAS,EAAEP,OAAO,CAACC,GAAG,CAACO,mBAAmB,IAAI;AAChD,CAAC;AAED,MAAMC,gBAAgB,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,OAAO,GAAGR,mBAAmB;IAClC,IAAI,CAACS,MAAM,GAAGb,kBAAkB;;IAEhC;IACAc,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAChDD,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAACH,OAAO,CAAC;IACxCE,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEV,KAAK,CAAC;IAC9BS,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAACF,MAAM,GAAG,GAAG,IAAI,CAACA,MAAM,CAACG,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAAG,SAAS,CAAC;IACzFF,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAER,WAAW,CAAC;EAC5C;EAEA,MAAMU,WAAWA,CAACC,QAAQ,EAAEC,WAAW,GAAG,GAAG,EAAE;IAC7C,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACT,OAAO,mBAAmB,EAAE;QAC/DU,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,eAAe,EAAE,UAAU,IAAI,CAACV,MAAM,EAAE;UACxC,cAAc,EAAE,kBAAkB;UAClC,GAAGN;QACL,CAAC;QACDiB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,KAAK,EAAEtB,KAAK;UACZa,QAAQ;UACRC,WAAW;UACXS,UAAU,EAAE,IAAI;UAAE;UAClBC,MAAM,EAAE;QACV,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACT,QAAQ,CAACU,EAAE,EAAE;QAAA,IAAAC,gBAAA;QAChB,MAAMC,SAAS,GAAG,MAAMZ,QAAQ,CAACa,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACzD,MAAM,IAAIC,KAAK,CAAC,yBAAyBf,QAAQ,CAACgB,MAAM,MAAM,EAAAL,gBAAA,GAAAC,SAAS,CAACK,KAAK,cAAAN,gBAAA,uBAAfA,gBAAA,CAAiBO,OAAO,KAAI,eAAe,EAAE,CAAC;MAC9G;MAEA,MAAMC,IAAI,GAAG,MAAMnB,QAAQ,CAACa,IAAI,CAAC,CAAC;MAClC,OAAOM,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,CAACF,OAAO,CAACG,OAAO;IACxC,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdvB,OAAO,CAACuB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAMA,KAAK;IACb;EACF;AACF;AAEA,MAAMK,MAAM,GAAG,IAAIhC,gBAAgB,CAAC,CAAC;;AAErC;AACA,OAAO,eAAeiC,qBAAqBA,CAACC,KAAK,EAAEC,QAAQ,GAAG,IAAI,EAAE;EAClE;EACA,MAAMC,WAAW,GAAGD,QAAQ,IAAIE,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAI,IAAI;EAExE,MAAMC,OAAO,GAAG;IACdC,EAAE,EAAE,oCAAoCN,KAAK;AACjD;AACA,0DAA0DA,KAAK;AAC/D;AACA;AACA;AACA,aAAaA,KAAK;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsDA,KAAK;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAWA,KAAK,EAAE;IAEdO,EAAE,EAAE,wBAAwBP,KAAK;AACrC;AACA,6DAA6DA,KAAK;AAClE;AACA;AACA;AACA,aAAaA,KAAK;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4CA,KAAK;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAWA,KAAK;EACd,CAAC;EAED,MAAMQ,MAAM,GAAGH,OAAO,CAACH,WAAW,CAAC,IAAIG,OAAO,CAACE,EAAE;EAEjD,IAAI;IACF,MAAM/B,QAAQ,GAAG,MAAMsB,MAAM,CAACzB,WAAW,CAAC,CACxC;MACEoC,IAAI,EAAE,QAAQ;MACdZ,OAAO,EAAE;IACX,CAAC,EACD;MACEY,IAAI,EAAE,MAAM;MACZZ,OAAO,EAAEW;IACX,CAAC,CACF,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET;IACA,MAAME,aAAa,GAAGlC,QAAQ,CAACmC,IAAI,CAAC,CAAC;IACrC,MAAMC,SAAS,GAAGF,aAAa,CAACG,KAAK,CAAC,aAAa,CAAC;IAEpD,IAAI,CAACD,SAAS,EAAE;MACd,MAAM,IAAIrB,KAAK,CAAC,iCAAiC,CAAC;IACpD;IAEA,MAAMuB,IAAI,GAAGjC,IAAI,CAACkC,KAAK,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC;;IAErC;IACA,IAAI,CAACE,IAAI,CAACE,IAAI,IAAI,CAACC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACK,MAAM,CAAC,EAAE;MAC7C,MAAM,IAAI5B,KAAK,CAAC,wBAAwB,CAAC;IAC3C;IAEA,OAAOuB,IAAI;EACb,CAAC,CAAC,OAAOrB,KAAK,EAAE;IACdvB,OAAO,CAACuB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;IAExD;IACA,MAAM2B,SAAS,GAAG;MAChBd,EAAE,EAAE;QACFU,IAAI,EAAEhB,KAAK;QACXmB,MAAM,EAAE,CACN;UACEE,IAAI,EAAE,cAAc;UACpBC,SAAS,EAAE,qCAAqCtB,KAAK,EAAE;UACvDuB,KAAK,EAAE,IAAI;UACXC,YAAY,EAAE,CAAC,oBAAoB,EAAE,iBAAiB,EAAE,gBAAgB;QAC1E,CAAC,EACD;UACEH,IAAI,EAAE,WAAW;UACjBC,SAAS,EAAE,iDAAiDtB,KAAK,EAAE;UACnEuB,KAAK,EAAE,IAAI;UACXC,YAAY,EAAE,CAAC,iBAAiB,EAAE,uBAAuB,EAAE,eAAe;QAC5E,CAAC,EACD;UACEH,IAAI,EAAE,mBAAmB;UACzBC,SAAS,EAAE,wCAAwCtB,KAAK,EAAE;UAC1DuB,KAAK,EAAE,IAAI;UACXC,YAAY,EAAE,CAAC,cAAc,EAAE,mBAAmB,EAAE,gBAAgB;QACtE,CAAC;MAEL,CAAC;MACDjB,EAAE,EAAE;QACFS,IAAI,EAAEhB,KAAK;QACXmB,MAAM,EAAE,CACN;UACEE,IAAI,EAAE,cAAc;UACpBC,SAAS,EAAE,oCAAoCtB,KAAK,EAAE;UACtDuB,KAAK,EAAE,IAAI;UACXC,YAAY,EAAE,CAAC,eAAe,EAAE,gBAAgB,EAAE,cAAc;QAClE,CAAC,EACD;UACEH,IAAI,EAAE,cAAc;UACpBC,SAAS,EAAE,2CAA2CtB,KAAK,EAAE;UAC7DuB,KAAK,EAAE,IAAI;UACXC,YAAY,EAAE,CAAC,iBAAiB,EAAE,uBAAuB,EAAE,cAAc;QAC3E,CAAC,EACD;UACEH,IAAI,EAAE,iBAAiB;UACvBC,SAAS,EAAE,sCAAsCtB,KAAK,EAAE;UACxDuB,KAAK,EAAE,IAAI;UACXC,YAAY,EAAE,CAAC,cAAc,EAAE,gBAAgB,EAAE,cAAc;QACjE,CAAC;MAEL;IACF,CAAC;IAED,OAAOJ,SAAS,CAAClB,WAAW,CAAC,IAAIkB,SAAS,CAACb,EAAE;EAC/C;AACF;;AAEA;AACA,OAAO,eAAekB,eAAeA,CAACzB,KAAK,EAAE0B,MAAM,EAAEC,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE;EAAA,IAAAC,oBAAA;EACnE;EACA,MAAM1B,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAI,IAAI;EAE5D,MAAMyB,gBAAgB,GAAG;IACvBvB,EAAE,EAAE;MACF;MACA,IAAI,EAAE,gLAAgL;MAEtL,IAAI,EAAE,sMAAsM;MAE5M,KAAK,EAAE,6LAA6L;MAEpM,IAAI,EAAE,mKAAmK;MAEzK,IAAI,EAAE,yLAAyL;MAE/L,MAAM,EAAE,8JAA8J;MAEtK,MAAM,EAAE,yJAAyJ;MAEjK;MACA,OAAO,EAAE,wJAAwJ;MAEjK,MAAM,EAAE,8LAA8L;MAEtM,OAAO,EAAE,+LAA+L;MAExM,OAAO,EAAE,mLAAmL;MAE5L;MACA,OAAO,EAAE,+LAA+L;MAExM,OAAO,EAAE,qKAAqK;MAE9K,OAAO,EAAE,qKAAqK;MAE9K;MACA,KAAK,EAAE,yLAAyL;MAEhM;MACA,OAAO,EAAE;IACX,CAAC;IACDC,EAAE,EAAE;MACF;MACA,IAAI,EAAE,wLAAwL;MAC9L,IAAI,EAAE,uMAAuM;MAC7M,KAAK,EAAE,yMAAyM;MAChN,IAAI,EAAE,wKAAwK;MAC9K,IAAI,EAAE,0MAA0M;MAChN,MAAM,EAAE,8KAA8K;MACtL,MAAM,EAAE,0JAA0J;MAClK,OAAO,EAAE,oJAAoJ;MAC7J,MAAM,EAAE,+LAA+L;MACvM,OAAO,EAAE,0MAA0M;MACnN,OAAO,EAAE,uLAAuL;MAChM,OAAO,EAAE,qMAAqM;MAC9M,OAAO,EAAE,kLAAkL;MAC3L,OAAO,EAAE,sKAAsK;MAC/K,KAAK,EAAE,4LAA4L;MACnM,OAAO,EAAE;IACX;EACF,CAAC;EAED,MAAMuB,uBAAuB,GAAGD,gBAAgB,CAAC3B,WAAW,CAAC,IAAI2B,gBAAgB,CAACvB,EAAE;;EAEpF;EACA,MAAMyB,oBAAoB,GAAGJ,KAAK,CAACK,GAAG,CAACC,IAAI,IAAIH,uBAAuB,CAACG,IAAI,CAAC,IAAI,EAAE,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;EACnG,MAAMC,oBAAoB,GAAGL,oBAAoB,CAACM,IAAI,CAAC,GAAG,CAAC;;EAE3D;EACA,MAAMC,eAAe,GAAG;IACtBhC,EAAE,EAAE;MACFiC,UAAU,EAAE,qBAAqBvC,KAAK,yCAAyC0B,MAAM,CAACL,IAAI,mBAAmBrB,KAAK,IAAI;MACtHwC,WAAW,EAAE,sBAAsBd,MAAM,CAACJ,SAAS,EAAE;MACrDmB,aAAa,EAAEf,MAAM,CAACF,YAAY,GAAG,iBAAiBE,MAAM,CAACF,YAAY,CAACa,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE;MAC3FK,SAAS,EAAE;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,8DAA8D;MACxDlE,QAAQ,EAAE;IACZ,CAAC;IACD+B,EAAE,EAAE;MACFgC,UAAU,EAAE,wBAAwBvC,KAAK,sCAAsC0B,MAAM,CAACL,IAAI,wBAAwBrB,KAAK,IAAI;MAC3HwC,WAAW,EAAE,uBAAuBd,MAAM,CAACJ,SAAS,EAAE;MACtDmB,aAAa,EAAEf,MAAM,CAACF,YAAY,GAAG,kBAAkBE,MAAM,CAACF,YAAY,CAACa,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE;MAC5FK,SAAS,EAAE;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD;MAC9ClE,QAAQ,EAAE;IACZ;EACF,CAAC;EAED,MAAMmE,cAAc,GAAGL,eAAe,CAACpC,WAAW,CAAC,IAAIoC,eAAe,CAAChC,EAAE;;EAEzE;EACA,IAAIsC,UAAU,GAAG,EAAE;EACnB,IAAIC,cAAc,GAAG,EAAE;EAEvB,IAAI;IACF3E,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEuD,MAAM,CAACL,IAAI,EAAE,cAAc,EAAEnB,WAAW,CAAC;;IAE1F;IACA,MAAM4C,WAAW,GAAG5C,WAAW,KAAK,IAAI,GACpC,GAAGF,KAAK,IAAI0B,MAAM,CAACL,IAAI,SAAS,GAChC,GAAGrB,KAAK,IAAI0B,MAAM,CAACL,IAAI,EAAE;IAE7BuB,UAAU,GAAG,MAAMzF,gBAAgB,CAAC4F,aAAa,CAACD,WAAW,EAAE,CAAC,CAAC;IACjE5E,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEyE,UAAU,CAACI,MAAM,EAAE,SAAS,CAAC;IAE7D,IAAIJ,UAAU,CAACI,MAAM,GAAG,CAAC,EAAE;MACzB,MAAMC,WAAW,GAAG/C,WAAW,KAAK,IAAI,GACpC,wDAAwD0C,UAAU,CAACZ,GAAG,CAACkB,MAAM,IAC3E,KAAKA,MAAM,CAACC,KAAK,KAAKD,MAAM,CAACV,WAAW,KAAKU,MAAM,CAACA,MAAM,GAC5D,CAAC,CAACb,IAAI,CAAC,IAAI,CAAC,wHAAwH,GACpI,6DAA6DO,UAAU,CAACZ,GAAG,CAACkB,MAAM,IAChF,KAAKA,MAAM,CAACC,KAAK,KAAKD,MAAM,CAACV,WAAW,KAAKU,MAAM,CAACA,MAAM,GAC5D,CAAC,CAACb,IAAI,CAAC,IAAI,CAAC,mHAAmH;MAEnIQ,cAAc,GAAGI,WAAW;IAC9B;EACF,CAAC,CAAC,OAAOxD,KAAK,EAAE;IACdvB,OAAO,CAACkF,IAAI,CAAC,mDAAmD,EAAE3D,KAAK,CAAC;EAC1E;EAEA,MAAMe,MAAM,GAAG,4DAA4DkB,MAAM,CAACL,IAAI,wBAAwBrB,KAAK;AACrH;AACA;AACA,EAAEqD,QAAQ,IAAIxB,gBAAgB,CAAC,IAAI,CAAC;AACpC;AACA,EAAEyB,mBAAmB,GAAG,8BAA8BA,mBAAmB,EAAE,GAAG,EAAE;AAChF,EAAEC,eAAe,GAAG,sBAAsBA,eAAe,EAAE,GAAG,EAAE;AAChE,EAAEV,cAAc;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAahE,IAAI,CAACC,SAAS,CAAC6C,KAAK,CAAC;AAClC,gBAAgB3B,KAAK,MAAM0B,MAAM,CAACL,IAAI;AACtC;AACA;AACA;AACA;AACA;AACA,iBAAiBrB,KAAK;AACtB,UAAU0B,MAAM,CAACL,IAAI;AACrB,sBAAsBK,MAAM,CAACJ,SAAS;AACtC,iBAAiB,EAAAM,oBAAA,GAAAF,MAAM,CAACF,YAAY,cAAAI,oBAAA,uBAAnBA,oBAAA,CAAqBS,IAAI,CAAC,IAAI,CAAC,KAAI,MAAM;AAC1D,kBAAkBV,KAAK,CAACU,IAAI,CAAC,GAAG,CAAC,EAAE;EAEjC,IAAI;IACF,MAAM7D,QAAQ,GAAG,MAAMsB,MAAM,CAACzB,WAAW,CAAC,CACxC;MACEoC,IAAI,EAAE,QAAQ;MACdZ,OAAO,EAAE;IACX,CAAC,EACD;MACEY,IAAI,EAAE,MAAM;MACZZ,OAAO,EAAEW;IACX,CAAC,CACF,EAAE,GAAG,CAAC;;IAEP;IACA,MAAME,aAAa,GAAGlC,QAAQ,CAACmC,IAAI,CAAC,CAAC;IACrC,MAAMC,SAAS,GAAGF,aAAa,CAACG,KAAK,CAAC,aAAa,CAAC;IAEpD,IAAI,CAACD,SAAS,EAAE;MACd,MAAM,IAAIrB,KAAK,CAAC,iCAAiC,CAAC;IACpD;IAEA,MAAMiE,OAAO,GAAG3E,IAAI,CAACkC,KAAK,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC;;IAExC;IACA,IAAI,CAAC4C,OAAO,CAACC,KAAK,IAAI,CAACD,OAAO,CAACE,QAAQ,EAAE;MACvC,MAAM,IAAInE,KAAK,CAAC,2BAA2B,CAAC;IAC9C;;IAEA;IACA,IAAIqD,UAAU,CAACI,MAAM,GAAG,CAAC,EAAE;MACzB,MAAMW,WAAW,GAAGxG,gBAAgB,CAACyG,iBAAiB,CAAChB,UAAU,CAAC;MAClEY,OAAO,CAACE,QAAQ,IAAI,MAAM,GAAGC,WAAW;MACxCH,OAAO,CAACZ,UAAU,GAAGA,UAAU;IACjC;IAEA,OAAOY,OAAO;EAChB,CAAC,CAAC,OAAO/D,KAAK,EAAE;IACdvB,OAAO,CAACuB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;;IAEjD;IACA,OAAO;MACLgE,KAAK,EAAE,GAAG/B,MAAM,CAACL,IAAI,MAAMrB,KAAK,EAAE;MAClC0D,QAAQ,EAAE,KAAKhC,MAAM,CAACL,IAAI;AAChC;AACA,wBAAwBK,MAAM,CAACL,IAAI,sBAAsBrB,KAAK;AAC9D;AACA;AACA,EAAE0B,MAAM,CAACJ,SAAS;AAClB;AACA;AACA,gBAAgBI,MAAM,CAACL,IAAI,+BAA+BrB,KAAK;AAC/D;AACA;AACA,kBAAkB0B,MAAM,CAACL,IAAI;AAC7B;AACA;AACA,yFAAyF;MACnFG,YAAY,EAAEE,MAAM,CAACF,YAAY,IAAI,EAAE;MACvCG,KAAK,EAAEA,KAAK;MACZkC,OAAO,EAAE,GAAG7D,KAAK,MAAM0B,MAAM,CAACL,IAAI;IACpC,CAAC;EACH;AACF;;AAEA;AACA,OAAO,eAAeyC,cAAcA,CAAA,EAAG;EACrC,IAAI;IACF,MAAMtF,QAAQ,GAAG,MAAMsB,MAAM,CAACzB,WAAW,CAAC,CACxC;MACEoC,IAAI,EAAE,MAAM;MACZZ,OAAO,EAAE;IACX,CAAC,CACF,CAAC;IAEF,OAAOrB,QAAQ,CAACuF,QAAQ,CAAC,YAAY,CAAC;EACxC,CAAC,CAAC,OAAOtE,KAAK,EAAE;IACdvB,OAAO,CAACuB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACnD,OAAO,KAAK;EACd;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}