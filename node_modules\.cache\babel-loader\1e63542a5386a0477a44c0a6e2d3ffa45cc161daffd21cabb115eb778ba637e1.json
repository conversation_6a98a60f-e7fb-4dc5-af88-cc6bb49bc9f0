{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Appv1\\\\src\\\\components\\\\OptimizedApp.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport '../styles/optimized.css';\nimport gestureService, { createFlagWheel } from '../services/gestureService';\nimport speechService from '../services/speechService';\nimport exportService from '../services/exportService';\nimport gamificationService from '../services/optimizedGamificationService';\nimport { generateKnowledgeTree as generateTreeAPI, generateArticle as generateArticleAPI, testConnection } from '../services/openRouterService';\nimport tabService from '../services/tabService';\nimport webSearchService from '../services/webSearchService';\nimport TabManager from './TabManager';\nimport LanguageSwitcher from './LanguageSwitcher';\nimport { useTranslation, getCurrentLanguage } from '../utils/i18n';\n\n// Optimized Knowledge Tree Explorer - Pareto 80/20 Implementation\n// Focus on core functionality with maximum impact\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst OptimizedApp = () => {\n  _s();\n  // Core state - now managed by tabs\n  const [currentView, setCurrentView] = useState('input'); // 'input', 'tree', 'article'\n  const [topic, setTopic] = useState('');\n  const [activeTab, setActiveTab] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [user, setUser] = useState(null);\n  const appRef = useRef(null);\n\n  // Canvas state for infinite tree view\n  const [canvasTransform, setCanvasTransform] = useState({\n    x: 0,\n    y: 0,\n    scale: 1\n  });\n  const [isDragging, setIsDragging] = useState(false);\n  const [dragStart, setDragStart] = useState({\n    x: 0,\n    y: 0\n  });\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);\n\n  // Get current tab data\n  const tree = (activeTab === null || activeTab === void 0 ? void 0 : activeTab.tree) || null;\n  const selectedBranch = (activeTab === null || activeTab === void 0 ? void 0 : activeTab.selectedBranch) || null;\n  const article = (activeTab === null || activeTab === void 0 ? void 0 : activeTab.article) || null;\n\n  // Translation hook\n  const {\n    t\n  } = useTranslation();\n\n  // Available flags for the optimized version\n  const availableFlags = React.useMemo(() => [{\n    code: '-a',\n    name: 'Article',\n    description: t('flagArticle')\n  }, {\n    code: '-ex',\n    name: 'Examples',\n    description: t('flagExamples')\n  }, {\n    code: '-q',\n    name: 'Quiz',\n    description: t('flagQuiz')\n  }, {\n    code: '-vis',\n    name: 'Visual',\n    description: t('flagVisual')\n  }, {\n    code: '-path',\n    name: 'Learning Path',\n    description: t('flagPath')\n  }, {\n    code: '-case',\n    name: 'Case Study',\n    description: t('flagCase')\n  }, {\n    code: '-ro',\n    name: 'Romanian',\n    description: t('flagRomanian')\n  }], [t]);\n\n  // Generate article with tabs support\n  const generateArticleForBranch = React.useCallback(async (branch, flags = ['-a']) => {\n    if (!activeTab) return;\n    setIsLoading(true);\n\n    // Set tab to loading state (yellow)\n    tabService.updateTabStatus(activeTab.id, 'loading', {\n      selectedBranch: branch,\n      article: null\n    });\n    setActiveTab(tabService.getTab(activeTab.id));\n    try {\n      console.log('📄 Generating article for branch:', branch.nume);\n      const articleData = await generateArticleAPI(activeTab.topic, branch, flags);\n      console.log('✅ Generated article data:', articleData);\n\n      // Map the article data to expected format\n      const mappedArticle = {\n        title: articleData.titlu || articleData.title || `${branch.nume} - ${activeTab.topic}`,\n        content: articleData.continut || articleData.content || 'Content not available',\n        topic: activeTab.topic,\n        flags: flags,\n        position: articleData.pozitie || `${activeTab.topic} → ${branch.nume}`,\n        webSources: articleData.webSources || []\n      };\n\n      // Update tab with article and set to completed (green)\n      tabService.updateTabStatus(activeTab.id, 'completed', {\n        selectedBranch: branch,\n        article: mappedArticle\n      });\n      const updatedTab = tabService.getTab(activeTab.id);\n      setActiveTab(updatedTab);\n      setCurrentView('article');\n\n      // Award points for article generation\n      const result = gamificationService.awardPoints('ARTICLE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (error) {\n      console.error('❌ Error generating article:', error);\n      setError('Failed to generate article. Please try again.');\n\n      // Set tab back to pending on error\n      tabService.updateTabStatus(activeTab.id, 'pending', {\n        selectedBranch: branch,\n        article: null\n      });\n      setActiveTab(tabService.getTab(activeTab.id));\n    } finally {\n      setIsLoading(false);\n    }\n  }, [activeTab]);\n\n  // Handle branch selection (single tap)\n  const handleBranchSelect = React.useCallback(branch => {\n    if (activeTab) {\n      tabService.updateTabStatus(activeTab.id, activeTab.status, {\n        selectedBranch: branch\n      });\n      setActiveTab(tabService.getTab(activeTab.id));\n    }\n  }, [activeTab]);\n\n  // Gesture handlers\n  const handleDoubleTap = React.useCallback((event, targetInfo) => {\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      // Show flag wheel on double tap of branch\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        createFlagWheel(targetInfo.position, availableFlags, selectedFlags => {\n          console.log('Selected flags:', selectedFlags);\n        }, selectedFlags => {\n          generateArticleForBranch(branch, selectedFlags);\n        });\n      }\n    }\n  }, [tree, availableFlags, generateArticleForBranch]);\n  const handleSingleTap = React.useCallback((event, targetInfo) => {\n    // Single tap for normal selection\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        handleBranchSelect(branch);\n      }\n    }\n  }, [tree, handleBranchSelect]);\n\n  // Expand branch to create relevant sub-branches with AI\n  const expandBranch = React.useCallback(async (branch, branchIndex) => {\n    if (!activeTab || !tree) {\n      setError(t('noActiveTab') || 'No active tab or tree available');\n      return;\n    }\n    setIsLoading(true);\n    setError(null);\n    try {\n      var _data$choices$, _data$choices$$messag;\n      console.log('🌿 Expanding branch:', branch.nume, 'with AI-generated sub-branches');\n\n      // Create AI-generated sub-branches specifically for this branch\n      const currentLang = getCurrentLanguage();\n      const prompt = currentLang === 'ro' ? `Generează 4 sub-ramuri specifice și relevante pentru \"${branch.nume}\" în contextul \"${tree.tema}\".\n\nDescrierea ramuri principale: ${branch.descriere}\n\nCreează sub-ramuri care să fie:\n- Specifice și relevante pentru \"${branch.nume}\"\n- Logice și bine organizate\n- Utile pentru învățare progresivă\n- În limba română\n\nRăspunde DOAR cu JSON în formatul:\n{\n  \"ramuri\": [\n    {\n      \"nume\": \"Nume sub-ramură\",\n      \"descriere\": \"Descriere detaliată\",\n      \"emoji\": \"🔧\",\n      \"subcategorii\": [\"subcategorie1\", \"subcategorie2\", \"subcategorie3\"]\n    }\n  ]\n}` : `Generate 4 specific and relevant sub-branches for \"${branch.nume}\" in the context of \"${tree.tema}\".\n\nMain branch description: ${branch.descriere}\n\nCreate sub-branches that are:\n- Specific and relevant to \"${branch.nume}\"\n- Logical and well-organized\n- Useful for progressive learning\n- In English\n\nRespond ONLY with JSON in the format:\n{\n  \"branches\": [\n    {\n      \"nume\": \"Sub-branch name\",\n      \"descriere\": \"Detailed description\",\n      \"emoji\": \"🔧\",\n      \"subcategorii\": [\"subcategory1\", \"subcategory2\", \"subcategory3\"]\n    }\n  ]\n}`;\n\n      // CRITICAL: Use STRICT AI + Web validation for sub-branches\n      console.log('🔒 STRICT: Starting sub-branch generation with mandatory validation');\n\n      // STEP 1: Web validation BEFORE AI generation\n      let webValidation = [];\n      try {\n        webValidation = await webSearchService.searchSources(`${tree.tema} ${branch.nume} sub-topics`, 5);\n        console.log('✅ VERIFIED: Web validation completed with', webValidation.length, 'sources');\n      } catch (webError) {\n        console.warn('⚠️ Web validation failed, continuing with AI generation:', webError);\n        webValidation = []; // Continue without web sources\n      }\n\n      // STEP 2: AI generation with DeepSeek R1 + web context\n      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer 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`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': window.location.origin,\n          'X-Title': 'Knowledge Tree Explorer'\n        },\n        body: JSON.stringify({\n          model: 'deepseek/deepseek-r1-0528:free',\n          messages: [{\n            role: 'system',\n            content: currentLang === 'ro' ? 'Ești un expert în organizarea cunoștințelor. Generează sub-ramuri relevante și specifice în format JSON valid.' : 'You are an expert in knowledge organization. Generate relevant and specific sub-branches in valid JSON format.'\n          }, {\n            role: 'user',\n            content: prompt + (webValidation.length > 0 ? `\\n\\nSurse web de referință:\\n${webValidation.map(s => `- ${s.title}: ${s.description || s.snippet || 'No description'}`).join('\\n')}` : '')\n          }],\n          temperature: 0.7,\n          max_tokens: 2000\n        })\n      });\n      if (!response.ok) {\n        throw new Error(`API request failed: ${response.status}`);\n      }\n      const data = await response.json();\n      const responseText = ((_data$choices$ = data.choices[0]) === null || _data$choices$ === void 0 ? void 0 : (_data$choices$$messag = _data$choices$.message) === null || _data$choices$$messag === void 0 ? void 0 : _data$choices$$messag.content) || '';\n\n      // Parse the JSON response\n      const jsonMatch = responseText.match(/\\{[\\s\\S]*\\}/);\n      if (!jsonMatch) {\n        throw new Error('No valid JSON found in response');\n      }\n      const expandedData = JSON.parse(jsonMatch[0]);\n      const subBranches = expandedData.ramuri || expandedData.branches || [];\n      if (subBranches.length === 0) {\n        throw new Error('No sub-branches generated');\n      }\n      console.log('✅ Generated', subBranches.length, 'AI sub-branches for:', branch.nume);\n\n      // Update tree with AI-generated sub-branches\n      const newTree = {\n        ...tree\n      };\n      newTree.ramuri = [...newTree.ramuri.slice(0, branchIndex + 1), ...subBranches.map(subBranch => ({\n        ...subBranch,\n        isSubBranch: true,\n        parentBranch: branch.nume,\n        level: (branch.level || 0) + 1,\n        id: `${branch.nume}-${subBranch.nume}`.replace(/\\s+/g, '-').toLowerCase()\n      })), ...newTree.ramuri.slice(branchIndex + 1)];\n\n      // Update tab with expanded tree\n      tabService.updateTabStatus(activeTab.id, 'completed', {\n        tree: newTree\n      });\n      setActiveTab(tabService.getTab(activeTab.id));\n      console.log('🌳 Tree expanded successfully with AI sub-branches');\n\n      // Award points for branch expansion\n      const result = gamificationService.awardPoints('BRANCH_EXPANDED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (error) {\n      console.error('❌ Error expanding branch with AI:', error);\n      setError(t('failedToExpand') || 'Nu s-a putut extinde ramura. Te rugăm să încerci din nou.');\n    } finally {\n      setIsLoading(false);\n    }\n  }, [tree, activeTab, t]);\n  const handleLongPress = React.useCallback(async (event, targetInfo) => {\n    // Long press to expand branch and create tree effect\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        await expandBranch(branch, targetInfo.branchData.index);\n      }\n    }\n  }, [tree, expandBranch]);\n\n  // Canvas control functions\n  const zoomCanvas = React.useCallback(factor => {\n    setCanvasTransform(prev => ({\n      ...prev,\n      scale: Math.max(0.1, Math.min(3, prev.scale * factor))\n    }));\n  }, []);\n  const resetCanvasView = React.useCallback(() => {\n    setCanvasTransform({\n      x: 0,\n      y: 0,\n      scale: 1\n    });\n  }, []);\n\n  // Pan & Drag functionality - using existing state\n  const [dragOffset, setDragOffset] = React.useState({\n    x: 0,\n    y: 0\n  });\n  const handleMouseDown = React.useCallback(e => {\n    // Only start drag if not clicking on interactive elements\n    if (e.target.closest('.tree-branch-node') || e.target.closest('.central-topic-node')) {\n      return;\n    }\n    setIsDragging(true);\n    setDragStart({\n      x: e.clientX,\n      y: e.clientY\n    });\n    setDragOffset({\n      x: canvasTransform.x,\n      y: canvasTransform.y\n    });\n    e.preventDefault();\n  }, [canvasTransform, setIsDragging, setDragStart]);\n  const handleMouseMove = React.useCallback(e => {\n    if (!isDragging) return;\n    const deltaX = e.clientX - dragStart.x;\n    const deltaY = e.clientY - dragStart.y;\n    setCanvasTransform(prev => ({\n      ...prev,\n      x: dragOffset.x + deltaX,\n      y: dragOffset.y + deltaY\n    }));\n  }, [isDragging, dragStart, dragOffset]);\n  const handleMouseUp = React.useCallback(() => {\n    setIsDragging(false);\n  }, []);\n\n  // Touch events for mobile drag\n  const handleTouchStart = React.useCallback(e => {\n    if (e.target.closest('.tree-branch-node') || e.target.closest('.central-topic-node')) {\n      return;\n    }\n    const touch = e.touches[0];\n    setIsDragging(true);\n    setDragStart({\n      x: touch.clientX,\n      y: touch.clientY\n    });\n    setDragOffset({\n      x: canvasTransform.x,\n      y: canvasTransform.y\n    });\n    e.preventDefault();\n  }, [canvasTransform, setIsDragging, setDragStart]);\n  const handleTouchMove = React.useCallback(e => {\n    if (!isDragging) return;\n    const touch = e.touches[0];\n    const deltaX = touch.clientX - dragStart.x;\n    const deltaY = touch.clientY - dragStart.y;\n    setCanvasTransform(prev => ({\n      ...prev,\n      x: dragOffset.x + deltaX,\n      y: dragOffset.y + deltaY\n    }));\n    e.preventDefault();\n  }, [isDragging, dragStart, dragOffset]);\n  const handleTouchEnd = React.useCallback(() => {\n    setIsDragging(false);\n  }, []);\n  const handleCanvasMouseDown = React.useCallback(e => {\n    if (e.button === 0) {\n      // Left mouse button\n      setIsDragging(true);\n      setDragStart({\n        x: e.clientX - canvasTransform.x,\n        y: e.clientY - canvasTransform.y\n      });\n    }\n  }, [canvasTransform]);\n  const handleCanvasMouseMove = React.useCallback(e => {\n    if (isDragging) {\n      setCanvasTransform(prev => ({\n        ...prev,\n        x: e.clientX - dragStart.x,\n        y: e.clientY - dragStart.y\n      }));\n    }\n  }, [isDragging, dragStart]);\n  const handleCanvasMouseUp = React.useCallback(() => {\n    setIsDragging(false);\n  }, []);\n  const handleCanvasWheel = React.useCallback(e => {\n    e.preventDefault();\n    const factor = e.deltaY > 0 ? 0.9 : 1.1;\n    zoomCanvas(factor);\n  }, [zoomCanvas]);\n\n  // Handle window resize for mobile detection\n  useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth <= 768);\n    };\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  // Initialize TikTok scroll behavior on mobile\n  useEffect(() => {\n    const tiktokContainer = document.getElementById('tiktok-scroll');\n    if (tiktokContainer && isMobile) {\n      // Smooth scroll behavior\n      tiktokContainer.style.scrollBehavior = 'smooth';\n\n      // Optional: Add snap scrolling enhancement\n      let isScrolling = false;\n      const handleScroll = () => {\n        if (!isScrolling) {\n          isScrolling = true;\n          setTimeout(() => {\n            isScrolling = false;\n          }, 150);\n        }\n      };\n      tiktokContainer.addEventListener('scroll', handleScroll);\n      return () => {\n        tiktokContainer.removeEventListener('scroll', handleScroll);\n      };\n    }\n  }, [isMobile]);\n\n  // Initialize canvas event listeners\n  useEffect(() => {\n    const canvas = document.getElementById('infinite-canvas');\n    if (canvas && !isMobile) {\n      // Apply transform\n      canvas.style.transform = `translate(${canvasTransform.x}px, ${canvasTransform.y}px) scale(${canvasTransform.scale})`;\n      canvas.addEventListener('mousedown', handleCanvasMouseDown);\n      canvas.addEventListener('wheel', handleCanvasWheel);\n\n      // Add global mouse events for dragging\n      const handleGlobalMouseMove = e => {\n        if (isDragging) {\n          handleCanvasMouseMove(e);\n        }\n      };\n      const handleGlobalMouseUp = () => {\n        if (isDragging) {\n          handleCanvasMouseUp();\n        }\n      };\n      document.addEventListener('mousemove', handleGlobalMouseMove);\n      document.addEventListener('mouseup', handleGlobalMouseUp);\n      return () => {\n        canvas.removeEventListener('mousedown', handleCanvasMouseDown);\n        canvas.removeEventListener('wheel', handleCanvasWheel);\n        document.removeEventListener('mousemove', handleGlobalMouseMove);\n        document.removeEventListener('mouseup', handleGlobalMouseUp);\n      };\n    }\n  }, [canvasTransform, isMobile, isDragging, handleCanvasMouseDown, handleCanvasMouseMove, handleCanvasMouseUp, handleCanvasWheel]);\n\n  // Initialize services and authentication\n  useEffect(() => {\n    const storedUser = localStorage.getItem('user');\n    const bypassSecurity = localStorage.getItem('bypassSecurity');\n    if (storedUser || bypassSecurity) {\n      const userData = {\n        id: 'user-1',\n        name: 'User',\n        subscriptionTier: 'premium'\n      };\n      setUser(userData);\n\n      // Award daily login points\n      const result = gamificationService.awardPoints('DAILY_LOGIN');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n\n    // Initialize gesture service\n    if (appRef.current) {\n      gestureService.init(appRef.current, {\n        doubleTap: handleDoubleTap,\n        singleTap: handleSingleTap,\n        longPress: handleLongPress\n      });\n    }\n\n    // Add drag event listeners\n    const handleGlobalMouseMove = e => handleMouseMove(e);\n    const handleGlobalMouseUp = e => handleMouseUp(e);\n    const handleGlobalTouchMove = e => handleTouchMove(e);\n    const handleGlobalTouchEnd = e => handleTouchEnd(e);\n    document.addEventListener('mousemove', handleGlobalMouseMove);\n    document.addEventListener('mouseup', handleGlobalMouseUp);\n    document.addEventListener('touchmove', handleGlobalTouchMove, {\n      passive: false\n    });\n    document.addEventListener('touchend', handleGlobalTouchEnd);\n    return () => {\n      gestureService.destroy();\n      document.removeEventListener('mousemove', handleGlobalMouseMove);\n      document.removeEventListener('mouseup', handleGlobalMouseUp);\n      document.removeEventListener('touchmove', handleGlobalTouchMove);\n      document.removeEventListener('touchend', handleGlobalTouchEnd);\n    };\n  }, [handleDoubleTap, handleSingleTap, handleLongPress, handleMouseMove, handleMouseUp, handleTouchMove, handleTouchEnd]);\n\n  // Initialize gamification UI when user is logged in\n  useEffect(() => {\n    if (user) {\n      const container = document.getElementById('gamification-container');\n      if (container) {\n        // Clear existing content\n        container.innerHTML = '';\n        // Create gamification UI\n        gamificationService.createGamificationUI(container);\n      }\n\n      // Test API connection\n      testConnection().then(isConnected => {\n        console.log('🔌 API Connection Status:', isConnected ? '✅ Connected' : '❌ Failed');\n        if (!isConnected) {\n          console.warn('⚠️ API connection failed. Check your API key and internet connection.');\n        }\n      }).catch(error => {\n        console.error('❌ API connection test error:', error);\n      });\n    }\n  }, [user]);\n\n  // Core API call - using optimized service with tabs\n  const generateKnowledgeTree = async (topicInput, tabId = null) => {\n    let currentTabId = tabId;\n\n    // Create new tab if none provided\n    if (!currentTabId) {\n      try {\n        const newTab = tabService.createTab(topicInput);\n        currentTabId = newTab.id;\n        setActiveTab(newTab);\n        setCurrentView('tree');\n      } catch (error) {\n        setError(error.message);\n        return;\n      }\n    }\n\n    // Update tab status to generating\n    tabService.updateTabStatus(currentTabId, 'generating', {\n      progress: 10\n    });\n    setIsLoading(true);\n    setError(null);\n    try {\n      console.log('🌳 Generating knowledge tree for:', topicInput, 'in tab:', currentTabId);\n\n      // Update progress\n      tabService.updateTabStatus(currentTabId, 'generating', {\n        progress: 30\n      });\n      const treeData = await generateTreeAPI(topicInput, getCurrentLanguage());\n      console.log('✅ Generated tree data:', treeData);\n\n      // Update tab with completed tree\n      tabService.updateTabStatus(currentTabId, 'completed', {\n        tree: treeData,\n        progress: 100\n      });\n\n      // Update active tab if this is the current one\n      if (currentTabId === (activeTab === null || activeTab === void 0 ? void 0 : activeTab.id)) {\n        setActiveTab(tabService.getTab(currentTabId));\n      }\n\n      // Award points for tree generation\n      const result = gamificationService.awardPoints('TREE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (err) {\n      console.error('❌ Error generating tree:', err);\n      tabService.updateTabStatus(currentTabId, 'error');\n      setError(`Failed to generate knowledge tree: ${err.message}. Please try again.`);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Handle form submission\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (topic.trim()) {\n      generateKnowledgeTree(topic.trim());\n      setTopic(''); // Clear input for next topic\n    }\n  };\n\n  // Handle tab changes\n  const handleTabChange = tab => {\n    // Clear any existing errors when switching tabs\n    setError(null);\n    setIsLoading(false);\n    setActiveTab(tab);\n    if (tab !== null && tab !== void 0 && tab.tree) {\n      setCurrentView('tree');\n    } else {\n      setCurrentView('input');\n    }\n  };\n\n  // Handle new tab creation\n  const handleNewTab = () => {\n    // Clear any existing errors and loading states\n    setError(null);\n    setIsLoading(false);\n    setCurrentView('input');\n    setActiveTab(null);\n    setTopic('');\n  };\n  const handleTabArticleAccess = tab => {\n    // Clear any existing errors when accessing article\n    setError(null);\n    setIsLoading(false);\n    setActiveTab(tab);\n    setCurrentView('article');\n  };\n\n  // Speech functions\n  const handleSpeechToggle = () => {\n    if (!(article !== null && article !== void 0 && article.content)) return;\n    if (speechService.getStatus().isPlaying) {\n      speechService.toggle();\n    } else {\n      speechService.speak(article.content);\n      // Award points for using speech\n      const result = gamificationService.awardPoints('SPEECH_USED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleSpeechStop = () => {\n    speechService.stop();\n  };\n  const handleSpeechRateChange = rate => {\n    speechService.setRate(rate);\n  };\n\n  // Export functions\n  const handleExportPDF = () => {\n    if (!(article !== null && article !== void 0 && article.title) || !(article !== null && article !== void 0 && article.content)) return;\n    const result = exportService.exportAsPDF(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleExportWord = () => {\n    if (!(article !== null && article !== void 0 && article.title) || !(article !== null && article !== void 0 && article.content)) return;\n    const result = exportService.exportAsWord(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleCopyToClipboard = async () => {\n    if (!(article !== null && article !== void 0 && article.content)) return;\n    const result = await exportService.copyToClipboard(article.content);\n    exportService.showMessage(result.message, result.success ? 'success' : 'error');\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  // Navigation functions\n  const goBack = () => {\n    if (currentView === 'article') {\n      setCurrentView('tree');\n      if (activeTab) {\n        tabService.updateTabStatus(activeTab.id, activeTab.status, {\n          article: null\n        });\n        setActiveTab(tabService.getTab(activeTab.id));\n      }\n    } else if (currentView === 'tree') {\n      setCurrentView('input');\n    }\n  };\n  const goHome = () => {\n    setCurrentView('input');\n    setActiveTab(null);\n    setTopic('');\n    // Clear all tabs\n    tabService.clearAllTabs();\n  };\n\n  // Quick login for development\n  const quickLogin = () => {\n    localStorage.setItem('bypassSecurity', 'true');\n    setUser({\n      id: 'dev-1',\n      name: 'Developer',\n      subscriptionTier: 'premium'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    ref: appRef,\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"app-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: goHome,\n          className: \"logo-text\",\n          children: t('appTitle')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 778,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-right\",\n          children: [user && /*#__PURE__*/_jsxDEV(\"div\", {\n            id: \"gamification-container\",\n            style: {\n              marginRight: '16px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 783,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(LanguageSwitcher, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 787,\n            columnNumber: 13\n          }, this), !user ? /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: quickLogin,\n            className: \"btn btn-primary\",\n            style: {\n              marginLeft: '12px'\n            },\n            children: t('quickLogin')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 789,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginLeft: '12px'\n            },\n            children: [t('welcome'), \", \", user.name, \"!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 793,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 781,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 777,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 776,\n      columnNumber: 7\n    }, this), user && /*#__PURE__*/_jsxDEV(TabManager, {\n      onTabChange: handleTabChange,\n      onNewTab: handleNewTab,\n      onTabArticleAccess: handleTabArticleAccess\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 801,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"main-content\",\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error\",\n        children: [\"\\u26A0\\uFE0F \", error, /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setError(null),\n          style: {\n            marginLeft: 'auto',\n            background: 'none',\n            border: 'none',\n            color: 'white',\n            cursor: 'pointer'\n          },\n          children: \"\\u2715\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 813,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 811,\n        columnNumber: 11\n      }, this), currentView === 'input' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"title\",\n          children: t('appTitle')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 822,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"subtitle\",\n          children: \"Enter any topic to generate an interactive knowledge tree with AI-powered content.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 823,\n          columnNumber: 13\n        }, this), !user ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#f1f5f9',\n            padding: '1rem',\n            borderRadius: '0.5rem',\n            marginBottom: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#334155',\n              marginBottom: '1rem'\n            },\n            children: t('loginRequired')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 829,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: quickLogin,\n            className: \"btn btn-primary\",\n            children: t('quickLoginDev')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 832,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 828,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: topic,\n              onChange: e => setTopic(e.target.value),\n              placeholder: t('topicPlaceholder'),\n              className: \"form-input\",\n              disabled: isLoading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 839,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 838,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: isLoading || !topic.trim(),\n            className: \"btn btn-primary\",\n            children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 855,\n                columnNumber: 23\n              }, this), t('generating')]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: t('exploreKnowledge')\n            }, void 0, false)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 848,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 837,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 821,\n        columnNumber: 11\n      }, this), currentView === 'tree' && tree && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tree-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `desktop-tree-view ${isDragging ? 'dragging' : ''}`,\n          onMouseDown: handleMouseDown,\n          onTouchStart: handleTouchStart,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `infinite-canvas ${isDragging ? 'dragging' : ''}`,\n            id: \"infinite-canvas\",\n            style: {\n              transform: `translate(${canvasTransform.x}px, ${canvasTransform.y}px) scale(${canvasTransform.scale})`\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"central-topic-node\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"topic-input-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  children: tree.tema\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 888,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: goBack,\n                  className: \"btn btn-secondary back-btn\",\n                  children: t('backToTree')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 889,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 887,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 886,\n              columnNumber: 17\n            }, this), tree.ramuri.map((branch, index) => {\n              const totalBranches = tree.ramuri.length;\n              const angle = index * 360 / totalBranches;\n              const baseRadius = 300;\n              const levelOffset = (branch.level || 0) * 120;\n              const radius = baseRadius + levelOffset;\n\n              // Add some randomness for more organic look\n              const angleOffset = Math.sin(index * 2.5) * 15;\n              const finalAngle = angle + angleOffset;\n              const x = Math.cos(finalAngle * Math.PI / 180) * radius;\n              const y = Math.sin(finalAngle * Math.PI / 180) * radius;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `tree-branch-node branch-item ${selectedBranch === branch ? 'selected' : ''}`,\n                style: {\n                  transform: `translate(${x}px, ${y}px)`,\n                  '--branch-angle': `${finalAngle}deg`\n                },\n                \"data-index\": index,\n                \"data-level\": branch.level || 0,\n                \"data-name\": branch.nume,\n                \"data-description\": branch.descriere,\n                onClick: () => handleBranchSelect(branch),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"branch-connection-line\",\n                  style: {\n                    transform: `rotate(${finalAngle + 180}deg)`,\n                    width: `${radius}px`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 925,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"branch-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"branch-emoji\",\n                    children: branch.emoji\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 931,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"branch-name\",\n                    children: branch.nume\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 932,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"branch-description\",\n                    children: branch.descriere\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 933,\n                    columnNumber: 25\n                  }, this), branch.subcategorii && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"branch-subcategories\",\n                    children: branch.subcategorii.slice(0, 2).map((sub, subIndex) => /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"subcategory-tag\",\n                      children: sub\n                    }, subIndex, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 938,\n                      columnNumber: 31\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 936,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 930,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 911,\n                columnNumber: 21\n              }, this);\n            }), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"loading-overlay\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 951,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: t('loading')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 952,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 950,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 878,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"canvas-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"control-btn\",\n              onClick: () => zoomCanvas(1.2),\n              children: \"\\uD83D\\uDD0D+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 959,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"control-btn\",\n              onClick: () => zoomCanvas(0.8),\n              children: \"\\uD83D\\uDD0D-\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 960,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"control-btn\",\n              onClick: () => resetCanvasView(),\n              children: \"\\uD83C\\uDFAF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 961,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 958,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 873,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mobile-tree-view\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tiktok-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"tiktok-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                children: tree.tema\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 969,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: goBack,\n                className: \"btn btn-secondary\",\n                children: t('backToTree')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 970,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 968,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"tiktok-scroll-container\",\n              id: \"tiktok-scroll\",\n              children: tree.ramuri.map((branch, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `tiktok-branch-card branch-item ${selectedBranch === branch ? 'selected' : ''}`,\n                \"data-index\": index,\n                \"data-name\": branch.nume,\n                \"data-description\": branch.descriere,\n                onClick: () => handleBranchSelect(branch),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"tiktok-card-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"branch-emoji-large\",\n                    children: branch.emoji\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 986,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"branch-name-large\",\n                    children: branch.nume\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 987,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"branch-description-large\",\n                    children: branch.descriere\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 988,\n                    columnNumber: 25\n                  }, this), branch.subcategorii && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"tiktok-subcategories\",\n                    children: [branch.subcategorii.slice(0, 3).map((sub, subIndex) => /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"tiktok-subcategory-tag\",\n                      children: sub\n                    }, subIndex, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 993,\n                      columnNumber: 31\n                    }, this)), branch.subcategorii.length > 3 && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"tiktok-subcategory-tag more\",\n                      children: [\"+\", branch.subcategorii.length - 3]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 998,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 991,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"tiktok-gesture-hint\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"tiktok-action-hint\",\n                      children: \"\\uD83D\\uDCD6 Swipe down alte crengii\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1006,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"tiktok-action-hint\",\n                      children: \"\\uD83C\\uDF3F Long-press pentru expansiune\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1007,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1005,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 985,\n                  columnNumber: 23\n                }, this), (branch.level || 0) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"tiktok-level-indicator\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"level-badge\",\n                    children: [\"Nivel \", branch.level]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1014,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1013,\n                  columnNumber: 25\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 977,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 975,\n              columnNumber: 17\n            }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"tiktok-loading\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1023,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: t('loading')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1024,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1022,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 967,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 966,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 871,\n        columnNumber: 11\n      }, this), currentView === 'article' && article && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"article-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"article-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"article-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: goBack,\n              className: \"btn btn-secondary article-back-btn\",\n              children: t('backToTree')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1037,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"article-controls\",\n              style: {\n                display: 'flex',\n                gap: '8px',\n                marginTop: '1rem',\n                flexWrap: 'wrap'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"speech-controls-compact\",\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '8px',\n                  padding: '8px 12px',\n                  background: '#f1f5f9',\n                  borderRadius: '6px',\n                  border: '1px solid #e2e8f0'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleSpeechToggle,\n                  className: \"btn-icon\",\n                  title: \"Play/Pause Speech\",\n                  style: {\n                    background: 'none',\n                    border: 'none',\n                    fontSize: '16px',\n                    cursor: 'pointer',\n                    padding: '4px'\n                  },\n                  children: speechService.getStatus().isPlaying ? '⏸️' : '▶️'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1058,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleSpeechStop,\n                  className: \"btn-icon\",\n                  title: \"Stop Speech\",\n                  style: {\n                    background: 'none',\n                    border: 'none',\n                    fontSize: '16px',\n                    cursor: 'pointer',\n                    padding: '4px'\n                  },\n                  children: \"\\u23F9\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1072,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"range\",\n                  min: \"0.5\",\n                  max: \"2\",\n                  step: \"0.1\",\n                  defaultValue: \"1\",\n                  onChange: e => handleSpeechRateChange(parseFloat(e.target.value)),\n                  style: {\n                    width: '60px'\n                  },\n                  title: \"Speech Speed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1086,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '12px',\n                    color: '#64748b'\n                  },\n                  children: \"\\uD83D\\uDDE3\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1096,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1049,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"export-controls-compact\",\n                style: {\n                  display: 'flex',\n                  gap: '4px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleCopyToClipboard,\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '6px 12px',\n                    fontSize: '12px'\n                  },\n                  title: \"Copy to Clipboard\",\n                  children: \"\\uD83D\\uDCCB Copy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1104,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleExportPDF,\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '6px 12px',\n                    fontSize: '12px'\n                  },\n                  title: \"Export as PDF\",\n                  children: \"\\uD83D\\uDCC4 PDF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1112,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleExportWord,\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '6px 12px',\n                    fontSize: '12px'\n                  },\n                  title: \"Export as Word\",\n                  children: \"\\uD83D\\uDCDD Word\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1120,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1100,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1042,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1036,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"article-title-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"article-title\",\n              children: (article === null || article === void 0 ? void 0 : article.title) || 'Loading...'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1133,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"article-meta\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"article-topic\",\n                children: [t('partOf'), \": \", (article === null || article === void 0 ? void 0 : article.topic) || 'Unknown']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1135,\n                columnNumber: 19\n              }, this), (article === null || article === void 0 ? void 0 : article.flags) && article.flags.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"article-flags\",\n                children: [t('flags'), \":\", article.flags.map((flag, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"flag-badge\",\n                  children: flag\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1140,\n                  columnNumber: 25\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1137,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1134,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1132,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"article-content\",\n            children: article !== null && article !== void 0 && article.content ? article.content.split('\\n').map((paragraph, index) => paragraph.trim() && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"article-paragraph\",\n              children: paragraph\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1150,\n              columnNumber: 21\n            }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"article-loading\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1156,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Loading article content...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1157,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1155,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1147,\n            columnNumber: 15\n          }, this), (article === null || article === void 0 ? void 0 : article.webSources) && article.webSources.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"web-sources-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83D\\uDCDA Surse Web\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1165,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sources-grid\",\n              children: article.webSources.map((source, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"source-item\",\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: source.url,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  children: source.title || `Sursa ${index + 1}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1169,\n                  columnNumber: 25\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1168,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1166,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1164,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1035,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1034,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 809,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 774,\n    columnNumber: 5\n  }, this);\n};\n_s(OptimizedApp, \"0YAmgH+E0xR9THT7j0UtIUUTdZ0=\", false, function () {\n  return [useTranslation];\n});\n_c = OptimizedApp;\nexport default OptimizedApp;\nvar _c;\n$RefreshReg$(_c, \"OptimizedApp\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "gestureService", "createFlagWheel", "speechService", "exportService", "gamificationService", "generateKnowledgeTree", "generateTreeAPI", "generateArticle", "generateArticleAPI", "testConnection", "tabService", "webSearchService", "TabManager", "LanguageSwitcher", "useTranslation", "getCurrentLanguage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "OptimizedApp", "_s", "current<PERSON>iew", "set<PERSON><PERSON><PERSON>View", "topic", "setTopic", "activeTab", "setActiveTab", "isLoading", "setIsLoading", "error", "setError", "user", "setUser", "appRef", "canvasTransform", "setCanvasTransform", "x", "y", "scale", "isDragging", "setIsDragging", "dragStart", "setDragStart", "isMobile", "setIsMobile", "window", "innerWidth", "tree", "<PERSON><PERSON><PERSON><PERSON>", "article", "t", "availableFlags", "useMemo", "code", "name", "description", "generateArticleForBranch", "useCallback", "branch", "flags", "updateTabStatus", "id", "getTab", "console", "log", "nume", "articleData", "mappedArticle", "title", "titlu", "content", "continut", "position", "pozitie", "webSources", "updatedTab", "result", "awardPoints", "newAchievements", "length", "for<PERSON>ach", "achievement", "showAchievementNotification", "handleBranchSelect", "status", "handleDoubleTap", "event", "targetInfo", "isBranchItem", "branchData", "<PERSON><PERSON>", "index", "selected<PERSON><PERSON><PERSON>", "handleSingleTap", "expandBranch", "branchIndex", "_data$choices$", "_data$choices$$messag", "currentLang", "prompt", "tema", "desc<PERSON><PERSON>", "webValidation", "searchSources", "webError", "warn", "response", "fetch", "method", "headers", "location", "origin", "body", "JSON", "stringify", "model", "messages", "role", "map", "s", "snippet", "join", "temperature", "max_tokens", "ok", "Error", "data", "json", "responseText", "choices", "message", "jsonMatch", "match", "expandedData", "parse", "subBranches", "branches", "newTree", "slice", "subBranch", "isSubBranch", "parentBranch", "level", "replace", "toLowerCase", "handleLongPress", "zoomCanvas", "factor", "prev", "Math", "max", "min", "resetCanvasView", "dragOffset", "setDragOffset", "handleMouseDown", "e", "target", "closest", "clientX", "clientY", "preventDefault", "handleMouseMove", "deltaX", "deltaY", "handleMouseUp", "handleTouchStart", "touch", "touches", "handleTouchMove", "handleTouchEnd", "handleCanvasMouseDown", "button", "handleCanvasMouseMove", "handleCanvasMouseUp", "handleCanvasWheel", "handleResize", "addEventListener", "removeEventListener", "tiktokContainer", "document", "getElementById", "style", "scroll<PERSON>eh<PERSON>or", "isScrolling", "handleScroll", "setTimeout", "canvas", "transform", "handleGlobalMouseMove", "handleGlobalMouseUp", "storedUser", "localStorage", "getItem", "bypassSecurity", "userData", "subscriptionTier", "current", "init", "doubleTap", "singleTap", "longPress", "handleGlobalTouchMove", "handleGlobalTouchEnd", "passive", "destroy", "container", "innerHTML", "createGamificationUI", "then", "isConnected", "catch", "topicInput", "tabId", "currentTabId", "newTab", "createTab", "progress", "treeData", "err", "handleSubmit", "trim", "handleTabChange", "tab", "handleNewTab", "handleTabArticleAccess", "handleSpeechToggle", "getStatus", "isPlaying", "toggle", "speak", "handleSpeechStop", "stop", "handleSpeechRateChange", "rate", "setRate", "handleExportPDF", "exportAsPDF", "success", "gamResult", "handleExportWord", "exportAsWord", "handleCopyToClipboard", "copyToClipboard", "showMessage", "goBack", "goHome", "clearAllTabs", "quickLogin", "setItem", "className", "ref", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginRight", "marginLeft", "onTabChange", "onNewTab", "onTabArticleAccess", "background", "border", "color", "cursor", "padding", "borderRadius", "marginBottom", "onSubmit", "type", "value", "onChange", "placeholder", "disabled", "onMouseDown", "onTouchStart", "totalBranches", "angle", "baseRadius", "levelOffset", "radius", "angleOffset", "sin", "finalAngle", "cos", "PI", "width", "emoji", "subcategorii", "sub", "subIndex", "display", "gap", "marginTop", "flexWrap", "alignItems", "fontSize", "step", "defaultValue", "parseFloat", "flag", "split", "paragraph", "source", "href", "url", "rel", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Appv1/src/components/OptimizedApp.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport '../styles/optimized.css';\nimport gestureService, { createFlagWheel } from '../services/gestureService';\nimport speechService from '../services/speechService';\nimport exportService from '../services/exportService';\nimport gamificationService from '../services/optimizedGamificationService';\nimport { generateKnowledgeTree as generateTreeAPI, generateArticle as generateArticleAPI, testConnection } from '../services/openRouterService';\nimport tabService from '../services/tabService';\nimport webSearchService from '../services/webSearchService';\nimport TabManager from './TabManager';\nimport LanguageSwitcher from './LanguageSwitcher';\nimport { useTranslation, getCurrentLanguage } from '../utils/i18n';\n\n// Optimized Knowledge Tree Explorer - Pareto 80/20 Implementation\n// Focus on core functionality with maximum impact\n\nconst OptimizedApp = () => {\n  // Core state - now managed by tabs\n  const [currentView, setCurrentView] = useState('input'); // 'input', 'tree', 'article'\n  const [topic, setTopic] = useState('');\n  const [activeTab, setActiveTab] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [user, setUser] = useState(null);\n  const appRef = useRef(null);\n\n  // Canvas state for infinite tree view\n  const [canvasTransform, setCanvasTransform] = useState({ x: 0, y: 0, scale: 1 });\n  const [isDragging, setIsDragging] = useState(false);\n  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);\n\n  // Get current tab data\n  const tree = activeTab?.tree || null;\n  const selectedBranch = activeTab?.selectedBranch || null;\n  const article = activeTab?.article || null;\n\n  // Translation hook\n  const { t } = useTranslation();\n\n  // Available flags for the optimized version\n  const availableFlags = React.useMemo(() => [\n    { code: '-a', name: 'Article', description: t('flagArticle') },\n    { code: '-ex', name: 'Examples', description: t('flagExamples') },\n    { code: '-q', name: 'Quiz', description: t('flagQuiz') },\n    { code: '-vis', name: 'Visual', description: t('flagVisual') },\n    { code: '-path', name: 'Learning Path', description: t('flagPath') },\n    { code: '-case', name: 'Case Study', description: t('flagCase') },\n    { code: '-ro', name: 'Romanian', description: t('flagRomanian') }\n  ], [t]);\n\n  // Generate article with tabs support\n  const generateArticleForBranch = React.useCallback(async (branch, flags = ['-a']) => {\n    if (!activeTab) return;\n\n    setIsLoading(true);\n\n    // Set tab to loading state (yellow)\n    tabService.updateTabStatus(activeTab.id, 'loading', {\n      selectedBranch: branch,\n      article: null\n    });\n    setActiveTab(tabService.getTab(activeTab.id));\n\n    try {\n      console.log('📄 Generating article for branch:', branch.nume);\n      const articleData = await generateArticleAPI(activeTab.topic, branch, flags);\n\n      console.log('✅ Generated article data:', articleData);\n\n      // Map the article data to expected format\n      const mappedArticle = {\n        title: articleData.titlu || articleData.title || `${branch.nume} - ${activeTab.topic}`,\n        content: articleData.continut || articleData.content || 'Content not available',\n        topic: activeTab.topic,\n        flags: flags,\n        position: articleData.pozitie || `${activeTab.topic} → ${branch.nume}`,\n        webSources: articleData.webSources || []\n      };\n\n      // Update tab with article and set to completed (green)\n      tabService.updateTabStatus(activeTab.id, 'completed', {\n        selectedBranch: branch,\n        article: mappedArticle\n      });\n\n      const updatedTab = tabService.getTab(activeTab.id);\n      setActiveTab(updatedTab);\n      setCurrentView('article');\n\n      // Award points for article generation\n      const result = gamificationService.awardPoints('ARTICLE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (error) {\n      console.error('❌ Error generating article:', error);\n      setError('Failed to generate article. Please try again.');\n\n      // Set tab back to pending on error\n      tabService.updateTabStatus(activeTab.id, 'pending', {\n        selectedBranch: branch,\n        article: null\n      });\n      setActiveTab(tabService.getTab(activeTab.id));\n    } finally {\n      setIsLoading(false);\n    }\n  }, [activeTab]);\n\n  // Handle branch selection (single tap)\n  const handleBranchSelect = React.useCallback((branch) => {\n    if (activeTab) {\n      tabService.updateTabStatus(activeTab.id, activeTab.status, { selectedBranch: branch });\n      setActiveTab(tabService.getTab(activeTab.id));\n    }\n  }, [activeTab]);\n\n  // Gesture handlers\n  const handleDoubleTap = React.useCallback((event, targetInfo) => {\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      // Show flag wheel on double tap of branch\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        createFlagWheel(\n          targetInfo.position,\n          availableFlags,\n          (selectedFlags) => {\n            console.log('Selected flags:', selectedFlags);\n          },\n          (selectedFlags) => {\n            generateArticleForBranch(branch, selectedFlags);\n          }\n        );\n      }\n    }\n  }, [tree, availableFlags, generateArticleForBranch]);\n\n  const handleSingleTap = React.useCallback((event, targetInfo) => {\n    // Single tap for normal selection\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        handleBranchSelect(branch);\n      }\n    }\n  }, [tree, handleBranchSelect]);\n\n  // Expand branch to create relevant sub-branches with AI\n  const expandBranch = React.useCallback(async (branch, branchIndex) => {\n    if (!activeTab || !tree) {\n      setError(t('noActiveTab') || 'No active tab or tree available');\n      return;\n    }\n\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      console.log('🌿 Expanding branch:', branch.nume, 'with AI-generated sub-branches');\n\n      // Create AI-generated sub-branches specifically for this branch\n      const currentLang = getCurrentLanguage();\n      const prompt = currentLang === 'ro'\n        ? `Generează 4 sub-ramuri specifice și relevante pentru \"${branch.nume}\" în contextul \"${tree.tema}\".\n\nDescrierea ramuri principale: ${branch.descriere}\n\nCreează sub-ramuri care să fie:\n- Specifice și relevante pentru \"${branch.nume}\"\n- Logice și bine organizate\n- Utile pentru învățare progresivă\n- În limba română\n\nRăspunde DOAR cu JSON în formatul:\n{\n  \"ramuri\": [\n    {\n      \"nume\": \"Nume sub-ramură\",\n      \"descriere\": \"Descriere detaliată\",\n      \"emoji\": \"🔧\",\n      \"subcategorii\": [\"subcategorie1\", \"subcategorie2\", \"subcategorie3\"]\n    }\n  ]\n}`\n        : `Generate 4 specific and relevant sub-branches for \"${branch.nume}\" in the context of \"${tree.tema}\".\n\nMain branch description: ${branch.descriere}\n\nCreate sub-branches that are:\n- Specific and relevant to \"${branch.nume}\"\n- Logical and well-organized\n- Useful for progressive learning\n- In English\n\nRespond ONLY with JSON in the format:\n{\n  \"branches\": [\n    {\n      \"nume\": \"Sub-branch name\",\n      \"descriere\": \"Detailed description\",\n      \"emoji\": \"🔧\",\n      \"subcategorii\": [\"subcategory1\", \"subcategory2\", \"subcategory3\"]\n    }\n  ]\n}`;\n\n      // CRITICAL: Use STRICT AI + Web validation for sub-branches\n      console.log('🔒 STRICT: Starting sub-branch generation with mandatory validation');\n\n      // STEP 1: Web validation BEFORE AI generation\n      let webValidation = [];\n      try {\n        webValidation = await webSearchService.searchSources(`${tree.tema} ${branch.nume} sub-topics`, 5);\n        console.log('✅ VERIFIED: Web validation completed with', webValidation.length, 'sources');\n      } catch (webError) {\n        console.warn('⚠️ Web validation failed, continuing with AI generation:', webError);\n        webValidation = []; // Continue without web sources\n      }\n\n      // STEP 2: AI generation with DeepSeek R1 + web context\n      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer 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`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': window.location.origin,\n          'X-Title': 'Knowledge Tree Explorer'\n        },\n        body: JSON.stringify({\n          model: 'deepseek/deepseek-r1-0528:free',\n          messages: [\n            {\n              role: 'system',\n              content: currentLang === 'ro'\n                ? 'Ești un expert în organizarea cunoștințelor. Generează sub-ramuri relevante și specifice în format JSON valid.'\n                : 'You are an expert in knowledge organization. Generate relevant and specific sub-branches in valid JSON format.'\n            },\n            {\n              role: 'user',\n              content: prompt + (webValidation.length > 0 ? `\\n\\nSurse web de referință:\\n${webValidation.map(s => `- ${s.title}: ${s.description || s.snippet || 'No description'}`).join('\\n')}` : '')\n            }\n          ],\n          temperature: 0.7,\n          max_tokens: 2000\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error(`API request failed: ${response.status}`);\n      }\n\n      const data = await response.json();\n      const responseText = data.choices[0]?.message?.content || '';\n\n      // Parse the JSON response\n      const jsonMatch = responseText.match(/\\{[\\s\\S]*\\}/);\n      if (!jsonMatch) {\n        throw new Error('No valid JSON found in response');\n      }\n\n      const expandedData = JSON.parse(jsonMatch[0]);\n      const subBranches = expandedData.ramuri || expandedData.branches || [];\n\n      if (subBranches.length === 0) {\n        throw new Error('No sub-branches generated');\n      }\n\n      console.log('✅ Generated', subBranches.length, 'AI sub-branches for:', branch.nume);\n\n      // Update tree with AI-generated sub-branches\n      const newTree = { ...tree };\n      newTree.ramuri = [\n        ...newTree.ramuri.slice(0, branchIndex + 1),\n        ...subBranches.map(subBranch => ({\n          ...subBranch,\n          isSubBranch: true,\n          parentBranch: branch.nume,\n          level: (branch.level || 0) + 1,\n          id: `${branch.nume}-${subBranch.nume}`.replace(/\\s+/g, '-').toLowerCase()\n        })),\n        ...newTree.ramuri.slice(branchIndex + 1)\n      ];\n\n      // Update tab with expanded tree\n      tabService.updateTabStatus(activeTab.id, 'completed', { tree: newTree });\n      setActiveTab(tabService.getTab(activeTab.id));\n\n      console.log('🌳 Tree expanded successfully with AI sub-branches');\n\n      // Award points for branch expansion\n      const result = gamificationService.awardPoints('BRANCH_EXPANDED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n\n    } catch (error) {\n      console.error('❌ Error expanding branch with AI:', error);\n      setError(t('failedToExpand') || 'Nu s-a putut extinde ramura. Te rugăm să încerci din nou.');\n    } finally {\n      setIsLoading(false);\n    }\n  }, [tree, activeTab, t]);\n\n  const handleLongPress = React.useCallback(async (event, targetInfo) => {\n    // Long press to expand branch and create tree effect\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        await expandBranch(branch, targetInfo.branchData.index);\n      }\n    }\n  }, [tree, expandBranch]);\n\n  // Canvas control functions\n  const zoomCanvas = React.useCallback((factor) => {\n    setCanvasTransform(prev => ({\n      ...prev,\n      scale: Math.max(0.1, Math.min(3, prev.scale * factor))\n    }));\n  }, []);\n\n  const resetCanvasView = React.useCallback(() => {\n    setCanvasTransform({ x: 0, y: 0, scale: 1 });\n  }, []);\n\n  // Pan & Drag functionality - using existing state\n  const [dragOffset, setDragOffset] = React.useState({ x: 0, y: 0 });\n\n  const handleMouseDown = React.useCallback((e) => {\n    // Only start drag if not clicking on interactive elements\n    if (e.target.closest('.tree-branch-node') || e.target.closest('.central-topic-node')) {\n      return;\n    }\n\n    setIsDragging(true);\n    setDragStart({ x: e.clientX, y: e.clientY });\n    setDragOffset({ x: canvasTransform.x, y: canvasTransform.y });\n    e.preventDefault();\n  }, [canvasTransform, setIsDragging, setDragStart]);\n\n  const handleMouseMove = React.useCallback((e) => {\n    if (!isDragging) return;\n\n    const deltaX = e.clientX - dragStart.x;\n    const deltaY = e.clientY - dragStart.y;\n\n    setCanvasTransform(prev => ({\n      ...prev,\n      x: dragOffset.x + deltaX,\n      y: dragOffset.y + deltaY\n    }));\n  }, [isDragging, dragStart, dragOffset]);\n\n  const handleMouseUp = React.useCallback(() => {\n    setIsDragging(false);\n  }, []);\n\n  // Touch events for mobile drag\n  const handleTouchStart = React.useCallback((e) => {\n    if (e.target.closest('.tree-branch-node') || e.target.closest('.central-topic-node')) {\n      return;\n    }\n\n    const touch = e.touches[0];\n    setIsDragging(true);\n    setDragStart({ x: touch.clientX, y: touch.clientY });\n    setDragOffset({ x: canvasTransform.x, y: canvasTransform.y });\n    e.preventDefault();\n  }, [canvasTransform, setIsDragging, setDragStart]);\n\n  const handleTouchMove = React.useCallback((e) => {\n    if (!isDragging) return;\n\n    const touch = e.touches[0];\n    const deltaX = touch.clientX - dragStart.x;\n    const deltaY = touch.clientY - dragStart.y;\n\n    setCanvasTransform(prev => ({\n      ...prev,\n      x: dragOffset.x + deltaX,\n      y: dragOffset.y + deltaY\n    }));\n    e.preventDefault();\n  }, [isDragging, dragStart, dragOffset]);\n\n  const handleTouchEnd = React.useCallback(() => {\n    setIsDragging(false);\n  }, []);\n\n  const handleCanvasMouseDown = React.useCallback((e) => {\n    if (e.button === 0) { // Left mouse button\n      setIsDragging(true);\n      setDragStart({ x: e.clientX - canvasTransform.x, y: e.clientY - canvasTransform.y });\n    }\n  }, [canvasTransform]);\n\n  const handleCanvasMouseMove = React.useCallback((e) => {\n    if (isDragging) {\n      setCanvasTransform(prev => ({\n        ...prev,\n        x: e.clientX - dragStart.x,\n        y: e.clientY - dragStart.y\n      }));\n    }\n  }, [isDragging, dragStart]);\n\n  const handleCanvasMouseUp = React.useCallback(() => {\n    setIsDragging(false);\n  }, []);\n\n  const handleCanvasWheel = React.useCallback((e) => {\n    e.preventDefault();\n    const factor = e.deltaY > 0 ? 0.9 : 1.1;\n    zoomCanvas(factor);\n  }, [zoomCanvas]);\n\n  // Handle window resize for mobile detection\n  useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth <= 768);\n    };\n\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  // Initialize TikTok scroll behavior on mobile\n  useEffect(() => {\n    const tiktokContainer = document.getElementById('tiktok-scroll');\n    if (tiktokContainer && isMobile) {\n      // Smooth scroll behavior\n      tiktokContainer.style.scrollBehavior = 'smooth';\n\n      // Optional: Add snap scrolling enhancement\n      let isScrolling = false;\n      const handleScroll = () => {\n        if (!isScrolling) {\n          isScrolling = true;\n          setTimeout(() => {\n            isScrolling = false;\n          }, 150);\n        }\n      };\n\n      tiktokContainer.addEventListener('scroll', handleScroll);\n\n      return () => {\n        tiktokContainer.removeEventListener('scroll', handleScroll);\n      };\n    }\n  }, [isMobile]);\n\n  // Initialize canvas event listeners\n  useEffect(() => {\n    const canvas = document.getElementById('infinite-canvas');\n    if (canvas && !isMobile) {\n      // Apply transform\n      canvas.style.transform = `translate(${canvasTransform.x}px, ${canvasTransform.y}px) scale(${canvasTransform.scale})`;\n\n      canvas.addEventListener('mousedown', handleCanvasMouseDown);\n      canvas.addEventListener('wheel', handleCanvasWheel);\n\n      // Add global mouse events for dragging\n      const handleGlobalMouseMove = (e) => {\n        if (isDragging) {\n          handleCanvasMouseMove(e);\n        }\n      };\n\n      const handleGlobalMouseUp = () => {\n        if (isDragging) {\n          handleCanvasMouseUp();\n        }\n      };\n\n      document.addEventListener('mousemove', handleGlobalMouseMove);\n      document.addEventListener('mouseup', handleGlobalMouseUp);\n\n      return () => {\n        canvas.removeEventListener('mousedown', handleCanvasMouseDown);\n        canvas.removeEventListener('wheel', handleCanvasWheel);\n        document.removeEventListener('mousemove', handleGlobalMouseMove);\n        document.removeEventListener('mouseup', handleGlobalMouseUp);\n      };\n    }\n  }, [canvasTransform, isMobile, isDragging, handleCanvasMouseDown, handleCanvasMouseMove, handleCanvasMouseUp, handleCanvasWheel]);\n\n  // Initialize services and authentication\n  useEffect(() => {\n    const storedUser = localStorage.getItem('user');\n    const bypassSecurity = localStorage.getItem('bypassSecurity');\n\n    if (storedUser || bypassSecurity) {\n      const userData = {\n        id: 'user-1',\n        name: 'User',\n        subscriptionTier: 'premium'\n      };\n      setUser(userData);\n\n      // Award daily login points\n      const result = gamificationService.awardPoints('DAILY_LOGIN');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n\n    // Initialize gesture service\n    if (appRef.current) {\n      gestureService.init(appRef.current, {\n        doubleTap: handleDoubleTap,\n        singleTap: handleSingleTap,\n        longPress: handleLongPress\n      });\n    }\n\n    // Add drag event listeners\n    const handleGlobalMouseMove = (e) => handleMouseMove(e);\n    const handleGlobalMouseUp = (e) => handleMouseUp(e);\n    const handleGlobalTouchMove = (e) => handleTouchMove(e);\n    const handleGlobalTouchEnd = (e) => handleTouchEnd(e);\n\n    document.addEventListener('mousemove', handleGlobalMouseMove);\n    document.addEventListener('mouseup', handleGlobalMouseUp);\n    document.addEventListener('touchmove', handleGlobalTouchMove, { passive: false });\n    document.addEventListener('touchend', handleGlobalTouchEnd);\n\n    return () => {\n      gestureService.destroy();\n      document.removeEventListener('mousemove', handleGlobalMouseMove);\n      document.removeEventListener('mouseup', handleGlobalMouseUp);\n      document.removeEventListener('touchmove', handleGlobalTouchMove);\n      document.removeEventListener('touchend', handleGlobalTouchEnd);\n    };\n  }, [handleDoubleTap, handleSingleTap, handleLongPress, handleMouseMove, handleMouseUp, handleTouchMove, handleTouchEnd]);\n\n  // Initialize gamification UI when user is logged in\n  useEffect(() => {\n    if (user) {\n      const container = document.getElementById('gamification-container');\n      if (container) {\n        // Clear existing content\n        container.innerHTML = '';\n        // Create gamification UI\n        gamificationService.createGamificationUI(container);\n      }\n\n      // Test API connection\n      testConnection().then(isConnected => {\n        console.log('🔌 API Connection Status:', isConnected ? '✅ Connected' : '❌ Failed');\n        if (!isConnected) {\n          console.warn('⚠️ API connection failed. Check your API key and internet connection.');\n        }\n      }).catch(error => {\n        console.error('❌ API connection test error:', error);\n      });\n    }\n  }, [user]);\n\n  // Core API call - using optimized service with tabs\n  const generateKnowledgeTree = async (topicInput, tabId = null) => {\n    let currentTabId = tabId;\n\n    // Create new tab if none provided\n    if (!currentTabId) {\n      try {\n        const newTab = tabService.createTab(topicInput);\n        currentTabId = newTab.id;\n        setActiveTab(newTab);\n        setCurrentView('tree');\n      } catch (error) {\n        setError(error.message);\n        return;\n      }\n    }\n\n    // Update tab status to generating\n    tabService.updateTabStatus(currentTabId, 'generating', { progress: 10 });\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      console.log('🌳 Generating knowledge tree for:', topicInput, 'in tab:', currentTabId);\n\n      // Update progress\n      tabService.updateTabStatus(currentTabId, 'generating', { progress: 30 });\n\n      const treeData = await generateTreeAPI(topicInput, getCurrentLanguage());\n      console.log('✅ Generated tree data:', treeData);\n\n      // Update tab with completed tree\n      tabService.updateTabStatus(currentTabId, 'completed', {\n        tree: treeData,\n        progress: 100\n      });\n\n      // Update active tab if this is the current one\n      if (currentTabId === activeTab?.id) {\n        setActiveTab(tabService.getTab(currentTabId));\n      }\n\n      // Award points for tree generation\n      const result = gamificationService.awardPoints('TREE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (err) {\n      console.error('❌ Error generating tree:', err);\n      tabService.updateTabStatus(currentTabId, 'error');\n      setError(`Failed to generate knowledge tree: ${err.message}. Please try again.`);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n\n\n  // Handle form submission\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    if (topic.trim()) {\n      generateKnowledgeTree(topic.trim());\n      setTopic(''); // Clear input for next topic\n    }\n  };\n\n  // Handle tab changes\n  const handleTabChange = (tab) => {\n    // Clear any existing errors when switching tabs\n    setError(null);\n    setIsLoading(false);\n\n    setActiveTab(tab);\n    if (tab?.tree) {\n      setCurrentView('tree');\n    } else {\n      setCurrentView('input');\n    }\n  };\n\n  // Handle new tab creation\n  const handleNewTab = () => {\n    // Clear any existing errors and loading states\n    setError(null);\n    setIsLoading(false);\n\n    setCurrentView('input');\n    setActiveTab(null);\n    setTopic('');\n  };\n\n  const handleTabArticleAccess = (tab) => {\n    // Clear any existing errors when accessing article\n    setError(null);\n    setIsLoading(false);\n\n    setActiveTab(tab);\n    setCurrentView('article');\n  };\n\n\n\n  // Speech functions\n  const handleSpeechToggle = () => {\n    if (!article?.content) return;\n\n    if (speechService.getStatus().isPlaying) {\n      speechService.toggle();\n    } else {\n      speechService.speak(article.content);\n      // Award points for using speech\n      const result = gamificationService.awardPoints('SPEECH_USED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleSpeechStop = () => {\n    speechService.stop();\n  };\n\n  const handleSpeechRateChange = (rate) => {\n    speechService.setRate(rate);\n  };\n\n  // Export functions\n  const handleExportPDF = () => {\n    if (!article?.title || !article?.content) return;\n    const result = exportService.exportAsPDF(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleExportWord = () => {\n    if (!article?.title || !article?.content) return;\n    const result = exportService.exportAsWord(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleCopyToClipboard = async () => {\n    if (!article?.content) return;\n    const result = await exportService.copyToClipboard(article.content);\n    exportService.showMessage(result.message, result.success ? 'success' : 'error');\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n\n\n  // Navigation functions\n  const goBack = () => {\n    if (currentView === 'article') {\n      setCurrentView('tree');\n      if (activeTab) {\n        tabService.updateTabStatus(activeTab.id, activeTab.status, { article: null });\n        setActiveTab(tabService.getTab(activeTab.id));\n      }\n    } else if (currentView === 'tree') {\n      setCurrentView('input');\n    }\n  };\n\n\n\n  const goHome = () => {\n    setCurrentView('input');\n    setActiveTab(null);\n    setTopic('');\n    // Clear all tabs\n    tabService.clearAllTabs();\n  };\n\n  // Quick login for development\n  const quickLogin = () => {\n    localStorage.setItem('bypassSecurity', 'true');\n    setUser({ id: 'dev-1', name: 'Developer', subscriptionTier: 'premium' });\n  };\n\n  return (\n    <div className=\"app\" ref={appRef}>\n      {/* Header */}\n      <header className=\"app-header\">\n        <div className=\"header-content\">\n          <button onClick={goHome} className=\"logo-text\">\n            {t('appTitle')}\n          </button>\n          <div className=\"header-right\">\n            {user && (\n              <div id=\"gamification-container\" style={{ marginRight: '16px' }}>\n                {/* Gamification UI will be inserted here */}\n              </div>\n            )}\n            <LanguageSwitcher />\n            {!user ? (\n              <button onClick={quickLogin} className=\"btn btn-primary\" style={{ marginLeft: '12px' }}>\n                {t('quickLogin')}\n              </button>\n            ) : (\n              <span style={{ marginLeft: '12px' }}>{t('welcome')}, {user.name}!</span>\n            )}\n          </div>\n        </div>\n      </header>\n\n      {/* Tab Manager */}\n      {user && (\n        <TabManager\n          onTabChange={handleTabChange}\n          onNewTab={handleNewTab}\n          onTabArticleAccess={handleTabArticleAccess}\n        />\n      )}\n\n      {/* Main Content */}\n      <main className=\"main-content\">\n        {error && (\n          <div className=\"error\">\n            ⚠️ {error}\n            <button onClick={() => setError(null)} style={{marginLeft: 'auto', background: 'none', border: 'none', color: 'white', cursor: 'pointer'}}>\n              ✕\n            </button>\n          </div>\n        )}\n\n        {/* Topic Input View */}\n        {currentView === 'input' && (\n          <div className=\"card text-center\">\n            <h1 className=\"title\">{t('appTitle')}</h1>\n            <p className=\"subtitle\">\n              Enter any topic to generate an interactive knowledge tree with AI-powered content.\n            </p>\n\n            {!user ? (\n              <div style={{background: '#f1f5f9', padding: '1rem', borderRadius: '0.5rem', marginBottom: '2rem'}}>\n                <p style={{color: '#334155', marginBottom: '1rem'}}>\n                  {t('loginRequired')}\n                </p>\n                <button onClick={quickLogin} className=\"btn btn-primary\">\n                  {t('quickLoginDev')}\n                </button>\n              </div>\n            ) : (\n              <form onSubmit={handleSubmit}>\n                <div className=\"form-group\">\n                  <input\n                    type=\"text\"\n                    value={topic}\n                    onChange={(e) => setTopic(e.target.value)}\n                    placeholder={t('topicPlaceholder')}\n                    className=\"form-input\"\n                    disabled={isLoading}\n                  />\n                </div>\n                <button\n                  type=\"submit\"\n                  disabled={isLoading || !topic.trim()}\n                  className=\"btn btn-primary\"\n                >\n                  {isLoading ? (\n                    <>\n                      <span className=\"spinner\"></span>\n                      {t('generating')}\n                    </>\n                  ) : (\n                    <>\n                      {t('exploreKnowledge')}\n                    </>\n                  )}\n                </button>\n              </form>\n            )}\n          </div>\n        )}\n\n        {/* Tree View - Desktop: Infinite Tree, Mobile: TikTok Style */}\n        {currentView === 'tree' && tree && (\n          <div className=\"tree-container\">\n            {/* Desktop Tree View - Infinite Mind Map */}\n            <div\n              className={`desktop-tree-view ${isDragging ? 'dragging' : ''}`}\n              onMouseDown={handleMouseDown}\n              onTouchStart={handleTouchStart}\n            >\n              <div\n                className={`infinite-canvas ${isDragging ? 'dragging' : ''}`}\n                id=\"infinite-canvas\"\n                style={{\n                  transform: `translate(${canvasTransform.x}px, ${canvasTransform.y}px) scale(${canvasTransform.scale})`\n                }}\n              >\n                {/* Central Topic Node */}\n                <div className=\"central-topic-node\">\n                  <div className=\"topic-input-center\">\n                    <h2>{tree.tema}</h2>\n                    <button onClick={goBack} className=\"btn btn-secondary back-btn\">\n                      {t('backToTree')}\n                    </button>\n                  </div>\n                </div>\n\n                {/* Branches positioned around center */}\n                {tree.ramuri.map((branch, index) => {\n                  const totalBranches = tree.ramuri.length;\n                  const angle = (index * 360) / totalBranches;\n                  const baseRadius = 300;\n                  const levelOffset = (branch.level || 0) * 120;\n                  const radius = baseRadius + levelOffset;\n\n                  // Add some randomness for more organic look\n                  const angleOffset = (Math.sin(index * 2.5) * 15);\n                  const finalAngle = angle + angleOffset;\n\n                  const x = Math.cos((finalAngle * Math.PI) / 180) * radius;\n                  const y = Math.sin((finalAngle * Math.PI) / 180) * radius;\n\n                  return (\n                    <div\n                      key={index}\n                      className={`tree-branch-node branch-item ${selectedBranch === branch ? 'selected' : ''}`}\n                      style={{\n                        transform: `translate(${x}px, ${y}px)`,\n                        '--branch-angle': `${finalAngle}deg`\n                      }}\n                      data-index={index}\n                      data-level={branch.level || 0}\n                      data-name={branch.nume}\n                      data-description={branch.descriere}\n                      onClick={() => handleBranchSelect(branch)}\n                    >\n                      {/* Connection Line to Center */}\n                      <div className=\"branch-connection-line\" style={{\n                        transform: `rotate(${finalAngle + 180}deg)`,\n                        width: `${radius}px`\n                      }}></div>\n\n                      <div className=\"branch-content\">\n                        <div className=\"branch-emoji\">{branch.emoji}</div>\n                        <h3 className=\"branch-name\">{branch.nume}</h3>\n                        <p className=\"branch-description\">{branch.descriere}</p>\n\n                        {branch.subcategorii && (\n                          <div className=\"branch-subcategories\">\n                            {branch.subcategorii.slice(0, 2).map((sub, subIndex) => (\n                              <span key={subIndex} className=\"subcategory-tag\">\n                                {sub}\n                              </span>\n                            ))}\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  );\n                })}\n\n                {isLoading && (\n                  <div className=\"loading-overlay\">\n                    <span className=\"spinner\"></span>\n                    <span>{t('loading')}</span>\n                  </div>\n                )}\n              </div>\n\n              {/* Pan & Zoom Controls */}\n              <div className=\"canvas-controls\">\n                <button className=\"control-btn\" onClick={() => zoomCanvas(1.2)}>🔍+</button>\n                <button className=\"control-btn\" onClick={() => zoomCanvas(0.8)}>🔍-</button>\n                <button className=\"control-btn\" onClick={() => resetCanvasView()}>🎯</button>\n              </div>\n            </div>\n\n            {/* Mobile Tree View - TikTok Style */}\n            <div className=\"mobile-tree-view\">\n              <div className=\"tiktok-container\">\n                <div className=\"tiktok-header\">\n                  <h2>{tree.tema}</h2>\n                  <button onClick={goBack} className=\"btn btn-secondary\">\n                    {t('backToTree')}\n                  </button>\n                </div>\n\n                <div className=\"tiktok-scroll-container\" id=\"tiktok-scroll\">\n                  {tree.ramuri.map((branch, index) => (\n                    <div\n                      key={index}\n                      className={`tiktok-branch-card branch-item ${selectedBranch === branch ? 'selected' : ''}`}\n                      data-index={index}\n                      data-name={branch.nume}\n                      data-description={branch.descriere}\n                      onClick={() => handleBranchSelect(branch)}\n                    >\n                      <div className=\"tiktok-card-content\">\n                        <div className=\"branch-emoji-large\">{branch.emoji}</div>\n                        <h3 className=\"branch-name-large\">{branch.nume}</h3>\n                        <p className=\"branch-description-large\">{branch.descriere}</p>\n\n                        {branch.subcategorii && (\n                          <div className=\"tiktok-subcategories\">\n                            {branch.subcategorii.slice(0, 3).map((sub, subIndex) => (\n                              <span key={subIndex} className=\"tiktok-subcategory-tag\">\n                                {sub}\n                              </span>\n                            ))}\n                            {branch.subcategorii.length > 3 && (\n                              <span className=\"tiktok-subcategory-tag more\">\n                                +{branch.subcategorii.length - 3}\n                              </span>\n                            )}\n                          </div>\n                        )}\n\n                        <div className=\"tiktok-gesture-hint\">\n                          <span className=\"tiktok-action-hint\">📖 Swipe down alte crengii</span>\n                          <span className=\"tiktok-action-hint\">🌿 Long-press pentru expansiune</span>\n                        </div>\n                      </div>\n\n                      {/* Level indicator for sub-branches */}\n                      {(branch.level || 0) > 0 && (\n                        <div className=\"tiktok-level-indicator\">\n                          <div className=\"level-badge\">Nivel {branch.level}</div>\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </div>\n\n                {isLoading && (\n                  <div className=\"tiktok-loading\">\n                    <span className=\"spinner\"></span>\n                    <span>{t('loading')}</span>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Article View - Redesigned */}\n        {currentView === 'article' && article && (\n          <div className=\"article-container\">\n            <div className=\"article-card\">\n              <div className=\"article-header\">\n                <button onClick={goBack} className=\"btn btn-secondary article-back-btn\">\n                  {t('backToTree')}\n                </button>\n\n                {/* Article Controls */}\n                <div className=\"article-controls\" style={{\n                  display: 'flex',\n                  gap: '8px',\n                  marginTop: '1rem',\n                  flexWrap: 'wrap'\n                }}>\n                  {/* Speech Controls */}\n                  <div className=\"speech-controls-compact\" style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '8px',\n                    padding: '8px 12px',\n                    background: '#f1f5f9',\n                    borderRadius: '6px',\n                    border: '1px solid #e2e8f0'\n                  }}>\n                    <button\n                      onClick={handleSpeechToggle}\n                      className=\"btn-icon\"\n                      title=\"Play/Pause Speech\"\n                      style={{\n                        background: 'none',\n                        border: 'none',\n                        fontSize: '16px',\n                        cursor: 'pointer',\n                        padding: '4px'\n                      }}\n                    >\n                      {speechService.getStatus().isPlaying ? '⏸️' : '▶️'}\n                    </button>\n                    <button\n                      onClick={handleSpeechStop}\n                      className=\"btn-icon\"\n                      title=\"Stop Speech\"\n                      style={{\n                        background: 'none',\n                        border: 'none',\n                        fontSize: '16px',\n                        cursor: 'pointer',\n                        padding: '4px'\n                      }}\n                    >\n                      ⏹️\n                    </button>\n                    <input\n                      type=\"range\"\n                      min=\"0.5\"\n                      max=\"2\"\n                      step=\"0.1\"\n                      defaultValue=\"1\"\n                      onChange={(e) => handleSpeechRateChange(parseFloat(e.target.value))}\n                      style={{width: '60px'}}\n                      title=\"Speech Speed\"\n                    />\n                    <span style={{fontSize: '12px', color: '#64748b'}}>🗣️</span>\n                  </div>\n\n                  {/* Export Controls */}\n                  <div className=\"export-controls-compact\" style={{\n                    display: 'flex',\n                    gap: '4px'\n                  }}>\n                    <button\n                      onClick={handleCopyToClipboard}\n                      className=\"btn btn-secondary\"\n                      style={{padding: '6px 12px', fontSize: '12px'}}\n                      title=\"Copy to Clipboard\"\n                    >\n                      📋 Copy\n                    </button>\n                    <button\n                      onClick={handleExportPDF}\n                      className=\"btn btn-secondary\"\n                      style={{padding: '6px 12px', fontSize: '12px'}}\n                      title=\"Export as PDF\"\n                    >\n                      📄 PDF\n                    </button>\n                    <button\n                      onClick={handleExportWord}\n                      className=\"btn btn-secondary\"\n                      style={{padding: '6px 12px', fontSize: '12px'}}\n                      title=\"Export as Word\"\n                    >\n                      📝 Word\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"article-title-section\">\n                <h1 className=\"article-title\">{article?.title || 'Loading...'}</h1>\n                <div className=\"article-meta\">\n                  <span className=\"article-topic\">{t('partOf')}: {article?.topic || 'Unknown'}</span>\n                  {article?.flags && article.flags.length > 0 && (\n                    <div className=\"article-flags\">\n                      {t('flags')}:\n                      {article.flags.map((flag, index) => (\n                        <span key={index} className=\"flag-badge\">{flag}</span>\n                      ))}\n                    </div>\n                  )}\n                </div>\n              </div>\n\n              <div className=\"article-content\">\n                {article?.content ? article.content.split('\\n').map((paragraph, index) => (\n                  paragraph.trim() && (\n                    <p key={index} className=\"article-paragraph\">\n                      {paragraph}\n                    </p>\n                  )\n                )) : (\n                  <div className=\"article-loading\">\n                    <span className=\"spinner\"></span>\n                    <p>Loading article content...</p>\n                  </div>\n                )}\n              </div>\n\n              {/* Web Sources Section */}\n              {article?.webSources && article.webSources.length > 0 && (\n                <div className=\"web-sources-section\">\n                  <h3>📚 Surse Web</h3>\n                  <div className=\"sources-grid\">\n                    {article.webSources.map((source, index) => (\n                      <div key={index} className=\"source-item\">\n                        <a href={source.url} target=\"_blank\" rel=\"noopener noreferrer\">\n                          {source.title || `Sursa ${index + 1}`}\n                        </a>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </main>\n    </div>\n  );\n};\n\nexport default OptimizedApp;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,yBAAyB;AAChC,OAAOC,cAAc,IAAIC,eAAe,QAAQ,4BAA4B;AAC5E,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,mBAAmB,MAAM,0CAA0C;AAC1E,SAASC,qBAAqB,IAAIC,eAAe,EAAEC,eAAe,IAAIC,kBAAkB,EAAEC,cAAc,QAAQ,+BAA+B;AAC/I,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,cAAc,EAAEC,kBAAkB,QAAQ,eAAe;;AAElE;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEA,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACzD,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiC,KAAK,EAAEC,QAAQ,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACmC,IAAI,EAAEC,OAAO,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAMqC,MAAM,GAAGnC,MAAM,CAAC,IAAI,CAAC;;EAE3B;EACA,MAAM,CAACoC,eAAe,EAAEC,kBAAkB,CAAC,GAAGvC,QAAQ,CAAC;IAAEwC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAE,CAAC,CAAC;EAChF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC6C,SAAS,EAAEC,YAAY,CAAC,GAAG9C,QAAQ,CAAC;IAAEwC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAC1D,MAAM,CAACM,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAACiD,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;;EAElE;EACA,MAAMC,IAAI,GAAG,CAAAtB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEsB,IAAI,KAAI,IAAI;EACpC,MAAMC,cAAc,GAAG,CAAAvB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEuB,cAAc,KAAI,IAAI;EACxD,MAAMC,OAAO,GAAG,CAAAxB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEwB,OAAO,KAAI,IAAI;;EAE1C;EACA,MAAM;IAAEC;EAAE,CAAC,GAAGrC,cAAc,CAAC,CAAC;;EAE9B;EACA,MAAMsC,cAAc,GAAGxD,KAAK,CAACyD,OAAO,CAAC,MAAM,CACzC;IAAEC,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,SAAS;IAAEC,WAAW,EAAEL,CAAC,CAAC,aAAa;EAAE,CAAC,EAC9D;IAAEG,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAE,UAAU;IAAEC,WAAW,EAAEL,CAAC,CAAC,cAAc;EAAE,CAAC,EACjE;IAAEG,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,MAAM;IAAEC,WAAW,EAAEL,CAAC,CAAC,UAAU;EAAE,CAAC,EACxD;IAAEG,IAAI,EAAE,MAAM;IAAEC,IAAI,EAAE,QAAQ;IAAEC,WAAW,EAAEL,CAAC,CAAC,YAAY;EAAE,CAAC,EAC9D;IAAEG,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,eAAe;IAAEC,WAAW,EAAEL,CAAC,CAAC,UAAU;EAAE,CAAC,EACpE;IAAEG,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,YAAY;IAAEC,WAAW,EAAEL,CAAC,CAAC,UAAU;EAAE,CAAC,EACjE;IAAEG,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAE,UAAU;IAAEC,WAAW,EAAEL,CAAC,CAAC,cAAc;EAAE,CAAC,CAClE,EAAE,CAACA,CAAC,CAAC,CAAC;;EAEP;EACA,MAAMM,wBAAwB,GAAG7D,KAAK,CAAC8D,WAAW,CAAC,OAAOC,MAAM,EAAEC,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK;IACnF,IAAI,CAAClC,SAAS,EAAE;IAEhBG,YAAY,CAAC,IAAI,CAAC;;IAElB;IACAnB,UAAU,CAACmD,eAAe,CAACnC,SAAS,CAACoC,EAAE,EAAE,SAAS,EAAE;MAClDb,cAAc,EAAEU,MAAM;MACtBT,OAAO,EAAE;IACX,CAAC,CAAC;IACFvB,YAAY,CAACjB,UAAU,CAACqD,MAAM,CAACrC,SAAS,CAACoC,EAAE,CAAC,CAAC;IAE7C,IAAI;MACFE,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEN,MAAM,CAACO,IAAI,CAAC;MAC7D,MAAMC,WAAW,GAAG,MAAM3D,kBAAkB,CAACkB,SAAS,CAACF,KAAK,EAAEmC,MAAM,EAAEC,KAAK,CAAC;MAE5EI,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEE,WAAW,CAAC;;MAErD;MACA,MAAMC,aAAa,GAAG;QACpBC,KAAK,EAAEF,WAAW,CAACG,KAAK,IAAIH,WAAW,CAACE,KAAK,IAAI,GAAGV,MAAM,CAACO,IAAI,MAAMxC,SAAS,CAACF,KAAK,EAAE;QACtF+C,OAAO,EAAEJ,WAAW,CAACK,QAAQ,IAAIL,WAAW,CAACI,OAAO,IAAI,uBAAuB;QAC/E/C,KAAK,EAAEE,SAAS,CAACF,KAAK;QACtBoC,KAAK,EAAEA,KAAK;QACZa,QAAQ,EAAEN,WAAW,CAACO,OAAO,IAAI,GAAGhD,SAAS,CAACF,KAAK,MAAMmC,MAAM,CAACO,IAAI,EAAE;QACtES,UAAU,EAAER,WAAW,CAACQ,UAAU,IAAI;MACxC,CAAC;;MAED;MACAjE,UAAU,CAACmD,eAAe,CAACnC,SAAS,CAACoC,EAAE,EAAE,WAAW,EAAE;QACpDb,cAAc,EAAEU,MAAM;QACtBT,OAAO,EAAEkB;MACX,CAAC,CAAC;MAEF,MAAMQ,UAAU,GAAGlE,UAAU,CAACqD,MAAM,CAACrC,SAAS,CAACoC,EAAE,CAAC;MAClDnC,YAAY,CAACiD,UAAU,CAAC;MACxBrD,cAAc,CAAC,SAAS,CAAC;;MAEzB;MACA,MAAMsD,MAAM,GAAGzE,mBAAmB,CAAC0E,WAAW,CAAC,mBAAmB,CAAC;MACnE,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5C9E,mBAAmB,CAAC+E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOpD,KAAK,EAAE;MACdkC,OAAO,CAAClC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDC,QAAQ,CAAC,+CAA+C,CAAC;;MAEzD;MACArB,UAAU,CAACmD,eAAe,CAACnC,SAAS,CAACoC,EAAE,EAAE,SAAS,EAAE;QAClDb,cAAc,EAAEU,MAAM;QACtBT,OAAO,EAAE;MACX,CAAC,CAAC;MACFvB,YAAY,CAACjB,UAAU,CAACqD,MAAM,CAACrC,SAAS,CAACoC,EAAE,CAAC,CAAC;IAC/C,CAAC,SAAS;MACRjC,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACH,SAAS,CAAC,CAAC;;EAEf;EACA,MAAM0D,kBAAkB,GAAGxF,KAAK,CAAC8D,WAAW,CAAEC,MAAM,IAAK;IACvD,IAAIjC,SAAS,EAAE;MACbhB,UAAU,CAACmD,eAAe,CAACnC,SAAS,CAACoC,EAAE,EAAEpC,SAAS,CAAC2D,MAAM,EAAE;QAAEpC,cAAc,EAAEU;MAAO,CAAC,CAAC;MACtFhC,YAAY,CAACjB,UAAU,CAACqD,MAAM,CAACrC,SAAS,CAACoC,EAAE,CAAC,CAAC;IAC/C;EACF,CAAC,EAAE,CAACpC,SAAS,CAAC,CAAC;;EAEf;EACA,MAAM4D,eAAe,GAAG1F,KAAK,CAAC8D,WAAW,CAAC,CAAC6B,KAAK,EAAEC,UAAU,KAAK;IAC/D,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,IAAI1C,IAAI,EAAE;MAC5D;MACA,MAAMW,MAAM,GAAGX,IAAI,CAAC2C,MAAM,CAACH,UAAU,CAACE,UAAU,CAACE,KAAK,CAAC;MACvD,IAAIjC,MAAM,EAAE;QACV1D,eAAe,CACbuF,UAAU,CAACf,QAAQ,EACnBrB,cAAc,EACbyC,aAAa,IAAK;UACjB7B,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE4B,aAAa,CAAC;QAC/C,CAAC,EACAA,aAAa,IAAK;UACjBpC,wBAAwB,CAACE,MAAM,EAAEkC,aAAa,CAAC;QACjD,CACF,CAAC;MACH;IACF;EACF,CAAC,EAAE,CAAC7C,IAAI,EAAEI,cAAc,EAAEK,wBAAwB,CAAC,CAAC;EAEpD,MAAMqC,eAAe,GAAGlG,KAAK,CAAC8D,WAAW,CAAC,CAAC6B,KAAK,EAAEC,UAAU,KAAK;IAC/D;IACA,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,IAAI1C,IAAI,EAAE;MAC5D,MAAMW,MAAM,GAAGX,IAAI,CAAC2C,MAAM,CAACH,UAAU,CAACE,UAAU,CAACE,KAAK,CAAC;MACvD,IAAIjC,MAAM,EAAE;QACVyB,kBAAkB,CAACzB,MAAM,CAAC;MAC5B;IACF;EACF,CAAC,EAAE,CAACX,IAAI,EAAEoC,kBAAkB,CAAC,CAAC;;EAE9B;EACA,MAAMW,YAAY,GAAGnG,KAAK,CAAC8D,WAAW,CAAC,OAAOC,MAAM,EAAEqC,WAAW,KAAK;IACpE,IAAI,CAACtE,SAAS,IAAI,CAACsB,IAAI,EAAE;MACvBjB,QAAQ,CAACoB,CAAC,CAAC,aAAa,CAAC,IAAI,iCAAiC,CAAC;MAC/D;IACF;IAEAtB,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MAAA,IAAAkE,cAAA,EAAAC,qBAAA;MACFlC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEN,MAAM,CAACO,IAAI,EAAE,gCAAgC,CAAC;;MAElF;MACA,MAAMiC,WAAW,GAAGpF,kBAAkB,CAAC,CAAC;MACxC,MAAMqF,MAAM,GAAGD,WAAW,KAAK,IAAI,GAC/B,yDAAyDxC,MAAM,CAACO,IAAI,mBAAmBlB,IAAI,CAACqD,IAAI;AAC1G;AACA,gCAAgC1C,MAAM,CAAC2C,SAAS;AAChD;AACA;AACA,mCAAmC3C,MAAM,CAACO,IAAI;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,GACQ,sDAAsDP,MAAM,CAACO,IAAI,wBAAwBlB,IAAI,CAACqD,IAAI;AAC5G;AACA,2BAA2B1C,MAAM,CAAC2C,SAAS;AAC3C;AACA;AACA,8BAA8B3C,MAAM,CAACO,IAAI;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;;MAEI;MACAF,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;;MAElF;MACA,IAAIsC,aAAa,GAAG,EAAE;MACtB,IAAI;QACFA,aAAa,GAAG,MAAM5F,gBAAgB,CAAC6F,aAAa,CAAC,GAAGxD,IAAI,CAACqD,IAAI,IAAI1C,MAAM,CAACO,IAAI,aAAa,EAAE,CAAC,CAAC;QACjGF,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEsC,aAAa,CAACvB,MAAM,EAAE,SAAS,CAAC;MAC3F,CAAC,CAAC,OAAOyB,QAAQ,EAAE;QACjBzC,OAAO,CAAC0C,IAAI,CAAC,0DAA0D,EAAED,QAAQ,CAAC;QAClFF,aAAa,GAAG,EAAE,CAAC,CAAC;MACtB;;MAEA;MACA,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,+CAA+C,EAAE;QAC5EC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,eAAe,EAAE,kFAAkF;UACnG,cAAc,EAAE,kBAAkB;UAClC,cAAc,EAAEhE,MAAM,CAACiE,QAAQ,CAACC,MAAM;UACtC,SAAS,EAAE;QACb,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,KAAK,EAAE,gCAAgC;UACvCC,QAAQ,EAAE,CACR;YACEC,IAAI,EAAE,QAAQ;YACd/C,OAAO,EAAE4B,WAAW,KAAK,IAAI,GACzB,gHAAgH,GAChH;UACN,CAAC,EACD;YACEmB,IAAI,EAAE,MAAM;YACZ/C,OAAO,EAAE6B,MAAM,IAAIG,aAAa,CAACvB,MAAM,GAAG,CAAC,GAAG,gCAAgCuB,aAAa,CAACgB,GAAG,CAACC,CAAC,IAAI,KAAKA,CAAC,CAACnD,KAAK,KAAKmD,CAAC,CAAChE,WAAW,IAAIgE,CAAC,CAACC,OAAO,IAAI,gBAAgB,EAAE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE;UAC3L,CAAC,CACF;UACDC,WAAW,EAAE,GAAG;UAChBC,UAAU,EAAE;QACd,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACjB,QAAQ,CAACkB,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBnB,QAAQ,CAACtB,MAAM,EAAE,CAAC;MAC3D;MAEA,MAAM0C,IAAI,GAAG,MAAMpB,QAAQ,CAACqB,IAAI,CAAC,CAAC;MAClC,MAAMC,YAAY,GAAG,EAAAhC,cAAA,GAAA8B,IAAI,CAACG,OAAO,CAAC,CAAC,CAAC,cAAAjC,cAAA,wBAAAC,qBAAA,GAAfD,cAAA,CAAiBkC,OAAO,cAAAjC,qBAAA,uBAAxBA,qBAAA,CAA0B3B,OAAO,KAAI,EAAE;;MAE5D;MACA,MAAM6D,SAAS,GAAGH,YAAY,CAACI,KAAK,CAAC,aAAa,CAAC;MACnD,IAAI,CAACD,SAAS,EAAE;QACd,MAAM,IAAIN,KAAK,CAAC,iCAAiC,CAAC;MACpD;MAEA,MAAMQ,YAAY,GAAGpB,IAAI,CAACqB,KAAK,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC;MAC7C,MAAMI,WAAW,GAAGF,YAAY,CAAC3C,MAAM,IAAI2C,YAAY,CAACG,QAAQ,IAAI,EAAE;MAEtE,IAAID,WAAW,CAACxD,MAAM,KAAK,CAAC,EAAE;QAC5B,MAAM,IAAI8C,KAAK,CAAC,2BAA2B,CAAC;MAC9C;MAEA9D,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEuE,WAAW,CAACxD,MAAM,EAAE,sBAAsB,EAAErB,MAAM,CAACO,IAAI,CAAC;;MAEnF;MACA,MAAMwE,OAAO,GAAG;QAAE,GAAG1F;MAAK,CAAC;MAC3B0F,OAAO,CAAC/C,MAAM,GAAG,CACf,GAAG+C,OAAO,CAAC/C,MAAM,CAACgD,KAAK,CAAC,CAAC,EAAE3C,WAAW,GAAG,CAAC,CAAC,EAC3C,GAAGwC,WAAW,CAACjB,GAAG,CAACqB,SAAS,KAAK;QAC/B,GAAGA,SAAS;QACZC,WAAW,EAAE,IAAI;QACjBC,YAAY,EAAEnF,MAAM,CAACO,IAAI;QACzB6E,KAAK,EAAE,CAACpF,MAAM,CAACoF,KAAK,IAAI,CAAC,IAAI,CAAC;QAC9BjF,EAAE,EAAE,GAAGH,MAAM,CAACO,IAAI,IAAI0E,SAAS,CAAC1E,IAAI,EAAE,CAAC8E,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC;MAC1E,CAAC,CAAC,CAAC,EACH,GAAGP,OAAO,CAAC/C,MAAM,CAACgD,KAAK,CAAC3C,WAAW,GAAG,CAAC,CAAC,CACzC;;MAED;MACAtF,UAAU,CAACmD,eAAe,CAACnC,SAAS,CAACoC,EAAE,EAAE,WAAW,EAAE;QAAEd,IAAI,EAAE0F;MAAQ,CAAC,CAAC;MACxE/G,YAAY,CAACjB,UAAU,CAACqD,MAAM,CAACrC,SAAS,CAACoC,EAAE,CAAC,CAAC;MAE7CE,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;;MAEjE;MACA,MAAMY,MAAM,GAAGzE,mBAAmB,CAAC0E,WAAW,CAAC,iBAAiB,CAAC;MACjE,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5C9E,mBAAmB,CAAC+E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IAEF,CAAC,CAAC,OAAOpD,KAAK,EAAE;MACdkC,OAAO,CAAClC,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzDC,QAAQ,CAACoB,CAAC,CAAC,gBAAgB,CAAC,IAAI,2DAA2D,CAAC;IAC9F,CAAC,SAAS;MACRtB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACmB,IAAI,EAAEtB,SAAS,EAAEyB,CAAC,CAAC,CAAC;EAExB,MAAM+F,eAAe,GAAGtJ,KAAK,CAAC8D,WAAW,CAAC,OAAO6B,KAAK,EAAEC,UAAU,KAAK;IACrE;IACA,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,IAAI1C,IAAI,EAAE;MAC5D,MAAMW,MAAM,GAAGX,IAAI,CAAC2C,MAAM,CAACH,UAAU,CAACE,UAAU,CAACE,KAAK,CAAC;MACvD,IAAIjC,MAAM,EAAE;QACV,MAAMoC,YAAY,CAACpC,MAAM,EAAE6B,UAAU,CAACE,UAAU,CAACE,KAAK,CAAC;MACzD;IACF;EACF,CAAC,EAAE,CAAC5C,IAAI,EAAE+C,YAAY,CAAC,CAAC;;EAExB;EACA,MAAMoD,UAAU,GAAGvJ,KAAK,CAAC8D,WAAW,CAAE0F,MAAM,IAAK;IAC/ChH,kBAAkB,CAACiH,IAAI,KAAK;MAC1B,GAAGA,IAAI;MACP9G,KAAK,EAAE+G,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEH,IAAI,CAAC9G,KAAK,GAAG6G,MAAM,CAAC;IACvD,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,eAAe,GAAG7J,KAAK,CAAC8D,WAAW,CAAC,MAAM;IAC9CtB,kBAAkB,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAE,CAAC,CAAC;EAC9C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM,CAACmH,UAAU,EAAEC,aAAa,CAAC,GAAG/J,KAAK,CAACC,QAAQ,CAAC;IAAEwC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAElE,MAAMsH,eAAe,GAAGhK,KAAK,CAAC8D,WAAW,CAAEmG,CAAC,IAAK;IAC/C;IACA,IAAIA,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,mBAAmB,CAAC,IAAIF,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,qBAAqB,CAAC,EAAE;MACpF;IACF;IAEAtH,aAAa,CAAC,IAAI,CAAC;IACnBE,YAAY,CAAC;MAAEN,CAAC,EAAEwH,CAAC,CAACG,OAAO;MAAE1H,CAAC,EAAEuH,CAAC,CAACI;IAAQ,CAAC,CAAC;IAC5CN,aAAa,CAAC;MAAEtH,CAAC,EAAEF,eAAe,CAACE,CAAC;MAAEC,CAAC,EAAEH,eAAe,CAACG;IAAE,CAAC,CAAC;IAC7DuH,CAAC,CAACK,cAAc,CAAC,CAAC;EACpB,CAAC,EAAE,CAAC/H,eAAe,EAAEM,aAAa,EAAEE,YAAY,CAAC,CAAC;EAElD,MAAMwH,eAAe,GAAGvK,KAAK,CAAC8D,WAAW,CAAEmG,CAAC,IAAK;IAC/C,IAAI,CAACrH,UAAU,EAAE;IAEjB,MAAM4H,MAAM,GAAGP,CAAC,CAACG,OAAO,GAAGtH,SAAS,CAACL,CAAC;IACtC,MAAMgI,MAAM,GAAGR,CAAC,CAACI,OAAO,GAAGvH,SAAS,CAACJ,CAAC;IAEtCF,kBAAkB,CAACiH,IAAI,KAAK;MAC1B,GAAGA,IAAI;MACPhH,CAAC,EAAEqH,UAAU,CAACrH,CAAC,GAAG+H,MAAM;MACxB9H,CAAC,EAAEoH,UAAU,CAACpH,CAAC,GAAG+H;IACpB,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAAC7H,UAAU,EAAEE,SAAS,EAAEgH,UAAU,CAAC,CAAC;EAEvC,MAAMY,aAAa,GAAG1K,KAAK,CAAC8D,WAAW,CAAC,MAAM;IAC5CjB,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM8H,gBAAgB,GAAG3K,KAAK,CAAC8D,WAAW,CAAEmG,CAAC,IAAK;IAChD,IAAIA,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,mBAAmB,CAAC,IAAIF,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,qBAAqB,CAAC,EAAE;MACpF;IACF;IAEA,MAAMS,KAAK,GAAGX,CAAC,CAACY,OAAO,CAAC,CAAC,CAAC;IAC1BhI,aAAa,CAAC,IAAI,CAAC;IACnBE,YAAY,CAAC;MAAEN,CAAC,EAAEmI,KAAK,CAACR,OAAO;MAAE1H,CAAC,EAAEkI,KAAK,CAACP;IAAQ,CAAC,CAAC;IACpDN,aAAa,CAAC;MAAEtH,CAAC,EAAEF,eAAe,CAACE,CAAC;MAAEC,CAAC,EAAEH,eAAe,CAACG;IAAE,CAAC,CAAC;IAC7DuH,CAAC,CAACK,cAAc,CAAC,CAAC;EACpB,CAAC,EAAE,CAAC/H,eAAe,EAAEM,aAAa,EAAEE,YAAY,CAAC,CAAC;EAElD,MAAM+H,eAAe,GAAG9K,KAAK,CAAC8D,WAAW,CAAEmG,CAAC,IAAK;IAC/C,IAAI,CAACrH,UAAU,EAAE;IAEjB,MAAMgI,KAAK,GAAGX,CAAC,CAACY,OAAO,CAAC,CAAC,CAAC;IAC1B,MAAML,MAAM,GAAGI,KAAK,CAACR,OAAO,GAAGtH,SAAS,CAACL,CAAC;IAC1C,MAAMgI,MAAM,GAAGG,KAAK,CAACP,OAAO,GAAGvH,SAAS,CAACJ,CAAC;IAE1CF,kBAAkB,CAACiH,IAAI,KAAK;MAC1B,GAAGA,IAAI;MACPhH,CAAC,EAAEqH,UAAU,CAACrH,CAAC,GAAG+H,MAAM;MACxB9H,CAAC,EAAEoH,UAAU,CAACpH,CAAC,GAAG+H;IACpB,CAAC,CAAC,CAAC;IACHR,CAAC,CAACK,cAAc,CAAC,CAAC;EACpB,CAAC,EAAE,CAAC1H,UAAU,EAAEE,SAAS,EAAEgH,UAAU,CAAC,CAAC;EAEvC,MAAMiB,cAAc,GAAG/K,KAAK,CAAC8D,WAAW,CAAC,MAAM;IAC7CjB,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMmI,qBAAqB,GAAGhL,KAAK,CAAC8D,WAAW,CAAEmG,CAAC,IAAK;IACrD,IAAIA,CAAC,CAACgB,MAAM,KAAK,CAAC,EAAE;MAAE;MACpBpI,aAAa,CAAC,IAAI,CAAC;MACnBE,YAAY,CAAC;QAAEN,CAAC,EAAEwH,CAAC,CAACG,OAAO,GAAG7H,eAAe,CAACE,CAAC;QAAEC,CAAC,EAAEuH,CAAC,CAACI,OAAO,GAAG9H,eAAe,CAACG;MAAE,CAAC,CAAC;IACtF;EACF,CAAC,EAAE,CAACH,eAAe,CAAC,CAAC;EAErB,MAAM2I,qBAAqB,GAAGlL,KAAK,CAAC8D,WAAW,CAAEmG,CAAC,IAAK;IACrD,IAAIrH,UAAU,EAAE;MACdJ,kBAAkB,CAACiH,IAAI,KAAK;QAC1B,GAAGA,IAAI;QACPhH,CAAC,EAAEwH,CAAC,CAACG,OAAO,GAAGtH,SAAS,CAACL,CAAC;QAC1BC,CAAC,EAAEuH,CAAC,CAACI,OAAO,GAAGvH,SAAS,CAACJ;MAC3B,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACE,UAAU,EAAEE,SAAS,CAAC,CAAC;EAE3B,MAAMqI,mBAAmB,GAAGnL,KAAK,CAAC8D,WAAW,CAAC,MAAM;IAClDjB,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMuI,iBAAiB,GAAGpL,KAAK,CAAC8D,WAAW,CAAEmG,CAAC,IAAK;IACjDA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClB,MAAMd,MAAM,GAAGS,CAAC,CAACQ,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;IACvClB,UAAU,CAACC,MAAM,CAAC;EACpB,CAAC,EAAE,CAACD,UAAU,CAAC,CAAC;;EAEhB;EACArJ,SAAS,CAAC,MAAM;IACd,MAAMmL,YAAY,GAAGA,CAAA,KAAM;MACzBpI,WAAW,CAACC,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;IACvC,CAAC;IAEDD,MAAM,CAACoI,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAMnI,MAAM,CAACqI,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnL,SAAS,CAAC,MAAM;IACd,MAAMsL,eAAe,GAAGC,QAAQ,CAACC,cAAc,CAAC,eAAe,CAAC;IAChE,IAAIF,eAAe,IAAIxI,QAAQ,EAAE;MAC/B;MACAwI,eAAe,CAACG,KAAK,CAACC,cAAc,GAAG,QAAQ;;MAE/C;MACA,IAAIC,WAAW,GAAG,KAAK;MACvB,MAAMC,YAAY,GAAGA,CAAA,KAAM;QACzB,IAAI,CAACD,WAAW,EAAE;UAChBA,WAAW,GAAG,IAAI;UAClBE,UAAU,CAAC,MAAM;YACfF,WAAW,GAAG,KAAK;UACrB,CAAC,EAAE,GAAG,CAAC;QACT;MACF,CAAC;MAEDL,eAAe,CAACF,gBAAgB,CAAC,QAAQ,EAAEQ,YAAY,CAAC;MAExD,OAAO,MAAM;QACXN,eAAe,CAACD,mBAAmB,CAAC,QAAQ,EAAEO,YAAY,CAAC;MAC7D,CAAC;IACH;EACF,CAAC,EAAE,CAAC9I,QAAQ,CAAC,CAAC;;EAEd;EACA9C,SAAS,CAAC,MAAM;IACd,MAAM8L,MAAM,GAAGP,QAAQ,CAACC,cAAc,CAAC,iBAAiB,CAAC;IACzD,IAAIM,MAAM,IAAI,CAAChJ,QAAQ,EAAE;MACvB;MACAgJ,MAAM,CAACL,KAAK,CAACM,SAAS,GAAG,aAAa1J,eAAe,CAACE,CAAC,OAAOF,eAAe,CAACG,CAAC,aAAaH,eAAe,CAACI,KAAK,GAAG;MAEpHqJ,MAAM,CAACV,gBAAgB,CAAC,WAAW,EAAEN,qBAAqB,CAAC;MAC3DgB,MAAM,CAACV,gBAAgB,CAAC,OAAO,EAAEF,iBAAiB,CAAC;;MAEnD;MACA,MAAMc,qBAAqB,GAAIjC,CAAC,IAAK;QACnC,IAAIrH,UAAU,EAAE;UACdsI,qBAAqB,CAACjB,CAAC,CAAC;QAC1B;MACF,CAAC;MAED,MAAMkC,mBAAmB,GAAGA,CAAA,KAAM;QAChC,IAAIvJ,UAAU,EAAE;UACduI,mBAAmB,CAAC,CAAC;QACvB;MACF,CAAC;MAEDM,QAAQ,CAACH,gBAAgB,CAAC,WAAW,EAAEY,qBAAqB,CAAC;MAC7DT,QAAQ,CAACH,gBAAgB,CAAC,SAAS,EAAEa,mBAAmB,CAAC;MAEzD,OAAO,MAAM;QACXH,MAAM,CAACT,mBAAmB,CAAC,WAAW,EAAEP,qBAAqB,CAAC;QAC9DgB,MAAM,CAACT,mBAAmB,CAAC,OAAO,EAAEH,iBAAiB,CAAC;QACtDK,QAAQ,CAACF,mBAAmB,CAAC,WAAW,EAAEW,qBAAqB,CAAC;QAChET,QAAQ,CAACF,mBAAmB,CAAC,SAAS,EAAEY,mBAAmB,CAAC;MAC9D,CAAC;IACH;EACF,CAAC,EAAE,CAAC5J,eAAe,EAAES,QAAQ,EAAEJ,UAAU,EAAEoI,qBAAqB,EAAEE,qBAAqB,EAAEC,mBAAmB,EAAEC,iBAAiB,CAAC,CAAC;;EAEjI;EACAlL,SAAS,CAAC,MAAM;IACd,MAAMkM,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAC/C,MAAMC,cAAc,GAAGF,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAE7D,IAAIF,UAAU,IAAIG,cAAc,EAAE;MAChC,MAAMC,QAAQ,GAAG;QACftI,EAAE,EAAE,QAAQ;QACZP,IAAI,EAAE,MAAM;QACZ8I,gBAAgB,EAAE;MACpB,CAAC;MACDpK,OAAO,CAACmK,QAAQ,CAAC;;MAEjB;MACA,MAAMvH,MAAM,GAAGzE,mBAAmB,CAAC0E,WAAW,CAAC,aAAa,CAAC;MAC7D,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5C9E,mBAAmB,CAAC+E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;;IAEA;IACA,IAAIhD,MAAM,CAACoK,OAAO,EAAE;MAClBtM,cAAc,CAACuM,IAAI,CAACrK,MAAM,CAACoK,OAAO,EAAE;QAClCE,SAAS,EAAElH,eAAe;QAC1BmH,SAAS,EAAE3G,eAAe;QAC1B4G,SAAS,EAAExD;MACb,CAAC,CAAC;IACJ;;IAEA;IACA,MAAM4C,qBAAqB,GAAIjC,CAAC,IAAKM,eAAe,CAACN,CAAC,CAAC;IACvD,MAAMkC,mBAAmB,GAAIlC,CAAC,IAAKS,aAAa,CAACT,CAAC,CAAC;IACnD,MAAM8C,qBAAqB,GAAI9C,CAAC,IAAKa,eAAe,CAACb,CAAC,CAAC;IACvD,MAAM+C,oBAAoB,GAAI/C,CAAC,IAAKc,cAAc,CAACd,CAAC,CAAC;IAErDwB,QAAQ,CAACH,gBAAgB,CAAC,WAAW,EAAEY,qBAAqB,CAAC;IAC7DT,QAAQ,CAACH,gBAAgB,CAAC,SAAS,EAAEa,mBAAmB,CAAC;IACzDV,QAAQ,CAACH,gBAAgB,CAAC,WAAW,EAAEyB,qBAAqB,EAAE;MAAEE,OAAO,EAAE;IAAM,CAAC,CAAC;IACjFxB,QAAQ,CAACH,gBAAgB,CAAC,UAAU,EAAE0B,oBAAoB,CAAC;IAE3D,OAAO,MAAM;MACX5M,cAAc,CAAC8M,OAAO,CAAC,CAAC;MACxBzB,QAAQ,CAACF,mBAAmB,CAAC,WAAW,EAAEW,qBAAqB,CAAC;MAChET,QAAQ,CAACF,mBAAmB,CAAC,SAAS,EAAEY,mBAAmB,CAAC;MAC5DV,QAAQ,CAACF,mBAAmB,CAAC,WAAW,EAAEwB,qBAAqB,CAAC;MAChEtB,QAAQ,CAACF,mBAAmB,CAAC,UAAU,EAAEyB,oBAAoB,CAAC;IAChE,CAAC;EACH,CAAC,EAAE,CAACtH,eAAe,EAAEQ,eAAe,EAAEoD,eAAe,EAAEiB,eAAe,EAAEG,aAAa,EAAEI,eAAe,EAAEC,cAAc,CAAC,CAAC;;EAExH;EACA7K,SAAS,CAAC,MAAM;IACd,IAAIkC,IAAI,EAAE;MACR,MAAM+K,SAAS,GAAG1B,QAAQ,CAACC,cAAc,CAAC,wBAAwB,CAAC;MACnE,IAAIyB,SAAS,EAAE;QACb;QACAA,SAAS,CAACC,SAAS,GAAG,EAAE;QACxB;QACA5M,mBAAmB,CAAC6M,oBAAoB,CAACF,SAAS,CAAC;MACrD;;MAEA;MACAtM,cAAc,CAAC,CAAC,CAACyM,IAAI,CAACC,WAAW,IAAI;QACnCnJ,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEkJ,WAAW,GAAG,aAAa,GAAG,UAAU,CAAC;QAClF,IAAI,CAACA,WAAW,EAAE;UAChBnJ,OAAO,CAAC0C,IAAI,CAAC,uEAAuE,CAAC;QACvF;MACF,CAAC,CAAC,CAAC0G,KAAK,CAACtL,KAAK,IAAI;QAChBkC,OAAO,CAAClC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACE,IAAI,CAAC,CAAC;;EAEV;EACA,MAAM3B,qBAAqB,GAAG,MAAAA,CAAOgN,UAAU,EAAEC,KAAK,GAAG,IAAI,KAAK;IAChE,IAAIC,YAAY,GAAGD,KAAK;;IAExB;IACA,IAAI,CAACC,YAAY,EAAE;MACjB,IAAI;QACF,MAAMC,MAAM,GAAG9M,UAAU,CAAC+M,SAAS,CAACJ,UAAU,CAAC;QAC/CE,YAAY,GAAGC,MAAM,CAAC1J,EAAE;QACxBnC,YAAY,CAAC6L,MAAM,CAAC;QACpBjM,cAAc,CAAC,MAAM,CAAC;MACxB,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,QAAQ,CAACD,KAAK,CAACqG,OAAO,CAAC;QACvB;MACF;IACF;;IAEA;IACAzH,UAAU,CAACmD,eAAe,CAAC0J,YAAY,EAAE,YAAY,EAAE;MAAEG,QAAQ,EAAE;IAAG,CAAC,CAAC;IACxE7L,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACFiC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEoJ,UAAU,EAAE,SAAS,EAAEE,YAAY,CAAC;;MAErF;MACA7M,UAAU,CAACmD,eAAe,CAAC0J,YAAY,EAAE,YAAY,EAAE;QAAEG,QAAQ,EAAE;MAAG,CAAC,CAAC;MAExE,MAAMC,QAAQ,GAAG,MAAMrN,eAAe,CAAC+M,UAAU,EAAEtM,kBAAkB,CAAC,CAAC,CAAC;MACxEiD,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE0J,QAAQ,CAAC;;MAE/C;MACAjN,UAAU,CAACmD,eAAe,CAAC0J,YAAY,EAAE,WAAW,EAAE;QACpDvK,IAAI,EAAE2K,QAAQ;QACdD,QAAQ,EAAE;MACZ,CAAC,CAAC;;MAEF;MACA,IAAIH,YAAY,MAAK7L,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEoC,EAAE,GAAE;QAClCnC,YAAY,CAACjB,UAAU,CAACqD,MAAM,CAACwJ,YAAY,CAAC,CAAC;MAC/C;;MAEA;MACA,MAAM1I,MAAM,GAAGzE,mBAAmB,CAAC0E,WAAW,CAAC,gBAAgB,CAAC;MAChE,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5C9E,mBAAmB,CAAC+E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAO0I,GAAG,EAAE;MACZ5J,OAAO,CAAClC,KAAK,CAAC,0BAA0B,EAAE8L,GAAG,CAAC;MAC9ClN,UAAU,CAACmD,eAAe,CAAC0J,YAAY,EAAE,OAAO,CAAC;MACjDxL,QAAQ,CAAC,sCAAsC6L,GAAG,CAACzF,OAAO,qBAAqB,CAAC;IAClF,CAAC,SAAS;MACRtG,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAID;EACA,MAAMgM,YAAY,GAAIhE,CAAC,IAAK;IAC1BA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClB,IAAI1I,KAAK,CAACsM,IAAI,CAAC,CAAC,EAAE;MAChBzN,qBAAqB,CAACmB,KAAK,CAACsM,IAAI,CAAC,CAAC,CAAC;MACnCrM,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB;EACF,CAAC;;EAED;EACA,MAAMsM,eAAe,GAAIC,GAAG,IAAK;IAC/B;IACAjM,QAAQ,CAAC,IAAI,CAAC;IACdF,YAAY,CAAC,KAAK,CAAC;IAEnBF,YAAY,CAACqM,GAAG,CAAC;IACjB,IAAIA,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAEhL,IAAI,EAAE;MACbzB,cAAc,CAAC,MAAM,CAAC;IACxB,CAAC,MAAM;MACLA,cAAc,CAAC,OAAO,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAM0M,YAAY,GAAGA,CAAA,KAAM;IACzB;IACAlM,QAAQ,CAAC,IAAI,CAAC;IACdF,YAAY,CAAC,KAAK,CAAC;IAEnBN,cAAc,CAAC,OAAO,CAAC;IACvBI,YAAY,CAAC,IAAI,CAAC;IAClBF,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAMyM,sBAAsB,GAAIF,GAAG,IAAK;IACtC;IACAjM,QAAQ,CAAC,IAAI,CAAC;IACdF,YAAY,CAAC,KAAK,CAAC;IAEnBF,YAAY,CAACqM,GAAG,CAAC;IACjBzM,cAAc,CAAC,SAAS,CAAC;EAC3B,CAAC;;EAID;EACA,MAAM4M,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI,EAACjL,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEqB,OAAO,GAAE;IAEvB,IAAIrE,aAAa,CAACkO,SAAS,CAAC,CAAC,CAACC,SAAS,EAAE;MACvCnO,aAAa,CAACoO,MAAM,CAAC,CAAC;IACxB,CAAC,MAAM;MACLpO,aAAa,CAACqO,KAAK,CAACrL,OAAO,CAACqB,OAAO,CAAC;MACpC;MACA,MAAMM,MAAM,GAAGzE,mBAAmB,CAAC0E,WAAW,CAAC,aAAa,CAAC;MAC7D,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5C9E,mBAAmB,CAAC+E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAMsJ,gBAAgB,GAAGA,CAAA,KAAM;IAC7BtO,aAAa,CAACuO,IAAI,CAAC,CAAC;EACtB,CAAC;EAED,MAAMC,sBAAsB,GAAIC,IAAI,IAAK;IACvCzO,aAAa,CAAC0O,OAAO,CAACD,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,EAAC3L,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEmB,KAAK,KAAI,EAACnB,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEqB,OAAO,GAAE;IAC1C,MAAMM,MAAM,GAAG1E,aAAa,CAAC2O,WAAW,CAAC5L,OAAO,EAAE,GAAGA,OAAO,CAACmB,KAAK,CAAC2E,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,EAAE,CAAC;IACnG,IAAInE,MAAM,CAACkK,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAG5O,mBAAmB,CAAC0E,WAAW,CAAC,aAAa,CAAC;MAChE,IAAIkK,SAAS,CAACjK,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxCgK,SAAS,CAACjK,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/C9E,mBAAmB,CAAC+E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAM+J,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,EAAC/L,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEmB,KAAK,KAAI,EAACnB,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEqB,OAAO,GAAE;IAC1C,MAAMM,MAAM,GAAG1E,aAAa,CAAC+O,YAAY,CAAChM,OAAO,EAAE,GAAGA,OAAO,CAACmB,KAAK,CAAC2E,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,EAAE,CAAC;IACpG,IAAInE,MAAM,CAACkK,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAG5O,mBAAmB,CAAC0E,WAAW,CAAC,aAAa,CAAC;MAChE,IAAIkK,SAAS,CAACjK,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxCgK,SAAS,CAACjK,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/C9E,mBAAmB,CAAC+E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAMiK,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI,EAACjM,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEqB,OAAO,GAAE;IACvB,MAAMM,MAAM,GAAG,MAAM1E,aAAa,CAACiP,eAAe,CAAClM,OAAO,CAACqB,OAAO,CAAC;IACnEpE,aAAa,CAACkP,WAAW,CAACxK,MAAM,CAACsD,OAAO,EAAEtD,MAAM,CAACkK,OAAO,GAAG,SAAS,GAAG,OAAO,CAAC;IAC/E,IAAIlK,MAAM,CAACkK,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAG5O,mBAAmB,CAAC0E,WAAW,CAAC,aAAa,CAAC;MAChE,IAAIkK,SAAS,CAACjK,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxCgK,SAAS,CAACjK,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/C9E,mBAAmB,CAAC+E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;;EAID;EACA,MAAMoK,MAAM,GAAGA,CAAA,KAAM;IACnB,IAAIhO,WAAW,KAAK,SAAS,EAAE;MAC7BC,cAAc,CAAC,MAAM,CAAC;MACtB,IAAIG,SAAS,EAAE;QACbhB,UAAU,CAACmD,eAAe,CAACnC,SAAS,CAACoC,EAAE,EAAEpC,SAAS,CAAC2D,MAAM,EAAE;UAAEnC,OAAO,EAAE;QAAK,CAAC,CAAC;QAC7EvB,YAAY,CAACjB,UAAU,CAACqD,MAAM,CAACrC,SAAS,CAACoC,EAAE,CAAC,CAAC;MAC/C;IACF,CAAC,MAAM,IAAIxC,WAAW,KAAK,MAAM,EAAE;MACjCC,cAAc,CAAC,OAAO,CAAC;IACzB;EACF,CAAC;EAID,MAAMgO,MAAM,GAAGA,CAAA,KAAM;IACnBhO,cAAc,CAAC,OAAO,CAAC;IACvBI,YAAY,CAAC,IAAI,CAAC;IAClBF,QAAQ,CAAC,EAAE,CAAC;IACZ;IACAf,UAAU,CAAC8O,YAAY,CAAC,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBxD,YAAY,CAACyD,OAAO,CAAC,gBAAgB,EAAE,MAAM,CAAC;IAC9CzN,OAAO,CAAC;MAAE6B,EAAE,EAAE,OAAO;MAAEP,IAAI,EAAE,WAAW;MAAE8I,gBAAgB,EAAE;IAAU,CAAC,CAAC;EAC1E,CAAC;EAED,oBACEpL,OAAA;IAAK0O,SAAS,EAAC,KAAK;IAACC,GAAG,EAAE1N,MAAO;IAAA2N,QAAA,gBAE/B5O,OAAA;MAAQ0O,SAAS,EAAC,YAAY;MAAAE,QAAA,eAC5B5O,OAAA;QAAK0O,SAAS,EAAC,gBAAgB;QAAAE,QAAA,gBAC7B5O,OAAA;UAAQ6O,OAAO,EAAEP,MAAO;UAACI,SAAS,EAAC,WAAW;UAAAE,QAAA,EAC3C1M,CAAC,CAAC,UAAU;QAAC;UAAA4M,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACTjP,OAAA;UAAK0O,SAAS,EAAC,cAAc;UAAAE,QAAA,GAC1B7N,IAAI,iBACHf,OAAA;YAAK6C,EAAE,EAAC,wBAAwB;YAACyH,KAAK,EAAE;cAAE4E,WAAW,EAAE;YAAO;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE3D,CACN,eACDjP,OAAA,CAACJ,gBAAgB;YAAAkP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACnB,CAAClO,IAAI,gBACJf,OAAA;YAAQ6O,OAAO,EAAEL,UAAW;YAACE,SAAS,EAAC,iBAAiB;YAACpE,KAAK,EAAE;cAAE6E,UAAU,EAAE;YAAO,CAAE;YAAAP,QAAA,EACpF1M,CAAC,CAAC,YAAY;UAAC;YAAA4M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,gBAETjP,OAAA;YAAMsK,KAAK,EAAE;cAAE6E,UAAU,EAAE;YAAO,CAAE;YAAAP,QAAA,GAAE1M,CAAC,CAAC,SAAS,CAAC,EAAC,IAAE,EAACnB,IAAI,CAACuB,IAAI,EAAC,GAAC;UAAA;YAAAwM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACxE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAGRlO,IAAI,iBACHf,OAAA,CAACL,UAAU;MACTyP,WAAW,EAAEtC,eAAgB;MAC7BuC,QAAQ,EAAErC,YAAa;MACvBsC,kBAAkB,EAAErC;IAAuB;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CACF,eAGDjP,OAAA;MAAM0O,SAAS,EAAC,cAAc;MAAAE,QAAA,GAC3B/N,KAAK,iBACJb,OAAA;QAAK0O,SAAS,EAAC,OAAO;QAAAE,QAAA,GAAC,eAClB,EAAC/N,KAAK,eACTb,OAAA;UAAQ6O,OAAO,EAAEA,CAAA,KAAM/N,QAAQ,CAAC,IAAI,CAAE;UAACwJ,KAAK,EAAE;YAAC6E,UAAU,EAAE,MAAM;YAAEI,UAAU,EAAE,MAAM;YAAEC,MAAM,EAAE,MAAM;YAAEC,KAAK,EAAE,OAAO;YAAEC,MAAM,EAAE;UAAS,CAAE;UAAAd,QAAA,EAAC;QAE3I;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAGA5O,WAAW,KAAK,OAAO,iBACtBL,OAAA;QAAK0O,SAAS,EAAC,kBAAkB;QAAAE,QAAA,gBAC/B5O,OAAA;UAAI0O,SAAS,EAAC,OAAO;UAAAE,QAAA,EAAE1M,CAAC,CAAC,UAAU;QAAC;UAAA4M,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1CjP,OAAA;UAAG0O,SAAS,EAAC,UAAU;UAAAE,QAAA,EAAC;QAExB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAEH,CAAClO,IAAI,gBACJf,OAAA;UAAKsK,KAAK,EAAE;YAACiF,UAAU,EAAE,SAAS;YAAEI,OAAO,EAAE,MAAM;YAAEC,YAAY,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAM,CAAE;UAAAjB,QAAA,gBACjG5O,OAAA;YAAGsK,KAAK,EAAE;cAACmF,KAAK,EAAE,SAAS;cAAEI,YAAY,EAAE;YAAM,CAAE;YAAAjB,QAAA,EAChD1M,CAAC,CAAC,eAAe;UAAC;YAAA4M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACJjP,OAAA;YAAQ6O,OAAO,EAAEL,UAAW;YAACE,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EACrD1M,CAAC,CAAC,eAAe;UAAC;YAAA4M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAENjP,OAAA;UAAM8P,QAAQ,EAAElD,YAAa;UAAAgC,QAAA,gBAC3B5O,OAAA;YAAK0O,SAAS,EAAC,YAAY;YAAAE,QAAA,eACzB5O,OAAA;cACE+P,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEzP,KAAM;cACb0P,QAAQ,EAAGrH,CAAC,IAAKpI,QAAQ,CAACoI,CAAC,CAACC,MAAM,CAACmH,KAAK,CAAE;cAC1CE,WAAW,EAAEhO,CAAC,CAAC,kBAAkB,CAAE;cACnCwM,SAAS,EAAC,YAAY;cACtByB,QAAQ,EAAExP;YAAU;cAAAmO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNjP,OAAA;YACE+P,IAAI,EAAC,QAAQ;YACbI,QAAQ,EAAExP,SAAS,IAAI,CAACJ,KAAK,CAACsM,IAAI,CAAC,CAAE;YACrC6B,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EAE1BjO,SAAS,gBACRX,OAAA,CAAAE,SAAA;cAAA0O,QAAA,gBACE5O,OAAA;gBAAM0O,SAAS,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAChC/M,CAAC,CAAC,YAAY,CAAC;YAAA,eAChB,CAAC,gBAEHlC,OAAA,CAAAE,SAAA;cAAA0O,QAAA,EACG1M,CAAC,CAAC,kBAAkB;YAAC,gBACtB;UACH;YAAA4M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGA5O,WAAW,KAAK,MAAM,IAAI0B,IAAI,iBAC7B/B,OAAA;QAAK0O,SAAS,EAAC,gBAAgB;QAAAE,QAAA,gBAE7B5O,OAAA;UACE0O,SAAS,EAAE,qBAAqBnN,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;UAC/D6O,WAAW,EAAEzH,eAAgB;UAC7B0H,YAAY,EAAE/G,gBAAiB;UAAAsF,QAAA,gBAE/B5O,OAAA;YACE0O,SAAS,EAAE,mBAAmBnN,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;YAC7DsB,EAAE,EAAC,iBAAiB;YACpByH,KAAK,EAAE;cACLM,SAAS,EAAE,aAAa1J,eAAe,CAACE,CAAC,OAAOF,eAAe,CAACG,CAAC,aAAaH,eAAe,CAACI,KAAK;YACrG,CAAE;YAAAsN,QAAA,gBAGF5O,OAAA;cAAK0O,SAAS,EAAC,oBAAoB;cAAAE,QAAA,eACjC5O,OAAA;gBAAK0O,SAAS,EAAC,oBAAoB;gBAAAE,QAAA,gBACjC5O,OAAA;kBAAA4O,QAAA,EAAK7M,IAAI,CAACqD;gBAAI;kBAAA0J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpBjP,OAAA;kBAAQ6O,OAAO,EAAER,MAAO;kBAACK,SAAS,EAAC,4BAA4B;kBAAAE,QAAA,EAC5D1M,CAAC,CAAC,YAAY;gBAAC;kBAAA4M,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGLlN,IAAI,CAAC2C,MAAM,CAAC4B,GAAG,CAAC,CAAC5D,MAAM,EAAEiC,KAAK,KAAK;cAClC,MAAM2L,aAAa,GAAGvO,IAAI,CAAC2C,MAAM,CAACX,MAAM;cACxC,MAAMwM,KAAK,GAAI5L,KAAK,GAAG,GAAG,GAAI2L,aAAa;cAC3C,MAAME,UAAU,GAAG,GAAG;cACtB,MAAMC,WAAW,GAAG,CAAC/N,MAAM,CAACoF,KAAK,IAAI,CAAC,IAAI,GAAG;cAC7C,MAAM4I,MAAM,GAAGF,UAAU,GAAGC,WAAW;;cAEvC;cACA,MAAME,WAAW,GAAItI,IAAI,CAACuI,GAAG,CAACjM,KAAK,GAAG,GAAG,CAAC,GAAG,EAAG;cAChD,MAAMkM,UAAU,GAAGN,KAAK,GAAGI,WAAW;cAEtC,MAAMvP,CAAC,GAAGiH,IAAI,CAACyI,GAAG,CAAED,UAAU,GAAGxI,IAAI,CAAC0I,EAAE,GAAI,GAAG,CAAC,GAAGL,MAAM;cACzD,MAAMrP,CAAC,GAAGgH,IAAI,CAACuI,GAAG,CAAEC,UAAU,GAAGxI,IAAI,CAAC0I,EAAE,GAAI,GAAG,CAAC,GAAGL,MAAM;cAEzD,oBACE1Q,OAAA;gBAEE0O,SAAS,EAAE,gCAAgC1M,cAAc,KAAKU,MAAM,GAAG,UAAU,GAAG,EAAE,EAAG;gBACzF4H,KAAK,EAAE;kBACLM,SAAS,EAAE,aAAaxJ,CAAC,OAAOC,CAAC,KAAK;kBACtC,gBAAgB,EAAE,GAAGwP,UAAU;gBACjC,CAAE;gBACF,cAAYlM,KAAM;gBAClB,cAAYjC,MAAM,CAACoF,KAAK,IAAI,CAAE;gBAC9B,aAAWpF,MAAM,CAACO,IAAK;gBACvB,oBAAkBP,MAAM,CAAC2C,SAAU;gBACnCwJ,OAAO,EAAEA,CAAA,KAAM1K,kBAAkB,CAACzB,MAAM,CAAE;gBAAAkM,QAAA,gBAG1C5O,OAAA;kBAAK0O,SAAS,EAAC,wBAAwB;kBAACpE,KAAK,EAAE;oBAC7CM,SAAS,EAAE,UAAUiG,UAAU,GAAG,GAAG,MAAM;oBAC3CG,KAAK,EAAE,GAAGN,MAAM;kBAClB;gBAAE;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAETjP,OAAA;kBAAK0O,SAAS,EAAC,gBAAgB;kBAAAE,QAAA,gBAC7B5O,OAAA;oBAAK0O,SAAS,EAAC,cAAc;oBAAAE,QAAA,EAAElM,MAAM,CAACuO;kBAAK;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClDjP,OAAA;oBAAI0O,SAAS,EAAC,aAAa;oBAAAE,QAAA,EAAElM,MAAM,CAACO;kBAAI;oBAAA6L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9CjP,OAAA;oBAAG0O,SAAS,EAAC,oBAAoB;oBAAAE,QAAA,EAAElM,MAAM,CAAC2C;kBAAS;oBAAAyJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAEvDvM,MAAM,CAACwO,YAAY,iBAClBlR,OAAA;oBAAK0O,SAAS,EAAC,sBAAsB;oBAAAE,QAAA,EAClClM,MAAM,CAACwO,YAAY,CAACxJ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACpB,GAAG,CAAC,CAAC6K,GAAG,EAAEC,QAAQ,kBACjDpR,OAAA;sBAAqB0O,SAAS,EAAC,iBAAiB;sBAAAE,QAAA,EAC7CuC;oBAAG,GADKC,QAAQ;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEb,CACP;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA,GAhCDtK,KAAK;gBAAAmK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiCP,CAAC;YAEV,CAAC,CAAC,EAEDtO,SAAS,iBACRX,OAAA;cAAK0O,SAAS,EAAC,iBAAiB;cAAAE,QAAA,gBAC9B5O,OAAA;gBAAM0O,SAAS,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjCjP,OAAA;gBAAA4O,QAAA,EAAO1M,CAAC,CAAC,SAAS;cAAC;gBAAA4M,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNjP,OAAA;YAAK0O,SAAS,EAAC,iBAAiB;YAAAE,QAAA,gBAC9B5O,OAAA;cAAQ0O,SAAS,EAAC,aAAa;cAACG,OAAO,EAAEA,CAAA,KAAM3G,UAAU,CAAC,GAAG,CAAE;cAAA0G,QAAA,EAAC;YAAG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5EjP,OAAA;cAAQ0O,SAAS,EAAC,aAAa;cAACG,OAAO,EAAEA,CAAA,KAAM3G,UAAU,CAAC,GAAG,CAAE;cAAA0G,QAAA,EAAC;YAAG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5EjP,OAAA;cAAQ0O,SAAS,EAAC,aAAa;cAACG,OAAO,EAAEA,CAAA,KAAMrG,eAAe,CAAC,CAAE;cAAAoG,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNjP,OAAA;UAAK0O,SAAS,EAAC,kBAAkB;UAAAE,QAAA,eAC/B5O,OAAA;YAAK0O,SAAS,EAAC,kBAAkB;YAAAE,QAAA,gBAC/B5O,OAAA;cAAK0O,SAAS,EAAC,eAAe;cAAAE,QAAA,gBAC5B5O,OAAA;gBAAA4O,QAAA,EAAK7M,IAAI,CAACqD;cAAI;gBAAA0J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpBjP,OAAA;gBAAQ6O,OAAO,EAAER,MAAO;gBAACK,SAAS,EAAC,mBAAmB;gBAAAE,QAAA,EACnD1M,CAAC,CAAC,YAAY;cAAC;gBAAA4M,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENjP,OAAA;cAAK0O,SAAS,EAAC,yBAAyB;cAAC7L,EAAE,EAAC,eAAe;cAAA+L,QAAA,EACxD7M,IAAI,CAAC2C,MAAM,CAAC4B,GAAG,CAAC,CAAC5D,MAAM,EAAEiC,KAAK,kBAC7B3E,OAAA;gBAEE0O,SAAS,EAAE,kCAAkC1M,cAAc,KAAKU,MAAM,GAAG,UAAU,GAAG,EAAE,EAAG;gBAC3F,cAAYiC,KAAM;gBAClB,aAAWjC,MAAM,CAACO,IAAK;gBACvB,oBAAkBP,MAAM,CAAC2C,SAAU;gBACnCwJ,OAAO,EAAEA,CAAA,KAAM1K,kBAAkB,CAACzB,MAAM,CAAE;gBAAAkM,QAAA,gBAE1C5O,OAAA;kBAAK0O,SAAS,EAAC,qBAAqB;kBAAAE,QAAA,gBAClC5O,OAAA;oBAAK0O,SAAS,EAAC,oBAAoB;oBAAAE,QAAA,EAAElM,MAAM,CAACuO;kBAAK;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACxDjP,OAAA;oBAAI0O,SAAS,EAAC,mBAAmB;oBAAAE,QAAA,EAAElM,MAAM,CAACO;kBAAI;oBAAA6L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpDjP,OAAA;oBAAG0O,SAAS,EAAC,0BAA0B;oBAAAE,QAAA,EAAElM,MAAM,CAAC2C;kBAAS;oBAAAyJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAE7DvM,MAAM,CAACwO,YAAY,iBAClBlR,OAAA;oBAAK0O,SAAS,EAAC,sBAAsB;oBAAAE,QAAA,GAClClM,MAAM,CAACwO,YAAY,CAACxJ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACpB,GAAG,CAAC,CAAC6K,GAAG,EAAEC,QAAQ,kBACjDpR,OAAA;sBAAqB0O,SAAS,EAAC,wBAAwB;sBAAAE,QAAA,EACpDuC;oBAAG,GADKC,QAAQ;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEb,CACP,CAAC,EACDvM,MAAM,CAACwO,YAAY,CAACnN,MAAM,GAAG,CAAC,iBAC7B/D,OAAA;sBAAM0O,SAAS,EAAC,6BAA6B;sBAAAE,QAAA,GAAC,GAC3C,EAAClM,MAAM,CAACwO,YAAY,CAACnN,MAAM,GAAG,CAAC;oBAAA;sBAAA+K,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CACP;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CACN,eAEDjP,OAAA;oBAAK0O,SAAS,EAAC,qBAAqB;oBAAAE,QAAA,gBAClC5O,OAAA;sBAAM0O,SAAS,EAAC,oBAAoB;sBAAAE,QAAA,EAAC;oBAA0B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtEjP,OAAA;sBAAM0O,SAAS,EAAC,oBAAoB;sBAAAE,QAAA,EAAC;oBAA+B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGL,CAACvM,MAAM,CAACoF,KAAK,IAAI,CAAC,IAAI,CAAC,iBACtB9H,OAAA;kBAAK0O,SAAS,EAAC,wBAAwB;kBAAAE,QAAA,eACrC5O,OAAA;oBAAK0O,SAAS,EAAC,aAAa;oBAAAE,QAAA,GAAC,QAAM,EAAClM,MAAM,CAACoF,KAAK;kBAAA;oBAAAgH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CACN;cAAA,GAtCItK,KAAK;gBAAAmK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAuCP,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAELtO,SAAS,iBACRX,OAAA;cAAK0O,SAAS,EAAC,gBAAgB;cAAAE,QAAA,gBAC7B5O,OAAA;gBAAM0O,SAAS,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjCjP,OAAA;gBAAA4O,QAAA,EAAO1M,CAAC,CAAC,SAAS;cAAC;gBAAA4M,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA5O,WAAW,KAAK,SAAS,IAAI4B,OAAO,iBACnCjC,OAAA;QAAK0O,SAAS,EAAC,mBAAmB;QAAAE,QAAA,eAChC5O,OAAA;UAAK0O,SAAS,EAAC,cAAc;UAAAE,QAAA,gBAC3B5O,OAAA;YAAK0O,SAAS,EAAC,gBAAgB;YAAAE,QAAA,gBAC7B5O,OAAA;cAAQ6O,OAAO,EAAER,MAAO;cAACK,SAAS,EAAC,oCAAoC;cAAAE,QAAA,EACpE1M,CAAC,CAAC,YAAY;YAAC;cAAA4M,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGTjP,OAAA;cAAK0O,SAAS,EAAC,kBAAkB;cAACpE,KAAK,EAAE;gBACvC+G,OAAO,EAAE,MAAM;gBACfC,GAAG,EAAE,KAAK;gBACVC,SAAS,EAAE,MAAM;gBACjBC,QAAQ,EAAE;cACZ,CAAE;cAAA5C,QAAA,gBAEA5O,OAAA;gBAAK0O,SAAS,EAAC,yBAAyB;gBAACpE,KAAK,EAAE;kBAC9C+G,OAAO,EAAE,MAAM;kBACfI,UAAU,EAAE,QAAQ;kBACpBH,GAAG,EAAE,KAAK;kBACV3B,OAAO,EAAE,UAAU;kBACnBJ,UAAU,EAAE,SAAS;kBACrBK,YAAY,EAAE,KAAK;kBACnBJ,MAAM,EAAE;gBACV,CAAE;gBAAAZ,QAAA,gBACA5O,OAAA;kBACE6O,OAAO,EAAE3B,kBAAmB;kBAC5BwB,SAAS,EAAC,UAAU;kBACpBtL,KAAK,EAAC,mBAAmB;kBACzBkH,KAAK,EAAE;oBACLiF,UAAU,EAAE,MAAM;oBAClBC,MAAM,EAAE,MAAM;oBACdkC,QAAQ,EAAE,MAAM;oBAChBhC,MAAM,EAAE,SAAS;oBACjBC,OAAO,EAAE;kBACX,CAAE;kBAAAf,QAAA,EAED3P,aAAa,CAACkO,SAAS,CAAC,CAAC,CAACC,SAAS,GAAG,IAAI,GAAG;gBAAI;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,eACTjP,OAAA;kBACE6O,OAAO,EAAEtB,gBAAiB;kBAC1BmB,SAAS,EAAC,UAAU;kBACpBtL,KAAK,EAAC,aAAa;kBACnBkH,KAAK,EAAE;oBACLiF,UAAU,EAAE,MAAM;oBAClBC,MAAM,EAAE,MAAM;oBACdkC,QAAQ,EAAE,MAAM;oBAChBhC,MAAM,EAAE,SAAS;oBACjBC,OAAO,EAAE;kBACX,CAAE;kBAAAf,QAAA,EACH;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTjP,OAAA;kBACE+P,IAAI,EAAC,OAAO;kBACZxH,GAAG,EAAC,KAAK;kBACTD,GAAG,EAAC,GAAG;kBACPqJ,IAAI,EAAC,KAAK;kBACVC,YAAY,EAAC,GAAG;kBAChB3B,QAAQ,EAAGrH,CAAC,IAAK6E,sBAAsB,CAACoE,UAAU,CAACjJ,CAAC,CAACC,MAAM,CAACmH,KAAK,CAAC,CAAE;kBACpE1F,KAAK,EAAE;oBAAC0G,KAAK,EAAE;kBAAM,CAAE;kBACvB5N,KAAK,EAAC;gBAAc;kBAAA0L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACFjP,OAAA;kBAAMsK,KAAK,EAAE;oBAACoH,QAAQ,EAAE,MAAM;oBAAEjC,KAAK,EAAE;kBAAS,CAAE;kBAAAb,QAAA,EAAC;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eAGNjP,OAAA;gBAAK0O,SAAS,EAAC,yBAAyB;gBAACpE,KAAK,EAAE;kBAC9C+G,OAAO,EAAE,MAAM;kBACfC,GAAG,EAAE;gBACP,CAAE;gBAAA1C,QAAA,gBACA5O,OAAA;kBACE6O,OAAO,EAAEX,qBAAsB;kBAC/BQ,SAAS,EAAC,mBAAmB;kBAC7BpE,KAAK,EAAE;oBAACqF,OAAO,EAAE,UAAU;oBAAE+B,QAAQ,EAAE;kBAAM,CAAE;kBAC/CtO,KAAK,EAAC,mBAAmB;kBAAAwL,QAAA,EAC1B;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTjP,OAAA;kBACE6O,OAAO,EAAEjB,eAAgB;kBACzBc,SAAS,EAAC,mBAAmB;kBAC7BpE,KAAK,EAAE;oBAACqF,OAAO,EAAE,UAAU;oBAAE+B,QAAQ,EAAE;kBAAM,CAAE;kBAC/CtO,KAAK,EAAC,eAAe;kBAAAwL,QAAA,EACtB;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTjP,OAAA;kBACE6O,OAAO,EAAEb,gBAAiB;kBAC1BU,SAAS,EAAC,mBAAmB;kBAC7BpE,KAAK,EAAE;oBAACqF,OAAO,EAAE,UAAU;oBAAE+B,QAAQ,EAAE;kBAAM,CAAE;kBAC/CtO,KAAK,EAAC,gBAAgB;kBAAAwL,QAAA,EACvB;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENjP,OAAA;YAAK0O,SAAS,EAAC,uBAAuB;YAAAE,QAAA,gBACpC5O,OAAA;cAAI0O,SAAS,EAAC,eAAe;cAAAE,QAAA,EAAE,CAAA3M,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEmB,KAAK,KAAI;YAAY;cAAA0L,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnEjP,OAAA;cAAK0O,SAAS,EAAC,cAAc;cAAAE,QAAA,gBAC3B5O,OAAA;gBAAM0O,SAAS,EAAC,eAAe;gBAAAE,QAAA,GAAE1M,CAAC,CAAC,QAAQ,CAAC,EAAC,IAAE,EAAC,CAAAD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE1B,KAAK,KAAI,SAAS;cAAA;gBAAAuO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAClF,CAAAhN,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEU,KAAK,KAAIV,OAAO,CAACU,KAAK,CAACoB,MAAM,GAAG,CAAC,iBACzC/D,OAAA;gBAAK0O,SAAS,EAAC,eAAe;gBAAAE,QAAA,GAC3B1M,CAAC,CAAC,OAAO,CAAC,EAAC,GACZ,EAACD,OAAO,CAACU,KAAK,CAAC2D,GAAG,CAAC,CAACwL,IAAI,EAAEnN,KAAK,kBAC7B3E,OAAA;kBAAkB0O,SAAS,EAAC,YAAY;kBAAAE,QAAA,EAAEkD;gBAAI,GAAnCnN,KAAK;kBAAAmK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAqC,CACtD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENjP,OAAA;YAAK0O,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EAC7B3M,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEqB,OAAO,GAAGrB,OAAO,CAACqB,OAAO,CAACyO,KAAK,CAAC,IAAI,CAAC,CAACzL,GAAG,CAAC,CAAC0L,SAAS,EAAErN,KAAK,KACnEqN,SAAS,CAACnF,IAAI,CAAC,CAAC,iBACd7M,OAAA;cAAe0O,SAAS,EAAC,mBAAmB;cAAAE,QAAA,EACzCoD;YAAS,GADJrN,KAAK;cAAAmK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CAEN,CAAC,gBACAjP,OAAA;cAAK0O,SAAS,EAAC,iBAAiB;cAAAE,QAAA,gBAC9B5O,OAAA;gBAAM0O,SAAS,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjCjP,OAAA;gBAAA4O,QAAA,EAAG;cAA0B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAGL,CAAAhN,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEyB,UAAU,KAAIzB,OAAO,CAACyB,UAAU,CAACK,MAAM,GAAG,CAAC,iBACnD/D,OAAA;YAAK0O,SAAS,EAAC,qBAAqB;YAAAE,QAAA,gBAClC5O,OAAA;cAAA4O,QAAA,EAAI;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrBjP,OAAA;cAAK0O,SAAS,EAAC,cAAc;cAAAE,QAAA,EAC1B3M,OAAO,CAACyB,UAAU,CAAC4C,GAAG,CAAC,CAAC2L,MAAM,EAAEtN,KAAK,kBACpC3E,OAAA;gBAAiB0O,SAAS,EAAC,aAAa;gBAAAE,QAAA,eACtC5O,OAAA;kBAAGkS,IAAI,EAAED,MAAM,CAACE,GAAI;kBAACtJ,MAAM,EAAC,QAAQ;kBAACuJ,GAAG,EAAC,qBAAqB;kBAAAxD,QAAA,EAC3DqD,MAAM,CAAC7O,KAAK,IAAI,SAASuB,KAAK,GAAG,CAAC;gBAAE;kBAAAmK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC;cAAC,GAHItK,KAAK;gBAAAmK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC7O,EAAA,CA9oCID,YAAY;EAAA,QAsBFN,cAAc;AAAA;AAAAwS,EAAA,GAtBxBlS,YAAY;AAgpClB,eAAeA,YAAY;AAAC,IAAAkS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}