{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Appv1\\\\src\\\\components\\\\OptimizedApp.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport '../styles/optimized.css';\nimport gestureService, { createFlagWheel } from '../services/gestureService';\nimport speechService from '../services/speechService';\nimport exportService from '../services/exportService';\nimport gamificationService from '../services/optimizedGamificationService';\nimport { generateKnowledgeTree as generateTreeAPI, generateArticle as generateArticleAPI, testConnection } from '../services/openRouterService';\nimport tabService from '../services/tabService';\nimport webSearchService from '../services/webSearchService';\nimport TabManager from './TabManager';\nimport LanguageSwitcher from './LanguageSwitcher';\nimport { useTranslation, getCurrentLanguage } from '../utils/i18n';\n\n// Optimized Knowledge Tree Explorer - Pareto 80/20 Implementation\n// Focus on core functionality with maximum impact\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst OptimizedApp = () => {\n  _s();\n  // Core state - now managed by tabs\n  const [currentView, setCurrentView] = useState('input'); // 'input', 'tree', 'article'\n  const [topic, setTopic] = useState('');\n  const [activeTab, setActiveTab] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [user, setUser] = useState(null);\n  const appRef = useRef(null);\n\n  // Canvas state for infinite tree view - CENTERED INITIALIZATION\n  const [canvasTransform, setCanvasTransform] = useState({\n    x: -1000,\n    y: -1000,\n    scale: 1\n  });\n  const [isDragging, setIsDragging] = useState(false);\n  const [dragStart, setDragStart] = useState({\n    x: 0,\n    y: 0\n  });\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);\n  const [errorMessage, setErrorMessage] = useState(null);\n\n  // Get current tab data\n  const tree = (activeTab === null || activeTab === void 0 ? void 0 : activeTab.tree) || null;\n  const selectedBranch = (activeTab === null || activeTab === void 0 ? void 0 : activeTab.selectedBranch) || null;\n  const article = (activeTab === null || activeTab === void 0 ? void 0 : activeTab.article) || null;\n\n  // Translation hook\n  const {\n    t\n  } = useTranslation();\n\n  // Available flags for the optimized version\n  const availableFlags = React.useMemo(() => [{\n    code: '-a',\n    name: 'Article',\n    description: t('flagArticle')\n  }, {\n    code: '-ex',\n    name: 'Examples',\n    description: t('flagExamples')\n  }, {\n    code: '-q',\n    name: 'Quiz',\n    description: t('flagQuiz')\n  }, {\n    code: '-vis',\n    name: 'Visual',\n    description: t('flagVisual')\n  }, {\n    code: '-path',\n    name: 'Learning Path',\n    description: t('flagPath')\n  }, {\n    code: '-case',\n    name: 'Case Study',\n    description: t('flagCase')\n  }, {\n    code: '-ro',\n    name: 'Romanian',\n    description: t('flagRomanian')\n  }], [t]);\n\n  // Generate article with tabs support\n  const generateArticleForBranch = React.useCallback(async (branch, flags = ['-a']) => {\n    if (!activeTab) return;\n    setIsLoading(true);\n\n    // Set tab to loading state (yellow)\n    tabService.updateTabStatus(activeTab.id, 'loading', {\n      selectedBranch: branch,\n      article: null\n    });\n    setActiveTab(tabService.getTab(activeTab.id));\n    try {\n      console.log('📄 Generating article for branch:', branch.nume);\n      const articleData = await generateArticleAPI(activeTab.topic, branch, flags);\n      console.log('✅ Generated article data:', articleData);\n\n      // Map the article data to expected format\n      const mappedArticle = {\n        title: articleData.titlu || articleData.title || `${branch.nume} - ${activeTab.topic}`,\n        content: articleData.continut || articleData.content || 'Content not available',\n        topic: activeTab.topic,\n        flags: flags,\n        position: articleData.pozitie || `${activeTab.topic} → ${branch.nume}`,\n        webSources: articleData.webSources || []\n      };\n\n      // Update tab with article and set to completed (green)\n      tabService.updateTabStatus(activeTab.id, 'completed', {\n        selectedBranch: branch,\n        article: mappedArticle\n      });\n      const updatedTab = tabService.getTab(activeTab.id);\n      setActiveTab(updatedTab);\n      setCurrentView('article');\n\n      // Award points for article generation\n      const result = gamificationService.awardPoints('ARTICLE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (error) {\n      console.error('❌ Error generating article:', error);\n      setError('Failed to generate article. Please try again.');\n\n      // Set tab back to pending on error\n      tabService.updateTabStatus(activeTab.id, 'pending', {\n        selectedBranch: branch,\n        article: null\n      });\n      setActiveTab(tabService.getTab(activeTab.id));\n    } finally {\n      setIsLoading(false);\n    }\n  }, [activeTab]);\n\n  // Handle branch selection (single tap)\n  const handleBranchSelect = React.useCallback(branch => {\n    if (activeTab) {\n      tabService.updateTabStatus(activeTab.id, activeTab.status, {\n        selectedBranch: branch\n      });\n      setActiveTab(tabService.getTab(activeTab.id));\n    }\n  }, [activeTab]);\n\n  // Gesture handlers\n  const handleDoubleTap = React.useCallback((event, targetInfo) => {\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      // Show flag wheel on double tap of branch\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        createFlagWheel(targetInfo.position, availableFlags, selectedFlags => {\n          console.log('Selected flags:', selectedFlags);\n        }, selectedFlags => {\n          generateArticleForBranch(branch, selectedFlags);\n        });\n      }\n    }\n  }, [tree, availableFlags, generateArticleForBranch]);\n  const handleSingleTap = React.useCallback((event, targetInfo) => {\n    // Single tap for normal selection\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        handleBranchSelect(branch);\n      }\n    }\n  }, [tree, handleBranchSelect]);\n\n  // Expand branch to create relevant sub-branches with AI\n  const expandBranch = React.useCallback(async (branch, branchIndex) => {\n    if (!activeTab || !tree) {\n      setError(t('noActiveTab') || 'No active tab or tree available');\n      return;\n    }\n    setIsLoading(true);\n    setError(null);\n    try {\n      var _data$choices$, _data$choices$$messag;\n      console.log('🌿 Expanding branch:', branch.nume, 'with AI-generated sub-branches');\n\n      // Create AI-generated sub-branches specifically for this branch\n      const currentLang = getCurrentLanguage();\n      const prompt = currentLang === 'ro' ? `Generează 4 sub-ramuri specifice și relevante pentru \"${branch.nume}\" în contextul \"${tree.tema}\".\n\nDescrierea ramuri principale: ${branch.descriere}\n\nCreează sub-ramuri care să fie:\n- Specifice și relevante pentru \"${branch.nume}\"\n- Logice și bine organizate\n- Utile pentru învățare progresivă\n- În limba română\n\nRăspunde DOAR cu JSON în formatul:\n{\n  \"ramuri\": [\n    {\n      \"nume\": \"Nume sub-ramură\",\n      \"descriere\": \"Descriere detaliată\",\n      \"emoji\": \"🔧\",\n      \"subcategorii\": [\"subcategorie1\", \"subcategorie2\", \"subcategorie3\"]\n    }\n  ]\n}` : `Generate 4 specific and relevant sub-branches for \"${branch.nume}\" in the context of \"${tree.tema}\".\n\nMain branch description: ${branch.descriere}\n\nCreate sub-branches that are:\n- Specific and relevant to \"${branch.nume}\"\n- Logical and well-organized\n- Useful for progressive learning\n- In English\n\nRespond ONLY with JSON in the format:\n{\n  \"branches\": [\n    {\n      \"nume\": \"Sub-branch name\",\n      \"descriere\": \"Detailed description\",\n      \"emoji\": \"🔧\",\n      \"subcategorii\": [\"subcategory1\", \"subcategory2\", \"subcategory3\"]\n    }\n  ]\n}`;\n\n      // CRITICAL: Use STRICT AI + Web validation for sub-branches\n      console.log('🔒 STRICT: Starting sub-branch generation with mandatory validation');\n\n      // STEP 1: Web validation BEFORE AI generation\n      let webValidation = [];\n      try {\n        webValidation = await webSearchService.searchSources(`${tree.tema} ${branch.nume} sub-topics`, 5);\n        console.log('✅ VERIFIED: Web validation completed with', webValidation.length, 'sources');\n      } catch (webError) {\n        console.warn('⚠️ Web validation failed, continuing with AI generation:', webError);\n        webValidation = []; // Continue without web sources\n      }\n\n      // STEP 2: AI generation with DeepSeek R1 + web context\n      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer 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`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': window.location.origin,\n          'X-Title': 'Knowledge Tree Explorer'\n        },\n        body: JSON.stringify({\n          model: 'deepseek/deepseek-r1-0528:free',\n          messages: [{\n            role: 'system',\n            content: currentLang === 'ro' ? 'Ești un expert în organizarea cunoștințelor. Generează sub-ramuri relevante și specifice în format JSON valid.' : 'You are an expert in knowledge organization. Generate relevant and specific sub-branches in valid JSON format.'\n          }, {\n            role: 'user',\n            content: prompt + (webValidation.length > 0 ? `\\n\\nSurse web de referință:\\n${webValidation.map(s => `- ${s.title}: ${s.description || s.snippet || 'No description'}`).join('\\n')}` : '')\n          }],\n          temperature: 0.7,\n          max_tokens: 2000\n        })\n      });\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error('❌ API Error Response:', errorText);\n        throw new Error(`API request failed: ${response.status} - ${errorText}`);\n      }\n      const data = await response.json();\n      console.log('🔍 Raw API Response:', data);\n      const responseText = ((_data$choices$ = data.choices[0]) === null || _data$choices$ === void 0 ? void 0 : (_data$choices$$messag = _data$choices$.message) === null || _data$choices$$messag === void 0 ? void 0 : _data$choices$$messag.content) || '';\n      console.log('📝 Response Text:', responseText);\n\n      // Parse the JSON response\n      const jsonMatch = responseText.match(/\\{[\\s\\S]*\\}/);\n      if (!jsonMatch) {\n        console.warn('⚠️ No JSON found, using fallback sub-branches');\n        // Fallback: generate simple sub-branches\n        const fallbackBranches = [{\n          nume: `${branch.nume} - Fundamentele`,\n          descriere: `Concepte de bază și principii fundamentale pentru ${branch.nume}`,\n          emoji: \"📚\",\n          subcategorii: [\"Definiții\", \"Principii\", \"Concepte\"]\n        }, {\n          nume: `${branch.nume} - Aplicații Practice`,\n          descriere: `Aplicații și exemple practice pentru ${branch.nume}`,\n          emoji: \"🔧\",\n          subcategorii: [\"Exemple\", \"Cazuri de studiu\", \"Exerciții\"]\n        }, {\n          nume: `${branch.nume} - Aspecte Avansate`,\n          descriere: `Aspecte avansate și specializate pentru ${branch.nume}`,\n          emoji: \"🎯\",\n          subcategorii: [\"Tehnici avansate\", \"Specializări\", \"Cercetare\"]\n        }];\n\n        // Update tree with fallback sub-branches\n        const newTree = {\n          ...tree\n        };\n        newTree.ramuri = [...newTree.ramuri.slice(0, branchIndex + 1), ...fallbackBranches.map(subBranch => ({\n          ...subBranch,\n          isSubBranch: true,\n          parentBranch: branch.nume,\n          level: (branch.level || 0) + 1,\n          id: `${branch.nume}-${subBranch.nume}`.replace(/\\s+/g, '-').toLowerCase()\n        })), ...newTree.ramuri.slice(branchIndex + 1)];\n\n        // Update tab with expanded tree\n        tabService.updateTabStatus(activeTab.id, 'completed', {\n          tree: newTree\n        });\n        setActiveTab(tabService.getTab(activeTab.id));\n        console.log('🌳 Tree expanded successfully with fallback sub-branches');\n        return;\n      }\n      const expandedData = JSON.parse(jsonMatch[0]);\n      const subBranches = expandedData.ramuri || expandedData.branches || [];\n      if (subBranches.length === 0) {\n        throw new Error('No sub-branches generated');\n      }\n      console.log('✅ Generated', subBranches.length, 'AI sub-branches for:', branch.nume);\n\n      // Update tree with AI-generated sub-branches\n      const newTree = {\n        ...tree\n      };\n      newTree.ramuri = [...newTree.ramuri.slice(0, branchIndex + 1), ...subBranches.map(subBranch => ({\n        ...subBranch,\n        isSubBranch: true,\n        parentBranch: branch.nume,\n        level: (branch.level || 0) + 1,\n        id: `${branch.nume}-${subBranch.nume}`.replace(/\\s+/g, '-').toLowerCase()\n      })), ...newTree.ramuri.slice(branchIndex + 1)];\n\n      // Update tab with expanded tree\n      tabService.updateTabStatus(activeTab.id, 'completed', {\n        tree: newTree\n      });\n      setActiveTab(tabService.getTab(activeTab.id));\n      console.log('🌳 Tree expanded successfully with AI sub-branches');\n\n      // Award points for branch expansion\n      const result = gamificationService.awardPoints('BRANCH_EXPANDED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (error) {\n      console.error('❌ Error expanding branch with AI:', error);\n      setError(t('failedToExpand') || 'Nu s-a putut extinde ramura. Te rugăm să încerci din nou.');\n    } finally {\n      setIsLoading(false);\n    }\n  }, [tree, activeTab, t]);\n  const handleLongPress = React.useCallback(async (event, targetInfo) => {\n    // Long press to expand branch and create tree effect\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        await expandBranch(branch, targetInfo.branchData.index);\n      }\n    }\n  }, [tree, expandBranch]);\n\n  // Canvas control functions\n  const zoomCanvas = React.useCallback(factor => {\n    setCanvasTransform(prev => ({\n      ...prev,\n      scale: Math.max(0.1, Math.min(3, prev.scale * factor))\n    }));\n  }, []);\n  const resetCanvasView = React.useCallback(() => {\n    // Center the large canvas properly in viewport\n    const container = document.querySelector('.desktop-tree-view');\n    if (container) {\n      const containerRect = container.getBoundingClientRect();\n      const canvasSize = 3000; // Match CSS canvas size\n\n      setCanvasTransform({\n        x: (containerRect.width - canvasSize) / 2,\n        y: (containerRect.height - canvasSize) / 2,\n        scale: 1\n      });\n    } else {\n      setCanvasTransform({\n        x: -1000,\n        y: -1000,\n        scale: 1\n      }); // Fallback centering\n    }\n  }, []);\n\n  // Pan & Drag functionality - using existing state\n  const [dragOffset, setDragOffset] = React.useState({\n    x: 0,\n    y: 0\n  });\n  const handleMouseDown = React.useCallback(e => {\n    // Only start drag if not clicking on interactive elements\n    if (e.target.closest('.tree-branch-node') || e.target.closest('.central-topic-node')) {\n      return;\n    }\n    setIsDragging(true);\n    setDragStart({\n      x: e.clientX,\n      y: e.clientY\n    });\n    setDragOffset({\n      x: canvasTransform.x,\n      y: canvasTransform.y\n    });\n    e.preventDefault();\n  }, [canvasTransform, setIsDragging, setDragStart]);\n  const handleMouseMove = React.useCallback(e => {\n    if (!isDragging) return;\n    const deltaX = e.clientX - dragStart.x;\n    const deltaY = e.clientY - dragStart.y;\n    setCanvasTransform(prev => ({\n      ...prev,\n      x: dragOffset.x + deltaX,\n      y: dragOffset.y + deltaY\n    }));\n  }, [isDragging, dragStart, dragOffset]);\n  const handleMouseUp = React.useCallback(() => {\n    setIsDragging(false);\n  }, []);\n\n  // Touch events for mobile drag\n  const handleTouchStart = React.useCallback(e => {\n    if (e.target.closest('.tree-branch-node') || e.target.closest('.central-topic-node')) {\n      return;\n    }\n    const touch = e.touches[0];\n    setIsDragging(true);\n    setDragStart({\n      x: touch.clientX,\n      y: touch.clientY\n    });\n    setDragOffset({\n      x: canvasTransform.x,\n      y: canvasTransform.y\n    });\n    e.preventDefault();\n  }, [canvasTransform, setIsDragging, setDragStart]);\n  const handleTouchMove = React.useCallback(e => {\n    if (!isDragging) return;\n    const touch = e.touches[0];\n    const deltaX = touch.clientX - dragStart.x;\n    const deltaY = touch.clientY - dragStart.y;\n    setCanvasTransform(prev => ({\n      ...prev,\n      x: dragOffset.x + deltaX,\n      y: dragOffset.y + deltaY\n    }));\n    e.preventDefault();\n  }, [isDragging, dragStart, dragOffset]);\n  const handleTouchEnd = React.useCallback(() => {\n    setIsDragging(false);\n  }, []);\n  const handleCanvasMouseDown = React.useCallback(e => {\n    if (e.button === 0) {\n      // Left mouse button\n      setIsDragging(true);\n      setDragStart({\n        x: e.clientX - canvasTransform.x,\n        y: e.clientY - canvasTransform.y\n      });\n    }\n  }, [canvasTransform]);\n  const handleCanvasMouseMove = React.useCallback(e => {\n    if (isDragging) {\n      setCanvasTransform(prev => ({\n        ...prev,\n        x: e.clientX - dragStart.x,\n        y: e.clientY - dragStart.y\n      }));\n    }\n  }, [isDragging, dragStart]);\n  const handleCanvasMouseUp = React.useCallback(() => {\n    setIsDragging(false);\n  }, []);\n  const handleCanvasWheel = React.useCallback(e => {\n    e.preventDefault();\n    const factor = e.deltaY > 0 ? 0.9 : 1.1;\n    zoomCanvas(factor);\n  }, [zoomCanvas]);\n\n  // Handle window resize for mobile detection\n  useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth <= 768);\n    };\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  // Initialize TikTok scroll behavior on mobile\n  useEffect(() => {\n    const tiktokContainer = document.getElementById('tiktok-scroll');\n    if (tiktokContainer && isMobile) {\n      // Smooth scroll behavior\n      tiktokContainer.style.scrollBehavior = 'smooth';\n\n      // Optional: Add snap scrolling enhancement\n      let isScrolling = false;\n      const handleScroll = () => {\n        if (!isScrolling) {\n          isScrolling = true;\n          setTimeout(() => {\n            isScrolling = false;\n          }, 150);\n        }\n      };\n      tiktokContainer.addEventListener('scroll', handleScroll);\n      return () => {\n        tiktokContainer.removeEventListener('scroll', handleScroll);\n      };\n    }\n  }, [isMobile]);\n\n  // Initialize canvas event listeners\n  useEffect(() => {\n    const canvas = document.getElementById('infinite-canvas');\n    if (canvas && !isMobile) {\n      // Apply transform\n      canvas.style.transform = `translate(${canvasTransform.x}px, ${canvasTransform.y}px) scale(${canvasTransform.scale})`;\n      canvas.addEventListener('mousedown', handleCanvasMouseDown);\n      canvas.addEventListener('wheel', handleCanvasWheel);\n\n      // Add global mouse events for dragging\n      const handleGlobalMouseMove = e => {\n        if (isDragging) {\n          handleCanvasMouseMove(e);\n        }\n      };\n      const handleGlobalMouseUp = () => {\n        if (isDragging) {\n          handleCanvasMouseUp();\n        }\n      };\n      document.addEventListener('mousemove', handleGlobalMouseMove);\n      document.addEventListener('mouseup', handleGlobalMouseUp);\n      return () => {\n        canvas.removeEventListener('mousedown', handleCanvasMouseDown);\n        canvas.removeEventListener('wheel', handleCanvasWheel);\n        document.removeEventListener('mousemove', handleGlobalMouseMove);\n        document.removeEventListener('mouseup', handleGlobalMouseUp);\n      };\n    }\n  }, [canvasTransform, isMobile, isDragging, handleCanvasMouseDown, handleCanvasMouseMove, handleCanvasMouseUp, handleCanvasWheel]);\n\n  // Initialize canvas centering when tree loads\n  useEffect(() => {\n    if (tree && !isMobile) {\n      // Delay to ensure DOM is ready\n      setTimeout(() => {\n        resetCanvasView();\n      }, 100);\n    }\n  }, [tree, isMobile, resetCanvasView]);\n\n  // Error message handler\n  const showError = React.useCallback(message => {\n    setErrorMessage(message);\n    setTimeout(() => setErrorMessage(null), 5000); // Auto-hide after 5 seconds\n  }, []);\n\n  // Enhanced expand branch with error handling\n  const expandBranchWithErrorHandling = React.useCallback(async (branch, index) => {\n    try {\n      await expandBranch(branch, index);\n    } catch (error) {\n      console.error('Failed to expand branch:', error);\n      showError('⚠️ Nu s-a putut extinde ramura. Te rugăm să încerci din nou.');\n    }\n  }, [expandBranch, showError]);\n\n  // Initialize services and authentication\n  useEffect(() => {\n    const storedUser = localStorage.getItem('user');\n    const bypassSecurity = localStorage.getItem('bypassSecurity');\n    if (storedUser || bypassSecurity) {\n      const userData = {\n        id: 'user-1',\n        name: 'User',\n        subscriptionTier: 'premium'\n      };\n      setUser(userData);\n\n      // Award daily login points\n      const result = gamificationService.awardPoints('DAILY_LOGIN');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n\n    // Initialize gesture service\n    if (appRef.current) {\n      gestureService.init(appRef.current, {\n        doubleTap: handleDoubleTap,\n        singleTap: handleSingleTap,\n        longPress: handleLongPress\n      });\n    }\n\n    // Add drag event listeners\n    const handleGlobalMouseMove = e => handleMouseMove(e);\n    const handleGlobalMouseUp = e => handleMouseUp(e);\n    const handleGlobalTouchMove = e => handleTouchMove(e);\n    const handleGlobalTouchEnd = e => handleTouchEnd(e);\n    document.addEventListener('mousemove', handleGlobalMouseMove);\n    document.addEventListener('mouseup', handleGlobalMouseUp);\n    document.addEventListener('touchmove', handleGlobalTouchMove, {\n      passive: false\n    });\n    document.addEventListener('touchend', handleGlobalTouchEnd);\n    return () => {\n      gestureService.destroy();\n      document.removeEventListener('mousemove', handleGlobalMouseMove);\n      document.removeEventListener('mouseup', handleGlobalMouseUp);\n      document.removeEventListener('touchmove', handleGlobalTouchMove);\n      document.removeEventListener('touchend', handleGlobalTouchEnd);\n    };\n  }, [handleDoubleTap, handleSingleTap, handleLongPress, handleMouseMove, handleMouseUp, handleTouchMove, handleTouchEnd]);\n\n  // Initialize gamification UI when user is logged in\n  useEffect(() => {\n    if (user) {\n      const container = document.getElementById('gamification-container');\n      if (container) {\n        // Clear existing content\n        container.innerHTML = '';\n        // Create gamification UI\n        gamificationService.createGamificationUI(container);\n      }\n\n      // Test API connection\n      testConnection().then(isConnected => {\n        console.log('🔌 API Connection Status:', isConnected ? '✅ Connected' : '❌ Failed');\n        if (!isConnected) {\n          console.warn('⚠️ API connection failed. Check your API key and internet connection.');\n        }\n      }).catch(error => {\n        console.error('❌ API connection test error:', error);\n      });\n    }\n  }, [user]);\n\n  // Core API call - using optimized service with tabs\n  const generateKnowledgeTree = async (topicInput, tabId = null) => {\n    let currentTabId = tabId;\n\n    // Create new tab if none provided\n    if (!currentTabId) {\n      try {\n        const newTab = tabService.createTab(topicInput);\n        currentTabId = newTab.id;\n        setActiveTab(newTab);\n        setCurrentView('tree');\n      } catch (error) {\n        setError(error.message);\n        return;\n      }\n    }\n\n    // Update tab status to generating\n    tabService.updateTabStatus(currentTabId, 'generating', {\n      progress: 10\n    });\n    setIsLoading(true);\n    setError(null);\n    try {\n      console.log('🌳 Generating knowledge tree for:', topicInput, 'in tab:', currentTabId);\n\n      // Update progress\n      tabService.updateTabStatus(currentTabId, 'generating', {\n        progress: 30\n      });\n      const treeData = await generateTreeAPI(topicInput, getCurrentLanguage());\n      console.log('✅ Generated tree data:', treeData);\n\n      // Update tab with completed tree\n      tabService.updateTabStatus(currentTabId, 'completed', {\n        tree: treeData,\n        progress: 100\n      });\n\n      // Update active tab if this is the current one\n      if (currentTabId === (activeTab === null || activeTab === void 0 ? void 0 : activeTab.id)) {\n        setActiveTab(tabService.getTab(currentTabId));\n      }\n\n      // Award points for tree generation\n      const result = gamificationService.awardPoints('TREE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (err) {\n      console.error('❌ Error generating tree:', err);\n      tabService.updateTabStatus(currentTabId, 'error');\n      setError(`Failed to generate knowledge tree: ${err.message}. Please try again.`);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Handle form submission\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (topic.trim()) {\n      generateKnowledgeTree(topic.trim());\n      setTopic(''); // Clear input for next topic\n    }\n  };\n\n  // Handle tab changes\n  const handleTabChange = tab => {\n    // Clear any existing errors when switching tabs\n    setError(null);\n    setIsLoading(false);\n    setActiveTab(tab);\n    if (tab !== null && tab !== void 0 && tab.tree) {\n      setCurrentView('tree');\n    } else {\n      setCurrentView('input');\n    }\n  };\n\n  // Handle new tab creation\n  const handleNewTab = () => {\n    // Clear any existing errors and loading states\n    setError(null);\n    setIsLoading(false);\n    setCurrentView('input');\n    setActiveTab(null);\n    setTopic('');\n  };\n  const handleTabArticleAccess = tab => {\n    // Clear any existing errors when accessing article\n    setError(null);\n    setIsLoading(false);\n    setActiveTab(tab);\n    setCurrentView('article');\n  };\n\n  // Speech functions\n  const handleSpeechToggle = () => {\n    if (!(article !== null && article !== void 0 && article.content)) return;\n    if (speechService.getStatus().isPlaying) {\n      speechService.toggle();\n    } else {\n      speechService.speak(article.content);\n      // Award points for using speech\n      const result = gamificationService.awardPoints('SPEECH_USED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleSpeechStop = () => {\n    speechService.stop();\n  };\n  const handleSpeechRateChange = rate => {\n    speechService.setRate(rate);\n  };\n\n  // Export functions\n  const handleExportPDF = () => {\n    if (!(article !== null && article !== void 0 && article.title) || !(article !== null && article !== void 0 && article.content)) return;\n    const result = exportService.exportAsPDF(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleExportWord = () => {\n    if (!(article !== null && article !== void 0 && article.title) || !(article !== null && article !== void 0 && article.content)) return;\n    const result = exportService.exportAsWord(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleCopyToClipboard = async () => {\n    if (!(article !== null && article !== void 0 && article.content)) return;\n    const result = await exportService.copyToClipboard(article.content);\n    exportService.showMessage(result.message, result.success ? 'success' : 'error');\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  // Navigation functions\n  const goBack = () => {\n    if (currentView === 'article') {\n      setCurrentView('tree');\n      if (activeTab) {\n        tabService.updateTabStatus(activeTab.id, activeTab.status, {\n          article: null\n        });\n        setActiveTab(tabService.getTab(activeTab.id));\n      }\n    } else if (currentView === 'tree') {\n      setCurrentView('input');\n    }\n  };\n  const goHome = () => {\n    setCurrentView('input');\n    setActiveTab(null);\n    setTopic('');\n    // Clear all tabs\n    tabService.clearAllTabs();\n  };\n\n  // Quick login for development\n  const quickLogin = () => {\n    localStorage.setItem('bypassSecurity', 'true');\n    setUser({\n      id: 'dev-1',\n      name: 'Developer',\n      subscriptionTier: 'premium'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    ref: appRef,\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"app-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: goHome,\n          className: \"logo-text\",\n          children: t('appTitle')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 864,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-right\",\n          children: [user && /*#__PURE__*/_jsxDEV(\"div\", {\n            id: \"gamification-container\",\n            style: {\n              marginRight: '16px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 869,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(LanguageSwitcher, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 873,\n            columnNumber: 13\n          }, this), !user ? /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: quickLogin,\n            className: \"btn btn-primary\",\n            style: {\n              marginLeft: '12px'\n            },\n            children: t('quickLogin')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 875,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginLeft: '12px'\n            },\n            children: [t('welcome'), \", \", user.name, \"!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 879,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 867,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 863,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 862,\n      columnNumber: 7\n    }, this), user && /*#__PURE__*/_jsxDEV(TabManager, {\n      onTabChange: handleTabChange,\n      onNewTab: handleNewTab,\n      onTabArticleAccess: handleTabArticleAccess\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 887,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"main-content\",\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error\",\n        children: [\"\\u26A0\\uFE0F \", error, /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setError(null),\n          style: {\n            marginLeft: 'auto',\n            background: 'none',\n            border: 'none',\n            color: 'white',\n            cursor: 'pointer'\n          },\n          children: \"\\u2715\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 899,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 897,\n        columnNumber: 11\n      }, this), currentView === 'input' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"title\",\n          children: t('appTitle')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 908,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"subtitle\",\n          children: \"Enter any topic to generate an interactive knowledge tree with AI-powered content.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 909,\n          columnNumber: 13\n        }, this), !user ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#f1f5f9',\n            padding: '1rem',\n            borderRadius: '0.5rem',\n            marginBottom: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#334155',\n              marginBottom: '1rem'\n            },\n            children: t('loginRequired')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 915,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: quickLogin,\n            className: \"btn btn-primary\",\n            children: t('quickLoginDev')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 918,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 914,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: topic,\n              onChange: e => setTopic(e.target.value),\n              placeholder: t('topicPlaceholder'),\n              className: \"form-input\",\n              disabled: isLoading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 925,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 924,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: isLoading || !topic.trim(),\n            className: \"btn btn-primary\",\n            children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 941,\n                columnNumber: 23\n              }, this), t('generating')]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: t('exploreKnowledge')\n            }, void 0, false)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 934,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 923,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 907,\n        columnNumber: 11\n      }, this), currentView === 'tree' && tree && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tree-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `desktop-tree-view ${isDragging ? 'dragging' : ''}`,\n          onMouseDown: handleMouseDown,\n          onTouchStart: handleTouchStart,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `infinite-canvas ${isDragging ? 'dragging' : ''}`,\n            id: \"infinite-canvas\",\n            style: {\n              transform: `translate(${canvasTransform.x}px, ${canvasTransform.y}px) scale(${canvasTransform.scale})`\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"central-topic-node\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"topic-input-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  children: tree.tema\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 974,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: goBack,\n                  className: \"btn btn-secondary back-btn\",\n                  children: t('backToTree')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 975,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 973,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 972,\n              columnNumber: 17\n            }, this), tree.ramuri.map((branch, index) => {\n              const totalBranches = tree.ramuri.length;\n\n              // Calculate proper spacing to prevent overlaps\n              const minAngleSpacing = Math.max(25, 360 / Math.max(totalBranches, 8)); // Minimum 25 degrees between branches\n              const angle = index * minAngleSpacing % 360;\n\n              // Dynamic radius based on number of branches and level\n              const baseRadius = Math.max(350, totalBranches * 35); // Increase radius with more branches\n              const levelOffset = (branch.level || 0) * 150; // More spacing between levels\n              const radius = baseRadius + levelOffset;\n\n              // Reduced randomness to prevent overlaps\n              const angleOffset = Math.sin(index * 1.5) * 8; // Smaller random offset\n              const finalAngle = angle + angleOffset;\n\n              // Calculate position with collision detection\n              let x = Math.cos(finalAngle * Math.PI / 180) * radius;\n              let y = Math.sin(finalAngle * Math.PI / 180) * radius;\n\n              // Add spiral effect for many branches\n              if (totalBranches > 12) {\n                const spiralOffset = index * 15; // Spiral outward\n                x += Math.cos(spiralOffset * Math.PI / 180) * (index * 8);\n                y += Math.sin(spiralOffset * Math.PI / 180) * (index * 8);\n              }\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `tree-branch-node branch-item ${selectedBranch === branch ? 'selected' : ''}`,\n                style: {\n                  transform: `translate(${x}px, ${y}px)`,\n                  '--branch-angle': `${finalAngle}deg`\n                },\n                \"data-index\": index,\n                \"data-level\": branch.level || 0,\n                \"data-name\": branch.nume,\n                \"data-description\": branch.descriere,\n                onClick: () => handleBranchSelect(branch),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"branch-connection-line\",\n                  style: {\n                    transform: `rotate(${finalAngle + 180}deg)`,\n                    width: `${radius}px`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1024,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"branch-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"branch-emoji\",\n                    children: branch.emoji\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1030,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"branch-name\",\n                    children: branch.nume\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1031,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"branch-description\",\n                    children: branch.descriere\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1032,\n                    columnNumber: 25\n                  }, this), branch.subcategorii && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"branch-subcategories\",\n                    children: branch.subcategorii.slice(0, 2).map((sub, subIndex) => /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"subcategory-tag\",\n                      children: sub\n                    }, subIndex, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1037,\n                      columnNumber: 31\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1035,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1029,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1010,\n                columnNumber: 21\n              }, this);\n            }), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"loading-overlay\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1050,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: t('loading')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1051,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1049,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 964,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"canvas-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"control-btn\",\n              onClick: () => zoomCanvas(1.2),\n              children: \"\\uD83D\\uDD0D+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1058,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"control-btn\",\n              onClick: () => zoomCanvas(0.8),\n              children: \"\\uD83D\\uDD0D-\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1059,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"control-btn\",\n              onClick: () => resetCanvasView(),\n              children: \"\\uD83C\\uDFAF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1060,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1057,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 959,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mobile-tree-view\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tiktok-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"tiktok-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                children: tree.tema\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1068,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: goBack,\n                className: \"btn btn-secondary\",\n                children: t('backToTree')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1069,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1067,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"tiktok-scroll-container\",\n              id: \"tiktok-scroll\",\n              children: tree.ramuri.map((branch, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `tiktok-branch-card branch-item ${selectedBranch === branch ? 'selected' : ''}`,\n                \"data-index\": index,\n                \"data-name\": branch.nume,\n                \"data-description\": branch.descriere,\n                onClick: () => handleBranchSelect(branch),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"tiktok-card-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"branch-emoji-large\",\n                    children: branch.emoji\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1085,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"branch-name-large\",\n                    children: branch.nume\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1086,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"branch-description-large\",\n                    children: branch.descriere\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1087,\n                    columnNumber: 25\n                  }, this), branch.subcategorii && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"tiktok-subcategories\",\n                    children: [branch.subcategorii.slice(0, 3).map((sub, subIndex) => /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"tiktok-subcategory-tag\",\n                      children: sub\n                    }, subIndex, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1092,\n                      columnNumber: 31\n                    }, this)), branch.subcategorii.length > 3 && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"tiktok-subcategory-tag more\",\n                      children: [\"+\", branch.subcategorii.length - 3]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1097,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1090,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"tiktok-gesture-hint\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"tiktok-action-hint\",\n                      children: \"\\uD83D\\uDCD6 Swipe down alte crengii\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1105,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"tiktok-action-hint\",\n                      children: \"\\uD83C\\uDF3F Long-press pentru expansiune\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1106,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1104,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1084,\n                  columnNumber: 23\n                }, this), (branch.level || 0) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"tiktok-level-indicator\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"level-badge\",\n                    children: [\"Nivel \", branch.level]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1113,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1112,\n                  columnNumber: 25\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1076,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1074,\n              columnNumber: 17\n            }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"tiktok-loading\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1122,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: t('loading')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1123,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1121,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1066,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1065,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 957,\n        columnNumber: 11\n      }, this), currentView === 'article' && article && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"article-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"article-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"article-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: goBack,\n              className: \"btn btn-secondary article-back-btn\",\n              children: t('backToTree')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1136,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"article-controls\",\n              style: {\n                display: 'flex',\n                gap: '8px',\n                marginTop: '1rem',\n                flexWrap: 'wrap'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"speech-controls-compact\",\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '8px',\n                  padding: '8px 12px',\n                  background: '#f1f5f9',\n                  borderRadius: '6px',\n                  border: '1px solid #e2e8f0'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleSpeechToggle,\n                  className: \"btn-icon\",\n                  title: \"Play/Pause Speech\",\n                  style: {\n                    background: 'none',\n                    border: 'none',\n                    fontSize: '16px',\n                    cursor: 'pointer',\n                    padding: '4px'\n                  },\n                  children: speechService.getStatus().isPlaying ? '⏸️' : '▶️'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1157,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleSpeechStop,\n                  className: \"btn-icon\",\n                  title: \"Stop Speech\",\n                  style: {\n                    background: 'none',\n                    border: 'none',\n                    fontSize: '16px',\n                    cursor: 'pointer',\n                    padding: '4px'\n                  },\n                  children: \"\\u23F9\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1171,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"range\",\n                  min: \"0.5\",\n                  max: \"2\",\n                  step: \"0.1\",\n                  defaultValue: \"1\",\n                  onChange: e => handleSpeechRateChange(parseFloat(e.target.value)),\n                  style: {\n                    width: '60px'\n                  },\n                  title: \"Speech Speed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1185,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '12px',\n                    color: '#64748b'\n                  },\n                  children: \"\\uD83D\\uDDE3\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1195,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1148,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"export-controls-compact\",\n                style: {\n                  display: 'flex',\n                  gap: '4px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleCopyToClipboard,\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '6px 12px',\n                    fontSize: '12px'\n                  },\n                  title: \"Copy to Clipboard\",\n                  children: \"\\uD83D\\uDCCB Copy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1203,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleExportPDF,\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '6px 12px',\n                    fontSize: '12px'\n                  },\n                  title: \"Export as PDF\",\n                  children: \"\\uD83D\\uDCC4 PDF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1211,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleExportWord,\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '6px 12px',\n                    fontSize: '12px'\n                  },\n                  title: \"Export as Word\",\n                  children: \"\\uD83D\\uDCDD Word\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1219,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1199,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1141,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1135,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"article-title-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"article-title\",\n              children: (article === null || article === void 0 ? void 0 : article.title) || 'Loading...'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1232,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"article-meta\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"article-topic\",\n                children: [t('partOf'), \": \", (article === null || article === void 0 ? void 0 : article.topic) || 'Unknown']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1234,\n                columnNumber: 19\n              }, this), (article === null || article === void 0 ? void 0 : article.flags) && article.flags.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"article-flags\",\n                children: [t('flags'), \":\", article.flags.map((flag, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"flag-badge\",\n                  children: flag\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1239,\n                  columnNumber: 25\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1236,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1233,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1231,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"article-content\",\n            children: article !== null && article !== void 0 && article.content ? article.content.split('\\n').map((paragraph, index) => paragraph.trim() && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"article-paragraph\",\n              children: paragraph\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1249,\n              columnNumber: 21\n            }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"article-loading\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1255,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Loading article content...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1256,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1254,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1246,\n            columnNumber: 15\n          }, this), (article === null || article === void 0 ? void 0 : article.webSources) && article.webSources.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"web-sources-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83D\\uDCDA Surse Web\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1264,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sources-grid\",\n              children: article.webSources.map((source, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"source-item\",\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: source.url,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  children: source.title || `Sursa ${index + 1}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1268,\n                  columnNumber: 25\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1267,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1265,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1263,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1134,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1133,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 895,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 860,\n    columnNumber: 5\n  }, this);\n};\n_s(OptimizedApp, \"lPy9Q8ZDUVZKKafoh6KN0WhFafY=\", false, function () {\n  return [useTranslation];\n});\n_c = OptimizedApp;\nexport default OptimizedApp;\nvar _c;\n$RefreshReg$(_c, \"OptimizedApp\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "gestureService", "createFlagWheel", "speechService", "exportService", "gamificationService", "generateKnowledgeTree", "generateTreeAPI", "generateArticle", "generateArticleAPI", "testConnection", "tabService", "webSearchService", "TabManager", "LanguageSwitcher", "useTranslation", "getCurrentLanguage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "OptimizedApp", "_s", "current<PERSON>iew", "set<PERSON><PERSON><PERSON>View", "topic", "setTopic", "activeTab", "setActiveTab", "isLoading", "setIsLoading", "error", "setError", "user", "setUser", "appRef", "canvasTransform", "setCanvasTransform", "x", "y", "scale", "isDragging", "setIsDragging", "dragStart", "setDragStart", "isMobile", "setIsMobile", "window", "innerWidth", "errorMessage", "setErrorMessage", "tree", "<PERSON><PERSON><PERSON><PERSON>", "article", "t", "availableFlags", "useMemo", "code", "name", "description", "generateArticleForBranch", "useCallback", "branch", "flags", "updateTabStatus", "id", "getTab", "console", "log", "nume", "articleData", "mappedArticle", "title", "titlu", "content", "continut", "position", "pozitie", "webSources", "updatedTab", "result", "awardPoints", "newAchievements", "length", "for<PERSON>ach", "achievement", "showAchievementNotification", "handleBranchSelect", "status", "handleDoubleTap", "event", "targetInfo", "isBranchItem", "branchData", "<PERSON><PERSON>", "index", "selected<PERSON><PERSON><PERSON>", "handleSingleTap", "expandBranch", "branchIndex", "_data$choices$", "_data$choices$$messag", "currentLang", "prompt", "tema", "desc<PERSON><PERSON>", "webValidation", "searchSources", "webError", "warn", "response", "fetch", "method", "headers", "location", "origin", "body", "JSON", "stringify", "model", "messages", "role", "map", "s", "snippet", "join", "temperature", "max_tokens", "ok", "errorText", "text", "Error", "data", "json", "responseText", "choices", "message", "jsonMatch", "match", "fallbackBranches", "emoji", "subcategorii", "newTree", "slice", "subBranch", "isSubBranch", "parentBranch", "level", "replace", "toLowerCase", "expandedData", "parse", "subBranches", "branches", "handleLongPress", "zoomCanvas", "factor", "prev", "Math", "max", "min", "resetCanvasView", "container", "document", "querySelector", "containerRect", "getBoundingClientRect", "canvasSize", "width", "height", "dragOffset", "setDragOffset", "handleMouseDown", "e", "target", "closest", "clientX", "clientY", "preventDefault", "handleMouseMove", "deltaX", "deltaY", "handleMouseUp", "handleTouchStart", "touch", "touches", "handleTouchMove", "handleTouchEnd", "handleCanvasMouseDown", "button", "handleCanvasMouseMove", "handleCanvasMouseUp", "handleCanvasWheel", "handleResize", "addEventListener", "removeEventListener", "tiktokContainer", "getElementById", "style", "scroll<PERSON>eh<PERSON>or", "isScrolling", "handleScroll", "setTimeout", "canvas", "transform", "handleGlobalMouseMove", "handleGlobalMouseUp", "showError", "expandBranchWithErrorHandling", "storedUser", "localStorage", "getItem", "bypassSecurity", "userData", "subscriptionTier", "current", "init", "doubleTap", "singleTap", "longPress", "handleGlobalTouchMove", "handleGlobalTouchEnd", "passive", "destroy", "innerHTML", "createGamificationUI", "then", "isConnected", "catch", "topicInput", "tabId", "currentTabId", "newTab", "createTab", "progress", "treeData", "err", "handleSubmit", "trim", "handleTabChange", "tab", "handleNewTab", "handleTabArticleAccess", "handleSpeechToggle", "getStatus", "isPlaying", "toggle", "speak", "handleSpeechStop", "stop", "handleSpeechRateChange", "rate", "setRate", "handleExportPDF", "exportAsPDF", "success", "gamResult", "handleExportWord", "exportAsWord", "handleCopyToClipboard", "copyToClipboard", "showMessage", "goBack", "goHome", "clearAllTabs", "quickLogin", "setItem", "className", "ref", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginRight", "marginLeft", "onTabChange", "onNewTab", "onTabArticleAccess", "background", "border", "color", "cursor", "padding", "borderRadius", "marginBottom", "onSubmit", "type", "value", "onChange", "placeholder", "disabled", "onMouseDown", "onTouchStart", "totalBranches", "minAngleSpacing", "angle", "baseRadius", "levelOffset", "radius", "angleOffset", "sin", "finalAngle", "cos", "PI", "spiralOffset", "sub", "subIndex", "display", "gap", "marginTop", "flexWrap", "alignItems", "fontSize", "step", "defaultValue", "parseFloat", "flag", "split", "paragraph", "source", "href", "url", "rel", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Appv1/src/components/OptimizedApp.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport '../styles/optimized.css';\nimport gestureService, { createFlagWheel } from '../services/gestureService';\nimport speechService from '../services/speechService';\nimport exportService from '../services/exportService';\nimport gamificationService from '../services/optimizedGamificationService';\nimport { generateKnowledgeTree as generateTreeAPI, generateArticle as generateArticleAPI, testConnection } from '../services/openRouterService';\nimport tabService from '../services/tabService';\nimport webSearchService from '../services/webSearchService';\nimport TabManager from './TabManager';\nimport LanguageSwitcher from './LanguageSwitcher';\nimport { useTranslation, getCurrentLanguage } from '../utils/i18n';\n\n// Optimized Knowledge Tree Explorer - Pareto 80/20 Implementation\n// Focus on core functionality with maximum impact\n\nconst OptimizedApp = () => {\n  // Core state - now managed by tabs\n  const [currentView, setCurrentView] = useState('input'); // 'input', 'tree', 'article'\n  const [topic, setTopic] = useState('');\n  const [activeTab, setActiveTab] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [user, setUser] = useState(null);\n  const appRef = useRef(null);\n\n  // Canvas state for infinite tree view - CENTERED INITIALIZATION\n  const [canvasTransform, setCanvasTransform] = useState({ x: -1000, y: -1000, scale: 1 });\n  const [isDragging, setIsDragging] = useState(false);\n  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);\n  const [errorMessage, setErrorMessage] = useState(null);\n\n  // Get current tab data\n  const tree = activeTab?.tree || null;\n  const selectedBranch = activeTab?.selectedBranch || null;\n  const article = activeTab?.article || null;\n\n  // Translation hook\n  const { t } = useTranslation();\n\n  // Available flags for the optimized version\n  const availableFlags = React.useMemo(() => [\n    { code: '-a', name: 'Article', description: t('flagArticle') },\n    { code: '-ex', name: 'Examples', description: t('flagExamples') },\n    { code: '-q', name: 'Quiz', description: t('flagQuiz') },\n    { code: '-vis', name: 'Visual', description: t('flagVisual') },\n    { code: '-path', name: 'Learning Path', description: t('flagPath') },\n    { code: '-case', name: 'Case Study', description: t('flagCase') },\n    { code: '-ro', name: 'Romanian', description: t('flagRomanian') }\n  ], [t]);\n\n  // Generate article with tabs support\n  const generateArticleForBranch = React.useCallback(async (branch, flags = ['-a']) => {\n    if (!activeTab) return;\n\n    setIsLoading(true);\n\n    // Set tab to loading state (yellow)\n    tabService.updateTabStatus(activeTab.id, 'loading', {\n      selectedBranch: branch,\n      article: null\n    });\n    setActiveTab(tabService.getTab(activeTab.id));\n\n    try {\n      console.log('📄 Generating article for branch:', branch.nume);\n      const articleData = await generateArticleAPI(activeTab.topic, branch, flags);\n\n      console.log('✅ Generated article data:', articleData);\n\n      // Map the article data to expected format\n      const mappedArticle = {\n        title: articleData.titlu || articleData.title || `${branch.nume} - ${activeTab.topic}`,\n        content: articleData.continut || articleData.content || 'Content not available',\n        topic: activeTab.topic,\n        flags: flags,\n        position: articleData.pozitie || `${activeTab.topic} → ${branch.nume}`,\n        webSources: articleData.webSources || []\n      };\n\n      // Update tab with article and set to completed (green)\n      tabService.updateTabStatus(activeTab.id, 'completed', {\n        selectedBranch: branch,\n        article: mappedArticle\n      });\n\n      const updatedTab = tabService.getTab(activeTab.id);\n      setActiveTab(updatedTab);\n      setCurrentView('article');\n\n      // Award points for article generation\n      const result = gamificationService.awardPoints('ARTICLE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (error) {\n      console.error('❌ Error generating article:', error);\n      setError('Failed to generate article. Please try again.');\n\n      // Set tab back to pending on error\n      tabService.updateTabStatus(activeTab.id, 'pending', {\n        selectedBranch: branch,\n        article: null\n      });\n      setActiveTab(tabService.getTab(activeTab.id));\n    } finally {\n      setIsLoading(false);\n    }\n  }, [activeTab]);\n\n  // Handle branch selection (single tap)\n  const handleBranchSelect = React.useCallback((branch) => {\n    if (activeTab) {\n      tabService.updateTabStatus(activeTab.id, activeTab.status, { selectedBranch: branch });\n      setActiveTab(tabService.getTab(activeTab.id));\n    }\n  }, [activeTab]);\n\n  // Gesture handlers\n  const handleDoubleTap = React.useCallback((event, targetInfo) => {\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      // Show flag wheel on double tap of branch\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        createFlagWheel(\n          targetInfo.position,\n          availableFlags,\n          (selectedFlags) => {\n            console.log('Selected flags:', selectedFlags);\n          },\n          (selectedFlags) => {\n            generateArticleForBranch(branch, selectedFlags);\n          }\n        );\n      }\n    }\n  }, [tree, availableFlags, generateArticleForBranch]);\n\n  const handleSingleTap = React.useCallback((event, targetInfo) => {\n    // Single tap for normal selection\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        handleBranchSelect(branch);\n      }\n    }\n  }, [tree, handleBranchSelect]);\n\n  // Expand branch to create relevant sub-branches with AI\n  const expandBranch = React.useCallback(async (branch, branchIndex) => {\n    if (!activeTab || !tree) {\n      setError(t('noActiveTab') || 'No active tab or tree available');\n      return;\n    }\n\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      console.log('🌿 Expanding branch:', branch.nume, 'with AI-generated sub-branches');\n\n      // Create AI-generated sub-branches specifically for this branch\n      const currentLang = getCurrentLanguage();\n      const prompt = currentLang === 'ro'\n        ? `Generează 4 sub-ramuri specifice și relevante pentru \"${branch.nume}\" în contextul \"${tree.tema}\".\n\nDescrierea ramuri principale: ${branch.descriere}\n\nCreează sub-ramuri care să fie:\n- Specifice și relevante pentru \"${branch.nume}\"\n- Logice și bine organizate\n- Utile pentru învățare progresivă\n- În limba română\n\nRăspunde DOAR cu JSON în formatul:\n{\n  \"ramuri\": [\n    {\n      \"nume\": \"Nume sub-ramură\",\n      \"descriere\": \"Descriere detaliată\",\n      \"emoji\": \"🔧\",\n      \"subcategorii\": [\"subcategorie1\", \"subcategorie2\", \"subcategorie3\"]\n    }\n  ]\n}`\n        : `Generate 4 specific and relevant sub-branches for \"${branch.nume}\" in the context of \"${tree.tema}\".\n\nMain branch description: ${branch.descriere}\n\nCreate sub-branches that are:\n- Specific and relevant to \"${branch.nume}\"\n- Logical and well-organized\n- Useful for progressive learning\n- In English\n\nRespond ONLY with JSON in the format:\n{\n  \"branches\": [\n    {\n      \"nume\": \"Sub-branch name\",\n      \"descriere\": \"Detailed description\",\n      \"emoji\": \"🔧\",\n      \"subcategorii\": [\"subcategory1\", \"subcategory2\", \"subcategory3\"]\n    }\n  ]\n}`;\n\n      // CRITICAL: Use STRICT AI + Web validation for sub-branches\n      console.log('🔒 STRICT: Starting sub-branch generation with mandatory validation');\n\n      // STEP 1: Web validation BEFORE AI generation\n      let webValidation = [];\n      try {\n        webValidation = await webSearchService.searchSources(`${tree.tema} ${branch.nume} sub-topics`, 5);\n        console.log('✅ VERIFIED: Web validation completed with', webValidation.length, 'sources');\n      } catch (webError) {\n        console.warn('⚠️ Web validation failed, continuing with AI generation:', webError);\n        webValidation = []; // Continue without web sources\n      }\n\n      // STEP 2: AI generation with DeepSeek R1 + web context\n      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer 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`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': window.location.origin,\n          'X-Title': 'Knowledge Tree Explorer'\n        },\n        body: JSON.stringify({\n          model: 'deepseek/deepseek-r1-0528:free',\n          messages: [\n            {\n              role: 'system',\n              content: currentLang === 'ro'\n                ? 'Ești un expert în organizarea cunoștințelor. Generează sub-ramuri relevante și specifice în format JSON valid.'\n                : 'You are an expert in knowledge organization. Generate relevant and specific sub-branches in valid JSON format.'\n            },\n            {\n              role: 'user',\n              content: prompt + (webValidation.length > 0 ? `\\n\\nSurse web de referință:\\n${webValidation.map(s => `- ${s.title}: ${s.description || s.snippet || 'No description'}`).join('\\n')}` : '')\n            }\n          ],\n          temperature: 0.7,\n          max_tokens: 2000\n        })\n      });\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error('❌ API Error Response:', errorText);\n        throw new Error(`API request failed: ${response.status} - ${errorText}`);\n      }\n\n      const data = await response.json();\n      console.log('🔍 Raw API Response:', data);\n\n      const responseText = data.choices[0]?.message?.content || '';\n      console.log('📝 Response Text:', responseText);\n\n      // Parse the JSON response\n      const jsonMatch = responseText.match(/\\{[\\s\\S]*\\}/);\n      if (!jsonMatch) {\n        console.warn('⚠️ No JSON found, using fallback sub-branches');\n        // Fallback: generate simple sub-branches\n        const fallbackBranches = [\n          {\n            nume: `${branch.nume} - Fundamentele`,\n            descriere: `Concepte de bază și principii fundamentale pentru ${branch.nume}`,\n            emoji: \"📚\",\n            subcategorii: [\"Definiții\", \"Principii\", \"Concepte\"]\n          },\n          {\n            nume: `${branch.nume} - Aplicații Practice`,\n            descriere: `Aplicații și exemple practice pentru ${branch.nume}`,\n            emoji: \"🔧\",\n            subcategorii: [\"Exemple\", \"Cazuri de studiu\", \"Exerciții\"]\n          },\n          {\n            nume: `${branch.nume} - Aspecte Avansate`,\n            descriere: `Aspecte avansate și specializate pentru ${branch.nume}`,\n            emoji: \"🎯\",\n            subcategorii: [\"Tehnici avansate\", \"Specializări\", \"Cercetare\"]\n          }\n        ];\n\n        // Update tree with fallback sub-branches\n        const newTree = { ...tree };\n        newTree.ramuri = [\n          ...newTree.ramuri.slice(0, branchIndex + 1),\n          ...fallbackBranches.map(subBranch => ({\n            ...subBranch,\n            isSubBranch: true,\n            parentBranch: branch.nume,\n            level: (branch.level || 0) + 1,\n            id: `${branch.nume}-${subBranch.nume}`.replace(/\\s+/g, '-').toLowerCase()\n          })),\n          ...newTree.ramuri.slice(branchIndex + 1)\n        ];\n\n        // Update tab with expanded tree\n        tabService.updateTabStatus(activeTab.id, 'completed', { tree: newTree });\n        setActiveTab(tabService.getTab(activeTab.id));\n        console.log('🌳 Tree expanded successfully with fallback sub-branches');\n        return;\n      }\n\n      const expandedData = JSON.parse(jsonMatch[0]);\n      const subBranches = expandedData.ramuri || expandedData.branches || [];\n\n      if (subBranches.length === 0) {\n        throw new Error('No sub-branches generated');\n      }\n\n      console.log('✅ Generated', subBranches.length, 'AI sub-branches for:', branch.nume);\n\n      // Update tree with AI-generated sub-branches\n      const newTree = { ...tree };\n      newTree.ramuri = [\n        ...newTree.ramuri.slice(0, branchIndex + 1),\n        ...subBranches.map(subBranch => ({\n          ...subBranch,\n          isSubBranch: true,\n          parentBranch: branch.nume,\n          level: (branch.level || 0) + 1,\n          id: `${branch.nume}-${subBranch.nume}`.replace(/\\s+/g, '-').toLowerCase()\n        })),\n        ...newTree.ramuri.slice(branchIndex + 1)\n      ];\n\n      // Update tab with expanded tree\n      tabService.updateTabStatus(activeTab.id, 'completed', { tree: newTree });\n      setActiveTab(tabService.getTab(activeTab.id));\n\n      console.log('🌳 Tree expanded successfully with AI sub-branches');\n\n      // Award points for branch expansion\n      const result = gamificationService.awardPoints('BRANCH_EXPANDED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n\n    } catch (error) {\n      console.error('❌ Error expanding branch with AI:', error);\n      setError(t('failedToExpand') || 'Nu s-a putut extinde ramura. Te rugăm să încerci din nou.');\n    } finally {\n      setIsLoading(false);\n    }\n  }, [tree, activeTab, t]);\n\n  const handleLongPress = React.useCallback(async (event, targetInfo) => {\n    // Long press to expand branch and create tree effect\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        await expandBranch(branch, targetInfo.branchData.index);\n      }\n    }\n  }, [tree, expandBranch]);\n\n  // Canvas control functions\n  const zoomCanvas = React.useCallback((factor) => {\n    setCanvasTransform(prev => ({\n      ...prev,\n      scale: Math.max(0.1, Math.min(3, prev.scale * factor))\n    }));\n  }, []);\n\n  const resetCanvasView = React.useCallback(() => {\n    // Center the large canvas properly in viewport\n    const container = document.querySelector('.desktop-tree-view');\n    if (container) {\n      const containerRect = container.getBoundingClientRect();\n      const canvasSize = 3000; // Match CSS canvas size\n\n      setCanvasTransform({\n        x: (containerRect.width - canvasSize) / 2,\n        y: (containerRect.height - canvasSize) / 2,\n        scale: 1\n      });\n    } else {\n      setCanvasTransform({ x: -1000, y: -1000, scale: 1 }); // Fallback centering\n    }\n  }, []);\n\n  // Pan & Drag functionality - using existing state\n  const [dragOffset, setDragOffset] = React.useState({ x: 0, y: 0 });\n\n  const handleMouseDown = React.useCallback((e) => {\n    // Only start drag if not clicking on interactive elements\n    if (e.target.closest('.tree-branch-node') || e.target.closest('.central-topic-node')) {\n      return;\n    }\n\n    setIsDragging(true);\n    setDragStart({ x: e.clientX, y: e.clientY });\n    setDragOffset({ x: canvasTransform.x, y: canvasTransform.y });\n    e.preventDefault();\n  }, [canvasTransform, setIsDragging, setDragStart]);\n\n  const handleMouseMove = React.useCallback((e) => {\n    if (!isDragging) return;\n\n    const deltaX = e.clientX - dragStart.x;\n    const deltaY = e.clientY - dragStart.y;\n\n    setCanvasTransform(prev => ({\n      ...prev,\n      x: dragOffset.x + deltaX,\n      y: dragOffset.y + deltaY\n    }));\n  }, [isDragging, dragStart, dragOffset]);\n\n  const handleMouseUp = React.useCallback(() => {\n    setIsDragging(false);\n  }, []);\n\n  // Touch events for mobile drag\n  const handleTouchStart = React.useCallback((e) => {\n    if (e.target.closest('.tree-branch-node') || e.target.closest('.central-topic-node')) {\n      return;\n    }\n\n    const touch = e.touches[0];\n    setIsDragging(true);\n    setDragStart({ x: touch.clientX, y: touch.clientY });\n    setDragOffset({ x: canvasTransform.x, y: canvasTransform.y });\n    e.preventDefault();\n  }, [canvasTransform, setIsDragging, setDragStart]);\n\n  const handleTouchMove = React.useCallback((e) => {\n    if (!isDragging) return;\n\n    const touch = e.touches[0];\n    const deltaX = touch.clientX - dragStart.x;\n    const deltaY = touch.clientY - dragStart.y;\n\n    setCanvasTransform(prev => ({\n      ...prev,\n      x: dragOffset.x + deltaX,\n      y: dragOffset.y + deltaY\n    }));\n    e.preventDefault();\n  }, [isDragging, dragStart, dragOffset]);\n\n  const handleTouchEnd = React.useCallback(() => {\n    setIsDragging(false);\n  }, []);\n\n  const handleCanvasMouseDown = React.useCallback((e) => {\n    if (e.button === 0) { // Left mouse button\n      setIsDragging(true);\n      setDragStart({ x: e.clientX - canvasTransform.x, y: e.clientY - canvasTransform.y });\n    }\n  }, [canvasTransform]);\n\n  const handleCanvasMouseMove = React.useCallback((e) => {\n    if (isDragging) {\n      setCanvasTransform(prev => ({\n        ...prev,\n        x: e.clientX - dragStart.x,\n        y: e.clientY - dragStart.y\n      }));\n    }\n  }, [isDragging, dragStart]);\n\n  const handleCanvasMouseUp = React.useCallback(() => {\n    setIsDragging(false);\n  }, []);\n\n  const handleCanvasWheel = React.useCallback((e) => {\n    e.preventDefault();\n    const factor = e.deltaY > 0 ? 0.9 : 1.1;\n    zoomCanvas(factor);\n  }, [zoomCanvas]);\n\n  // Handle window resize for mobile detection\n  useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth <= 768);\n    };\n\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  // Initialize TikTok scroll behavior on mobile\n  useEffect(() => {\n    const tiktokContainer = document.getElementById('tiktok-scroll');\n    if (tiktokContainer && isMobile) {\n      // Smooth scroll behavior\n      tiktokContainer.style.scrollBehavior = 'smooth';\n\n      // Optional: Add snap scrolling enhancement\n      let isScrolling = false;\n      const handleScroll = () => {\n        if (!isScrolling) {\n          isScrolling = true;\n          setTimeout(() => {\n            isScrolling = false;\n          }, 150);\n        }\n      };\n\n      tiktokContainer.addEventListener('scroll', handleScroll);\n\n      return () => {\n        tiktokContainer.removeEventListener('scroll', handleScroll);\n      };\n    }\n  }, [isMobile]);\n\n  // Initialize canvas event listeners\n  useEffect(() => {\n    const canvas = document.getElementById('infinite-canvas');\n    if (canvas && !isMobile) {\n      // Apply transform\n      canvas.style.transform = `translate(${canvasTransform.x}px, ${canvasTransform.y}px) scale(${canvasTransform.scale})`;\n\n      canvas.addEventListener('mousedown', handleCanvasMouseDown);\n      canvas.addEventListener('wheel', handleCanvasWheel);\n\n      // Add global mouse events for dragging\n      const handleGlobalMouseMove = (e) => {\n        if (isDragging) {\n          handleCanvasMouseMove(e);\n        }\n      };\n\n      const handleGlobalMouseUp = () => {\n        if (isDragging) {\n          handleCanvasMouseUp();\n        }\n      };\n\n      document.addEventListener('mousemove', handleGlobalMouseMove);\n      document.addEventListener('mouseup', handleGlobalMouseUp);\n\n      return () => {\n        canvas.removeEventListener('mousedown', handleCanvasMouseDown);\n        canvas.removeEventListener('wheel', handleCanvasWheel);\n        document.removeEventListener('mousemove', handleGlobalMouseMove);\n        document.removeEventListener('mouseup', handleGlobalMouseUp);\n      };\n    }\n  }, [canvasTransform, isMobile, isDragging, handleCanvasMouseDown, handleCanvasMouseMove, handleCanvasMouseUp, handleCanvasWheel]);\n\n  // Initialize canvas centering when tree loads\n  useEffect(() => {\n    if (tree && !isMobile) {\n      // Delay to ensure DOM is ready\n      setTimeout(() => {\n        resetCanvasView();\n      }, 100);\n    }\n  }, [tree, isMobile, resetCanvasView]);\n\n  // Error message handler\n  const showError = React.useCallback((message) => {\n    setErrorMessage(message);\n    setTimeout(() => setErrorMessage(null), 5000); // Auto-hide after 5 seconds\n  }, []);\n\n  // Enhanced expand branch with error handling\n  const expandBranchWithErrorHandling = React.useCallback(async (branch, index) => {\n    try {\n      await expandBranch(branch, index);\n    } catch (error) {\n      console.error('Failed to expand branch:', error);\n      showError('⚠️ Nu s-a putut extinde ramura. Te rugăm să încerci din nou.');\n    }\n  }, [expandBranch, showError]);\n\n  // Initialize services and authentication\n  useEffect(() => {\n    const storedUser = localStorage.getItem('user');\n    const bypassSecurity = localStorage.getItem('bypassSecurity');\n\n    if (storedUser || bypassSecurity) {\n      const userData = {\n        id: 'user-1',\n        name: 'User',\n        subscriptionTier: 'premium'\n      };\n      setUser(userData);\n\n      // Award daily login points\n      const result = gamificationService.awardPoints('DAILY_LOGIN');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n\n    // Initialize gesture service\n    if (appRef.current) {\n      gestureService.init(appRef.current, {\n        doubleTap: handleDoubleTap,\n        singleTap: handleSingleTap,\n        longPress: handleLongPress\n      });\n    }\n\n    // Add drag event listeners\n    const handleGlobalMouseMove = (e) => handleMouseMove(e);\n    const handleGlobalMouseUp = (e) => handleMouseUp(e);\n    const handleGlobalTouchMove = (e) => handleTouchMove(e);\n    const handleGlobalTouchEnd = (e) => handleTouchEnd(e);\n\n    document.addEventListener('mousemove', handleGlobalMouseMove);\n    document.addEventListener('mouseup', handleGlobalMouseUp);\n    document.addEventListener('touchmove', handleGlobalTouchMove, { passive: false });\n    document.addEventListener('touchend', handleGlobalTouchEnd);\n\n    return () => {\n      gestureService.destroy();\n      document.removeEventListener('mousemove', handleGlobalMouseMove);\n      document.removeEventListener('mouseup', handleGlobalMouseUp);\n      document.removeEventListener('touchmove', handleGlobalTouchMove);\n      document.removeEventListener('touchend', handleGlobalTouchEnd);\n    };\n  }, [handleDoubleTap, handleSingleTap, handleLongPress, handleMouseMove, handleMouseUp, handleTouchMove, handleTouchEnd]);\n\n  // Initialize gamification UI when user is logged in\n  useEffect(() => {\n    if (user) {\n      const container = document.getElementById('gamification-container');\n      if (container) {\n        // Clear existing content\n        container.innerHTML = '';\n        // Create gamification UI\n        gamificationService.createGamificationUI(container);\n      }\n\n      // Test API connection\n      testConnection().then(isConnected => {\n        console.log('🔌 API Connection Status:', isConnected ? '✅ Connected' : '❌ Failed');\n        if (!isConnected) {\n          console.warn('⚠️ API connection failed. Check your API key and internet connection.');\n        }\n      }).catch(error => {\n        console.error('❌ API connection test error:', error);\n      });\n    }\n  }, [user]);\n\n  // Core API call - using optimized service with tabs\n  const generateKnowledgeTree = async (topicInput, tabId = null) => {\n    let currentTabId = tabId;\n\n    // Create new tab if none provided\n    if (!currentTabId) {\n      try {\n        const newTab = tabService.createTab(topicInput);\n        currentTabId = newTab.id;\n        setActiveTab(newTab);\n        setCurrentView('tree');\n      } catch (error) {\n        setError(error.message);\n        return;\n      }\n    }\n\n    // Update tab status to generating\n    tabService.updateTabStatus(currentTabId, 'generating', { progress: 10 });\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      console.log('🌳 Generating knowledge tree for:', topicInput, 'in tab:', currentTabId);\n\n      // Update progress\n      tabService.updateTabStatus(currentTabId, 'generating', { progress: 30 });\n\n      const treeData = await generateTreeAPI(topicInput, getCurrentLanguage());\n      console.log('✅ Generated tree data:', treeData);\n\n      // Update tab with completed tree\n      tabService.updateTabStatus(currentTabId, 'completed', {\n        tree: treeData,\n        progress: 100\n      });\n\n      // Update active tab if this is the current one\n      if (currentTabId === activeTab?.id) {\n        setActiveTab(tabService.getTab(currentTabId));\n      }\n\n      // Award points for tree generation\n      const result = gamificationService.awardPoints('TREE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (err) {\n      console.error('❌ Error generating tree:', err);\n      tabService.updateTabStatus(currentTabId, 'error');\n      setError(`Failed to generate knowledge tree: ${err.message}. Please try again.`);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n\n\n  // Handle form submission\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    if (topic.trim()) {\n      generateKnowledgeTree(topic.trim());\n      setTopic(''); // Clear input for next topic\n    }\n  };\n\n  // Handle tab changes\n  const handleTabChange = (tab) => {\n    // Clear any existing errors when switching tabs\n    setError(null);\n    setIsLoading(false);\n\n    setActiveTab(tab);\n    if (tab?.tree) {\n      setCurrentView('tree');\n    } else {\n      setCurrentView('input');\n    }\n  };\n\n  // Handle new tab creation\n  const handleNewTab = () => {\n    // Clear any existing errors and loading states\n    setError(null);\n    setIsLoading(false);\n\n    setCurrentView('input');\n    setActiveTab(null);\n    setTopic('');\n  };\n\n  const handleTabArticleAccess = (tab) => {\n    // Clear any existing errors when accessing article\n    setError(null);\n    setIsLoading(false);\n\n    setActiveTab(tab);\n    setCurrentView('article');\n  };\n\n\n\n  // Speech functions\n  const handleSpeechToggle = () => {\n    if (!article?.content) return;\n\n    if (speechService.getStatus().isPlaying) {\n      speechService.toggle();\n    } else {\n      speechService.speak(article.content);\n      // Award points for using speech\n      const result = gamificationService.awardPoints('SPEECH_USED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleSpeechStop = () => {\n    speechService.stop();\n  };\n\n  const handleSpeechRateChange = (rate) => {\n    speechService.setRate(rate);\n  };\n\n  // Export functions\n  const handleExportPDF = () => {\n    if (!article?.title || !article?.content) return;\n    const result = exportService.exportAsPDF(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleExportWord = () => {\n    if (!article?.title || !article?.content) return;\n    const result = exportService.exportAsWord(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleCopyToClipboard = async () => {\n    if (!article?.content) return;\n    const result = await exportService.copyToClipboard(article.content);\n    exportService.showMessage(result.message, result.success ? 'success' : 'error');\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n\n\n  // Navigation functions\n  const goBack = () => {\n    if (currentView === 'article') {\n      setCurrentView('tree');\n      if (activeTab) {\n        tabService.updateTabStatus(activeTab.id, activeTab.status, { article: null });\n        setActiveTab(tabService.getTab(activeTab.id));\n      }\n    } else if (currentView === 'tree') {\n      setCurrentView('input');\n    }\n  };\n\n\n\n  const goHome = () => {\n    setCurrentView('input');\n    setActiveTab(null);\n    setTopic('');\n    // Clear all tabs\n    tabService.clearAllTabs();\n  };\n\n  // Quick login for development\n  const quickLogin = () => {\n    localStorage.setItem('bypassSecurity', 'true');\n    setUser({ id: 'dev-1', name: 'Developer', subscriptionTier: 'premium' });\n  };\n\n  return (\n    <div className=\"app\" ref={appRef}>\n      {/* Header */}\n      <header className=\"app-header\">\n        <div className=\"header-content\">\n          <button onClick={goHome} className=\"logo-text\">\n            {t('appTitle')}\n          </button>\n          <div className=\"header-right\">\n            {user && (\n              <div id=\"gamification-container\" style={{ marginRight: '16px' }}>\n                {/* Gamification UI will be inserted here */}\n              </div>\n            )}\n            <LanguageSwitcher />\n            {!user ? (\n              <button onClick={quickLogin} className=\"btn btn-primary\" style={{ marginLeft: '12px' }}>\n                {t('quickLogin')}\n              </button>\n            ) : (\n              <span style={{ marginLeft: '12px' }}>{t('welcome')}, {user.name}!</span>\n            )}\n          </div>\n        </div>\n      </header>\n\n      {/* Tab Manager */}\n      {user && (\n        <TabManager\n          onTabChange={handleTabChange}\n          onNewTab={handleNewTab}\n          onTabArticleAccess={handleTabArticleAccess}\n        />\n      )}\n\n      {/* Main Content */}\n      <main className=\"main-content\">\n        {error && (\n          <div className=\"error\">\n            ⚠️ {error}\n            <button onClick={() => setError(null)} style={{marginLeft: 'auto', background: 'none', border: 'none', color: 'white', cursor: 'pointer'}}>\n              ✕\n            </button>\n          </div>\n        )}\n\n        {/* Topic Input View */}\n        {currentView === 'input' && (\n          <div className=\"card text-center\">\n            <h1 className=\"title\">{t('appTitle')}</h1>\n            <p className=\"subtitle\">\n              Enter any topic to generate an interactive knowledge tree with AI-powered content.\n            </p>\n\n            {!user ? (\n              <div style={{background: '#f1f5f9', padding: '1rem', borderRadius: '0.5rem', marginBottom: '2rem'}}>\n                <p style={{color: '#334155', marginBottom: '1rem'}}>\n                  {t('loginRequired')}\n                </p>\n                <button onClick={quickLogin} className=\"btn btn-primary\">\n                  {t('quickLoginDev')}\n                </button>\n              </div>\n            ) : (\n              <form onSubmit={handleSubmit}>\n                <div className=\"form-group\">\n                  <input\n                    type=\"text\"\n                    value={topic}\n                    onChange={(e) => setTopic(e.target.value)}\n                    placeholder={t('topicPlaceholder')}\n                    className=\"form-input\"\n                    disabled={isLoading}\n                  />\n                </div>\n                <button\n                  type=\"submit\"\n                  disabled={isLoading || !topic.trim()}\n                  className=\"btn btn-primary\"\n                >\n                  {isLoading ? (\n                    <>\n                      <span className=\"spinner\"></span>\n                      {t('generating')}\n                    </>\n                  ) : (\n                    <>\n                      {t('exploreKnowledge')}\n                    </>\n                  )}\n                </button>\n              </form>\n            )}\n          </div>\n        )}\n\n        {/* Tree View - Desktop: Infinite Tree, Mobile: TikTok Style */}\n        {currentView === 'tree' && tree && (\n          <div className=\"tree-container\">\n            {/* Desktop Tree View - Infinite Mind Map */}\n            <div\n              className={`desktop-tree-view ${isDragging ? 'dragging' : ''}`}\n              onMouseDown={handleMouseDown}\n              onTouchStart={handleTouchStart}\n            >\n              <div\n                className={`infinite-canvas ${isDragging ? 'dragging' : ''}`}\n                id=\"infinite-canvas\"\n                style={{\n                  transform: `translate(${canvasTransform.x}px, ${canvasTransform.y}px) scale(${canvasTransform.scale})`\n                }}\n              >\n                {/* Central Topic Node */}\n                <div className=\"central-topic-node\">\n                  <div className=\"topic-input-center\">\n                    <h2>{tree.tema}</h2>\n                    <button onClick={goBack} className=\"btn btn-secondary back-btn\">\n                      {t('backToTree')}\n                    </button>\n                  </div>\n                </div>\n\n                {/* Branches positioned around center - FIXED POSITIONING */}\n                {tree.ramuri.map((branch, index) => {\n                  const totalBranches = tree.ramuri.length;\n\n                  // Calculate proper spacing to prevent overlaps\n                  const minAngleSpacing = Math.max(25, 360 / Math.max(totalBranches, 8)); // Minimum 25 degrees between branches\n                  const angle = (index * minAngleSpacing) % 360;\n\n                  // Dynamic radius based on number of branches and level\n                  const baseRadius = Math.max(350, totalBranches * 35); // Increase radius with more branches\n                  const levelOffset = (branch.level || 0) * 150; // More spacing between levels\n                  const radius = baseRadius + levelOffset;\n\n                  // Reduced randomness to prevent overlaps\n                  const angleOffset = (Math.sin(index * 1.5) * 8); // Smaller random offset\n                  const finalAngle = angle + angleOffset;\n\n                  // Calculate position with collision detection\n                  let x = Math.cos((finalAngle * Math.PI) / 180) * radius;\n                  let y = Math.sin((finalAngle * Math.PI) / 180) * radius;\n\n                  // Add spiral effect for many branches\n                  if (totalBranches > 12) {\n                    const spiralOffset = index * 15; // Spiral outward\n                    x += Math.cos((spiralOffset * Math.PI) / 180) * (index * 8);\n                    y += Math.sin((spiralOffset * Math.PI) / 180) * (index * 8);\n                  }\n\n                  return (\n                    <div\n                      key={index}\n                      className={`tree-branch-node branch-item ${selectedBranch === branch ? 'selected' : ''}`}\n                      style={{\n                        transform: `translate(${x}px, ${y}px)`,\n                        '--branch-angle': `${finalAngle}deg`\n                      }}\n                      data-index={index}\n                      data-level={branch.level || 0}\n                      data-name={branch.nume}\n                      data-description={branch.descriere}\n                      onClick={() => handleBranchSelect(branch)}\n                    >\n                      {/* Connection Line to Center */}\n                      <div className=\"branch-connection-line\" style={{\n                        transform: `rotate(${finalAngle + 180}deg)`,\n                        width: `${radius}px`\n                      }}></div>\n\n                      <div className=\"branch-content\">\n                        <div className=\"branch-emoji\">{branch.emoji}</div>\n                        <h3 className=\"branch-name\">{branch.nume}</h3>\n                        <p className=\"branch-description\">{branch.descriere}</p>\n\n                        {branch.subcategorii && (\n                          <div className=\"branch-subcategories\">\n                            {branch.subcategorii.slice(0, 2).map((sub, subIndex) => (\n                              <span key={subIndex} className=\"subcategory-tag\">\n                                {sub}\n                              </span>\n                            ))}\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  );\n                })}\n\n                {isLoading && (\n                  <div className=\"loading-overlay\">\n                    <span className=\"spinner\"></span>\n                    <span>{t('loading')}</span>\n                  </div>\n                )}\n              </div>\n\n              {/* Pan & Zoom Controls */}\n              <div className=\"canvas-controls\">\n                <button className=\"control-btn\" onClick={() => zoomCanvas(1.2)}>🔍+</button>\n                <button className=\"control-btn\" onClick={() => zoomCanvas(0.8)}>🔍-</button>\n                <button className=\"control-btn\" onClick={() => resetCanvasView()}>🎯</button>\n              </div>\n            </div>\n\n            {/* Mobile Tree View - TikTok Style */}\n            <div className=\"mobile-tree-view\">\n              <div className=\"tiktok-container\">\n                <div className=\"tiktok-header\">\n                  <h2>{tree.tema}</h2>\n                  <button onClick={goBack} className=\"btn btn-secondary\">\n                    {t('backToTree')}\n                  </button>\n                </div>\n\n                <div className=\"tiktok-scroll-container\" id=\"tiktok-scroll\">\n                  {tree.ramuri.map((branch, index) => (\n                    <div\n                      key={index}\n                      className={`tiktok-branch-card branch-item ${selectedBranch === branch ? 'selected' : ''}`}\n                      data-index={index}\n                      data-name={branch.nume}\n                      data-description={branch.descriere}\n                      onClick={() => handleBranchSelect(branch)}\n                    >\n                      <div className=\"tiktok-card-content\">\n                        <div className=\"branch-emoji-large\">{branch.emoji}</div>\n                        <h3 className=\"branch-name-large\">{branch.nume}</h3>\n                        <p className=\"branch-description-large\">{branch.descriere}</p>\n\n                        {branch.subcategorii && (\n                          <div className=\"tiktok-subcategories\">\n                            {branch.subcategorii.slice(0, 3).map((sub, subIndex) => (\n                              <span key={subIndex} className=\"tiktok-subcategory-tag\">\n                                {sub}\n                              </span>\n                            ))}\n                            {branch.subcategorii.length > 3 && (\n                              <span className=\"tiktok-subcategory-tag more\">\n                                +{branch.subcategorii.length - 3}\n                              </span>\n                            )}\n                          </div>\n                        )}\n\n                        <div className=\"tiktok-gesture-hint\">\n                          <span className=\"tiktok-action-hint\">📖 Swipe down alte crengii</span>\n                          <span className=\"tiktok-action-hint\">🌿 Long-press pentru expansiune</span>\n                        </div>\n                      </div>\n\n                      {/* Level indicator for sub-branches */}\n                      {(branch.level || 0) > 0 && (\n                        <div className=\"tiktok-level-indicator\">\n                          <div className=\"level-badge\">Nivel {branch.level}</div>\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </div>\n\n                {isLoading && (\n                  <div className=\"tiktok-loading\">\n                    <span className=\"spinner\"></span>\n                    <span>{t('loading')}</span>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Article View - Redesigned */}\n        {currentView === 'article' && article && (\n          <div className=\"article-container\">\n            <div className=\"article-card\">\n              <div className=\"article-header\">\n                <button onClick={goBack} className=\"btn btn-secondary article-back-btn\">\n                  {t('backToTree')}\n                </button>\n\n                {/* Article Controls */}\n                <div className=\"article-controls\" style={{\n                  display: 'flex',\n                  gap: '8px',\n                  marginTop: '1rem',\n                  flexWrap: 'wrap'\n                }}>\n                  {/* Speech Controls */}\n                  <div className=\"speech-controls-compact\" style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '8px',\n                    padding: '8px 12px',\n                    background: '#f1f5f9',\n                    borderRadius: '6px',\n                    border: '1px solid #e2e8f0'\n                  }}>\n                    <button\n                      onClick={handleSpeechToggle}\n                      className=\"btn-icon\"\n                      title=\"Play/Pause Speech\"\n                      style={{\n                        background: 'none',\n                        border: 'none',\n                        fontSize: '16px',\n                        cursor: 'pointer',\n                        padding: '4px'\n                      }}\n                    >\n                      {speechService.getStatus().isPlaying ? '⏸️' : '▶️'}\n                    </button>\n                    <button\n                      onClick={handleSpeechStop}\n                      className=\"btn-icon\"\n                      title=\"Stop Speech\"\n                      style={{\n                        background: 'none',\n                        border: 'none',\n                        fontSize: '16px',\n                        cursor: 'pointer',\n                        padding: '4px'\n                      }}\n                    >\n                      ⏹️\n                    </button>\n                    <input\n                      type=\"range\"\n                      min=\"0.5\"\n                      max=\"2\"\n                      step=\"0.1\"\n                      defaultValue=\"1\"\n                      onChange={(e) => handleSpeechRateChange(parseFloat(e.target.value))}\n                      style={{width: '60px'}}\n                      title=\"Speech Speed\"\n                    />\n                    <span style={{fontSize: '12px', color: '#64748b'}}>🗣️</span>\n                  </div>\n\n                  {/* Export Controls */}\n                  <div className=\"export-controls-compact\" style={{\n                    display: 'flex',\n                    gap: '4px'\n                  }}>\n                    <button\n                      onClick={handleCopyToClipboard}\n                      className=\"btn btn-secondary\"\n                      style={{padding: '6px 12px', fontSize: '12px'}}\n                      title=\"Copy to Clipboard\"\n                    >\n                      📋 Copy\n                    </button>\n                    <button\n                      onClick={handleExportPDF}\n                      className=\"btn btn-secondary\"\n                      style={{padding: '6px 12px', fontSize: '12px'}}\n                      title=\"Export as PDF\"\n                    >\n                      📄 PDF\n                    </button>\n                    <button\n                      onClick={handleExportWord}\n                      className=\"btn btn-secondary\"\n                      style={{padding: '6px 12px', fontSize: '12px'}}\n                      title=\"Export as Word\"\n                    >\n                      📝 Word\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"article-title-section\">\n                <h1 className=\"article-title\">{article?.title || 'Loading...'}</h1>\n                <div className=\"article-meta\">\n                  <span className=\"article-topic\">{t('partOf')}: {article?.topic || 'Unknown'}</span>\n                  {article?.flags && article.flags.length > 0 && (\n                    <div className=\"article-flags\">\n                      {t('flags')}:\n                      {article.flags.map((flag, index) => (\n                        <span key={index} className=\"flag-badge\">{flag}</span>\n                      ))}\n                    </div>\n                  )}\n                </div>\n              </div>\n\n              <div className=\"article-content\">\n                {article?.content ? article.content.split('\\n').map((paragraph, index) => (\n                  paragraph.trim() && (\n                    <p key={index} className=\"article-paragraph\">\n                      {paragraph}\n                    </p>\n                  )\n                )) : (\n                  <div className=\"article-loading\">\n                    <span className=\"spinner\"></span>\n                    <p>Loading article content...</p>\n                  </div>\n                )}\n              </div>\n\n              {/* Web Sources Section */}\n              {article?.webSources && article.webSources.length > 0 && (\n                <div className=\"web-sources-section\">\n                  <h3>📚 Surse Web</h3>\n                  <div className=\"sources-grid\">\n                    {article.webSources.map((source, index) => (\n                      <div key={index} className=\"source-item\">\n                        <a href={source.url} target=\"_blank\" rel=\"noopener noreferrer\">\n                          {source.title || `Sursa ${index + 1}`}\n                        </a>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </main>\n    </div>\n  );\n};\n\nexport default OptimizedApp;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,yBAAyB;AAChC,OAAOC,cAAc,IAAIC,eAAe,QAAQ,4BAA4B;AAC5E,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,mBAAmB,MAAM,0CAA0C;AAC1E,SAASC,qBAAqB,IAAIC,eAAe,EAAEC,eAAe,IAAIC,kBAAkB,EAAEC,cAAc,QAAQ,+BAA+B;AAC/I,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,cAAc,EAAEC,kBAAkB,QAAQ,eAAe;;AAElE;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEA,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACzD,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiC,KAAK,EAAEC,QAAQ,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACmC,IAAI,EAAEC,OAAO,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAMqC,MAAM,GAAGnC,MAAM,CAAC,IAAI,CAAC;;EAE3B;EACA,MAAM,CAACoC,eAAe,EAAEC,kBAAkB,CAAC,GAAGvC,QAAQ,CAAC;IAAEwC,CAAC,EAAE,CAAC,IAAI;IAAEC,CAAC,EAAE,CAAC,IAAI;IAAEC,KAAK,EAAE;EAAE,CAAC,CAAC;EACxF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC6C,SAAS,EAAEC,YAAY,CAAC,GAAG9C,QAAQ,CAAC;IAAEwC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAC1D,MAAM,CAACM,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAACiD,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;EAClE,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;;EAEtD;EACA,MAAMqD,IAAI,GAAG,CAAAxB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEwB,IAAI,KAAI,IAAI;EACpC,MAAMC,cAAc,GAAG,CAAAzB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEyB,cAAc,KAAI,IAAI;EACxD,MAAMC,OAAO,GAAG,CAAA1B,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE0B,OAAO,KAAI,IAAI;;EAE1C;EACA,MAAM;IAAEC;EAAE,CAAC,GAAGvC,cAAc,CAAC,CAAC;;EAE9B;EACA,MAAMwC,cAAc,GAAG1D,KAAK,CAAC2D,OAAO,CAAC,MAAM,CACzC;IAAEC,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,SAAS;IAAEC,WAAW,EAAEL,CAAC,CAAC,aAAa;EAAE,CAAC,EAC9D;IAAEG,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAE,UAAU;IAAEC,WAAW,EAAEL,CAAC,CAAC,cAAc;EAAE,CAAC,EACjE;IAAEG,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,MAAM;IAAEC,WAAW,EAAEL,CAAC,CAAC,UAAU;EAAE,CAAC,EACxD;IAAEG,IAAI,EAAE,MAAM;IAAEC,IAAI,EAAE,QAAQ;IAAEC,WAAW,EAAEL,CAAC,CAAC,YAAY;EAAE,CAAC,EAC9D;IAAEG,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,eAAe;IAAEC,WAAW,EAAEL,CAAC,CAAC,UAAU;EAAE,CAAC,EACpE;IAAEG,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,YAAY;IAAEC,WAAW,EAAEL,CAAC,CAAC,UAAU;EAAE,CAAC,EACjE;IAAEG,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAE,UAAU;IAAEC,WAAW,EAAEL,CAAC,CAAC,cAAc;EAAE,CAAC,CAClE,EAAE,CAACA,CAAC,CAAC,CAAC;;EAEP;EACA,MAAMM,wBAAwB,GAAG/D,KAAK,CAACgE,WAAW,CAAC,OAAOC,MAAM,EAAEC,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK;IACnF,IAAI,CAACpC,SAAS,EAAE;IAEhBG,YAAY,CAAC,IAAI,CAAC;;IAElB;IACAnB,UAAU,CAACqD,eAAe,CAACrC,SAAS,CAACsC,EAAE,EAAE,SAAS,EAAE;MAClDb,cAAc,EAAEU,MAAM;MACtBT,OAAO,EAAE;IACX,CAAC,CAAC;IACFzB,YAAY,CAACjB,UAAU,CAACuD,MAAM,CAACvC,SAAS,CAACsC,EAAE,CAAC,CAAC;IAE7C,IAAI;MACFE,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEN,MAAM,CAACO,IAAI,CAAC;MAC7D,MAAMC,WAAW,GAAG,MAAM7D,kBAAkB,CAACkB,SAAS,CAACF,KAAK,EAAEqC,MAAM,EAAEC,KAAK,CAAC;MAE5EI,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEE,WAAW,CAAC;;MAErD;MACA,MAAMC,aAAa,GAAG;QACpBC,KAAK,EAAEF,WAAW,CAACG,KAAK,IAAIH,WAAW,CAACE,KAAK,IAAI,GAAGV,MAAM,CAACO,IAAI,MAAM1C,SAAS,CAACF,KAAK,EAAE;QACtFiD,OAAO,EAAEJ,WAAW,CAACK,QAAQ,IAAIL,WAAW,CAACI,OAAO,IAAI,uBAAuB;QAC/EjD,KAAK,EAAEE,SAAS,CAACF,KAAK;QACtBsC,KAAK,EAAEA,KAAK;QACZa,QAAQ,EAAEN,WAAW,CAACO,OAAO,IAAI,GAAGlD,SAAS,CAACF,KAAK,MAAMqC,MAAM,CAACO,IAAI,EAAE;QACtES,UAAU,EAAER,WAAW,CAACQ,UAAU,IAAI;MACxC,CAAC;;MAED;MACAnE,UAAU,CAACqD,eAAe,CAACrC,SAAS,CAACsC,EAAE,EAAE,WAAW,EAAE;QACpDb,cAAc,EAAEU,MAAM;QACtBT,OAAO,EAAEkB;MACX,CAAC,CAAC;MAEF,MAAMQ,UAAU,GAAGpE,UAAU,CAACuD,MAAM,CAACvC,SAAS,CAACsC,EAAE,CAAC;MAClDrC,YAAY,CAACmD,UAAU,CAAC;MACxBvD,cAAc,CAAC,SAAS,CAAC;;MAEzB;MACA,MAAMwD,MAAM,GAAG3E,mBAAmB,CAAC4E,WAAW,CAAC,mBAAmB,CAAC;MACnE,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5ChF,mBAAmB,CAACiF,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOtD,KAAK,EAAE;MACdoC,OAAO,CAACpC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDC,QAAQ,CAAC,+CAA+C,CAAC;;MAEzD;MACArB,UAAU,CAACqD,eAAe,CAACrC,SAAS,CAACsC,EAAE,EAAE,SAAS,EAAE;QAClDb,cAAc,EAAEU,MAAM;QACtBT,OAAO,EAAE;MACX,CAAC,CAAC;MACFzB,YAAY,CAACjB,UAAU,CAACuD,MAAM,CAACvC,SAAS,CAACsC,EAAE,CAAC,CAAC;IAC/C,CAAC,SAAS;MACRnC,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACH,SAAS,CAAC,CAAC;;EAEf;EACA,MAAM4D,kBAAkB,GAAG1F,KAAK,CAACgE,WAAW,CAAEC,MAAM,IAAK;IACvD,IAAInC,SAAS,EAAE;MACbhB,UAAU,CAACqD,eAAe,CAACrC,SAAS,CAACsC,EAAE,EAAEtC,SAAS,CAAC6D,MAAM,EAAE;QAAEpC,cAAc,EAAEU;MAAO,CAAC,CAAC;MACtFlC,YAAY,CAACjB,UAAU,CAACuD,MAAM,CAACvC,SAAS,CAACsC,EAAE,CAAC,CAAC;IAC/C;EACF,CAAC,EAAE,CAACtC,SAAS,CAAC,CAAC;;EAEf;EACA,MAAM8D,eAAe,GAAG5F,KAAK,CAACgE,WAAW,CAAC,CAAC6B,KAAK,EAAEC,UAAU,KAAK;IAC/D,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,IAAI1C,IAAI,EAAE;MAC5D;MACA,MAAMW,MAAM,GAAGX,IAAI,CAAC2C,MAAM,CAACH,UAAU,CAACE,UAAU,CAACE,KAAK,CAAC;MACvD,IAAIjC,MAAM,EAAE;QACV5D,eAAe,CACbyF,UAAU,CAACf,QAAQ,EACnBrB,cAAc,EACbyC,aAAa,IAAK;UACjB7B,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE4B,aAAa,CAAC;QAC/C,CAAC,EACAA,aAAa,IAAK;UACjBpC,wBAAwB,CAACE,MAAM,EAAEkC,aAAa,CAAC;QACjD,CACF,CAAC;MACH;IACF;EACF,CAAC,EAAE,CAAC7C,IAAI,EAAEI,cAAc,EAAEK,wBAAwB,CAAC,CAAC;EAEpD,MAAMqC,eAAe,GAAGpG,KAAK,CAACgE,WAAW,CAAC,CAAC6B,KAAK,EAAEC,UAAU,KAAK;IAC/D;IACA,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,IAAI1C,IAAI,EAAE;MAC5D,MAAMW,MAAM,GAAGX,IAAI,CAAC2C,MAAM,CAACH,UAAU,CAACE,UAAU,CAACE,KAAK,CAAC;MACvD,IAAIjC,MAAM,EAAE;QACVyB,kBAAkB,CAACzB,MAAM,CAAC;MAC5B;IACF;EACF,CAAC,EAAE,CAACX,IAAI,EAAEoC,kBAAkB,CAAC,CAAC;;EAE9B;EACA,MAAMW,YAAY,GAAGrG,KAAK,CAACgE,WAAW,CAAC,OAAOC,MAAM,EAAEqC,WAAW,KAAK;IACpE,IAAI,CAACxE,SAAS,IAAI,CAACwB,IAAI,EAAE;MACvBnB,QAAQ,CAACsB,CAAC,CAAC,aAAa,CAAC,IAAI,iCAAiC,CAAC;MAC/D;IACF;IAEAxB,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MAAA,IAAAoE,cAAA,EAAAC,qBAAA;MACFlC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEN,MAAM,CAACO,IAAI,EAAE,gCAAgC,CAAC;;MAElF;MACA,MAAMiC,WAAW,GAAGtF,kBAAkB,CAAC,CAAC;MACxC,MAAMuF,MAAM,GAAGD,WAAW,KAAK,IAAI,GAC/B,yDAAyDxC,MAAM,CAACO,IAAI,mBAAmBlB,IAAI,CAACqD,IAAI;AAC1G;AACA,gCAAgC1C,MAAM,CAAC2C,SAAS;AAChD;AACA;AACA,mCAAmC3C,MAAM,CAACO,IAAI;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,GACQ,sDAAsDP,MAAM,CAACO,IAAI,wBAAwBlB,IAAI,CAACqD,IAAI;AAC5G;AACA,2BAA2B1C,MAAM,CAAC2C,SAAS;AAC3C;AACA;AACA,8BAA8B3C,MAAM,CAACO,IAAI;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;;MAEI;MACAF,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;;MAElF;MACA,IAAIsC,aAAa,GAAG,EAAE;MACtB,IAAI;QACFA,aAAa,GAAG,MAAM9F,gBAAgB,CAAC+F,aAAa,CAAC,GAAGxD,IAAI,CAACqD,IAAI,IAAI1C,MAAM,CAACO,IAAI,aAAa,EAAE,CAAC,CAAC;QACjGF,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEsC,aAAa,CAACvB,MAAM,EAAE,SAAS,CAAC;MAC3F,CAAC,CAAC,OAAOyB,QAAQ,EAAE;QACjBzC,OAAO,CAAC0C,IAAI,CAAC,0DAA0D,EAAED,QAAQ,CAAC;QAClFF,aAAa,GAAG,EAAE,CAAC,CAAC;MACtB;;MAEA;MACA,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,+CAA+C,EAAE;QAC5EC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,eAAe,EAAE,kFAAkF;UACnG,cAAc,EAAE,kBAAkB;UAClC,cAAc,EAAElE,MAAM,CAACmE,QAAQ,CAACC,MAAM;UACtC,SAAS,EAAE;QACb,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,KAAK,EAAE,gCAAgC;UACvCC,QAAQ,EAAE,CACR;YACEC,IAAI,EAAE,QAAQ;YACd/C,OAAO,EAAE4B,WAAW,KAAK,IAAI,GACzB,gHAAgH,GAChH;UACN,CAAC,EACD;YACEmB,IAAI,EAAE,MAAM;YACZ/C,OAAO,EAAE6B,MAAM,IAAIG,aAAa,CAACvB,MAAM,GAAG,CAAC,GAAG,gCAAgCuB,aAAa,CAACgB,GAAG,CAACC,CAAC,IAAI,KAAKA,CAAC,CAACnD,KAAK,KAAKmD,CAAC,CAAChE,WAAW,IAAIgE,CAAC,CAACC,OAAO,IAAI,gBAAgB,EAAE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE;UAC3L,CAAC,CACF;UACDC,WAAW,EAAE,GAAG;UAChBC,UAAU,EAAE;QACd,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACjB,QAAQ,CAACkB,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAMnB,QAAQ,CAACoB,IAAI,CAAC,CAAC;QACvC/D,OAAO,CAACpC,KAAK,CAAC,uBAAuB,EAAEkG,SAAS,CAAC;QACjD,MAAM,IAAIE,KAAK,CAAC,uBAAuBrB,QAAQ,CAACtB,MAAM,MAAMyC,SAAS,EAAE,CAAC;MAC1E;MAEA,MAAMG,IAAI,GAAG,MAAMtB,QAAQ,CAACuB,IAAI,CAAC,CAAC;MAClClE,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEgE,IAAI,CAAC;MAEzC,MAAME,YAAY,GAAG,EAAAlC,cAAA,GAAAgC,IAAI,CAACG,OAAO,CAAC,CAAC,CAAC,cAAAnC,cAAA,wBAAAC,qBAAA,GAAfD,cAAA,CAAiBoC,OAAO,cAAAnC,qBAAA,uBAAxBA,qBAAA,CAA0B3B,OAAO,KAAI,EAAE;MAC5DP,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEkE,YAAY,CAAC;;MAE9C;MACA,MAAMG,SAAS,GAAGH,YAAY,CAACI,KAAK,CAAC,aAAa,CAAC;MACnD,IAAI,CAACD,SAAS,EAAE;QACdtE,OAAO,CAAC0C,IAAI,CAAC,+CAA+C,CAAC;QAC7D;QACA,MAAM8B,gBAAgB,GAAG,CACvB;UACEtE,IAAI,EAAE,GAAGP,MAAM,CAACO,IAAI,iBAAiB;UACrCoC,SAAS,EAAE,qDAAqD3C,MAAM,CAACO,IAAI,EAAE;UAC7EuE,KAAK,EAAE,IAAI;UACXC,YAAY,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU;QACrD,CAAC,EACD;UACExE,IAAI,EAAE,GAAGP,MAAM,CAACO,IAAI,uBAAuB;UAC3CoC,SAAS,EAAE,wCAAwC3C,MAAM,CAACO,IAAI,EAAE;UAChEuE,KAAK,EAAE,IAAI;UACXC,YAAY,EAAE,CAAC,SAAS,EAAE,kBAAkB,EAAE,WAAW;QAC3D,CAAC,EACD;UACExE,IAAI,EAAE,GAAGP,MAAM,CAACO,IAAI,qBAAqB;UACzCoC,SAAS,EAAE,2CAA2C3C,MAAM,CAACO,IAAI,EAAE;UACnEuE,KAAK,EAAE,IAAI;UACXC,YAAY,EAAE,CAAC,kBAAkB,EAAE,cAAc,EAAE,WAAW;QAChE,CAAC,CACF;;QAED;QACA,MAAMC,OAAO,GAAG;UAAE,GAAG3F;QAAK,CAAC;QAC3B2F,OAAO,CAAChD,MAAM,GAAG,CACf,GAAGgD,OAAO,CAAChD,MAAM,CAACiD,KAAK,CAAC,CAAC,EAAE5C,WAAW,GAAG,CAAC,CAAC,EAC3C,GAAGwC,gBAAgB,CAACjB,GAAG,CAACsB,SAAS,KAAK;UACpC,GAAGA,SAAS;UACZC,WAAW,EAAE,IAAI;UACjBC,YAAY,EAAEpF,MAAM,CAACO,IAAI;UACzB8E,KAAK,EAAE,CAACrF,MAAM,CAACqF,KAAK,IAAI,CAAC,IAAI,CAAC;UAC9BlF,EAAE,EAAE,GAAGH,MAAM,CAACO,IAAI,IAAI2E,SAAS,CAAC3E,IAAI,EAAE,CAAC+E,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC;QAC1E,CAAC,CAAC,CAAC,EACH,GAAGP,OAAO,CAAChD,MAAM,CAACiD,KAAK,CAAC5C,WAAW,GAAG,CAAC,CAAC,CACzC;;QAED;QACAxF,UAAU,CAACqD,eAAe,CAACrC,SAAS,CAACsC,EAAE,EAAE,WAAW,EAAE;UAAEd,IAAI,EAAE2F;QAAQ,CAAC,CAAC;QACxElH,YAAY,CAACjB,UAAU,CAACuD,MAAM,CAACvC,SAAS,CAACsC,EAAE,CAAC,CAAC;QAC7CE,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;QACvE;MACF;MAEA,MAAMkF,YAAY,GAAGjC,IAAI,CAACkC,KAAK,CAACd,SAAS,CAAC,CAAC,CAAC,CAAC;MAC7C,MAAMe,WAAW,GAAGF,YAAY,CAACxD,MAAM,IAAIwD,YAAY,CAACG,QAAQ,IAAI,EAAE;MAEtE,IAAID,WAAW,CAACrE,MAAM,KAAK,CAAC,EAAE;QAC5B,MAAM,IAAIgD,KAAK,CAAC,2BAA2B,CAAC;MAC9C;MAEAhE,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEoF,WAAW,CAACrE,MAAM,EAAE,sBAAsB,EAAErB,MAAM,CAACO,IAAI,CAAC;;MAEnF;MACA,MAAMyE,OAAO,GAAG;QAAE,GAAG3F;MAAK,CAAC;MAC3B2F,OAAO,CAAChD,MAAM,GAAG,CACf,GAAGgD,OAAO,CAAChD,MAAM,CAACiD,KAAK,CAAC,CAAC,EAAE5C,WAAW,GAAG,CAAC,CAAC,EAC3C,GAAGqD,WAAW,CAAC9B,GAAG,CAACsB,SAAS,KAAK;QAC/B,GAAGA,SAAS;QACZC,WAAW,EAAE,IAAI;QACjBC,YAAY,EAAEpF,MAAM,CAACO,IAAI;QACzB8E,KAAK,EAAE,CAACrF,MAAM,CAACqF,KAAK,IAAI,CAAC,IAAI,CAAC;QAC9BlF,EAAE,EAAE,GAAGH,MAAM,CAACO,IAAI,IAAI2E,SAAS,CAAC3E,IAAI,EAAE,CAAC+E,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC;MAC1E,CAAC,CAAC,CAAC,EACH,GAAGP,OAAO,CAAChD,MAAM,CAACiD,KAAK,CAAC5C,WAAW,GAAG,CAAC,CAAC,CACzC;;MAED;MACAxF,UAAU,CAACqD,eAAe,CAACrC,SAAS,CAACsC,EAAE,EAAE,WAAW,EAAE;QAAEd,IAAI,EAAE2F;MAAQ,CAAC,CAAC;MACxElH,YAAY,CAACjB,UAAU,CAACuD,MAAM,CAACvC,SAAS,CAACsC,EAAE,CAAC,CAAC;MAE7CE,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;;MAEjE;MACA,MAAMY,MAAM,GAAG3E,mBAAmB,CAAC4E,WAAW,CAAC,iBAAiB,CAAC;MACjE,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5ChF,mBAAmB,CAACiF,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IAEF,CAAC,CAAC,OAAOtD,KAAK,EAAE;MACdoC,OAAO,CAACpC,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzDC,QAAQ,CAACsB,CAAC,CAAC,gBAAgB,CAAC,IAAI,2DAA2D,CAAC;IAC9F,CAAC,SAAS;MACRxB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACqB,IAAI,EAAExB,SAAS,EAAE2B,CAAC,CAAC,CAAC;EAExB,MAAMoG,eAAe,GAAG7J,KAAK,CAACgE,WAAW,CAAC,OAAO6B,KAAK,EAAEC,UAAU,KAAK;IACrE;IACA,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,IAAI1C,IAAI,EAAE;MAC5D,MAAMW,MAAM,GAAGX,IAAI,CAAC2C,MAAM,CAACH,UAAU,CAACE,UAAU,CAACE,KAAK,CAAC;MACvD,IAAIjC,MAAM,EAAE;QACV,MAAMoC,YAAY,CAACpC,MAAM,EAAE6B,UAAU,CAACE,UAAU,CAACE,KAAK,CAAC;MACzD;IACF;EACF,CAAC,EAAE,CAAC5C,IAAI,EAAE+C,YAAY,CAAC,CAAC;;EAExB;EACA,MAAMyD,UAAU,GAAG9J,KAAK,CAACgE,WAAW,CAAE+F,MAAM,IAAK;IAC/CvH,kBAAkB,CAACwH,IAAI,KAAK;MAC1B,GAAGA,IAAI;MACPrH,KAAK,EAAEsH,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEH,IAAI,CAACrH,KAAK,GAAGoH,MAAM,CAAC;IACvD,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,eAAe,GAAGpK,KAAK,CAACgE,WAAW,CAAC,MAAM;IAC9C;IACA,MAAMqG,SAAS,GAAGC,QAAQ,CAACC,aAAa,CAAC,oBAAoB,CAAC;IAC9D,IAAIF,SAAS,EAAE;MACb,MAAMG,aAAa,GAAGH,SAAS,CAACI,qBAAqB,CAAC,CAAC;MACvD,MAAMC,UAAU,GAAG,IAAI,CAAC,CAAC;;MAEzBlI,kBAAkB,CAAC;QACjBC,CAAC,EAAE,CAAC+H,aAAa,CAACG,KAAK,GAAGD,UAAU,IAAI,CAAC;QACzChI,CAAC,EAAE,CAAC8H,aAAa,CAACI,MAAM,GAAGF,UAAU,IAAI,CAAC;QAC1C/H,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC,MAAM;MACLH,kBAAkB,CAAC;QAAEC,CAAC,EAAE,CAAC,IAAI;QAAEC,CAAC,EAAE,CAAC,IAAI;QAAEC,KAAK,EAAE;MAAE,CAAC,CAAC,CAAC,CAAC;IACxD;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM,CAACkI,UAAU,EAAEC,aAAa,CAAC,GAAG9K,KAAK,CAACC,QAAQ,CAAC;IAAEwC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAElE,MAAMqI,eAAe,GAAG/K,KAAK,CAACgE,WAAW,CAAEgH,CAAC,IAAK;IAC/C;IACA,IAAIA,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,mBAAmB,CAAC,IAAIF,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,qBAAqB,CAAC,EAAE;MACpF;IACF;IAEArI,aAAa,CAAC,IAAI,CAAC;IACnBE,YAAY,CAAC;MAAEN,CAAC,EAAEuI,CAAC,CAACG,OAAO;MAAEzI,CAAC,EAAEsI,CAAC,CAACI;IAAQ,CAAC,CAAC;IAC5CN,aAAa,CAAC;MAAErI,CAAC,EAAEF,eAAe,CAACE,CAAC;MAAEC,CAAC,EAAEH,eAAe,CAACG;IAAE,CAAC,CAAC;IAC7DsI,CAAC,CAACK,cAAc,CAAC,CAAC;EACpB,CAAC,EAAE,CAAC9I,eAAe,EAAEM,aAAa,EAAEE,YAAY,CAAC,CAAC;EAElD,MAAMuI,eAAe,GAAGtL,KAAK,CAACgE,WAAW,CAAEgH,CAAC,IAAK;IAC/C,IAAI,CAACpI,UAAU,EAAE;IAEjB,MAAM2I,MAAM,GAAGP,CAAC,CAACG,OAAO,GAAGrI,SAAS,CAACL,CAAC;IACtC,MAAM+I,MAAM,GAAGR,CAAC,CAACI,OAAO,GAAGtI,SAAS,CAACJ,CAAC;IAEtCF,kBAAkB,CAACwH,IAAI,KAAK;MAC1B,GAAGA,IAAI;MACPvH,CAAC,EAAEoI,UAAU,CAACpI,CAAC,GAAG8I,MAAM;MACxB7I,CAAC,EAAEmI,UAAU,CAACnI,CAAC,GAAG8I;IACpB,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAAC5I,UAAU,EAAEE,SAAS,EAAE+H,UAAU,CAAC,CAAC;EAEvC,MAAMY,aAAa,GAAGzL,KAAK,CAACgE,WAAW,CAAC,MAAM;IAC5CnB,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM6I,gBAAgB,GAAG1L,KAAK,CAACgE,WAAW,CAAEgH,CAAC,IAAK;IAChD,IAAIA,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,mBAAmB,CAAC,IAAIF,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,qBAAqB,CAAC,EAAE;MACpF;IACF;IAEA,MAAMS,KAAK,GAAGX,CAAC,CAACY,OAAO,CAAC,CAAC,CAAC;IAC1B/I,aAAa,CAAC,IAAI,CAAC;IACnBE,YAAY,CAAC;MAAEN,CAAC,EAAEkJ,KAAK,CAACR,OAAO;MAAEzI,CAAC,EAAEiJ,KAAK,CAACP;IAAQ,CAAC,CAAC;IACpDN,aAAa,CAAC;MAAErI,CAAC,EAAEF,eAAe,CAACE,CAAC;MAAEC,CAAC,EAAEH,eAAe,CAACG;IAAE,CAAC,CAAC;IAC7DsI,CAAC,CAACK,cAAc,CAAC,CAAC;EACpB,CAAC,EAAE,CAAC9I,eAAe,EAAEM,aAAa,EAAEE,YAAY,CAAC,CAAC;EAElD,MAAM8I,eAAe,GAAG7L,KAAK,CAACgE,WAAW,CAAEgH,CAAC,IAAK;IAC/C,IAAI,CAACpI,UAAU,EAAE;IAEjB,MAAM+I,KAAK,GAAGX,CAAC,CAACY,OAAO,CAAC,CAAC,CAAC;IAC1B,MAAML,MAAM,GAAGI,KAAK,CAACR,OAAO,GAAGrI,SAAS,CAACL,CAAC;IAC1C,MAAM+I,MAAM,GAAGG,KAAK,CAACP,OAAO,GAAGtI,SAAS,CAACJ,CAAC;IAE1CF,kBAAkB,CAACwH,IAAI,KAAK;MAC1B,GAAGA,IAAI;MACPvH,CAAC,EAAEoI,UAAU,CAACpI,CAAC,GAAG8I,MAAM;MACxB7I,CAAC,EAAEmI,UAAU,CAACnI,CAAC,GAAG8I;IACpB,CAAC,CAAC,CAAC;IACHR,CAAC,CAACK,cAAc,CAAC,CAAC;EACpB,CAAC,EAAE,CAACzI,UAAU,EAAEE,SAAS,EAAE+H,UAAU,CAAC,CAAC;EAEvC,MAAMiB,cAAc,GAAG9L,KAAK,CAACgE,WAAW,CAAC,MAAM;IAC7CnB,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMkJ,qBAAqB,GAAG/L,KAAK,CAACgE,WAAW,CAAEgH,CAAC,IAAK;IACrD,IAAIA,CAAC,CAACgB,MAAM,KAAK,CAAC,EAAE;MAAE;MACpBnJ,aAAa,CAAC,IAAI,CAAC;MACnBE,YAAY,CAAC;QAAEN,CAAC,EAAEuI,CAAC,CAACG,OAAO,GAAG5I,eAAe,CAACE,CAAC;QAAEC,CAAC,EAAEsI,CAAC,CAACI,OAAO,GAAG7I,eAAe,CAACG;MAAE,CAAC,CAAC;IACtF;EACF,CAAC,EAAE,CAACH,eAAe,CAAC,CAAC;EAErB,MAAM0J,qBAAqB,GAAGjM,KAAK,CAACgE,WAAW,CAAEgH,CAAC,IAAK;IACrD,IAAIpI,UAAU,EAAE;MACdJ,kBAAkB,CAACwH,IAAI,KAAK;QAC1B,GAAGA,IAAI;QACPvH,CAAC,EAAEuI,CAAC,CAACG,OAAO,GAAGrI,SAAS,CAACL,CAAC;QAC1BC,CAAC,EAAEsI,CAAC,CAACI,OAAO,GAAGtI,SAAS,CAACJ;MAC3B,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACE,UAAU,EAAEE,SAAS,CAAC,CAAC;EAE3B,MAAMoJ,mBAAmB,GAAGlM,KAAK,CAACgE,WAAW,CAAC,MAAM;IAClDnB,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMsJ,iBAAiB,GAAGnM,KAAK,CAACgE,WAAW,CAAEgH,CAAC,IAAK;IACjDA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClB,MAAMtB,MAAM,GAAGiB,CAAC,CAACQ,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;IACvC1B,UAAU,CAACC,MAAM,CAAC;EACpB,CAAC,EAAE,CAACD,UAAU,CAAC,CAAC;;EAEhB;EACA5J,SAAS,CAAC,MAAM;IACd,MAAMkM,YAAY,GAAGA,CAAA,KAAM;MACzBnJ,WAAW,CAACC,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;IACvC,CAAC;IAEDD,MAAM,CAACmJ,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAMlJ,MAAM,CAACoJ,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAlM,SAAS,CAAC,MAAM;IACd,MAAMqM,eAAe,GAAGjC,QAAQ,CAACkC,cAAc,CAAC,eAAe,CAAC;IAChE,IAAID,eAAe,IAAIvJ,QAAQ,EAAE;MAC/B;MACAuJ,eAAe,CAACE,KAAK,CAACC,cAAc,GAAG,QAAQ;;MAE/C;MACA,IAAIC,WAAW,GAAG,KAAK;MACvB,MAAMC,YAAY,GAAGA,CAAA,KAAM;QACzB,IAAI,CAACD,WAAW,EAAE;UAChBA,WAAW,GAAG,IAAI;UAClBE,UAAU,CAAC,MAAM;YACfF,WAAW,GAAG,KAAK;UACrB,CAAC,EAAE,GAAG,CAAC;QACT;MACF,CAAC;MAEDJ,eAAe,CAACF,gBAAgB,CAAC,QAAQ,EAAEO,YAAY,CAAC;MAExD,OAAO,MAAM;QACXL,eAAe,CAACD,mBAAmB,CAAC,QAAQ,EAAEM,YAAY,CAAC;MAC7D,CAAC;IACH;EACF,CAAC,EAAE,CAAC5J,QAAQ,CAAC,CAAC;;EAEd;EACA9C,SAAS,CAAC,MAAM;IACd,MAAM4M,MAAM,GAAGxC,QAAQ,CAACkC,cAAc,CAAC,iBAAiB,CAAC;IACzD,IAAIM,MAAM,IAAI,CAAC9J,QAAQ,EAAE;MACvB;MACA8J,MAAM,CAACL,KAAK,CAACM,SAAS,GAAG,aAAaxK,eAAe,CAACE,CAAC,OAAOF,eAAe,CAACG,CAAC,aAAaH,eAAe,CAACI,KAAK,GAAG;MAEpHmK,MAAM,CAACT,gBAAgB,CAAC,WAAW,EAAEN,qBAAqB,CAAC;MAC3De,MAAM,CAACT,gBAAgB,CAAC,OAAO,EAAEF,iBAAiB,CAAC;;MAEnD;MACA,MAAMa,qBAAqB,GAAIhC,CAAC,IAAK;QACnC,IAAIpI,UAAU,EAAE;UACdqJ,qBAAqB,CAACjB,CAAC,CAAC;QAC1B;MACF,CAAC;MAED,MAAMiC,mBAAmB,GAAGA,CAAA,KAAM;QAChC,IAAIrK,UAAU,EAAE;UACdsJ,mBAAmB,CAAC,CAAC;QACvB;MACF,CAAC;MAED5B,QAAQ,CAAC+B,gBAAgB,CAAC,WAAW,EAAEW,qBAAqB,CAAC;MAC7D1C,QAAQ,CAAC+B,gBAAgB,CAAC,SAAS,EAAEY,mBAAmB,CAAC;MAEzD,OAAO,MAAM;QACXH,MAAM,CAACR,mBAAmB,CAAC,WAAW,EAAEP,qBAAqB,CAAC;QAC9De,MAAM,CAACR,mBAAmB,CAAC,OAAO,EAAEH,iBAAiB,CAAC;QACtD7B,QAAQ,CAACgC,mBAAmB,CAAC,WAAW,EAAEU,qBAAqB,CAAC;QAChE1C,QAAQ,CAACgC,mBAAmB,CAAC,SAAS,EAAEW,mBAAmB,CAAC;MAC9D,CAAC;IACH;EACF,CAAC,EAAE,CAAC1K,eAAe,EAAES,QAAQ,EAAEJ,UAAU,EAAEmJ,qBAAqB,EAAEE,qBAAqB,EAAEC,mBAAmB,EAAEC,iBAAiB,CAAC,CAAC;;EAEjI;EACAjM,SAAS,CAAC,MAAM;IACd,IAAIoD,IAAI,IAAI,CAACN,QAAQ,EAAE;MACrB;MACA6J,UAAU,CAAC,MAAM;QACfzC,eAAe,CAAC,CAAC;MACnB,CAAC,EAAE,GAAG,CAAC;IACT;EACF,CAAC,EAAE,CAAC9G,IAAI,EAAEN,QAAQ,EAAEoH,eAAe,CAAC,CAAC;;EAErC;EACA,MAAM8C,SAAS,GAAGlN,KAAK,CAACgE,WAAW,CAAE2E,OAAO,IAAK;IAC/CtF,eAAe,CAACsF,OAAO,CAAC;IACxBkE,UAAU,CAAC,MAAMxJ,eAAe,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACjD,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM8J,6BAA6B,GAAGnN,KAAK,CAACgE,WAAW,CAAC,OAAOC,MAAM,EAAEiC,KAAK,KAAK;IAC/E,IAAI;MACF,MAAMG,YAAY,CAACpC,MAAM,EAAEiC,KAAK,CAAC;IACnC,CAAC,CAAC,OAAOhE,KAAK,EAAE;MACdoC,OAAO,CAACpC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDgL,SAAS,CAAC,8DAA8D,CAAC;IAC3E;EACF,CAAC,EAAE,CAAC7G,YAAY,EAAE6G,SAAS,CAAC,CAAC;;EAE7B;EACAhN,SAAS,CAAC,MAAM;IACd,MAAMkN,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAC/C,MAAMC,cAAc,GAAGF,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAE7D,IAAIF,UAAU,IAAIG,cAAc,EAAE;MAChC,MAAMC,QAAQ,GAAG;QACfpJ,EAAE,EAAE,QAAQ;QACZP,IAAI,EAAE,MAAM;QACZ4J,gBAAgB,EAAE;MACpB,CAAC;MACDpL,OAAO,CAACmL,QAAQ,CAAC;;MAEjB;MACA,MAAMrI,MAAM,GAAG3E,mBAAmB,CAAC4E,WAAW,CAAC,aAAa,CAAC;MAC7D,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5ChF,mBAAmB,CAACiF,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;;IAEA;IACA,IAAIlD,MAAM,CAACoL,OAAO,EAAE;MAClBtN,cAAc,CAACuN,IAAI,CAACrL,MAAM,CAACoL,OAAO,EAAE;QAClCE,SAAS,EAAEhI,eAAe;QAC1BiI,SAAS,EAAEzH,eAAe;QAC1B0H,SAAS,EAAEjE;MACb,CAAC,CAAC;IACJ;;IAEA;IACA,MAAMmD,qBAAqB,GAAIhC,CAAC,IAAKM,eAAe,CAACN,CAAC,CAAC;IACvD,MAAMiC,mBAAmB,GAAIjC,CAAC,IAAKS,aAAa,CAACT,CAAC,CAAC;IACnD,MAAM+C,qBAAqB,GAAI/C,CAAC,IAAKa,eAAe,CAACb,CAAC,CAAC;IACvD,MAAMgD,oBAAoB,GAAIhD,CAAC,IAAKc,cAAc,CAACd,CAAC,CAAC;IAErDV,QAAQ,CAAC+B,gBAAgB,CAAC,WAAW,EAAEW,qBAAqB,CAAC;IAC7D1C,QAAQ,CAAC+B,gBAAgB,CAAC,SAAS,EAAEY,mBAAmB,CAAC;IACzD3C,QAAQ,CAAC+B,gBAAgB,CAAC,WAAW,EAAE0B,qBAAqB,EAAE;MAAEE,OAAO,EAAE;IAAM,CAAC,CAAC;IACjF3D,QAAQ,CAAC+B,gBAAgB,CAAC,UAAU,EAAE2B,oBAAoB,CAAC;IAE3D,OAAO,MAAM;MACX5N,cAAc,CAAC8N,OAAO,CAAC,CAAC;MACxB5D,QAAQ,CAACgC,mBAAmB,CAAC,WAAW,EAAEU,qBAAqB,CAAC;MAChE1C,QAAQ,CAACgC,mBAAmB,CAAC,SAAS,EAAEW,mBAAmB,CAAC;MAC5D3C,QAAQ,CAACgC,mBAAmB,CAAC,WAAW,EAAEyB,qBAAqB,CAAC;MAChEzD,QAAQ,CAACgC,mBAAmB,CAAC,UAAU,EAAE0B,oBAAoB,CAAC;IAChE,CAAC;EACH,CAAC,EAAE,CAACpI,eAAe,EAAEQ,eAAe,EAAEyD,eAAe,EAAEyB,eAAe,EAAEG,aAAa,EAAEI,eAAe,EAAEC,cAAc,CAAC,CAAC;;EAExH;EACA5L,SAAS,CAAC,MAAM;IACd,IAAIkC,IAAI,EAAE;MACR,MAAMiI,SAAS,GAAGC,QAAQ,CAACkC,cAAc,CAAC,wBAAwB,CAAC;MACnE,IAAInC,SAAS,EAAE;QACb;QACAA,SAAS,CAAC8D,SAAS,GAAG,EAAE;QACxB;QACA3N,mBAAmB,CAAC4N,oBAAoB,CAAC/D,SAAS,CAAC;MACrD;;MAEA;MACAxJ,cAAc,CAAC,CAAC,CAACwN,IAAI,CAACC,WAAW,IAAI;QACnChK,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE+J,WAAW,GAAG,aAAa,GAAG,UAAU,CAAC;QAClF,IAAI,CAACA,WAAW,EAAE;UAChBhK,OAAO,CAAC0C,IAAI,CAAC,uEAAuE,CAAC;QACvF;MACF,CAAC,CAAC,CAACuH,KAAK,CAACrM,KAAK,IAAI;QAChBoC,OAAO,CAACpC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACE,IAAI,CAAC,CAAC;;EAEV;EACA,MAAM3B,qBAAqB,GAAG,MAAAA,CAAO+N,UAAU,EAAEC,KAAK,GAAG,IAAI,KAAK;IAChE,IAAIC,YAAY,GAAGD,KAAK;;IAExB;IACA,IAAI,CAACC,YAAY,EAAE;MACjB,IAAI;QACF,MAAMC,MAAM,GAAG7N,UAAU,CAAC8N,SAAS,CAACJ,UAAU,CAAC;QAC/CE,YAAY,GAAGC,MAAM,CAACvK,EAAE;QACxBrC,YAAY,CAAC4M,MAAM,CAAC;QACpBhN,cAAc,CAAC,MAAM,CAAC;MACxB,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,QAAQ,CAACD,KAAK,CAACyG,OAAO,CAAC;QACvB;MACF;IACF;;IAEA;IACA7H,UAAU,CAACqD,eAAe,CAACuK,YAAY,EAAE,YAAY,EAAE;MAAEG,QAAQ,EAAE;IAAG,CAAC,CAAC;IACxE5M,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACFmC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEiK,UAAU,EAAE,SAAS,EAAEE,YAAY,CAAC;;MAErF;MACA5N,UAAU,CAACqD,eAAe,CAACuK,YAAY,EAAE,YAAY,EAAE;QAAEG,QAAQ,EAAE;MAAG,CAAC,CAAC;MAExE,MAAMC,QAAQ,GAAG,MAAMpO,eAAe,CAAC8N,UAAU,EAAErN,kBAAkB,CAAC,CAAC,CAAC;MACxEmD,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEuK,QAAQ,CAAC;;MAE/C;MACAhO,UAAU,CAACqD,eAAe,CAACuK,YAAY,EAAE,WAAW,EAAE;QACpDpL,IAAI,EAAEwL,QAAQ;QACdD,QAAQ,EAAE;MACZ,CAAC,CAAC;;MAEF;MACA,IAAIH,YAAY,MAAK5M,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEsC,EAAE,GAAE;QAClCrC,YAAY,CAACjB,UAAU,CAACuD,MAAM,CAACqK,YAAY,CAAC,CAAC;MAC/C;;MAEA;MACA,MAAMvJ,MAAM,GAAG3E,mBAAmB,CAAC4E,WAAW,CAAC,gBAAgB,CAAC;MAChE,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5ChF,mBAAmB,CAACiF,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOuJ,GAAG,EAAE;MACZzK,OAAO,CAACpC,KAAK,CAAC,0BAA0B,EAAE6M,GAAG,CAAC;MAC9CjO,UAAU,CAACqD,eAAe,CAACuK,YAAY,EAAE,OAAO,CAAC;MACjDvM,QAAQ,CAAC,sCAAsC4M,GAAG,CAACpG,OAAO,qBAAqB,CAAC;IAClF,CAAC,SAAS;MACR1G,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAID;EACA,MAAM+M,YAAY,GAAIhE,CAAC,IAAK;IAC1BA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClB,IAAIzJ,KAAK,CAACqN,IAAI,CAAC,CAAC,EAAE;MAChBxO,qBAAqB,CAACmB,KAAK,CAACqN,IAAI,CAAC,CAAC,CAAC;MACnCpN,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB;EACF,CAAC;;EAED;EACA,MAAMqN,eAAe,GAAIC,GAAG,IAAK;IAC/B;IACAhN,QAAQ,CAAC,IAAI,CAAC;IACdF,YAAY,CAAC,KAAK,CAAC;IAEnBF,YAAY,CAACoN,GAAG,CAAC;IACjB,IAAIA,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAE7L,IAAI,EAAE;MACb3B,cAAc,CAAC,MAAM,CAAC;IACxB,CAAC,MAAM;MACLA,cAAc,CAAC,OAAO,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMyN,YAAY,GAAGA,CAAA,KAAM;IACzB;IACAjN,QAAQ,CAAC,IAAI,CAAC;IACdF,YAAY,CAAC,KAAK,CAAC;IAEnBN,cAAc,CAAC,OAAO,CAAC;IACvBI,YAAY,CAAC,IAAI,CAAC;IAClBF,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAMwN,sBAAsB,GAAIF,GAAG,IAAK;IACtC;IACAhN,QAAQ,CAAC,IAAI,CAAC;IACdF,YAAY,CAAC,KAAK,CAAC;IAEnBF,YAAY,CAACoN,GAAG,CAAC;IACjBxN,cAAc,CAAC,SAAS,CAAC;EAC3B,CAAC;;EAID;EACA,MAAM2N,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI,EAAC9L,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEqB,OAAO,GAAE;IAEvB,IAAIvE,aAAa,CAACiP,SAAS,CAAC,CAAC,CAACC,SAAS,EAAE;MACvClP,aAAa,CAACmP,MAAM,CAAC,CAAC;IACxB,CAAC,MAAM;MACLnP,aAAa,CAACoP,KAAK,CAAClM,OAAO,CAACqB,OAAO,CAAC;MACpC;MACA,MAAMM,MAAM,GAAG3E,mBAAmB,CAAC4E,WAAW,CAAC,aAAa,CAAC;MAC7D,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5ChF,mBAAmB,CAACiF,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAMmK,gBAAgB,GAAGA,CAAA,KAAM;IAC7BrP,aAAa,CAACsP,IAAI,CAAC,CAAC;EACtB,CAAC;EAED,MAAMC,sBAAsB,GAAIC,IAAI,IAAK;IACvCxP,aAAa,CAACyP,OAAO,CAACD,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,EAACxM,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEmB,KAAK,KAAI,EAACnB,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEqB,OAAO,GAAE;IAC1C,MAAMM,MAAM,GAAG5E,aAAa,CAAC0P,WAAW,CAACzM,OAAO,EAAE,GAAGA,OAAO,CAACmB,KAAK,CAAC4E,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,EAAE,CAAC;IACnG,IAAIpE,MAAM,CAAC+K,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAG3P,mBAAmB,CAAC4E,WAAW,CAAC,aAAa,CAAC;MAChE,IAAI+K,SAAS,CAAC9K,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxC6K,SAAS,CAAC9K,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/ChF,mBAAmB,CAACiF,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAM4K,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,EAAC5M,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEmB,KAAK,KAAI,EAACnB,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEqB,OAAO,GAAE;IAC1C,MAAMM,MAAM,GAAG5E,aAAa,CAAC8P,YAAY,CAAC7M,OAAO,EAAE,GAAGA,OAAO,CAACmB,KAAK,CAAC4E,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,EAAE,CAAC;IACpG,IAAIpE,MAAM,CAAC+K,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAG3P,mBAAmB,CAAC4E,WAAW,CAAC,aAAa,CAAC;MAChE,IAAI+K,SAAS,CAAC9K,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxC6K,SAAS,CAAC9K,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/ChF,mBAAmB,CAACiF,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAM8K,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI,EAAC9M,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEqB,OAAO,GAAE;IACvB,MAAMM,MAAM,GAAG,MAAM5E,aAAa,CAACgQ,eAAe,CAAC/M,OAAO,CAACqB,OAAO,CAAC;IACnEtE,aAAa,CAACiQ,WAAW,CAACrL,MAAM,CAACwD,OAAO,EAAExD,MAAM,CAAC+K,OAAO,GAAG,SAAS,GAAG,OAAO,CAAC;IAC/E,IAAI/K,MAAM,CAAC+K,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAG3P,mBAAmB,CAAC4E,WAAW,CAAC,aAAa,CAAC;MAChE,IAAI+K,SAAS,CAAC9K,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxC6K,SAAS,CAAC9K,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/ChF,mBAAmB,CAACiF,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;;EAID;EACA,MAAMiL,MAAM,GAAGA,CAAA,KAAM;IACnB,IAAI/O,WAAW,KAAK,SAAS,EAAE;MAC7BC,cAAc,CAAC,MAAM,CAAC;MACtB,IAAIG,SAAS,EAAE;QACbhB,UAAU,CAACqD,eAAe,CAACrC,SAAS,CAACsC,EAAE,EAAEtC,SAAS,CAAC6D,MAAM,EAAE;UAAEnC,OAAO,EAAE;QAAK,CAAC,CAAC;QAC7EzB,YAAY,CAACjB,UAAU,CAACuD,MAAM,CAACvC,SAAS,CAACsC,EAAE,CAAC,CAAC;MAC/C;IACF,CAAC,MAAM,IAAI1C,WAAW,KAAK,MAAM,EAAE;MACjCC,cAAc,CAAC,OAAO,CAAC;IACzB;EACF,CAAC;EAID,MAAM+O,MAAM,GAAGA,CAAA,KAAM;IACnB/O,cAAc,CAAC,OAAO,CAAC;IACvBI,YAAY,CAAC,IAAI,CAAC;IAClBF,QAAQ,CAAC,EAAE,CAAC;IACZ;IACAf,UAAU,CAAC6P,YAAY,CAAC,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBvD,YAAY,CAACwD,OAAO,CAAC,gBAAgB,EAAE,MAAM,CAAC;IAC9CxO,OAAO,CAAC;MAAE+B,EAAE,EAAE,OAAO;MAAEP,IAAI,EAAE,WAAW;MAAE4J,gBAAgB,EAAE;IAAU,CAAC,CAAC;EAC1E,CAAC;EAED,oBACEpM,OAAA;IAAKyP,SAAS,EAAC,KAAK;IAACC,GAAG,EAAEzO,MAAO;IAAA0O,QAAA,gBAE/B3P,OAAA;MAAQyP,SAAS,EAAC,YAAY;MAAAE,QAAA,eAC5B3P,OAAA;QAAKyP,SAAS,EAAC,gBAAgB;QAAAE,QAAA,gBAC7B3P,OAAA;UAAQ4P,OAAO,EAAEP,MAAO;UAACI,SAAS,EAAC,WAAW;UAAAE,QAAA,EAC3CvN,CAAC,CAAC,UAAU;QAAC;UAAAyN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACThQ,OAAA;UAAKyP,SAAS,EAAC,cAAc;UAAAE,QAAA,GAC1B5O,IAAI,iBACHf,OAAA;YAAK+C,EAAE,EAAC,wBAAwB;YAACqI,KAAK,EAAE;cAAE6E,WAAW,EAAE;YAAO;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE3D,CACN,eACDhQ,OAAA,CAACJ,gBAAgB;YAAAiQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACnB,CAACjP,IAAI,gBACJf,OAAA;YAAQ4P,OAAO,EAAEL,UAAW;YAACE,SAAS,EAAC,iBAAiB;YAACrE,KAAK,EAAE;cAAE8E,UAAU,EAAE;YAAO,CAAE;YAAAP,QAAA,EACpFvN,CAAC,CAAC,YAAY;UAAC;YAAAyN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,gBAEThQ,OAAA;YAAMoL,KAAK,EAAE;cAAE8E,UAAU,EAAE;YAAO,CAAE;YAAAP,QAAA,GAAEvN,CAAC,CAAC,SAAS,CAAC,EAAC,IAAE,EAACrB,IAAI,CAACyB,IAAI,EAAC,GAAC;UAAA;YAAAqN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACxE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAGRjP,IAAI,iBACHf,OAAA,CAACL,UAAU;MACTwQ,WAAW,EAAEtC,eAAgB;MAC7BuC,QAAQ,EAAErC,YAAa;MACvBsC,kBAAkB,EAAErC;IAAuB;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CACF,eAGDhQ,OAAA;MAAMyP,SAAS,EAAC,cAAc;MAAAE,QAAA,GAC3B9O,KAAK,iBACJb,OAAA;QAAKyP,SAAS,EAAC,OAAO;QAAAE,QAAA,GAAC,eAClB,EAAC9O,KAAK,eACTb,OAAA;UAAQ4P,OAAO,EAAEA,CAAA,KAAM9O,QAAQ,CAAC,IAAI,CAAE;UAACsK,KAAK,EAAE;YAAC8E,UAAU,EAAE,MAAM;YAAEI,UAAU,EAAE,MAAM;YAAEC,MAAM,EAAE,MAAM;YAAEC,KAAK,EAAE,OAAO;YAAEC,MAAM,EAAE;UAAS,CAAE;UAAAd,QAAA,EAAC;QAE3I;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAGA3P,WAAW,KAAK,OAAO,iBACtBL,OAAA;QAAKyP,SAAS,EAAC,kBAAkB;QAAAE,QAAA,gBAC/B3P,OAAA;UAAIyP,SAAS,EAAC,OAAO;UAAAE,QAAA,EAAEvN,CAAC,CAAC,UAAU;QAAC;UAAAyN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1ChQ,OAAA;UAAGyP,SAAS,EAAC,UAAU;UAAAE,QAAA,EAAC;QAExB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAEH,CAACjP,IAAI,gBACJf,OAAA;UAAKoL,KAAK,EAAE;YAACkF,UAAU,EAAE,SAAS;YAAEI,OAAO,EAAE,MAAM;YAAEC,YAAY,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAM,CAAE;UAAAjB,QAAA,gBACjG3P,OAAA;YAAGoL,KAAK,EAAE;cAACoF,KAAK,EAAE,SAAS;cAAEI,YAAY,EAAE;YAAM,CAAE;YAAAjB,QAAA,EAChDvN,CAAC,CAAC,eAAe;UAAC;YAAAyN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACJhQ,OAAA;YAAQ4P,OAAO,EAAEL,UAAW;YAACE,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EACrDvN,CAAC,CAAC,eAAe;UAAC;YAAAyN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAENhQ,OAAA;UAAM6Q,QAAQ,EAAElD,YAAa;UAAAgC,QAAA,gBAC3B3P,OAAA;YAAKyP,SAAS,EAAC,YAAY;YAAAE,QAAA,eACzB3P,OAAA;cACE8Q,IAAI,EAAC,MAAM;cACXC,KAAK,EAAExQ,KAAM;cACbyQ,QAAQ,EAAGrH,CAAC,IAAKnJ,QAAQ,CAACmJ,CAAC,CAACC,MAAM,CAACmH,KAAK,CAAE;cAC1CE,WAAW,EAAE7O,CAAC,CAAC,kBAAkB,CAAE;cACnCqN,SAAS,EAAC,YAAY;cACtByB,QAAQ,EAAEvQ;YAAU;cAAAkP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNhQ,OAAA;YACE8Q,IAAI,EAAC,QAAQ;YACbI,QAAQ,EAAEvQ,SAAS,IAAI,CAACJ,KAAK,CAACqN,IAAI,CAAC,CAAE;YACrC6B,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EAE1BhP,SAAS,gBACRX,OAAA,CAAAE,SAAA;cAAAyP,QAAA,gBACE3P,OAAA;gBAAMyP,SAAS,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAChC5N,CAAC,CAAC,YAAY,CAAC;YAAA,eAChB,CAAC,gBAEHpC,OAAA,CAAAE,SAAA;cAAAyP,QAAA,EACGvN,CAAC,CAAC,kBAAkB;YAAC,gBACtB;UACH;YAAAyN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGA3P,WAAW,KAAK,MAAM,IAAI4B,IAAI,iBAC7BjC,OAAA;QAAKyP,SAAS,EAAC,gBAAgB;QAAAE,QAAA,gBAE7B3P,OAAA;UACEyP,SAAS,EAAE,qBAAqBlO,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;UAC/D4P,WAAW,EAAEzH,eAAgB;UAC7B0H,YAAY,EAAE/G,gBAAiB;UAAAsF,QAAA,gBAE/B3P,OAAA;YACEyP,SAAS,EAAE,mBAAmBlO,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;YAC7DwB,EAAE,EAAC,iBAAiB;YACpBqI,KAAK,EAAE;cACLM,SAAS,EAAE,aAAaxK,eAAe,CAACE,CAAC,OAAOF,eAAe,CAACG,CAAC,aAAaH,eAAe,CAACI,KAAK;YACrG,CAAE;YAAAqO,QAAA,gBAGF3P,OAAA;cAAKyP,SAAS,EAAC,oBAAoB;cAAAE,QAAA,eACjC3P,OAAA;gBAAKyP,SAAS,EAAC,oBAAoB;gBAAAE,QAAA,gBACjC3P,OAAA;kBAAA2P,QAAA,EAAK1N,IAAI,CAACqD;gBAAI;kBAAAuK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpBhQ,OAAA;kBAAQ4P,OAAO,EAAER,MAAO;kBAACK,SAAS,EAAC,4BAA4B;kBAAAE,QAAA,EAC5DvN,CAAC,CAAC,YAAY;gBAAC;kBAAAyN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGL/N,IAAI,CAAC2C,MAAM,CAAC4B,GAAG,CAAC,CAAC5D,MAAM,EAAEiC,KAAK,KAAK;cAClC,MAAMwM,aAAa,GAAGpP,IAAI,CAAC2C,MAAM,CAACX,MAAM;;cAExC;cACA,MAAMqN,eAAe,GAAG1I,IAAI,CAACC,GAAG,CAAC,EAAE,EAAE,GAAG,GAAGD,IAAI,CAACC,GAAG,CAACwI,aAAa,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACxE,MAAME,KAAK,GAAI1M,KAAK,GAAGyM,eAAe,GAAI,GAAG;;cAE7C;cACA,MAAME,UAAU,GAAG5I,IAAI,CAACC,GAAG,CAAC,GAAG,EAAEwI,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC;cACtD,MAAMI,WAAW,GAAG,CAAC7O,MAAM,CAACqF,KAAK,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;cAC/C,MAAMyJ,MAAM,GAAGF,UAAU,GAAGC,WAAW;;cAEvC;cACA,MAAME,WAAW,GAAI/I,IAAI,CAACgJ,GAAG,CAAC/M,KAAK,GAAG,GAAG,CAAC,GAAG,CAAE,CAAC,CAAC;cACjD,MAAMgN,UAAU,GAAGN,KAAK,GAAGI,WAAW;;cAEtC;cACA,IAAIvQ,CAAC,GAAGwH,IAAI,CAACkJ,GAAG,CAAED,UAAU,GAAGjJ,IAAI,CAACmJ,EAAE,GAAI,GAAG,CAAC,GAAGL,MAAM;cACvD,IAAIrQ,CAAC,GAAGuH,IAAI,CAACgJ,GAAG,CAAEC,UAAU,GAAGjJ,IAAI,CAACmJ,EAAE,GAAI,GAAG,CAAC,GAAGL,MAAM;;cAEvD;cACA,IAAIL,aAAa,GAAG,EAAE,EAAE;gBACtB,MAAMW,YAAY,GAAGnN,KAAK,GAAG,EAAE,CAAC,CAAC;gBACjCzD,CAAC,IAAIwH,IAAI,CAACkJ,GAAG,CAAEE,YAAY,GAAGpJ,IAAI,CAACmJ,EAAE,GAAI,GAAG,CAAC,IAAIlN,KAAK,GAAG,CAAC,CAAC;gBAC3DxD,CAAC,IAAIuH,IAAI,CAACgJ,GAAG,CAAEI,YAAY,GAAGpJ,IAAI,CAACmJ,EAAE,GAAI,GAAG,CAAC,IAAIlN,KAAK,GAAG,CAAC,CAAC;cAC7D;cAEA,oBACE7E,OAAA;gBAEEyP,SAAS,EAAE,gCAAgCvN,cAAc,KAAKU,MAAM,GAAG,UAAU,GAAG,EAAE,EAAG;gBACzFwI,KAAK,EAAE;kBACLM,SAAS,EAAE,aAAatK,CAAC,OAAOC,CAAC,KAAK;kBACtC,gBAAgB,EAAE,GAAGwQ,UAAU;gBACjC,CAAE;gBACF,cAAYhN,KAAM;gBAClB,cAAYjC,MAAM,CAACqF,KAAK,IAAI,CAAE;gBAC9B,aAAWrF,MAAM,CAACO,IAAK;gBACvB,oBAAkBP,MAAM,CAAC2C,SAAU;gBACnCqK,OAAO,EAAEA,CAAA,KAAMvL,kBAAkB,CAACzB,MAAM,CAAE;gBAAA+M,QAAA,gBAG1C3P,OAAA;kBAAKyP,SAAS,EAAC,wBAAwB;kBAACrE,KAAK,EAAE;oBAC7CM,SAAS,EAAE,UAAUmG,UAAU,GAAG,GAAG,MAAM;oBAC3CvI,KAAK,EAAE,GAAGoI,MAAM;kBAClB;gBAAE;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAEThQ,OAAA;kBAAKyP,SAAS,EAAC,gBAAgB;kBAAAE,QAAA,gBAC7B3P,OAAA;oBAAKyP,SAAS,EAAC,cAAc;oBAAAE,QAAA,EAAE/M,MAAM,CAAC8E;kBAAK;oBAAAmI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClDhQ,OAAA;oBAAIyP,SAAS,EAAC,aAAa;oBAAAE,QAAA,EAAE/M,MAAM,CAACO;kBAAI;oBAAA0M,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9ChQ,OAAA;oBAAGyP,SAAS,EAAC,oBAAoB;oBAAAE,QAAA,EAAE/M,MAAM,CAAC2C;kBAAS;oBAAAsK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAEvDpN,MAAM,CAAC+E,YAAY,iBAClB3H,OAAA;oBAAKyP,SAAS,EAAC,sBAAsB;oBAAAE,QAAA,EAClC/M,MAAM,CAAC+E,YAAY,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACrB,GAAG,CAAC,CAACyL,GAAG,EAAEC,QAAQ,kBACjDlS,OAAA;sBAAqByP,SAAS,EAAC,iBAAiB;sBAAAE,QAAA,EAC7CsC;oBAAG,GADKC,QAAQ;sBAAArC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEb,CACP;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA,GAhCDnL,KAAK;gBAAAgL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiCP,CAAC;YAEV,CAAC,CAAC,EAEDrP,SAAS,iBACRX,OAAA;cAAKyP,SAAS,EAAC,iBAAiB;cAAAE,QAAA,gBAC9B3P,OAAA;gBAAMyP,SAAS,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjChQ,OAAA;gBAAA2P,QAAA,EAAOvN,CAAC,CAAC,SAAS;cAAC;gBAAAyN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNhQ,OAAA;YAAKyP,SAAS,EAAC,iBAAiB;YAAAE,QAAA,gBAC9B3P,OAAA;cAAQyP,SAAS,EAAC,aAAa;cAACG,OAAO,EAAEA,CAAA,KAAMnH,UAAU,CAAC,GAAG,CAAE;cAAAkH,QAAA,EAAC;YAAG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5EhQ,OAAA;cAAQyP,SAAS,EAAC,aAAa;cAACG,OAAO,EAAEA,CAAA,KAAMnH,UAAU,CAAC,GAAG,CAAE;cAAAkH,QAAA,EAAC;YAAG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5EhQ,OAAA;cAAQyP,SAAS,EAAC,aAAa;cAACG,OAAO,EAAEA,CAAA,KAAM7G,eAAe,CAAC,CAAE;cAAA4G,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNhQ,OAAA;UAAKyP,SAAS,EAAC,kBAAkB;UAAAE,QAAA,eAC/B3P,OAAA;YAAKyP,SAAS,EAAC,kBAAkB;YAAAE,QAAA,gBAC/B3P,OAAA;cAAKyP,SAAS,EAAC,eAAe;cAAAE,QAAA,gBAC5B3P,OAAA;gBAAA2P,QAAA,EAAK1N,IAAI,CAACqD;cAAI;gBAAAuK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpBhQ,OAAA;gBAAQ4P,OAAO,EAAER,MAAO;gBAACK,SAAS,EAAC,mBAAmB;gBAAAE,QAAA,EACnDvN,CAAC,CAAC,YAAY;cAAC;gBAAAyN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENhQ,OAAA;cAAKyP,SAAS,EAAC,yBAAyB;cAAC1M,EAAE,EAAC,eAAe;cAAA4M,QAAA,EACxD1N,IAAI,CAAC2C,MAAM,CAAC4B,GAAG,CAAC,CAAC5D,MAAM,EAAEiC,KAAK,kBAC7B7E,OAAA;gBAEEyP,SAAS,EAAE,kCAAkCvN,cAAc,KAAKU,MAAM,GAAG,UAAU,GAAG,EAAE,EAAG;gBAC3F,cAAYiC,KAAM;gBAClB,aAAWjC,MAAM,CAACO,IAAK;gBACvB,oBAAkBP,MAAM,CAAC2C,SAAU;gBACnCqK,OAAO,EAAEA,CAAA,KAAMvL,kBAAkB,CAACzB,MAAM,CAAE;gBAAA+M,QAAA,gBAE1C3P,OAAA;kBAAKyP,SAAS,EAAC,qBAAqB;kBAAAE,QAAA,gBAClC3P,OAAA;oBAAKyP,SAAS,EAAC,oBAAoB;oBAAAE,QAAA,EAAE/M,MAAM,CAAC8E;kBAAK;oBAAAmI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACxDhQ,OAAA;oBAAIyP,SAAS,EAAC,mBAAmB;oBAAAE,QAAA,EAAE/M,MAAM,CAACO;kBAAI;oBAAA0M,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpDhQ,OAAA;oBAAGyP,SAAS,EAAC,0BAA0B;oBAAAE,QAAA,EAAE/M,MAAM,CAAC2C;kBAAS;oBAAAsK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAE7DpN,MAAM,CAAC+E,YAAY,iBAClB3H,OAAA;oBAAKyP,SAAS,EAAC,sBAAsB;oBAAAE,QAAA,GAClC/M,MAAM,CAAC+E,YAAY,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACrB,GAAG,CAAC,CAACyL,GAAG,EAAEC,QAAQ,kBACjDlS,OAAA;sBAAqByP,SAAS,EAAC,wBAAwB;sBAAAE,QAAA,EACpDsC;oBAAG,GADKC,QAAQ;sBAAArC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEb,CACP,CAAC,EACDpN,MAAM,CAAC+E,YAAY,CAAC1D,MAAM,GAAG,CAAC,iBAC7BjE,OAAA;sBAAMyP,SAAS,EAAC,6BAA6B;sBAAAE,QAAA,GAAC,GAC3C,EAAC/M,MAAM,CAAC+E,YAAY,CAAC1D,MAAM,GAAG,CAAC;oBAAA;sBAAA4L,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CACP;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CACN,eAEDhQ,OAAA;oBAAKyP,SAAS,EAAC,qBAAqB;oBAAAE,QAAA,gBAClC3P,OAAA;sBAAMyP,SAAS,EAAC,oBAAoB;sBAAAE,QAAA,EAAC;oBAA0B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtEhQ,OAAA;sBAAMyP,SAAS,EAAC,oBAAoB;sBAAAE,QAAA,EAAC;oBAA+B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGL,CAACpN,MAAM,CAACqF,KAAK,IAAI,CAAC,IAAI,CAAC,iBACtBjI,OAAA;kBAAKyP,SAAS,EAAC,wBAAwB;kBAAAE,QAAA,eACrC3P,OAAA;oBAAKyP,SAAS,EAAC,aAAa;oBAAAE,QAAA,GAAC,QAAM,EAAC/M,MAAM,CAACqF,KAAK;kBAAA;oBAAA4H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CACN;cAAA,GAtCInL,KAAK;gBAAAgL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAuCP,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAELrP,SAAS,iBACRX,OAAA;cAAKyP,SAAS,EAAC,gBAAgB;cAAAE,QAAA,gBAC7B3P,OAAA;gBAAMyP,SAAS,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjChQ,OAAA;gBAAA2P,QAAA,EAAOvN,CAAC,CAAC,SAAS;cAAC;gBAAAyN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA3P,WAAW,KAAK,SAAS,IAAI8B,OAAO,iBACnCnC,OAAA;QAAKyP,SAAS,EAAC,mBAAmB;QAAAE,QAAA,eAChC3P,OAAA;UAAKyP,SAAS,EAAC,cAAc;UAAAE,QAAA,gBAC3B3P,OAAA;YAAKyP,SAAS,EAAC,gBAAgB;YAAAE,QAAA,gBAC7B3P,OAAA;cAAQ4P,OAAO,EAAER,MAAO;cAACK,SAAS,EAAC,oCAAoC;cAAAE,QAAA,EACpEvN,CAAC,CAAC,YAAY;YAAC;cAAAyN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGThQ,OAAA;cAAKyP,SAAS,EAAC,kBAAkB;cAACrE,KAAK,EAAE;gBACvC+G,OAAO,EAAE,MAAM;gBACfC,GAAG,EAAE,KAAK;gBACVC,SAAS,EAAE,MAAM;gBACjBC,QAAQ,EAAE;cACZ,CAAE;cAAA3C,QAAA,gBAEA3P,OAAA;gBAAKyP,SAAS,EAAC,yBAAyB;gBAACrE,KAAK,EAAE;kBAC9C+G,OAAO,EAAE,MAAM;kBACfI,UAAU,EAAE,QAAQ;kBACpBH,GAAG,EAAE,KAAK;kBACV1B,OAAO,EAAE,UAAU;kBACnBJ,UAAU,EAAE,SAAS;kBACrBK,YAAY,EAAE,KAAK;kBACnBJ,MAAM,EAAE;gBACV,CAAE;gBAAAZ,QAAA,gBACA3P,OAAA;kBACE4P,OAAO,EAAE3B,kBAAmB;kBAC5BwB,SAAS,EAAC,UAAU;kBACpBnM,KAAK,EAAC,mBAAmB;kBACzB8H,KAAK,EAAE;oBACLkF,UAAU,EAAE,MAAM;oBAClBC,MAAM,EAAE,MAAM;oBACdiC,QAAQ,EAAE,MAAM;oBAChB/B,MAAM,EAAE,SAAS;oBACjBC,OAAO,EAAE;kBACX,CAAE;kBAAAf,QAAA,EAED1Q,aAAa,CAACiP,SAAS,CAAC,CAAC,CAACC,SAAS,GAAG,IAAI,GAAG;gBAAI;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,eACThQ,OAAA;kBACE4P,OAAO,EAAEtB,gBAAiB;kBAC1BmB,SAAS,EAAC,UAAU;kBACpBnM,KAAK,EAAC,aAAa;kBACnB8H,KAAK,EAAE;oBACLkF,UAAU,EAAE,MAAM;oBAClBC,MAAM,EAAE,MAAM;oBACdiC,QAAQ,EAAE,MAAM;oBAChB/B,MAAM,EAAE,SAAS;oBACjBC,OAAO,EAAE;kBACX,CAAE;kBAAAf,QAAA,EACH;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACThQ,OAAA;kBACE8Q,IAAI,EAAC,OAAO;kBACZhI,GAAG,EAAC,KAAK;kBACTD,GAAG,EAAC,GAAG;kBACP4J,IAAI,EAAC,KAAK;kBACVC,YAAY,EAAC,GAAG;kBAChB1B,QAAQ,EAAGrH,CAAC,IAAK6E,sBAAsB,CAACmE,UAAU,CAAChJ,CAAC,CAACC,MAAM,CAACmH,KAAK,CAAC,CAAE;kBACpE3F,KAAK,EAAE;oBAAC9B,KAAK,EAAE;kBAAM,CAAE;kBACvBhG,KAAK,EAAC;gBAAc;kBAAAuM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACFhQ,OAAA;kBAAMoL,KAAK,EAAE;oBAACoH,QAAQ,EAAE,MAAM;oBAAEhC,KAAK,EAAE;kBAAS,CAAE;kBAAAb,QAAA,EAAC;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eAGNhQ,OAAA;gBAAKyP,SAAS,EAAC,yBAAyB;gBAACrE,KAAK,EAAE;kBAC9C+G,OAAO,EAAE,MAAM;kBACfC,GAAG,EAAE;gBACP,CAAE;gBAAAzC,QAAA,gBACA3P,OAAA;kBACE4P,OAAO,EAAEX,qBAAsB;kBAC/BQ,SAAS,EAAC,mBAAmB;kBAC7BrE,KAAK,EAAE;oBAACsF,OAAO,EAAE,UAAU;oBAAE8B,QAAQ,EAAE;kBAAM,CAAE;kBAC/ClP,KAAK,EAAC,mBAAmB;kBAAAqM,QAAA,EAC1B;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACThQ,OAAA;kBACE4P,OAAO,EAAEjB,eAAgB;kBACzBc,SAAS,EAAC,mBAAmB;kBAC7BrE,KAAK,EAAE;oBAACsF,OAAO,EAAE,UAAU;oBAAE8B,QAAQ,EAAE;kBAAM,CAAE;kBAC/ClP,KAAK,EAAC,eAAe;kBAAAqM,QAAA,EACtB;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACThQ,OAAA;kBACE4P,OAAO,EAAEb,gBAAiB;kBAC1BU,SAAS,EAAC,mBAAmB;kBAC7BrE,KAAK,EAAE;oBAACsF,OAAO,EAAE,UAAU;oBAAE8B,QAAQ,EAAE;kBAAM,CAAE;kBAC/ClP,KAAK,EAAC,gBAAgB;kBAAAqM,QAAA,EACvB;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhQ,OAAA;YAAKyP,SAAS,EAAC,uBAAuB;YAAAE,QAAA,gBACpC3P,OAAA;cAAIyP,SAAS,EAAC,eAAe;cAAAE,QAAA,EAAE,CAAAxN,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEmB,KAAK,KAAI;YAAY;cAAAuM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnEhQ,OAAA;cAAKyP,SAAS,EAAC,cAAc;cAAAE,QAAA,gBAC3B3P,OAAA;gBAAMyP,SAAS,EAAC,eAAe;gBAAAE,QAAA,GAAEvN,CAAC,CAAC,QAAQ,CAAC,EAAC,IAAE,EAAC,CAAAD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE5B,KAAK,KAAI,SAAS;cAAA;gBAAAsP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAClF,CAAA7N,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEU,KAAK,KAAIV,OAAO,CAACU,KAAK,CAACoB,MAAM,GAAG,CAAC,iBACzCjE,OAAA;gBAAKyP,SAAS,EAAC,eAAe;gBAAAE,QAAA,GAC3BvN,CAAC,CAAC,OAAO,CAAC,EAAC,GACZ,EAACD,OAAO,CAACU,KAAK,CAAC2D,GAAG,CAAC,CAACoM,IAAI,EAAE/N,KAAK,kBAC7B7E,OAAA;kBAAkByP,SAAS,EAAC,YAAY;kBAAAE,QAAA,EAAEiD;gBAAI,GAAnC/N,KAAK;kBAAAgL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAqC,CACtD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhQ,OAAA;YAAKyP,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EAC7BxN,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEqB,OAAO,GAAGrB,OAAO,CAACqB,OAAO,CAACqP,KAAK,CAAC,IAAI,CAAC,CAACrM,GAAG,CAAC,CAACsM,SAAS,EAAEjO,KAAK,KACnEiO,SAAS,CAAClF,IAAI,CAAC,CAAC,iBACd5N,OAAA;cAAeyP,SAAS,EAAC,mBAAmB;cAAAE,QAAA,EACzCmD;YAAS,GADJjO,KAAK;cAAAgL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CAEN,CAAC,gBACAhQ,OAAA;cAAKyP,SAAS,EAAC,iBAAiB;cAAAE,QAAA,gBAC9B3P,OAAA;gBAAMyP,SAAS,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjChQ,OAAA;gBAAA2P,QAAA,EAAG;cAA0B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAGL,CAAA7N,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEyB,UAAU,KAAIzB,OAAO,CAACyB,UAAU,CAACK,MAAM,GAAG,CAAC,iBACnDjE,OAAA;YAAKyP,SAAS,EAAC,qBAAqB;YAAAE,QAAA,gBAClC3P,OAAA;cAAA2P,QAAA,EAAI;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrBhQ,OAAA;cAAKyP,SAAS,EAAC,cAAc;cAAAE,QAAA,EAC1BxN,OAAO,CAACyB,UAAU,CAAC4C,GAAG,CAAC,CAACuM,MAAM,EAAElO,KAAK,kBACpC7E,OAAA;gBAAiByP,SAAS,EAAC,aAAa;gBAAAE,QAAA,eACtC3P,OAAA;kBAAGgT,IAAI,EAAED,MAAM,CAACE,GAAI;kBAACrJ,MAAM,EAAC,QAAQ;kBAACsJ,GAAG,EAAC,qBAAqB;kBAAAvD,QAAA,EAC3DoD,MAAM,CAACzP,KAAK,IAAI,SAASuB,KAAK,GAAG,CAAC;gBAAE;kBAAAgL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC;cAAC,GAHInL,KAAK;gBAAAgL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC5P,EAAA,CAjvCID,YAAY;EAAA,QAuBFN,cAAc;AAAA;AAAAsT,EAAA,GAvBxBhT,YAAY;AAmvClB,eAAeA,YAAY;AAAC,IAAAgT,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}