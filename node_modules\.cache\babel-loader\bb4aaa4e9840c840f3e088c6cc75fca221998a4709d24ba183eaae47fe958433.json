{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Appv1\\\\src\\\\components\\\\OptimizedApp.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport '../styles/optimized.css';\nimport gestureService, { createFlagWheel } from '../services/gestureService';\nimport speechService from '../services/speechService';\nimport exportService from '../services/exportService';\nimport gamificationService from '../services/optimizedGamificationService';\nimport { generateKnowledgeTree as generateTreeAPI, generateArticle as generateArticleAPI, testConnection } from '../services/openRouterService';\nimport tabService from '../services/tabService';\nimport TabManager from './TabManager';\nimport LanguageSwitcher from './LanguageSwitcher';\nimport { useTranslation, getCurrentLanguage } from '../utils/i18n';\n\n// Optimized Knowledge Tree Explorer - Pareto 80/20 Implementation\n// Focus on core functionality with maximum impact\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst OptimizedApp = () => {\n  _s();\n  // Core state - now managed by tabs\n  const [currentView, setCurrentView] = useState('input'); // 'input', 'tree', 'article'\n  const [topic, setTopic] = useState('');\n  const [activeTab, setActiveTab] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [user, setUser] = useState(null);\n  const appRef = useRef(null);\n\n  // Canvas state for infinite tree view\n  const [canvasTransform, setCanvasTransform] = useState({\n    x: 0,\n    y: 0,\n    scale: 1\n  });\n  const [isDragging, setIsDragging] = useState(false);\n  const [dragStart, setDragStart] = useState({\n    x: 0,\n    y: 0\n  });\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);\n\n  // Get current tab data\n  const tree = (activeTab === null || activeTab === void 0 ? void 0 : activeTab.tree) || null;\n  const selectedBranch = (activeTab === null || activeTab === void 0 ? void 0 : activeTab.selectedBranch) || null;\n  const article = (activeTab === null || activeTab === void 0 ? void 0 : activeTab.article) || null;\n\n  // Translation hook\n  const {\n    t\n  } = useTranslation();\n\n  // Available flags for the optimized version\n  const availableFlags = React.useMemo(() => [{\n    code: '-a',\n    name: 'Article',\n    description: t('flagArticle')\n  }, {\n    code: '-ex',\n    name: 'Examples',\n    description: t('flagExamples')\n  }, {\n    code: '-q',\n    name: 'Quiz',\n    description: t('flagQuiz')\n  }, {\n    code: '-vis',\n    name: 'Visual',\n    description: t('flagVisual')\n  }, {\n    code: '-path',\n    name: 'Learning Path',\n    description: t('flagPath')\n  }, {\n    code: '-case',\n    name: 'Case Study',\n    description: t('flagCase')\n  }, {\n    code: '-ro',\n    name: 'Romanian',\n    description: t('flagRomanian')\n  }], [t]);\n\n  // Generate article with tabs support\n  const generateArticleForBranch = React.useCallback(async (branch, flags = ['-a']) => {\n    if (!activeTab) return;\n    setIsLoading(true);\n\n    // Set tab to loading state (yellow)\n    tabService.updateTabStatus(activeTab.id, 'loading', {\n      selectedBranch: branch,\n      article: null\n    });\n    setActiveTab(tabService.getTab(activeTab.id));\n    try {\n      console.log('📄 Generating article for branch:', branch.nume);\n      const articleData = await generateArticleAPI(activeTab.topic, branch, flags);\n      console.log('✅ Generated article data:', articleData);\n\n      // Map the article data to expected format\n      const mappedArticle = {\n        title: articleData.titlu || articleData.title || `${branch.nume} - ${activeTab.topic}`,\n        content: articleData.continut || articleData.content || 'Content not available',\n        topic: activeTab.topic,\n        flags: flags,\n        position: articleData.pozitie || `${activeTab.topic} → ${branch.nume}`,\n        webSources: articleData.webSources || []\n      };\n\n      // Update tab with article and set to completed (green)\n      tabService.updateTabStatus(activeTab.id, 'completed', {\n        selectedBranch: branch,\n        article: mappedArticle\n      });\n      const updatedTab = tabService.getTab(activeTab.id);\n      setActiveTab(updatedTab);\n      setCurrentView('article');\n\n      // Award points for article generation\n      const result = gamificationService.awardPoints('ARTICLE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (error) {\n      console.error('❌ Error generating article:', error);\n      setError('Failed to generate article. Please try again.');\n\n      // Set tab back to pending on error\n      tabService.updateTabStatus(activeTab.id, 'pending', {\n        selectedBranch: branch,\n        article: null\n      });\n      setActiveTab(tabService.getTab(activeTab.id));\n    } finally {\n      setIsLoading(false);\n    }\n  }, [activeTab]);\n\n  // Handle branch selection (single tap)\n  const handleBranchSelect = React.useCallback(branch => {\n    if (activeTab) {\n      tabService.updateTabStatus(activeTab.id, activeTab.status, {\n        selectedBranch: branch\n      });\n      setActiveTab(tabService.getTab(activeTab.id));\n    }\n  }, [activeTab]);\n\n  // Gesture handlers\n  const handleDoubleTap = React.useCallback((event, targetInfo) => {\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      // Show flag wheel on double tap of branch\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        createFlagWheel(targetInfo.position, availableFlags, selectedFlags => {\n          console.log('Selected flags:', selectedFlags);\n        }, selectedFlags => {\n          generateArticleForBranch(branch, selectedFlags);\n        });\n      }\n    }\n  }, [tree, availableFlags, generateArticleForBranch]);\n  const handleSingleTap = React.useCallback((event, targetInfo) => {\n    // Single tap for normal selection\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        handleBranchSelect(branch);\n      }\n    }\n  }, [tree, handleBranchSelect]);\n\n  // Expand branch to create relevant sub-branches with AI\n  const expandBranch = React.useCallback(async (branch, branchIndex) => {\n    if (!activeTab || !tree) {\n      setError(t('noActiveTab') || 'No active tab or tree available');\n      return;\n    }\n    setIsLoading(true);\n    setError(null);\n    try {\n      var _data$choices$, _data$choices$$messag;\n      console.log('🌿 Expanding branch:', branch.nume, 'with AI-generated sub-branches');\n\n      // Create AI-generated sub-branches specifically for this branch\n      const currentLang = getCurrentLanguage();\n      const prompt = currentLang === 'ro' ? `Generează 4 sub-ramuri specifice și relevante pentru \"${branch.nume}\" în contextul \"${tree.tema}\".\n\nDescrierea ramuri principale: ${branch.descriere}\n\nCreează sub-ramuri care să fie:\n- Specifice și relevante pentru \"${branch.nume}\"\n- Logice și bine organizate\n- Utile pentru învățare progresivă\n- În limba română\n\nRăspunde DOAR cu JSON în formatul:\n{\n  \"ramuri\": [\n    {\n      \"nume\": \"Nume sub-ramură\",\n      \"descriere\": \"Descriere detaliată\",\n      \"emoji\": \"🔧\",\n      \"subcategorii\": [\"subcategorie1\", \"subcategorie2\", \"subcategorie3\"]\n    }\n  ]\n}` : `Generate 4 specific and relevant sub-branches for \"${branch.nume}\" in the context of \"${tree.tema}\".\n\nMain branch description: ${branch.descriere}\n\nCreate sub-branches that are:\n- Specific and relevant to \"${branch.nume}\"\n- Logical and well-organized\n- Useful for progressive learning\n- In English\n\nRespond ONLY with JSON in the format:\n{\n  \"branches\": [\n    {\n      \"nume\": \"Sub-branch name\",\n      \"descriere\": \"Detailed description\",\n      \"emoji\": \"🔧\",\n      \"subcategorii\": [\"subcategory1\", \"subcategory2\", \"subcategory3\"]\n    }\n  ]\n}`;\n\n      // Use DeepSeek R1 for intelligent sub-branch generation\n      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer 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`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': window.location.origin,\n          'X-Title': 'Knowledge Tree Explorer'\n        },\n        body: JSON.stringify({\n          model: 'deepseek/deepseek-r1-0528:free',\n          messages: [{\n            role: 'system',\n            content: currentLang === 'ro' ? 'Ești un expert în organizarea cunoștințelor. Generează sub-ramuri relevante și specifice în format JSON valid.' : 'You are an expert in knowledge organization. Generate relevant and specific sub-branches in valid JSON format.'\n          }, {\n            role: 'user',\n            content: prompt\n          }],\n          temperature: 0.7,\n          max_tokens: 2000\n        })\n      });\n      if (!response.ok) {\n        throw new Error(`API request failed: ${response.status}`);\n      }\n      const data = await response.json();\n      const responseText = ((_data$choices$ = data.choices[0]) === null || _data$choices$ === void 0 ? void 0 : (_data$choices$$messag = _data$choices$.message) === null || _data$choices$$messag === void 0 ? void 0 : _data$choices$$messag.content) || '';\n\n      // Parse the JSON response\n      const jsonMatch = responseText.match(/\\{[\\s\\S]*\\}/);\n      if (!jsonMatch) {\n        throw new Error('No valid JSON found in response');\n      }\n      const expandedData = JSON.parse(jsonMatch[0]);\n      const subBranches = expandedData.ramuri || expandedData.branches || [];\n      if (subBranches.length === 0) {\n        throw new Error('No sub-branches generated');\n      }\n      console.log('✅ Generated', subBranches.length, 'AI sub-branches for:', branch.nume);\n\n      // Update tree with AI-generated sub-branches\n      const newTree = {\n        ...tree\n      };\n      newTree.ramuri = [...newTree.ramuri.slice(0, branchIndex + 1), ...subBranches.map(subBranch => ({\n        ...subBranch,\n        isSubBranch: true,\n        parentBranch: branch.nume,\n        level: (branch.level || 0) + 1,\n        id: `${branch.nume}-${subBranch.nume}`.replace(/\\s+/g, '-').toLowerCase()\n      })), ...newTree.ramuri.slice(branchIndex + 1)];\n\n      // Update tab with expanded tree\n      tabService.updateTabStatus(activeTab.id, 'completed', {\n        tree: newTree\n      });\n      setActiveTab(tabService.getTab(activeTab.id));\n      console.log('🌳 Tree expanded successfully with AI sub-branches');\n\n      // Award points for branch expansion\n      const result = gamificationService.awardPoints('BRANCH_EXPANDED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (error) {\n      console.error('❌ Error expanding branch with AI:', error);\n      setError(t('failedToExpand') || 'Nu s-a putut extinde ramura. Te rugăm să încerci din nou.');\n    } finally {\n      setIsLoading(false);\n    }\n  }, [tree, activeTab, t]);\n  const handleLongPress = React.useCallback(async (event, targetInfo) => {\n    // Long press to expand branch and create tree effect\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        await expandBranch(branch, targetInfo.branchData.index);\n      }\n    }\n  }, [tree, expandBranch]);\n\n  // Canvas control functions\n  const zoomCanvas = React.useCallback(factor => {\n    setCanvasTransform(prev => ({\n      ...prev,\n      scale: Math.max(0.1, Math.min(3, prev.scale * factor))\n    }));\n  }, []);\n  const resetCanvasView = React.useCallback(() => {\n    setCanvasTransform({\n      x: 0,\n      y: 0,\n      scale: 1\n    });\n  }, []);\n  const handleCanvasMouseDown = React.useCallback(e => {\n    if (e.button === 0) {\n      // Left mouse button\n      setIsDragging(true);\n      setDragStart({\n        x: e.clientX - canvasTransform.x,\n        y: e.clientY - canvasTransform.y\n      });\n    }\n  }, [canvasTransform]);\n  const handleCanvasMouseMove = React.useCallback(e => {\n    if (isDragging) {\n      setCanvasTransform(prev => ({\n        ...prev,\n        x: e.clientX - dragStart.x,\n        y: e.clientY - dragStart.y\n      }));\n    }\n  }, [isDragging, dragStart]);\n  const handleCanvasMouseUp = React.useCallback(() => {\n    setIsDragging(false);\n  }, []);\n  const handleCanvasWheel = React.useCallback(e => {\n    e.preventDefault();\n    const factor = e.deltaY > 0 ? 0.9 : 1.1;\n    zoomCanvas(factor);\n  }, [zoomCanvas]);\n\n  // Handle window resize for mobile detection\n  useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth <= 768);\n    };\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  // Initialize TikTok scroll behavior on mobile\n  useEffect(() => {\n    const tiktokContainer = document.getElementById('tiktok-scroll');\n    if (tiktokContainer && isMobile) {\n      // Smooth scroll behavior\n      tiktokContainer.style.scrollBehavior = 'smooth';\n\n      // Optional: Add snap scrolling enhancement\n      let isScrolling = false;\n      const handleScroll = () => {\n        if (!isScrolling) {\n          isScrolling = true;\n          setTimeout(() => {\n            isScrolling = false;\n          }, 150);\n        }\n      };\n      tiktokContainer.addEventListener('scroll', handleScroll);\n      return () => {\n        tiktokContainer.removeEventListener('scroll', handleScroll);\n      };\n    }\n  }, [isMobile]);\n\n  // Initialize canvas event listeners\n  useEffect(() => {\n    const canvas = document.getElementById('infinite-canvas');\n    if (canvas && !isMobile) {\n      // Apply transform\n      canvas.style.transform = `translate(${canvasTransform.x}px, ${canvasTransform.y}px) scale(${canvasTransform.scale})`;\n      canvas.addEventListener('mousedown', handleCanvasMouseDown);\n      canvas.addEventListener('wheel', handleCanvasWheel);\n\n      // Add global mouse events for dragging\n      const handleGlobalMouseMove = e => {\n        if (isDragging) {\n          handleCanvasMouseMove(e);\n        }\n      };\n      const handleGlobalMouseUp = () => {\n        if (isDragging) {\n          handleCanvasMouseUp();\n        }\n      };\n      document.addEventListener('mousemove', handleGlobalMouseMove);\n      document.addEventListener('mouseup', handleGlobalMouseUp);\n      return () => {\n        canvas.removeEventListener('mousedown', handleCanvasMouseDown);\n        canvas.removeEventListener('wheel', handleCanvasWheel);\n        document.removeEventListener('mousemove', handleGlobalMouseMove);\n        document.removeEventListener('mouseup', handleGlobalMouseUp);\n      };\n    }\n  }, [canvasTransform, isMobile, isDragging, handleCanvasMouseDown, handleCanvasMouseMove, handleCanvasMouseUp, handleCanvasWheel]);\n\n  // Initialize services and authentication\n  useEffect(() => {\n    const storedUser = localStorage.getItem('user');\n    const bypassSecurity = localStorage.getItem('bypassSecurity');\n    if (storedUser || bypassSecurity) {\n      const userData = {\n        id: 'user-1',\n        name: 'User',\n        subscriptionTier: 'premium'\n      };\n      setUser(userData);\n\n      // Award daily login points\n      const result = gamificationService.awardPoints('DAILY_LOGIN');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n\n    // Initialize gesture service\n    if (appRef.current) {\n      gestureService.init(appRef.current, {\n        doubleTap: handleDoubleTap,\n        singleTap: handleSingleTap,\n        longPress: handleLongPress\n      });\n    }\n    return () => {\n      gestureService.destroy();\n    };\n  }, [handleDoubleTap, handleSingleTap, handleLongPress]);\n\n  // Initialize gamification UI when user is logged in\n  useEffect(() => {\n    if (user) {\n      const container = document.getElementById('gamification-container');\n      if (container) {\n        // Clear existing content\n        container.innerHTML = '';\n        // Create gamification UI\n        gamificationService.createGamificationUI(container);\n      }\n\n      // Test API connection\n      testConnection().then(isConnected => {\n        console.log('🔌 API Connection Status:', isConnected ? '✅ Connected' : '❌ Failed');\n        if (!isConnected) {\n          console.warn('⚠️ API connection failed. Check your API key and internet connection.');\n        }\n      }).catch(error => {\n        console.error('❌ API connection test error:', error);\n      });\n    }\n  }, [user]);\n\n  // Core API call - using optimized service with tabs\n  const generateKnowledgeTree = async (topicInput, tabId = null) => {\n    let currentTabId = tabId;\n\n    // Create new tab if none provided\n    if (!currentTabId) {\n      try {\n        const newTab = tabService.createTab(topicInput);\n        currentTabId = newTab.id;\n        setActiveTab(newTab);\n        setCurrentView('tree');\n      } catch (error) {\n        setError(error.message);\n        return;\n      }\n    }\n\n    // Update tab status to generating\n    tabService.updateTabStatus(currentTabId, 'generating', {\n      progress: 10\n    });\n    setIsLoading(true);\n    setError(null);\n    try {\n      console.log('🌳 Generating knowledge tree for:', topicInput, 'in tab:', currentTabId);\n\n      // Update progress\n      tabService.updateTabStatus(currentTabId, 'generating', {\n        progress: 30\n      });\n      const treeData = await generateTreeAPI(topicInput, getCurrentLanguage());\n      console.log('✅ Generated tree data:', treeData);\n\n      // Update tab with completed tree\n      tabService.updateTabStatus(currentTabId, 'completed', {\n        tree: treeData,\n        progress: 100\n      });\n\n      // Update active tab if this is the current one\n      if (currentTabId === (activeTab === null || activeTab === void 0 ? void 0 : activeTab.id)) {\n        setActiveTab(tabService.getTab(currentTabId));\n      }\n\n      // Award points for tree generation\n      const result = gamificationService.awardPoints('TREE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (err) {\n      console.error('❌ Error generating tree:', err);\n      tabService.updateTabStatus(currentTabId, 'error');\n      setError(`Failed to generate knowledge tree: ${err.message}. Please try again.`);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Handle form submission\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (topic.trim()) {\n      generateKnowledgeTree(topic.trim());\n      setTopic(''); // Clear input for next topic\n    }\n  };\n\n  // Handle tab changes\n  const handleTabChange = tab => {\n    // Clear any existing errors when switching tabs\n    setError(null);\n    setIsLoading(false);\n    setActiveTab(tab);\n    if (tab !== null && tab !== void 0 && tab.tree) {\n      setCurrentView('tree');\n    } else {\n      setCurrentView('input');\n    }\n  };\n\n  // Handle new tab creation\n  const handleNewTab = () => {\n    // Clear any existing errors and loading states\n    setError(null);\n    setIsLoading(false);\n    setCurrentView('input');\n    setActiveTab(null);\n    setTopic('');\n  };\n  const handleTabArticleAccess = tab => {\n    // Clear any existing errors when accessing article\n    setError(null);\n    setIsLoading(false);\n    setActiveTab(tab);\n    setCurrentView('article');\n  };\n\n  // Speech functions\n  const handleSpeechToggle = () => {\n    if (!(article !== null && article !== void 0 && article.content)) return;\n    if (speechService.getStatus().isPlaying) {\n      speechService.toggle();\n    } else {\n      speechService.speak(article.content);\n      // Award points for using speech\n      const result = gamificationService.awardPoints('SPEECH_USED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleSpeechStop = () => {\n    speechService.stop();\n  };\n  const handleSpeechRateChange = rate => {\n    speechService.setRate(rate);\n  };\n\n  // Export functions\n  const handleExportPDF = () => {\n    if (!(article !== null && article !== void 0 && article.title) || !(article !== null && article !== void 0 && article.content)) return;\n    const result = exportService.exportAsPDF(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleExportWord = () => {\n    if (!(article !== null && article !== void 0 && article.title) || !(article !== null && article !== void 0 && article.content)) return;\n    const result = exportService.exportAsWord(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleCopyToClipboard = async () => {\n    if (!(article !== null && article !== void 0 && article.content)) return;\n    const result = await exportService.copyToClipboard(article.content);\n    exportService.showMessage(result.message, result.success ? 'success' : 'error');\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  // Navigation functions\n  const goBack = () => {\n    if (currentView === 'article') {\n      setCurrentView('tree');\n      if (activeTab) {\n        tabService.updateTabStatus(activeTab.id, activeTab.status, {\n          article: null\n        });\n        setActiveTab(tabService.getTab(activeTab.id));\n      }\n    } else if (currentView === 'tree') {\n      setCurrentView('input');\n    }\n  };\n  const goHome = () => {\n    setCurrentView('input');\n    setActiveTab(null);\n    setTopic('');\n    // Clear all tabs\n    tabService.clearAllTabs();\n  };\n\n  // Quick login for development\n  const quickLogin = () => {\n    localStorage.setItem('bypassSecurity', 'true');\n    setUser({\n      id: 'dev-1',\n      name: 'Developer',\n      subscriptionTier: 'premium'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    ref: appRef,\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"app-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: goHome,\n          className: \"logo-text\",\n          children: t('appTitle')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 685,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-right\",\n          children: [user && /*#__PURE__*/_jsxDEV(\"div\", {\n            id: \"gamification-container\",\n            style: {\n              marginRight: '16px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 690,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(LanguageSwitcher, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 694,\n            columnNumber: 13\n          }, this), !user ? /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: quickLogin,\n            className: \"btn btn-primary\",\n            style: {\n              marginLeft: '12px'\n            },\n            children: t('quickLogin')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 696,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginLeft: '12px'\n            },\n            children: [t('welcome'), \", \", user.name, \"!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 700,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 688,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 684,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 683,\n      columnNumber: 7\n    }, this), user && /*#__PURE__*/_jsxDEV(TabManager, {\n      onTabChange: handleTabChange,\n      onNewTab: handleNewTab,\n      onTabArticleAccess: handleTabArticleAccess\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 708,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"main-content\",\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error\",\n        children: [\"\\u26A0\\uFE0F \", error, /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setError(null),\n          style: {\n            marginLeft: 'auto',\n            background: 'none',\n            border: 'none',\n            color: 'white',\n            cursor: 'pointer'\n          },\n          children: \"\\u2715\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 720,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 718,\n        columnNumber: 11\n      }, this), currentView === 'input' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"title\",\n          children: t('appTitle')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 729,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"subtitle\",\n          children: \"Enter any topic to generate an interactive knowledge tree with AI-powered content.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 730,\n          columnNumber: 13\n        }, this), !user ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#f1f5f9',\n            padding: '1rem',\n            borderRadius: '0.5rem',\n            marginBottom: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#334155',\n              marginBottom: '1rem'\n            },\n            children: t('loginRequired')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 736,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: quickLogin,\n            className: \"btn btn-primary\",\n            children: t('quickLoginDev')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 739,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 735,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: topic,\n              onChange: e => setTopic(e.target.value),\n              placeholder: t('topicPlaceholder'),\n              className: \"form-input\",\n              disabled: isLoading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 746,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 745,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: isLoading || !topic.trim(),\n            className: \"btn btn-primary\",\n            children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 762,\n                columnNumber: 23\n              }, this), t('generating')]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: t('exploreKnowledge')\n            }, void 0, false)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 755,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 744,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 728,\n        columnNumber: 11\n      }, this), currentView === 'tree' && tree && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tree-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"desktop-tree-view\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"infinite-canvas\",\n            id: \"infinite-canvas\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"central-topic-node\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"topic-input-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  children: tree.tema\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 785,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: goBack,\n                  className: \"btn btn-secondary back-btn\",\n                  children: t('backToTree')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 786,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 784,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 783,\n              columnNumber: 17\n            }, this), tree.ramuri.map((branch, index) => {\n              const totalBranches = tree.ramuri.length;\n              const angle = index * 360 / totalBranches;\n              const baseRadius = 300;\n              const levelOffset = (branch.level || 0) * 120;\n              const radius = baseRadius + levelOffset;\n\n              // Add some randomness for more organic look\n              const angleOffset = Math.sin(index * 2.5) * 15;\n              const finalAngle = angle + angleOffset;\n              const x = Math.cos(finalAngle * Math.PI / 180) * radius;\n              const y = Math.sin(finalAngle * Math.PI / 180) * radius;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `tree-branch-node ${selectedBranch === branch ? 'selected' : ''}`,\n                style: {\n                  transform: `translate(${x}px, ${y}px)`,\n                  '--branch-angle': `${finalAngle}deg`\n                },\n                \"data-index\": index,\n                \"data-level\": branch.level || 0,\n                onClick: () => handleBranchSelect(branch),\n                onDoubleClick: () => generateArticleForBranch(branch),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"branch-connection-line\",\n                  style: {\n                    transform: `rotate(${angle + 180}deg)`,\n                    width: `${radius}px`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 821,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"branch-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"branch-emoji\",\n                    children: branch.emoji\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 827,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"branch-name\",\n                    children: branch.nume\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 828,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"branch-description\",\n                    children: branch.descriere\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 829,\n                    columnNumber: 25\n                  }, this), branch.subcategorii && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"branch-subcategories\",\n                    children: branch.subcategorii.slice(0, 2).map((sub, subIndex) => /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"subcategory-tag\",\n                      children: sub\n                    }, subIndex, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 834,\n                      columnNumber: 31\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 832,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 826,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 808,\n                columnNumber: 21\n              }, this);\n            }), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"loading-overlay\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 847,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: t('loading')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 848,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 846,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 781,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"canvas-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"control-btn\",\n              onClick: () => zoomCanvas(1.2),\n              children: \"\\uD83D\\uDD0D+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 855,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"control-btn\",\n              onClick: () => zoomCanvas(0.8),\n              children: \"\\uD83D\\uDD0D-\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 856,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"control-btn\",\n              onClick: () => resetCanvasView(),\n              children: \"\\uD83C\\uDFAF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 857,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 854,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 780,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mobile-tree-view\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tiktok-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"tiktok-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                children: tree.tema\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 865,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: goBack,\n                className: \"btn btn-secondary\",\n                children: t('backToTree')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 866,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 864,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"tiktok-scroll-container\",\n              id: \"tiktok-scroll\",\n              children: tree.ramuri.map((branch, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `tiktok-branch-card ${selectedBranch === branch ? 'selected' : ''}`,\n                \"data-index\": index,\n                onClick: () => handleBranchSelect(branch),\n                onDoubleClick: () => generateArticleForBranch(branch),\n                onTouchStart: e => {\n                  const touch = e.touches[0];\n                  e.currentTarget.touchStartTime = Date.now();\n                  e.currentTarget.touchStartX = touch.clientX;\n                  e.currentTarget.touchStartY = touch.clientY;\n                },\n                onTouchEnd: e => {\n                  const touch = e.changedTouches[0];\n                  const deltaTime = Date.now() - (e.currentTarget.touchStartTime || 0);\n                  const deltaX = Math.abs(touch.clientX - (e.currentTarget.touchStartX || 0));\n                  const deltaY = Math.abs(touch.clientY - (e.currentTarget.touchStartY || 0));\n\n                  // Long press detection (500ms+, minimal movement)\n                  if (deltaTime > 500 && deltaX < 10 && deltaY < 10) {\n                    e.preventDefault();\n                    expandBranch(branch, index);\n                  }\n                  // Double tap detection\n                  else if (deltaTime < 300 && deltaX < 10 && deltaY < 10) {\n                    const now = Date.now();\n                    const lastTap = e.currentTarget.lastTapTime || 0;\n                    if (now - lastTap < 300) {\n                      e.preventDefault();\n                      generateArticleForBranch(branch);\n                    }\n                    e.currentTarget.lastTapTime = now;\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"tiktok-card-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"branch-emoji-large\",\n                    children: branch.emoji\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 909,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"branch-name-large\",\n                    children: branch.nume\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 910,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"branch-description-large\",\n                    children: branch.descriere\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 911,\n                    columnNumber: 25\n                  }, this), branch.subcategorii && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"tiktok-subcategories\",\n                    children: [branch.subcategorii.slice(0, 3).map((sub, subIndex) => /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"tiktok-subcategory-tag\",\n                      children: sub\n                    }, subIndex, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 916,\n                      columnNumber: 31\n                    }, this)), branch.subcategorii.length > 3 && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"tiktok-subcategory-tag more\",\n                      children: [\"+\", branch.subcategorii.length - 3]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 921,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 914,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"tiktok-gesture-hint\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"tiktok-action-hint\",\n                      children: \"\\uD83D\\uDCD6 Swipe down alte crengii\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 929,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"tiktok-action-hint\",\n                      children: \"\\uD83C\\uDF3F Long-press pentru expansiune\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 930,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 928,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 908,\n                  columnNumber: 23\n                }, this), (branch.level || 0) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"tiktok-level-indicator\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"level-badge\",\n                    children: [\"Nivel \", branch.level]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 937,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 936,\n                  columnNumber: 25\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 873,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 871,\n              columnNumber: 17\n            }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"tiktok-loading\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 946,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: t('loading')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 947,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 945,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 863,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 862,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 778,\n        columnNumber: 11\n      }, this), currentView === 'article' && article && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"article-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"article-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"article-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: goBack,\n              className: \"btn btn-secondary article-back-btn\",\n              children: t('backToTree')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 960,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"article-controls\",\n              style: {\n                display: 'flex',\n                gap: '8px',\n                marginTop: '1rem',\n                flexWrap: 'wrap'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"speech-controls-compact\",\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '8px',\n                  padding: '8px 12px',\n                  background: '#f1f5f9',\n                  borderRadius: '6px',\n                  border: '1px solid #e2e8f0'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleSpeechToggle,\n                  className: \"btn-icon\",\n                  title: \"Play/Pause Speech\",\n                  style: {\n                    background: 'none',\n                    border: 'none',\n                    fontSize: '16px',\n                    cursor: 'pointer',\n                    padding: '4px'\n                  },\n                  children: speechService.getStatus().isPlaying ? '⏸️' : '▶️'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 981,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleSpeechStop,\n                  className: \"btn-icon\",\n                  title: \"Stop Speech\",\n                  style: {\n                    background: 'none',\n                    border: 'none',\n                    fontSize: '16px',\n                    cursor: 'pointer',\n                    padding: '4px'\n                  },\n                  children: \"\\u23F9\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 995,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"range\",\n                  min: \"0.5\",\n                  max: \"2\",\n                  step: \"0.1\",\n                  defaultValue: \"1\",\n                  onChange: e => handleSpeechRateChange(parseFloat(e.target.value)),\n                  style: {\n                    width: '60px'\n                  },\n                  title: \"Speech Speed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1009,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '12px',\n                    color: '#64748b'\n                  },\n                  children: \"\\uD83D\\uDDE3\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1019,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 972,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"export-controls-compact\",\n                style: {\n                  display: 'flex',\n                  gap: '4px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleCopyToClipboard,\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '6px 12px',\n                    fontSize: '12px'\n                  },\n                  title: \"Copy to Clipboard\",\n                  children: \"\\uD83D\\uDCCB Copy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1027,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleExportPDF,\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '6px 12px',\n                    fontSize: '12px'\n                  },\n                  title: \"Export as PDF\",\n                  children: \"\\uD83D\\uDCC4 PDF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1035,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleExportWord,\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '6px 12px',\n                    fontSize: '12px'\n                  },\n                  title: \"Export as Word\",\n                  children: \"\\uD83D\\uDCDD Word\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1043,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1023,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 965,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 959,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"title\",\n            children: (article === null || article === void 0 ? void 0 : article.title) || 'Loading...'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1055,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#475569',\n              marginBottom: '2rem',\n              fontSize: '0.9rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: [t('partOf'), \": \", (article === null || article === void 0 ? void 0 : article.topic) || 'Unknown']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1057,\n              columnNumber: 17\n            }, this), (article === null || article === void 0 ? void 0 : article.flags) && article.flags.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                marginLeft: '16px'\n              },\n              children: [t('flags'), \": \", article.flags.join(', ')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1059,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1056,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"article-content\",\n            style: {\n              lineHeight: '1.8',\n              fontSize: '1.1rem'\n            },\n            children: article !== null && article !== void 0 && article.content ? article.content.split('\\n').map((paragraph, index) => paragraph.trim() && /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                marginBottom: '1rem'\n              },\n              children: paragraph\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1068,\n              columnNumber: 21\n            }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Loading article content...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1073,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1065,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 958,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 957,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 716,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 681,\n    columnNumber: 5\n  }, this);\n};\n_s(OptimizedApp, \"bqF6Rl5Jado2XhsXOxp49x2MwUc=\", false, function () {\n  return [useTranslation];\n});\n_c = OptimizedApp;\nexport default OptimizedApp;\nvar _c;\n$RefreshReg$(_c, \"OptimizedApp\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "gestureService", "createFlagWheel", "speechService", "exportService", "gamificationService", "generateKnowledgeTree", "generateTreeAPI", "generateArticle", "generateArticleAPI", "testConnection", "tabService", "TabManager", "LanguageSwitcher", "useTranslation", "getCurrentLanguage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "OptimizedApp", "_s", "current<PERSON>iew", "set<PERSON><PERSON><PERSON>View", "topic", "setTopic", "activeTab", "setActiveTab", "isLoading", "setIsLoading", "error", "setError", "user", "setUser", "appRef", "canvasTransform", "setCanvasTransform", "x", "y", "scale", "isDragging", "setIsDragging", "dragStart", "setDragStart", "isMobile", "setIsMobile", "window", "innerWidth", "tree", "<PERSON><PERSON><PERSON><PERSON>", "article", "t", "availableFlags", "useMemo", "code", "name", "description", "generateArticleForBranch", "useCallback", "branch", "flags", "updateTabStatus", "id", "getTab", "console", "log", "nume", "articleData", "mappedArticle", "title", "titlu", "content", "continut", "position", "pozitie", "webSources", "updatedTab", "result", "awardPoints", "newAchievements", "length", "for<PERSON>ach", "achievement", "showAchievementNotification", "handleBranchSelect", "status", "handleDoubleTap", "event", "targetInfo", "isBranchItem", "branchData", "<PERSON><PERSON>", "index", "selected<PERSON><PERSON><PERSON>", "handleSingleTap", "expandBranch", "branchIndex", "_data$choices$", "_data$choices$$messag", "currentLang", "prompt", "tema", "desc<PERSON><PERSON>", "response", "fetch", "method", "headers", "location", "origin", "body", "JSON", "stringify", "model", "messages", "role", "temperature", "max_tokens", "ok", "Error", "data", "json", "responseText", "choices", "message", "jsonMatch", "match", "expandedData", "parse", "subBranches", "branches", "newTree", "slice", "map", "subBranch", "isSubBranch", "parentBranch", "level", "replace", "toLowerCase", "handleLongPress", "zoomCanvas", "factor", "prev", "Math", "max", "min", "resetCanvasView", "handleCanvasMouseDown", "e", "button", "clientX", "clientY", "handleCanvasMouseMove", "handleCanvasMouseUp", "handleCanvasWheel", "preventDefault", "deltaY", "handleResize", "addEventListener", "removeEventListener", "tiktokContainer", "document", "getElementById", "style", "scroll<PERSON>eh<PERSON>or", "isScrolling", "handleScroll", "setTimeout", "canvas", "transform", "handleGlobalMouseMove", "handleGlobalMouseUp", "storedUser", "localStorage", "getItem", "bypassSecurity", "userData", "subscriptionTier", "current", "init", "doubleTap", "singleTap", "longPress", "destroy", "container", "innerHTML", "createGamificationUI", "then", "isConnected", "warn", "catch", "topicInput", "tabId", "currentTabId", "newTab", "createTab", "progress", "treeData", "err", "handleSubmit", "trim", "handleTabChange", "tab", "handleNewTab", "handleTabArticleAccess", "handleSpeechToggle", "getStatus", "isPlaying", "toggle", "speak", "handleSpeechStop", "stop", "handleSpeechRateChange", "rate", "setRate", "handleExportPDF", "exportAsPDF", "success", "gamResult", "handleExportWord", "exportAsWord", "handleCopyToClipboard", "copyToClipboard", "showMessage", "goBack", "goHome", "clearAllTabs", "quickLogin", "setItem", "className", "ref", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginRight", "marginLeft", "onTabChange", "onNewTab", "onTabArticleAccess", "background", "border", "color", "cursor", "padding", "borderRadius", "marginBottom", "onSubmit", "type", "value", "onChange", "target", "placeholder", "disabled", "totalBranches", "angle", "baseRadius", "levelOffset", "radius", "angleOffset", "sin", "finalAngle", "cos", "PI", "onDoubleClick", "width", "emoji", "subcategorii", "sub", "subIndex", "onTouchStart", "touch", "touches", "currentTarget", "touchStartTime", "Date", "now", "touchStartX", "touchStartY", "onTouchEnd", "changedTouches", "deltaTime", "deltaX", "abs", "lastTap", "lastTapTime", "display", "gap", "marginTop", "flexWrap", "alignItems", "fontSize", "step", "defaultValue", "parseFloat", "join", "lineHeight", "split", "paragraph", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Appv1/src/components/OptimizedApp.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport '../styles/optimized.css';\nimport gestureService, { createFlagWheel } from '../services/gestureService';\nimport speechService from '../services/speechService';\nimport exportService from '../services/exportService';\nimport gamificationService from '../services/optimizedGamificationService';\nimport { generateKnowledgeTree as generateTreeAPI, generateArticle as generateArticleAPI, testConnection } from '../services/openRouterService';\nimport tabService from '../services/tabService';\nimport TabManager from './TabManager';\nimport LanguageSwitcher from './LanguageSwitcher';\nimport { useTranslation, getCurrentLanguage } from '../utils/i18n';\n\n// Optimized Knowledge Tree Explorer - Pareto 80/20 Implementation\n// Focus on core functionality with maximum impact\n\nconst OptimizedApp = () => {\n  // Core state - now managed by tabs\n  const [currentView, setCurrentView] = useState('input'); // 'input', 'tree', 'article'\n  const [topic, setTopic] = useState('');\n  const [activeTab, setActiveTab] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [user, setUser] = useState(null);\n  const appRef = useRef(null);\n\n  // Canvas state for infinite tree view\n  const [canvasTransform, setCanvasTransform] = useState({ x: 0, y: 0, scale: 1 });\n  const [isDragging, setIsDragging] = useState(false);\n  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);\n\n  // Get current tab data\n  const tree = activeTab?.tree || null;\n  const selectedBranch = activeTab?.selectedBranch || null;\n  const article = activeTab?.article || null;\n\n  // Translation hook\n  const { t } = useTranslation();\n\n  // Available flags for the optimized version\n  const availableFlags = React.useMemo(() => [\n    { code: '-a', name: 'Article', description: t('flagArticle') },\n    { code: '-ex', name: 'Examples', description: t('flagExamples') },\n    { code: '-q', name: 'Quiz', description: t('flagQuiz') },\n    { code: '-vis', name: 'Visual', description: t('flagVisual') },\n    { code: '-path', name: 'Learning Path', description: t('flagPath') },\n    { code: '-case', name: 'Case Study', description: t('flagCase') },\n    { code: '-ro', name: 'Romanian', description: t('flagRomanian') }\n  ], [t]);\n\n  // Generate article with tabs support\n  const generateArticleForBranch = React.useCallback(async (branch, flags = ['-a']) => {\n    if (!activeTab) return;\n\n    setIsLoading(true);\n\n    // Set tab to loading state (yellow)\n    tabService.updateTabStatus(activeTab.id, 'loading', {\n      selectedBranch: branch,\n      article: null\n    });\n    setActiveTab(tabService.getTab(activeTab.id));\n\n    try {\n      console.log('📄 Generating article for branch:', branch.nume);\n      const articleData = await generateArticleAPI(activeTab.topic, branch, flags);\n\n      console.log('✅ Generated article data:', articleData);\n\n      // Map the article data to expected format\n      const mappedArticle = {\n        title: articleData.titlu || articleData.title || `${branch.nume} - ${activeTab.topic}`,\n        content: articleData.continut || articleData.content || 'Content not available',\n        topic: activeTab.topic,\n        flags: flags,\n        position: articleData.pozitie || `${activeTab.topic} → ${branch.nume}`,\n        webSources: articleData.webSources || []\n      };\n\n      // Update tab with article and set to completed (green)\n      tabService.updateTabStatus(activeTab.id, 'completed', {\n        selectedBranch: branch,\n        article: mappedArticle\n      });\n\n      const updatedTab = tabService.getTab(activeTab.id);\n      setActiveTab(updatedTab);\n      setCurrentView('article');\n\n      // Award points for article generation\n      const result = gamificationService.awardPoints('ARTICLE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (error) {\n      console.error('❌ Error generating article:', error);\n      setError('Failed to generate article. Please try again.');\n\n      // Set tab back to pending on error\n      tabService.updateTabStatus(activeTab.id, 'pending', {\n        selectedBranch: branch,\n        article: null\n      });\n      setActiveTab(tabService.getTab(activeTab.id));\n    } finally {\n      setIsLoading(false);\n    }\n  }, [activeTab]);\n\n  // Handle branch selection (single tap)\n  const handleBranchSelect = React.useCallback((branch) => {\n    if (activeTab) {\n      tabService.updateTabStatus(activeTab.id, activeTab.status, { selectedBranch: branch });\n      setActiveTab(tabService.getTab(activeTab.id));\n    }\n  }, [activeTab]);\n\n  // Gesture handlers\n  const handleDoubleTap = React.useCallback((event, targetInfo) => {\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      // Show flag wheel on double tap of branch\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        createFlagWheel(\n          targetInfo.position,\n          availableFlags,\n          (selectedFlags) => {\n            console.log('Selected flags:', selectedFlags);\n          },\n          (selectedFlags) => {\n            generateArticleForBranch(branch, selectedFlags);\n          }\n        );\n      }\n    }\n  }, [tree, availableFlags, generateArticleForBranch]);\n\n  const handleSingleTap = React.useCallback((event, targetInfo) => {\n    // Single tap for normal selection\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        handleBranchSelect(branch);\n      }\n    }\n  }, [tree, handleBranchSelect]);\n\n  // Expand branch to create relevant sub-branches with AI\n  const expandBranch = React.useCallback(async (branch, branchIndex) => {\n    if (!activeTab || !tree) {\n      setError(t('noActiveTab') || 'No active tab or tree available');\n      return;\n    }\n\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      console.log('🌿 Expanding branch:', branch.nume, 'with AI-generated sub-branches');\n\n      // Create AI-generated sub-branches specifically for this branch\n      const currentLang = getCurrentLanguage();\n      const prompt = currentLang === 'ro'\n        ? `Generează 4 sub-ramuri specifice și relevante pentru \"${branch.nume}\" în contextul \"${tree.tema}\".\n\nDescrierea ramuri principale: ${branch.descriere}\n\nCreează sub-ramuri care să fie:\n- Specifice și relevante pentru \"${branch.nume}\"\n- Logice și bine organizate\n- Utile pentru învățare progresivă\n- În limba română\n\nRăspunde DOAR cu JSON în formatul:\n{\n  \"ramuri\": [\n    {\n      \"nume\": \"Nume sub-ramură\",\n      \"descriere\": \"Descriere detaliată\",\n      \"emoji\": \"🔧\",\n      \"subcategorii\": [\"subcategorie1\", \"subcategorie2\", \"subcategorie3\"]\n    }\n  ]\n}`\n        : `Generate 4 specific and relevant sub-branches for \"${branch.nume}\" in the context of \"${tree.tema}\".\n\nMain branch description: ${branch.descriere}\n\nCreate sub-branches that are:\n- Specific and relevant to \"${branch.nume}\"\n- Logical and well-organized\n- Useful for progressive learning\n- In English\n\nRespond ONLY with JSON in the format:\n{\n  \"branches\": [\n    {\n      \"nume\": \"Sub-branch name\",\n      \"descriere\": \"Detailed description\",\n      \"emoji\": \"🔧\",\n      \"subcategorii\": [\"subcategory1\", \"subcategory2\", \"subcategory3\"]\n    }\n  ]\n}`;\n\n      // Use DeepSeek R1 for intelligent sub-branch generation\n      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer 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`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': window.location.origin,\n          'X-Title': 'Knowledge Tree Explorer'\n        },\n        body: JSON.stringify({\n          model: 'deepseek/deepseek-r1-0528:free',\n          messages: [\n            {\n              role: 'system',\n              content: currentLang === 'ro'\n                ? 'Ești un expert în organizarea cunoștințelor. Generează sub-ramuri relevante și specifice în format JSON valid.'\n                : 'You are an expert in knowledge organization. Generate relevant and specific sub-branches in valid JSON format.'\n            },\n            {\n              role: 'user',\n              content: prompt\n            }\n          ],\n          temperature: 0.7,\n          max_tokens: 2000\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error(`API request failed: ${response.status}`);\n      }\n\n      const data = await response.json();\n      const responseText = data.choices[0]?.message?.content || '';\n\n      // Parse the JSON response\n      const jsonMatch = responseText.match(/\\{[\\s\\S]*\\}/);\n      if (!jsonMatch) {\n        throw new Error('No valid JSON found in response');\n      }\n\n      const expandedData = JSON.parse(jsonMatch[0]);\n      const subBranches = expandedData.ramuri || expandedData.branches || [];\n\n      if (subBranches.length === 0) {\n        throw new Error('No sub-branches generated');\n      }\n\n      console.log('✅ Generated', subBranches.length, 'AI sub-branches for:', branch.nume);\n\n      // Update tree with AI-generated sub-branches\n      const newTree = { ...tree };\n      newTree.ramuri = [\n        ...newTree.ramuri.slice(0, branchIndex + 1),\n        ...subBranches.map(subBranch => ({\n          ...subBranch,\n          isSubBranch: true,\n          parentBranch: branch.nume,\n          level: (branch.level || 0) + 1,\n          id: `${branch.nume}-${subBranch.nume}`.replace(/\\s+/g, '-').toLowerCase()\n        })),\n        ...newTree.ramuri.slice(branchIndex + 1)\n      ];\n\n      // Update tab with expanded tree\n      tabService.updateTabStatus(activeTab.id, 'completed', { tree: newTree });\n      setActiveTab(tabService.getTab(activeTab.id));\n\n      console.log('🌳 Tree expanded successfully with AI sub-branches');\n\n      // Award points for branch expansion\n      const result = gamificationService.awardPoints('BRANCH_EXPANDED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n\n    } catch (error) {\n      console.error('❌ Error expanding branch with AI:', error);\n      setError(t('failedToExpand') || 'Nu s-a putut extinde ramura. Te rugăm să încerci din nou.');\n    } finally {\n      setIsLoading(false);\n    }\n  }, [tree, activeTab, t]);\n\n  const handleLongPress = React.useCallback(async (event, targetInfo) => {\n    // Long press to expand branch and create tree effect\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        await expandBranch(branch, targetInfo.branchData.index);\n      }\n    }\n  }, [tree, expandBranch]);\n\n  // Canvas control functions\n  const zoomCanvas = React.useCallback((factor) => {\n    setCanvasTransform(prev => ({\n      ...prev,\n      scale: Math.max(0.1, Math.min(3, prev.scale * factor))\n    }));\n  }, []);\n\n  const resetCanvasView = React.useCallback(() => {\n    setCanvasTransform({ x: 0, y: 0, scale: 1 });\n  }, []);\n\n  const handleCanvasMouseDown = React.useCallback((e) => {\n    if (e.button === 0) { // Left mouse button\n      setIsDragging(true);\n      setDragStart({ x: e.clientX - canvasTransform.x, y: e.clientY - canvasTransform.y });\n    }\n  }, [canvasTransform]);\n\n  const handleCanvasMouseMove = React.useCallback((e) => {\n    if (isDragging) {\n      setCanvasTransform(prev => ({\n        ...prev,\n        x: e.clientX - dragStart.x,\n        y: e.clientY - dragStart.y\n      }));\n    }\n  }, [isDragging, dragStart]);\n\n  const handleCanvasMouseUp = React.useCallback(() => {\n    setIsDragging(false);\n  }, []);\n\n  const handleCanvasWheel = React.useCallback((e) => {\n    e.preventDefault();\n    const factor = e.deltaY > 0 ? 0.9 : 1.1;\n    zoomCanvas(factor);\n  }, [zoomCanvas]);\n\n  // Handle window resize for mobile detection\n  useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth <= 768);\n    };\n\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  // Initialize TikTok scroll behavior on mobile\n  useEffect(() => {\n    const tiktokContainer = document.getElementById('tiktok-scroll');\n    if (tiktokContainer && isMobile) {\n      // Smooth scroll behavior\n      tiktokContainer.style.scrollBehavior = 'smooth';\n\n      // Optional: Add snap scrolling enhancement\n      let isScrolling = false;\n      const handleScroll = () => {\n        if (!isScrolling) {\n          isScrolling = true;\n          setTimeout(() => {\n            isScrolling = false;\n          }, 150);\n        }\n      };\n\n      tiktokContainer.addEventListener('scroll', handleScroll);\n\n      return () => {\n        tiktokContainer.removeEventListener('scroll', handleScroll);\n      };\n    }\n  }, [isMobile]);\n\n  // Initialize canvas event listeners\n  useEffect(() => {\n    const canvas = document.getElementById('infinite-canvas');\n    if (canvas && !isMobile) {\n      // Apply transform\n      canvas.style.transform = `translate(${canvasTransform.x}px, ${canvasTransform.y}px) scale(${canvasTransform.scale})`;\n\n      canvas.addEventListener('mousedown', handleCanvasMouseDown);\n      canvas.addEventListener('wheel', handleCanvasWheel);\n\n      // Add global mouse events for dragging\n      const handleGlobalMouseMove = (e) => {\n        if (isDragging) {\n          handleCanvasMouseMove(e);\n        }\n      };\n\n      const handleGlobalMouseUp = () => {\n        if (isDragging) {\n          handleCanvasMouseUp();\n        }\n      };\n\n      document.addEventListener('mousemove', handleGlobalMouseMove);\n      document.addEventListener('mouseup', handleGlobalMouseUp);\n\n      return () => {\n        canvas.removeEventListener('mousedown', handleCanvasMouseDown);\n        canvas.removeEventListener('wheel', handleCanvasWheel);\n        document.removeEventListener('mousemove', handleGlobalMouseMove);\n        document.removeEventListener('mouseup', handleGlobalMouseUp);\n      };\n    }\n  }, [canvasTransform, isMobile, isDragging, handleCanvasMouseDown, handleCanvasMouseMove, handleCanvasMouseUp, handleCanvasWheel]);\n\n  // Initialize services and authentication\n  useEffect(() => {\n    const storedUser = localStorage.getItem('user');\n    const bypassSecurity = localStorage.getItem('bypassSecurity');\n\n    if (storedUser || bypassSecurity) {\n      const userData = {\n        id: 'user-1',\n        name: 'User',\n        subscriptionTier: 'premium'\n      };\n      setUser(userData);\n\n      // Award daily login points\n      const result = gamificationService.awardPoints('DAILY_LOGIN');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n\n    // Initialize gesture service\n    if (appRef.current) {\n      gestureService.init(appRef.current, {\n        doubleTap: handleDoubleTap,\n        singleTap: handleSingleTap,\n        longPress: handleLongPress\n      });\n    }\n\n    return () => {\n      gestureService.destroy();\n    };\n  }, [handleDoubleTap, handleSingleTap, handleLongPress]);\n\n  // Initialize gamification UI when user is logged in\n  useEffect(() => {\n    if (user) {\n      const container = document.getElementById('gamification-container');\n      if (container) {\n        // Clear existing content\n        container.innerHTML = '';\n        // Create gamification UI\n        gamificationService.createGamificationUI(container);\n      }\n\n      // Test API connection\n      testConnection().then(isConnected => {\n        console.log('🔌 API Connection Status:', isConnected ? '✅ Connected' : '❌ Failed');\n        if (!isConnected) {\n          console.warn('⚠️ API connection failed. Check your API key and internet connection.');\n        }\n      }).catch(error => {\n        console.error('❌ API connection test error:', error);\n      });\n    }\n  }, [user]);\n\n  // Core API call - using optimized service with tabs\n  const generateKnowledgeTree = async (topicInput, tabId = null) => {\n    let currentTabId = tabId;\n\n    // Create new tab if none provided\n    if (!currentTabId) {\n      try {\n        const newTab = tabService.createTab(topicInput);\n        currentTabId = newTab.id;\n        setActiveTab(newTab);\n        setCurrentView('tree');\n      } catch (error) {\n        setError(error.message);\n        return;\n      }\n    }\n\n    // Update tab status to generating\n    tabService.updateTabStatus(currentTabId, 'generating', { progress: 10 });\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      console.log('🌳 Generating knowledge tree for:', topicInput, 'in tab:', currentTabId);\n\n      // Update progress\n      tabService.updateTabStatus(currentTabId, 'generating', { progress: 30 });\n\n      const treeData = await generateTreeAPI(topicInput, getCurrentLanguage());\n      console.log('✅ Generated tree data:', treeData);\n\n      // Update tab with completed tree\n      tabService.updateTabStatus(currentTabId, 'completed', {\n        tree: treeData,\n        progress: 100\n      });\n\n      // Update active tab if this is the current one\n      if (currentTabId === activeTab?.id) {\n        setActiveTab(tabService.getTab(currentTabId));\n      }\n\n      // Award points for tree generation\n      const result = gamificationService.awardPoints('TREE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (err) {\n      console.error('❌ Error generating tree:', err);\n      tabService.updateTabStatus(currentTabId, 'error');\n      setError(`Failed to generate knowledge tree: ${err.message}. Please try again.`);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n\n\n  // Handle form submission\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    if (topic.trim()) {\n      generateKnowledgeTree(topic.trim());\n      setTopic(''); // Clear input for next topic\n    }\n  };\n\n  // Handle tab changes\n  const handleTabChange = (tab) => {\n    // Clear any existing errors when switching tabs\n    setError(null);\n    setIsLoading(false);\n\n    setActiveTab(tab);\n    if (tab?.tree) {\n      setCurrentView('tree');\n    } else {\n      setCurrentView('input');\n    }\n  };\n\n  // Handle new tab creation\n  const handleNewTab = () => {\n    // Clear any existing errors and loading states\n    setError(null);\n    setIsLoading(false);\n\n    setCurrentView('input');\n    setActiveTab(null);\n    setTopic('');\n  };\n\n  const handleTabArticleAccess = (tab) => {\n    // Clear any existing errors when accessing article\n    setError(null);\n    setIsLoading(false);\n\n    setActiveTab(tab);\n    setCurrentView('article');\n  };\n\n\n\n  // Speech functions\n  const handleSpeechToggle = () => {\n    if (!article?.content) return;\n\n    if (speechService.getStatus().isPlaying) {\n      speechService.toggle();\n    } else {\n      speechService.speak(article.content);\n      // Award points for using speech\n      const result = gamificationService.awardPoints('SPEECH_USED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleSpeechStop = () => {\n    speechService.stop();\n  };\n\n  const handleSpeechRateChange = (rate) => {\n    speechService.setRate(rate);\n  };\n\n  // Export functions\n  const handleExportPDF = () => {\n    if (!article?.title || !article?.content) return;\n    const result = exportService.exportAsPDF(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleExportWord = () => {\n    if (!article?.title || !article?.content) return;\n    const result = exportService.exportAsWord(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleCopyToClipboard = async () => {\n    if (!article?.content) return;\n    const result = await exportService.copyToClipboard(article.content);\n    exportService.showMessage(result.message, result.success ? 'success' : 'error');\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n\n\n  // Navigation functions\n  const goBack = () => {\n    if (currentView === 'article') {\n      setCurrentView('tree');\n      if (activeTab) {\n        tabService.updateTabStatus(activeTab.id, activeTab.status, { article: null });\n        setActiveTab(tabService.getTab(activeTab.id));\n      }\n    } else if (currentView === 'tree') {\n      setCurrentView('input');\n    }\n  };\n\n\n\n  const goHome = () => {\n    setCurrentView('input');\n    setActiveTab(null);\n    setTopic('');\n    // Clear all tabs\n    tabService.clearAllTabs();\n  };\n\n  // Quick login for development\n  const quickLogin = () => {\n    localStorage.setItem('bypassSecurity', 'true');\n    setUser({ id: 'dev-1', name: 'Developer', subscriptionTier: 'premium' });\n  };\n\n  return (\n    <div className=\"app\" ref={appRef}>\n      {/* Header */}\n      <header className=\"app-header\">\n        <div className=\"header-content\">\n          <button onClick={goHome} className=\"logo-text\">\n            {t('appTitle')}\n          </button>\n          <div className=\"header-right\">\n            {user && (\n              <div id=\"gamification-container\" style={{ marginRight: '16px' }}>\n                {/* Gamification UI will be inserted here */}\n              </div>\n            )}\n            <LanguageSwitcher />\n            {!user ? (\n              <button onClick={quickLogin} className=\"btn btn-primary\" style={{ marginLeft: '12px' }}>\n                {t('quickLogin')}\n              </button>\n            ) : (\n              <span style={{ marginLeft: '12px' }}>{t('welcome')}, {user.name}!</span>\n            )}\n          </div>\n        </div>\n      </header>\n\n      {/* Tab Manager */}\n      {user && (\n        <TabManager\n          onTabChange={handleTabChange}\n          onNewTab={handleNewTab}\n          onTabArticleAccess={handleTabArticleAccess}\n        />\n      )}\n\n      {/* Main Content */}\n      <main className=\"main-content\">\n        {error && (\n          <div className=\"error\">\n            ⚠️ {error}\n            <button onClick={() => setError(null)} style={{marginLeft: 'auto', background: 'none', border: 'none', color: 'white', cursor: 'pointer'}}>\n              ✕\n            </button>\n          </div>\n        )}\n\n        {/* Topic Input View */}\n        {currentView === 'input' && (\n          <div className=\"card text-center\">\n            <h1 className=\"title\">{t('appTitle')}</h1>\n            <p className=\"subtitle\">\n              Enter any topic to generate an interactive knowledge tree with AI-powered content.\n            </p>\n\n            {!user ? (\n              <div style={{background: '#f1f5f9', padding: '1rem', borderRadius: '0.5rem', marginBottom: '2rem'}}>\n                <p style={{color: '#334155', marginBottom: '1rem'}}>\n                  {t('loginRequired')}\n                </p>\n                <button onClick={quickLogin} className=\"btn btn-primary\">\n                  {t('quickLoginDev')}\n                </button>\n              </div>\n            ) : (\n              <form onSubmit={handleSubmit}>\n                <div className=\"form-group\">\n                  <input\n                    type=\"text\"\n                    value={topic}\n                    onChange={(e) => setTopic(e.target.value)}\n                    placeholder={t('topicPlaceholder')}\n                    className=\"form-input\"\n                    disabled={isLoading}\n                  />\n                </div>\n                <button\n                  type=\"submit\"\n                  disabled={isLoading || !topic.trim()}\n                  className=\"btn btn-primary\"\n                >\n                  {isLoading ? (\n                    <>\n                      <span className=\"spinner\"></span>\n                      {t('generating')}\n                    </>\n                  ) : (\n                    <>\n                      {t('exploreKnowledge')}\n                    </>\n                  )}\n                </button>\n              </form>\n            )}\n          </div>\n        )}\n\n        {/* Tree View - Desktop: Infinite Tree, Mobile: TikTok Style */}\n        {currentView === 'tree' && tree && (\n          <div className=\"tree-container\">\n            {/* Desktop Tree View - Infinite Mind Map */}\n            <div className=\"desktop-tree-view\">\n              <div className=\"infinite-canvas\" id=\"infinite-canvas\">\n                {/* Central Topic Node */}\n                <div className=\"central-topic-node\">\n                  <div className=\"topic-input-center\">\n                    <h2>{tree.tema}</h2>\n                    <button onClick={goBack} className=\"btn btn-secondary back-btn\">\n                      {t('backToTree')}\n                    </button>\n                  </div>\n                </div>\n\n                {/* Branches positioned around center */}\n                {tree.ramuri.map((branch, index) => {\n                  const totalBranches = tree.ramuri.length;\n                  const angle = (index * 360) / totalBranches;\n                  const baseRadius = 300;\n                  const levelOffset = (branch.level || 0) * 120;\n                  const radius = baseRadius + levelOffset;\n\n                  // Add some randomness for more organic look\n                  const angleOffset = (Math.sin(index * 2.5) * 15);\n                  const finalAngle = angle + angleOffset;\n\n                  const x = Math.cos((finalAngle * Math.PI) / 180) * radius;\n                  const y = Math.sin((finalAngle * Math.PI) / 180) * radius;\n\n                  return (\n                    <div\n                      key={index}\n                      className={`tree-branch-node ${selectedBranch === branch ? 'selected' : ''}`}\n                      style={{\n                        transform: `translate(${x}px, ${y}px)`,\n                        '--branch-angle': `${finalAngle}deg`\n                      }}\n                      data-index={index}\n                      data-level={branch.level || 0}\n                      onClick={() => handleBranchSelect(branch)}\n                      onDoubleClick={() => generateArticleForBranch(branch)}\n                    >\n                      {/* Connection Line to Center */}\n                      <div className=\"branch-connection-line\" style={{\n                        transform: `rotate(${angle + 180}deg)`,\n                        width: `${radius}px`\n                      }}></div>\n\n                      <div className=\"branch-content\">\n                        <div className=\"branch-emoji\">{branch.emoji}</div>\n                        <h3 className=\"branch-name\">{branch.nume}</h3>\n                        <p className=\"branch-description\">{branch.descriere}</p>\n\n                        {branch.subcategorii && (\n                          <div className=\"branch-subcategories\">\n                            {branch.subcategorii.slice(0, 2).map((sub, subIndex) => (\n                              <span key={subIndex} className=\"subcategory-tag\">\n                                {sub}\n                              </span>\n                            ))}\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  );\n                })}\n\n                {isLoading && (\n                  <div className=\"loading-overlay\">\n                    <span className=\"spinner\"></span>\n                    <span>{t('loading')}</span>\n                  </div>\n                )}\n              </div>\n\n              {/* Pan & Zoom Controls */}\n              <div className=\"canvas-controls\">\n                <button className=\"control-btn\" onClick={() => zoomCanvas(1.2)}>🔍+</button>\n                <button className=\"control-btn\" onClick={() => zoomCanvas(0.8)}>🔍-</button>\n                <button className=\"control-btn\" onClick={() => resetCanvasView()}>🎯</button>\n              </div>\n            </div>\n\n            {/* Mobile Tree View - TikTok Style */}\n            <div className=\"mobile-tree-view\">\n              <div className=\"tiktok-container\">\n                <div className=\"tiktok-header\">\n                  <h2>{tree.tema}</h2>\n                  <button onClick={goBack} className=\"btn btn-secondary\">\n                    {t('backToTree')}\n                  </button>\n                </div>\n\n                <div className=\"tiktok-scroll-container\" id=\"tiktok-scroll\">\n                  {tree.ramuri.map((branch, index) => (\n                    <div\n                      key={index}\n                      className={`tiktok-branch-card ${selectedBranch === branch ? 'selected' : ''}`}\n                      data-index={index}\n                      onClick={() => handleBranchSelect(branch)}\n                      onDoubleClick={() => generateArticleForBranch(branch)}\n                      onTouchStart={(e) => {\n                        const touch = e.touches[0];\n                        e.currentTarget.touchStartTime = Date.now();\n                        e.currentTarget.touchStartX = touch.clientX;\n                        e.currentTarget.touchStartY = touch.clientY;\n                      }}\n                      onTouchEnd={(e) => {\n                        const touch = e.changedTouches[0];\n                        const deltaTime = Date.now() - (e.currentTarget.touchStartTime || 0);\n                        const deltaX = Math.abs(touch.clientX - (e.currentTarget.touchStartX || 0));\n                        const deltaY = Math.abs(touch.clientY - (e.currentTarget.touchStartY || 0));\n\n                        // Long press detection (500ms+, minimal movement)\n                        if (deltaTime > 500 && deltaX < 10 && deltaY < 10) {\n                          e.preventDefault();\n                          expandBranch(branch, index);\n                        }\n                        // Double tap detection\n                        else if (deltaTime < 300 && deltaX < 10 && deltaY < 10) {\n                          const now = Date.now();\n                          const lastTap = e.currentTarget.lastTapTime || 0;\n                          if (now - lastTap < 300) {\n                            e.preventDefault();\n                            generateArticleForBranch(branch);\n                          }\n                          e.currentTarget.lastTapTime = now;\n                        }\n                      }}\n                    >\n                      <div className=\"tiktok-card-content\">\n                        <div className=\"branch-emoji-large\">{branch.emoji}</div>\n                        <h3 className=\"branch-name-large\">{branch.nume}</h3>\n                        <p className=\"branch-description-large\">{branch.descriere}</p>\n\n                        {branch.subcategorii && (\n                          <div className=\"tiktok-subcategories\">\n                            {branch.subcategorii.slice(0, 3).map((sub, subIndex) => (\n                              <span key={subIndex} className=\"tiktok-subcategory-tag\">\n                                {sub}\n                              </span>\n                            ))}\n                            {branch.subcategorii.length > 3 && (\n                              <span className=\"tiktok-subcategory-tag more\">\n                                +{branch.subcategorii.length - 3}\n                              </span>\n                            )}\n                          </div>\n                        )}\n\n                        <div className=\"tiktok-gesture-hint\">\n                          <span className=\"tiktok-action-hint\">📖 Swipe down alte crengii</span>\n                          <span className=\"tiktok-action-hint\">🌿 Long-press pentru expansiune</span>\n                        </div>\n                      </div>\n\n                      {/* Level indicator for sub-branches */}\n                      {(branch.level || 0) > 0 && (\n                        <div className=\"tiktok-level-indicator\">\n                          <div className=\"level-badge\">Nivel {branch.level}</div>\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </div>\n\n                {isLoading && (\n                  <div className=\"tiktok-loading\">\n                    <span className=\"spinner\"></span>\n                    <span>{t('loading')}</span>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Article View - Redesigned */}\n        {currentView === 'article' && article && (\n          <div className=\"article-container\">\n            <div className=\"article-card\">\n              <div className=\"article-header\">\n                <button onClick={goBack} className=\"btn btn-secondary article-back-btn\">\n                  {t('backToTree')}\n                </button>\n\n                {/* Article Controls */}\n                <div className=\"article-controls\" style={{\n                  display: 'flex',\n                  gap: '8px',\n                  marginTop: '1rem',\n                  flexWrap: 'wrap'\n                }}>\n                  {/* Speech Controls */}\n                  <div className=\"speech-controls-compact\" style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '8px',\n                    padding: '8px 12px',\n                    background: '#f1f5f9',\n                    borderRadius: '6px',\n                    border: '1px solid #e2e8f0'\n                  }}>\n                    <button\n                      onClick={handleSpeechToggle}\n                      className=\"btn-icon\"\n                      title=\"Play/Pause Speech\"\n                      style={{\n                        background: 'none',\n                        border: 'none',\n                        fontSize: '16px',\n                        cursor: 'pointer',\n                        padding: '4px'\n                      }}\n                    >\n                      {speechService.getStatus().isPlaying ? '⏸️' : '▶️'}\n                    </button>\n                    <button\n                      onClick={handleSpeechStop}\n                      className=\"btn-icon\"\n                      title=\"Stop Speech\"\n                      style={{\n                        background: 'none',\n                        border: 'none',\n                        fontSize: '16px',\n                        cursor: 'pointer',\n                        padding: '4px'\n                      }}\n                    >\n                      ⏹️\n                    </button>\n                    <input\n                      type=\"range\"\n                      min=\"0.5\"\n                      max=\"2\"\n                      step=\"0.1\"\n                      defaultValue=\"1\"\n                      onChange={(e) => handleSpeechRateChange(parseFloat(e.target.value))}\n                      style={{width: '60px'}}\n                      title=\"Speech Speed\"\n                    />\n                    <span style={{fontSize: '12px', color: '#64748b'}}>🗣️</span>\n                  </div>\n\n                  {/* Export Controls */}\n                  <div className=\"export-controls-compact\" style={{\n                    display: 'flex',\n                    gap: '4px'\n                  }}>\n                    <button\n                      onClick={handleCopyToClipboard}\n                      className=\"btn btn-secondary\"\n                      style={{padding: '6px 12px', fontSize: '12px'}}\n                      title=\"Copy to Clipboard\"\n                    >\n                      📋 Copy\n                    </button>\n                    <button\n                      onClick={handleExportPDF}\n                      className=\"btn btn-secondary\"\n                      style={{padding: '6px 12px', fontSize: '12px'}}\n                      title=\"Export as PDF\"\n                    >\n                      📄 PDF\n                    </button>\n                    <button\n                      onClick={handleExportWord}\n                      className=\"btn btn-secondary\"\n                      style={{padding: '6px 12px', fontSize: '12px'}}\n                      title=\"Export as Word\"\n                    >\n                      📝 Word\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              <h1 className=\"title\">{article?.title || 'Loading...'}</h1>\n              <div style={{color: '#475569', marginBottom: '2rem', fontSize: '0.9rem'}}>\n                <span>{t('partOf')}: {article?.topic || 'Unknown'}</span>\n                {article?.flags && article.flags.length > 0 && (\n                  <span style={{marginLeft: '16px'}}>\n                    {t('flags')}: {article.flags.join(', ')}\n                  </span>\n                )}\n              </div>\n\n              <div className=\"article-content\" style={{lineHeight: '1.8', fontSize: '1.1rem'}}>\n                {article?.content ? article.content.split('\\n').map((paragraph, index) => (\n                  paragraph.trim() && (\n                    <p key={index} style={{marginBottom: '1rem'}}>\n                      {paragraph}\n                    </p>\n                  )\n                )) : (\n                  <p>Loading article content...</p>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n      </main>\n    </div>\n  );\n};\n\nexport default OptimizedApp;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,yBAAyB;AAChC,OAAOC,cAAc,IAAIC,eAAe,QAAQ,4BAA4B;AAC5E,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,mBAAmB,MAAM,0CAA0C;AAC1E,SAASC,qBAAqB,IAAIC,eAAe,EAAEC,eAAe,IAAIC,kBAAkB,EAAEC,cAAc,QAAQ,+BAA+B;AAC/I,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,cAAc,EAAEC,kBAAkB,QAAQ,eAAe;;AAElE;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEA,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACzD,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgC,KAAK,EAAEC,QAAQ,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACkC,IAAI,EAAEC,OAAO,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAMoC,MAAM,GAAGlC,MAAM,CAAC,IAAI,CAAC;;EAE3B;EACA,MAAM,CAACmC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtC,QAAQ,CAAC;IAAEuC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAE,CAAC,CAAC;EAChF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC4C,SAAS,EAAEC,YAAY,CAAC,GAAG7C,QAAQ,CAAC;IAAEuC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAC1D,MAAM,CAACM,QAAQ,EAAEC,WAAW,CAAC,GAAG/C,QAAQ,CAACgD,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;;EAElE;EACA,MAAMC,IAAI,GAAG,CAAAtB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEsB,IAAI,KAAI,IAAI;EACpC,MAAMC,cAAc,GAAG,CAAAvB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEuB,cAAc,KAAI,IAAI;EACxD,MAAMC,OAAO,GAAG,CAAAxB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEwB,OAAO,KAAI,IAAI;;EAE1C;EACA,MAAM;IAAEC;EAAE,CAAC,GAAGrC,cAAc,CAAC,CAAC;;EAE9B;EACA,MAAMsC,cAAc,GAAGvD,KAAK,CAACwD,OAAO,CAAC,MAAM,CACzC;IAAEC,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,SAAS;IAAEC,WAAW,EAAEL,CAAC,CAAC,aAAa;EAAE,CAAC,EAC9D;IAAEG,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAE,UAAU;IAAEC,WAAW,EAAEL,CAAC,CAAC,cAAc;EAAE,CAAC,EACjE;IAAEG,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,MAAM;IAAEC,WAAW,EAAEL,CAAC,CAAC,UAAU;EAAE,CAAC,EACxD;IAAEG,IAAI,EAAE,MAAM;IAAEC,IAAI,EAAE,QAAQ;IAAEC,WAAW,EAAEL,CAAC,CAAC,YAAY;EAAE,CAAC,EAC9D;IAAEG,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,eAAe;IAAEC,WAAW,EAAEL,CAAC,CAAC,UAAU;EAAE,CAAC,EACpE;IAAEG,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,YAAY;IAAEC,WAAW,EAAEL,CAAC,CAAC,UAAU;EAAE,CAAC,EACjE;IAAEG,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAE,UAAU;IAAEC,WAAW,EAAEL,CAAC,CAAC,cAAc;EAAE,CAAC,CAClE,EAAE,CAACA,CAAC,CAAC,CAAC;;EAEP;EACA,MAAMM,wBAAwB,GAAG5D,KAAK,CAAC6D,WAAW,CAAC,OAAOC,MAAM,EAAEC,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK;IACnF,IAAI,CAAClC,SAAS,EAAE;IAEhBG,YAAY,CAAC,IAAI,CAAC;;IAElB;IACAlB,UAAU,CAACkD,eAAe,CAACnC,SAAS,CAACoC,EAAE,EAAE,SAAS,EAAE;MAClDb,cAAc,EAAEU,MAAM;MACtBT,OAAO,EAAE;IACX,CAAC,CAAC;IACFvB,YAAY,CAAChB,UAAU,CAACoD,MAAM,CAACrC,SAAS,CAACoC,EAAE,CAAC,CAAC;IAE7C,IAAI;MACFE,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEN,MAAM,CAACO,IAAI,CAAC;MAC7D,MAAMC,WAAW,GAAG,MAAM1D,kBAAkB,CAACiB,SAAS,CAACF,KAAK,EAAEmC,MAAM,EAAEC,KAAK,CAAC;MAE5EI,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEE,WAAW,CAAC;;MAErD;MACA,MAAMC,aAAa,GAAG;QACpBC,KAAK,EAAEF,WAAW,CAACG,KAAK,IAAIH,WAAW,CAACE,KAAK,IAAI,GAAGV,MAAM,CAACO,IAAI,MAAMxC,SAAS,CAACF,KAAK,EAAE;QACtF+C,OAAO,EAAEJ,WAAW,CAACK,QAAQ,IAAIL,WAAW,CAACI,OAAO,IAAI,uBAAuB;QAC/E/C,KAAK,EAAEE,SAAS,CAACF,KAAK;QACtBoC,KAAK,EAAEA,KAAK;QACZa,QAAQ,EAAEN,WAAW,CAACO,OAAO,IAAI,GAAGhD,SAAS,CAACF,KAAK,MAAMmC,MAAM,CAACO,IAAI,EAAE;QACtES,UAAU,EAAER,WAAW,CAACQ,UAAU,IAAI;MACxC,CAAC;;MAED;MACAhE,UAAU,CAACkD,eAAe,CAACnC,SAAS,CAACoC,EAAE,EAAE,WAAW,EAAE;QACpDb,cAAc,EAAEU,MAAM;QACtBT,OAAO,EAAEkB;MACX,CAAC,CAAC;MAEF,MAAMQ,UAAU,GAAGjE,UAAU,CAACoD,MAAM,CAACrC,SAAS,CAACoC,EAAE,CAAC;MAClDnC,YAAY,CAACiD,UAAU,CAAC;MACxBrD,cAAc,CAAC,SAAS,CAAC;;MAEzB;MACA,MAAMsD,MAAM,GAAGxE,mBAAmB,CAACyE,WAAW,CAAC,mBAAmB,CAAC;MACnE,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5C7E,mBAAmB,CAAC8E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOpD,KAAK,EAAE;MACdkC,OAAO,CAAClC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDC,QAAQ,CAAC,+CAA+C,CAAC;;MAEzD;MACApB,UAAU,CAACkD,eAAe,CAACnC,SAAS,CAACoC,EAAE,EAAE,SAAS,EAAE;QAClDb,cAAc,EAAEU,MAAM;QACtBT,OAAO,EAAE;MACX,CAAC,CAAC;MACFvB,YAAY,CAAChB,UAAU,CAACoD,MAAM,CAACrC,SAAS,CAACoC,EAAE,CAAC,CAAC;IAC/C,CAAC,SAAS;MACRjC,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACH,SAAS,CAAC,CAAC;;EAEf;EACA,MAAM0D,kBAAkB,GAAGvF,KAAK,CAAC6D,WAAW,CAAEC,MAAM,IAAK;IACvD,IAAIjC,SAAS,EAAE;MACbf,UAAU,CAACkD,eAAe,CAACnC,SAAS,CAACoC,EAAE,EAAEpC,SAAS,CAAC2D,MAAM,EAAE;QAAEpC,cAAc,EAAEU;MAAO,CAAC,CAAC;MACtFhC,YAAY,CAAChB,UAAU,CAACoD,MAAM,CAACrC,SAAS,CAACoC,EAAE,CAAC,CAAC;IAC/C;EACF,CAAC,EAAE,CAACpC,SAAS,CAAC,CAAC;;EAEf;EACA,MAAM4D,eAAe,GAAGzF,KAAK,CAAC6D,WAAW,CAAC,CAAC6B,KAAK,EAAEC,UAAU,KAAK;IAC/D,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,IAAI1C,IAAI,EAAE;MAC5D;MACA,MAAMW,MAAM,GAAGX,IAAI,CAAC2C,MAAM,CAACH,UAAU,CAACE,UAAU,CAACE,KAAK,CAAC;MACvD,IAAIjC,MAAM,EAAE;QACVzD,eAAe,CACbsF,UAAU,CAACf,QAAQ,EACnBrB,cAAc,EACbyC,aAAa,IAAK;UACjB7B,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE4B,aAAa,CAAC;QAC/C,CAAC,EACAA,aAAa,IAAK;UACjBpC,wBAAwB,CAACE,MAAM,EAAEkC,aAAa,CAAC;QACjD,CACF,CAAC;MACH;IACF;EACF,CAAC,EAAE,CAAC7C,IAAI,EAAEI,cAAc,EAAEK,wBAAwB,CAAC,CAAC;EAEpD,MAAMqC,eAAe,GAAGjG,KAAK,CAAC6D,WAAW,CAAC,CAAC6B,KAAK,EAAEC,UAAU,KAAK;IAC/D;IACA,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,IAAI1C,IAAI,EAAE;MAC5D,MAAMW,MAAM,GAAGX,IAAI,CAAC2C,MAAM,CAACH,UAAU,CAACE,UAAU,CAACE,KAAK,CAAC;MACvD,IAAIjC,MAAM,EAAE;QACVyB,kBAAkB,CAACzB,MAAM,CAAC;MAC5B;IACF;EACF,CAAC,EAAE,CAACX,IAAI,EAAEoC,kBAAkB,CAAC,CAAC;;EAE9B;EACA,MAAMW,YAAY,GAAGlG,KAAK,CAAC6D,WAAW,CAAC,OAAOC,MAAM,EAAEqC,WAAW,KAAK;IACpE,IAAI,CAACtE,SAAS,IAAI,CAACsB,IAAI,EAAE;MACvBjB,QAAQ,CAACoB,CAAC,CAAC,aAAa,CAAC,IAAI,iCAAiC,CAAC;MAC/D;IACF;IAEAtB,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MAAA,IAAAkE,cAAA,EAAAC,qBAAA;MACFlC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEN,MAAM,CAACO,IAAI,EAAE,gCAAgC,CAAC;;MAElF;MACA,MAAMiC,WAAW,GAAGpF,kBAAkB,CAAC,CAAC;MACxC,MAAMqF,MAAM,GAAGD,WAAW,KAAK,IAAI,GAC/B,yDAAyDxC,MAAM,CAACO,IAAI,mBAAmBlB,IAAI,CAACqD,IAAI;AAC1G;AACA,gCAAgC1C,MAAM,CAAC2C,SAAS;AAChD;AACA;AACA,mCAAmC3C,MAAM,CAACO,IAAI;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,GACQ,sDAAsDP,MAAM,CAACO,IAAI,wBAAwBlB,IAAI,CAACqD,IAAI;AAC5G;AACA,2BAA2B1C,MAAM,CAAC2C,SAAS;AAC3C;AACA;AACA,8BAA8B3C,MAAM,CAACO,IAAI;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;;MAEI;MACA,MAAMqC,QAAQ,GAAG,MAAMC,KAAK,CAAC,+CAA+C,EAAE;QAC5EC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,eAAe,EAAE,kFAAkF;UACnG,cAAc,EAAE,kBAAkB;UAClC,cAAc,EAAE5D,MAAM,CAAC6D,QAAQ,CAACC,MAAM;UACtC,SAAS,EAAE;QACb,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,KAAK,EAAE,gCAAgC;UACvCC,QAAQ,EAAE,CACR;YACEC,IAAI,EAAE,QAAQ;YACd3C,OAAO,EAAE4B,WAAW,KAAK,IAAI,GACzB,gHAAgH,GAChH;UACN,CAAC,EACD;YACEe,IAAI,EAAE,MAAM;YACZ3C,OAAO,EAAE6B;UACX,CAAC,CACF;UACDe,WAAW,EAAE,GAAG;UAChBC,UAAU,EAAE;QACd,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACb,QAAQ,CAACc,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBf,QAAQ,CAAClB,MAAM,EAAE,CAAC;MAC3D;MAEA,MAAMkC,IAAI,GAAG,MAAMhB,QAAQ,CAACiB,IAAI,CAAC,CAAC;MAClC,MAAMC,YAAY,GAAG,EAAAxB,cAAA,GAAAsB,IAAI,CAACG,OAAO,CAAC,CAAC,CAAC,cAAAzB,cAAA,wBAAAC,qBAAA,GAAfD,cAAA,CAAiB0B,OAAO,cAAAzB,qBAAA,uBAAxBA,qBAAA,CAA0B3B,OAAO,KAAI,EAAE;;MAE5D;MACA,MAAMqD,SAAS,GAAGH,YAAY,CAACI,KAAK,CAAC,aAAa,CAAC;MACnD,IAAI,CAACD,SAAS,EAAE;QACd,MAAM,IAAIN,KAAK,CAAC,iCAAiC,CAAC;MACpD;MAEA,MAAMQ,YAAY,GAAGhB,IAAI,CAACiB,KAAK,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC;MAC7C,MAAMI,WAAW,GAAGF,YAAY,CAACnC,MAAM,IAAImC,YAAY,CAACG,QAAQ,IAAI,EAAE;MAEtE,IAAID,WAAW,CAAChD,MAAM,KAAK,CAAC,EAAE;QAC5B,MAAM,IAAIsC,KAAK,CAAC,2BAA2B,CAAC;MAC9C;MAEAtD,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE+D,WAAW,CAAChD,MAAM,EAAE,sBAAsB,EAAErB,MAAM,CAACO,IAAI,CAAC;;MAEnF;MACA,MAAMgE,OAAO,GAAG;QAAE,GAAGlF;MAAK,CAAC;MAC3BkF,OAAO,CAACvC,MAAM,GAAG,CACf,GAAGuC,OAAO,CAACvC,MAAM,CAACwC,KAAK,CAAC,CAAC,EAAEnC,WAAW,GAAG,CAAC,CAAC,EAC3C,GAAGgC,WAAW,CAACI,GAAG,CAACC,SAAS,KAAK;QAC/B,GAAGA,SAAS;QACZC,WAAW,EAAE,IAAI;QACjBC,YAAY,EAAE5E,MAAM,CAACO,IAAI;QACzBsE,KAAK,EAAE,CAAC7E,MAAM,CAAC6E,KAAK,IAAI,CAAC,IAAI,CAAC;QAC9B1E,EAAE,EAAE,GAAGH,MAAM,CAACO,IAAI,IAAImE,SAAS,CAACnE,IAAI,EAAE,CAACuE,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC;MAC1E,CAAC,CAAC,CAAC,EACH,GAAGR,OAAO,CAACvC,MAAM,CAACwC,KAAK,CAACnC,WAAW,GAAG,CAAC,CAAC,CACzC;;MAED;MACArF,UAAU,CAACkD,eAAe,CAACnC,SAAS,CAACoC,EAAE,EAAE,WAAW,EAAE;QAAEd,IAAI,EAAEkF;MAAQ,CAAC,CAAC;MACxEvG,YAAY,CAAChB,UAAU,CAACoD,MAAM,CAACrC,SAAS,CAACoC,EAAE,CAAC,CAAC;MAE7CE,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;;MAEjE;MACA,MAAMY,MAAM,GAAGxE,mBAAmB,CAACyE,WAAW,CAAC,iBAAiB,CAAC;MACjE,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5C7E,mBAAmB,CAAC8E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IAEF,CAAC,CAAC,OAAOpD,KAAK,EAAE;MACdkC,OAAO,CAAClC,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzDC,QAAQ,CAACoB,CAAC,CAAC,gBAAgB,CAAC,IAAI,2DAA2D,CAAC;IAC9F,CAAC,SAAS;MACRtB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACmB,IAAI,EAAEtB,SAAS,EAAEyB,CAAC,CAAC,CAAC;EAExB,MAAMwF,eAAe,GAAG9I,KAAK,CAAC6D,WAAW,CAAC,OAAO6B,KAAK,EAAEC,UAAU,KAAK;IACrE;IACA,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,IAAI1C,IAAI,EAAE;MAC5D,MAAMW,MAAM,GAAGX,IAAI,CAAC2C,MAAM,CAACH,UAAU,CAACE,UAAU,CAACE,KAAK,CAAC;MACvD,IAAIjC,MAAM,EAAE;QACV,MAAMoC,YAAY,CAACpC,MAAM,EAAE6B,UAAU,CAACE,UAAU,CAACE,KAAK,CAAC;MACzD;IACF;EACF,CAAC,EAAE,CAAC5C,IAAI,EAAE+C,YAAY,CAAC,CAAC;;EAExB;EACA,MAAM6C,UAAU,GAAG/I,KAAK,CAAC6D,WAAW,CAAEmF,MAAM,IAAK;IAC/CzG,kBAAkB,CAAC0G,IAAI,KAAK;MAC1B,GAAGA,IAAI;MACPvG,KAAK,EAAEwG,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEH,IAAI,CAACvG,KAAK,GAAGsG,MAAM,CAAC;IACvD,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,eAAe,GAAGrJ,KAAK,CAAC6D,WAAW,CAAC,MAAM;IAC9CtB,kBAAkB,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAE,CAAC,CAAC;EAC9C,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM4G,qBAAqB,GAAGtJ,KAAK,CAAC6D,WAAW,CAAE0F,CAAC,IAAK;IACrD,IAAIA,CAAC,CAACC,MAAM,KAAK,CAAC,EAAE;MAAE;MACpB5G,aAAa,CAAC,IAAI,CAAC;MACnBE,YAAY,CAAC;QAAEN,CAAC,EAAE+G,CAAC,CAACE,OAAO,GAAGnH,eAAe,CAACE,CAAC;QAAEC,CAAC,EAAE8G,CAAC,CAACG,OAAO,GAAGpH,eAAe,CAACG;MAAE,CAAC,CAAC;IACtF;EACF,CAAC,EAAE,CAACH,eAAe,CAAC,CAAC;EAErB,MAAMqH,qBAAqB,GAAG3J,KAAK,CAAC6D,WAAW,CAAE0F,CAAC,IAAK;IACrD,IAAI5G,UAAU,EAAE;MACdJ,kBAAkB,CAAC0G,IAAI,KAAK;QAC1B,GAAGA,IAAI;QACPzG,CAAC,EAAE+G,CAAC,CAACE,OAAO,GAAG5G,SAAS,CAACL,CAAC;QAC1BC,CAAC,EAAE8G,CAAC,CAACG,OAAO,GAAG7G,SAAS,CAACJ;MAC3B,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACE,UAAU,EAAEE,SAAS,CAAC,CAAC;EAE3B,MAAM+G,mBAAmB,GAAG5J,KAAK,CAAC6D,WAAW,CAAC,MAAM;IAClDjB,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMiH,iBAAiB,GAAG7J,KAAK,CAAC6D,WAAW,CAAE0F,CAAC,IAAK;IACjDA,CAAC,CAACO,cAAc,CAAC,CAAC;IAClB,MAAMd,MAAM,GAAGO,CAAC,CAACQ,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;IACvChB,UAAU,CAACC,MAAM,CAAC;EACpB,CAAC,EAAE,CAACD,UAAU,CAAC,CAAC;;EAEhB;EACA7I,SAAS,CAAC,MAAM;IACd,MAAM8J,YAAY,GAAGA,CAAA,KAAM;MACzBhH,WAAW,CAACC,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;IACvC,CAAC;IAEDD,MAAM,CAACgH,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAM/G,MAAM,CAACiH,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA9J,SAAS,CAAC,MAAM;IACd,MAAMiK,eAAe,GAAGC,QAAQ,CAACC,cAAc,CAAC,eAAe,CAAC;IAChE,IAAIF,eAAe,IAAIpH,QAAQ,EAAE;MAC/B;MACAoH,eAAe,CAACG,KAAK,CAACC,cAAc,GAAG,QAAQ;;MAE/C;MACA,IAAIC,WAAW,GAAG,KAAK;MACvB,MAAMC,YAAY,GAAGA,CAAA,KAAM;QACzB,IAAI,CAACD,WAAW,EAAE;UAChBA,WAAW,GAAG,IAAI;UAClBE,UAAU,CAAC,MAAM;YACfF,WAAW,GAAG,KAAK;UACrB,CAAC,EAAE,GAAG,CAAC;QACT;MACF,CAAC;MAEDL,eAAe,CAACF,gBAAgB,CAAC,QAAQ,EAAEQ,YAAY,CAAC;MAExD,OAAO,MAAM;QACXN,eAAe,CAACD,mBAAmB,CAAC,QAAQ,EAAEO,YAAY,CAAC;MAC7D,CAAC;IACH;EACF,CAAC,EAAE,CAAC1H,QAAQ,CAAC,CAAC;;EAEd;EACA7C,SAAS,CAAC,MAAM;IACd,MAAMyK,MAAM,GAAGP,QAAQ,CAACC,cAAc,CAAC,iBAAiB,CAAC;IACzD,IAAIM,MAAM,IAAI,CAAC5H,QAAQ,EAAE;MACvB;MACA4H,MAAM,CAACL,KAAK,CAACM,SAAS,GAAG,aAAatI,eAAe,CAACE,CAAC,OAAOF,eAAe,CAACG,CAAC,aAAaH,eAAe,CAACI,KAAK,GAAG;MAEpHiI,MAAM,CAACV,gBAAgB,CAAC,WAAW,EAAEX,qBAAqB,CAAC;MAC3DqB,MAAM,CAACV,gBAAgB,CAAC,OAAO,EAAEJ,iBAAiB,CAAC;;MAEnD;MACA,MAAMgB,qBAAqB,GAAItB,CAAC,IAAK;QACnC,IAAI5G,UAAU,EAAE;UACdgH,qBAAqB,CAACJ,CAAC,CAAC;QAC1B;MACF,CAAC;MAED,MAAMuB,mBAAmB,GAAGA,CAAA,KAAM;QAChC,IAAInI,UAAU,EAAE;UACdiH,mBAAmB,CAAC,CAAC;QACvB;MACF,CAAC;MAEDQ,QAAQ,CAACH,gBAAgB,CAAC,WAAW,EAAEY,qBAAqB,CAAC;MAC7DT,QAAQ,CAACH,gBAAgB,CAAC,SAAS,EAAEa,mBAAmB,CAAC;MAEzD,OAAO,MAAM;QACXH,MAAM,CAACT,mBAAmB,CAAC,WAAW,EAAEZ,qBAAqB,CAAC;QAC9DqB,MAAM,CAACT,mBAAmB,CAAC,OAAO,EAAEL,iBAAiB,CAAC;QACtDO,QAAQ,CAACF,mBAAmB,CAAC,WAAW,EAAEW,qBAAqB,CAAC;QAChET,QAAQ,CAACF,mBAAmB,CAAC,SAAS,EAAEY,mBAAmB,CAAC;MAC9D,CAAC;IACH;EACF,CAAC,EAAE,CAACxI,eAAe,EAAES,QAAQ,EAAEJ,UAAU,EAAE2G,qBAAqB,EAAEK,qBAAqB,EAAEC,mBAAmB,EAAEC,iBAAiB,CAAC,CAAC;;EAEjI;EACA3J,SAAS,CAAC,MAAM;IACd,MAAM6K,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAC/C,MAAMC,cAAc,GAAGF,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAE7D,IAAIF,UAAU,IAAIG,cAAc,EAAE;MAChC,MAAMC,QAAQ,GAAG;QACflH,EAAE,EAAE,QAAQ;QACZP,IAAI,EAAE,MAAM;QACZ0H,gBAAgB,EAAE;MACpB,CAAC;MACDhJ,OAAO,CAAC+I,QAAQ,CAAC;;MAEjB;MACA,MAAMnG,MAAM,GAAGxE,mBAAmB,CAACyE,WAAW,CAAC,aAAa,CAAC;MAC7D,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5C7E,mBAAmB,CAAC8E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;;IAEA;IACA,IAAIhD,MAAM,CAACgJ,OAAO,EAAE;MAClBjL,cAAc,CAACkL,IAAI,CAACjJ,MAAM,CAACgJ,OAAO,EAAE;QAClCE,SAAS,EAAE9F,eAAe;QAC1B+F,SAAS,EAAEvF,eAAe;QAC1BwF,SAAS,EAAE3C;MACb,CAAC,CAAC;IACJ;IAEA,OAAO,MAAM;MACX1I,cAAc,CAACsL,OAAO,CAAC,CAAC;IAC1B,CAAC;EACH,CAAC,EAAE,CAACjG,eAAe,EAAEQ,eAAe,EAAE6C,eAAe,CAAC,CAAC;;EAEvD;EACA5I,SAAS,CAAC,MAAM;IACd,IAAIiC,IAAI,EAAE;MACR,MAAMwJ,SAAS,GAAGvB,QAAQ,CAACC,cAAc,CAAC,wBAAwB,CAAC;MACnE,IAAIsB,SAAS,EAAE;QACb;QACAA,SAAS,CAACC,SAAS,GAAG,EAAE;QACxB;QACApL,mBAAmB,CAACqL,oBAAoB,CAACF,SAAS,CAAC;MACrD;;MAEA;MACA9K,cAAc,CAAC,CAAC,CAACiL,IAAI,CAACC,WAAW,IAAI;QACnC5H,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE2H,WAAW,GAAG,aAAa,GAAG,UAAU,CAAC;QAClF,IAAI,CAACA,WAAW,EAAE;UAChB5H,OAAO,CAAC6H,IAAI,CAAC,uEAAuE,CAAC;QACvF;MACF,CAAC,CAAC,CAACC,KAAK,CAAChK,KAAK,IAAI;QAChBkC,OAAO,CAAClC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACE,IAAI,CAAC,CAAC;;EAEV;EACA,MAAM1B,qBAAqB,GAAG,MAAAA,CAAOyL,UAAU,EAAEC,KAAK,GAAG,IAAI,KAAK;IAChE,IAAIC,YAAY,GAAGD,KAAK;;IAExB;IACA,IAAI,CAACC,YAAY,EAAE;MACjB,IAAI;QACF,MAAMC,MAAM,GAAGvL,UAAU,CAACwL,SAAS,CAACJ,UAAU,CAAC;QAC/CE,YAAY,GAAGC,MAAM,CAACpI,EAAE;QACxBnC,YAAY,CAACuK,MAAM,CAAC;QACpB3K,cAAc,CAAC,MAAM,CAAC;MACxB,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,QAAQ,CAACD,KAAK,CAAC6F,OAAO,CAAC;QACvB;MACF;IACF;;IAEA;IACAhH,UAAU,CAACkD,eAAe,CAACoI,YAAY,EAAE,YAAY,EAAE;MAAEG,QAAQ,EAAE;IAAG,CAAC,CAAC;IACxEvK,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACFiC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE8H,UAAU,EAAE,SAAS,EAAEE,YAAY,CAAC;;MAErF;MACAtL,UAAU,CAACkD,eAAe,CAACoI,YAAY,EAAE,YAAY,EAAE;QAAEG,QAAQ,EAAE;MAAG,CAAC,CAAC;MAExE,MAAMC,QAAQ,GAAG,MAAM9L,eAAe,CAACwL,UAAU,EAAEhL,kBAAkB,CAAC,CAAC,CAAC;MACxEiD,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEoI,QAAQ,CAAC;;MAE/C;MACA1L,UAAU,CAACkD,eAAe,CAACoI,YAAY,EAAE,WAAW,EAAE;QACpDjJ,IAAI,EAAEqJ,QAAQ;QACdD,QAAQ,EAAE;MACZ,CAAC,CAAC;;MAEF;MACA,IAAIH,YAAY,MAAKvK,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEoC,EAAE,GAAE;QAClCnC,YAAY,CAAChB,UAAU,CAACoD,MAAM,CAACkI,YAAY,CAAC,CAAC;MAC/C;;MAEA;MACA,MAAMpH,MAAM,GAAGxE,mBAAmB,CAACyE,WAAW,CAAC,gBAAgB,CAAC;MAChE,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5C7E,mBAAmB,CAAC8E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOoH,GAAG,EAAE;MACZtI,OAAO,CAAClC,KAAK,CAAC,0BAA0B,EAAEwK,GAAG,CAAC;MAC9C3L,UAAU,CAACkD,eAAe,CAACoI,YAAY,EAAE,OAAO,CAAC;MACjDlK,QAAQ,CAAC,sCAAsCuK,GAAG,CAAC3E,OAAO,qBAAqB,CAAC;IAClF,CAAC,SAAS;MACR9F,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAID;EACA,MAAM0K,YAAY,GAAInD,CAAC,IAAK;IAC1BA,CAAC,CAACO,cAAc,CAAC,CAAC;IAClB,IAAInI,KAAK,CAACgL,IAAI,CAAC,CAAC,EAAE;MAChBlM,qBAAqB,CAACkB,KAAK,CAACgL,IAAI,CAAC,CAAC,CAAC;MACnC/K,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB;EACF,CAAC;;EAED;EACA,MAAMgL,eAAe,GAAIC,GAAG,IAAK;IAC/B;IACA3K,QAAQ,CAAC,IAAI,CAAC;IACdF,YAAY,CAAC,KAAK,CAAC;IAEnBF,YAAY,CAAC+K,GAAG,CAAC;IACjB,IAAIA,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAE1J,IAAI,EAAE;MACbzB,cAAc,CAAC,MAAM,CAAC;IACxB,CAAC,MAAM;MACLA,cAAc,CAAC,OAAO,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMoL,YAAY,GAAGA,CAAA,KAAM;IACzB;IACA5K,QAAQ,CAAC,IAAI,CAAC;IACdF,YAAY,CAAC,KAAK,CAAC;IAEnBN,cAAc,CAAC,OAAO,CAAC;IACvBI,YAAY,CAAC,IAAI,CAAC;IAClBF,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAMmL,sBAAsB,GAAIF,GAAG,IAAK;IACtC;IACA3K,QAAQ,CAAC,IAAI,CAAC;IACdF,YAAY,CAAC,KAAK,CAAC;IAEnBF,YAAY,CAAC+K,GAAG,CAAC;IACjBnL,cAAc,CAAC,SAAS,CAAC;EAC3B,CAAC;;EAID;EACA,MAAMsL,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI,EAAC3J,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEqB,OAAO,GAAE;IAEvB,IAAIpE,aAAa,CAAC2M,SAAS,CAAC,CAAC,CAACC,SAAS,EAAE;MACvC5M,aAAa,CAAC6M,MAAM,CAAC,CAAC;IACxB,CAAC,MAAM;MACL7M,aAAa,CAAC8M,KAAK,CAAC/J,OAAO,CAACqB,OAAO,CAAC;MACpC;MACA,MAAMM,MAAM,GAAGxE,mBAAmB,CAACyE,WAAW,CAAC,aAAa,CAAC;MAC7D,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5C7E,mBAAmB,CAAC8E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAMgI,gBAAgB,GAAGA,CAAA,KAAM;IAC7B/M,aAAa,CAACgN,IAAI,CAAC,CAAC;EACtB,CAAC;EAED,MAAMC,sBAAsB,GAAIC,IAAI,IAAK;IACvClN,aAAa,CAACmN,OAAO,CAACD,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,EAACrK,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEmB,KAAK,KAAI,EAACnB,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEqB,OAAO,GAAE;IAC1C,MAAMM,MAAM,GAAGzE,aAAa,CAACoN,WAAW,CAACtK,OAAO,EAAE,GAAGA,OAAO,CAACmB,KAAK,CAACoE,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,EAAE,CAAC;IACnG,IAAI5D,MAAM,CAAC4I,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAGrN,mBAAmB,CAACyE,WAAW,CAAC,aAAa,CAAC;MAChE,IAAI4I,SAAS,CAAC3I,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxC0I,SAAS,CAAC3I,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/C7E,mBAAmB,CAAC8E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAMyI,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,EAACzK,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEmB,KAAK,KAAI,EAACnB,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEqB,OAAO,GAAE;IAC1C,MAAMM,MAAM,GAAGzE,aAAa,CAACwN,YAAY,CAAC1K,OAAO,EAAE,GAAGA,OAAO,CAACmB,KAAK,CAACoE,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,EAAE,CAAC;IACpG,IAAI5D,MAAM,CAAC4I,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAGrN,mBAAmB,CAACyE,WAAW,CAAC,aAAa,CAAC;MAChE,IAAI4I,SAAS,CAAC3I,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxC0I,SAAS,CAAC3I,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/C7E,mBAAmB,CAAC8E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAM2I,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI,EAAC3K,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEqB,OAAO,GAAE;IACvB,MAAMM,MAAM,GAAG,MAAMzE,aAAa,CAAC0N,eAAe,CAAC5K,OAAO,CAACqB,OAAO,CAAC;IACnEnE,aAAa,CAAC2N,WAAW,CAAClJ,MAAM,CAAC8C,OAAO,EAAE9C,MAAM,CAAC4I,OAAO,GAAG,SAAS,GAAG,OAAO,CAAC;IAC/E,IAAI5I,MAAM,CAAC4I,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAGrN,mBAAmB,CAACyE,WAAW,CAAC,aAAa,CAAC;MAChE,IAAI4I,SAAS,CAAC3I,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxC0I,SAAS,CAAC3I,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/C7E,mBAAmB,CAAC8E,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;;EAID;EACA,MAAM8I,MAAM,GAAGA,CAAA,KAAM;IACnB,IAAI1M,WAAW,KAAK,SAAS,EAAE;MAC7BC,cAAc,CAAC,MAAM,CAAC;MACtB,IAAIG,SAAS,EAAE;QACbf,UAAU,CAACkD,eAAe,CAACnC,SAAS,CAACoC,EAAE,EAAEpC,SAAS,CAAC2D,MAAM,EAAE;UAAEnC,OAAO,EAAE;QAAK,CAAC,CAAC;QAC7EvB,YAAY,CAAChB,UAAU,CAACoD,MAAM,CAACrC,SAAS,CAACoC,EAAE,CAAC,CAAC;MAC/C;IACF,CAAC,MAAM,IAAIxC,WAAW,KAAK,MAAM,EAAE;MACjCC,cAAc,CAAC,OAAO,CAAC;IACzB;EACF,CAAC;EAID,MAAM0M,MAAM,GAAGA,CAAA,KAAM;IACnB1M,cAAc,CAAC,OAAO,CAAC;IACvBI,YAAY,CAAC,IAAI,CAAC;IAClBF,QAAQ,CAAC,EAAE,CAAC;IACZ;IACAd,UAAU,CAACuN,YAAY,CAAC,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBtD,YAAY,CAACuD,OAAO,CAAC,gBAAgB,EAAE,MAAM,CAAC;IAC9CnM,OAAO,CAAC;MAAE6B,EAAE,EAAE,OAAO;MAAEP,IAAI,EAAE,WAAW;MAAE0H,gBAAgB,EAAE;IAAU,CAAC,CAAC;EAC1E,CAAC;EAED,oBACEhK,OAAA;IAAKoN,SAAS,EAAC,KAAK;IAACC,GAAG,EAAEpM,MAAO;IAAAqM,QAAA,gBAE/BtN,OAAA;MAAQoN,SAAS,EAAC,YAAY;MAAAE,QAAA,eAC5BtN,OAAA;QAAKoN,SAAS,EAAC,gBAAgB;QAAAE,QAAA,gBAC7BtN,OAAA;UAAQuN,OAAO,EAAEP,MAAO;UAACI,SAAS,EAAC,WAAW;UAAAE,QAAA,EAC3CpL,CAAC,CAAC,UAAU;QAAC;UAAAsL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACT3N,OAAA;UAAKoN,SAAS,EAAC,cAAc;UAAAE,QAAA,GAC1BvM,IAAI,iBACHf,OAAA;YAAK6C,EAAE,EAAC,wBAAwB;YAACqG,KAAK,EAAE;cAAE0E,WAAW,EAAE;YAAO;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE3D,CACN,eACD3N,OAAA,CAACJ,gBAAgB;YAAA4N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACnB,CAAC5M,IAAI,gBACJf,OAAA;YAAQuN,OAAO,EAAEL,UAAW;YAACE,SAAS,EAAC,iBAAiB;YAAClE,KAAK,EAAE;cAAE2E,UAAU,EAAE;YAAO,CAAE;YAAAP,QAAA,EACpFpL,CAAC,CAAC,YAAY;UAAC;YAAAsL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,gBAET3N,OAAA;YAAMkJ,KAAK,EAAE;cAAE2E,UAAU,EAAE;YAAO,CAAE;YAAAP,QAAA,GAAEpL,CAAC,CAAC,SAAS,CAAC,EAAC,IAAE,EAACnB,IAAI,CAACuB,IAAI,EAAC,GAAC;UAAA;YAAAkL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACxE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAGR5M,IAAI,iBACHf,OAAA,CAACL,UAAU;MACTmO,WAAW,EAAEtC,eAAgB;MAC7BuC,QAAQ,EAAErC,YAAa;MACvBsC,kBAAkB,EAAErC;IAAuB;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CACF,eAGD3N,OAAA;MAAMoN,SAAS,EAAC,cAAc;MAAAE,QAAA,GAC3BzM,KAAK,iBACJb,OAAA;QAAKoN,SAAS,EAAC,OAAO;QAAAE,QAAA,GAAC,eAClB,EAACzM,KAAK,eACTb,OAAA;UAAQuN,OAAO,EAAEA,CAAA,KAAMzM,QAAQ,CAAC,IAAI,CAAE;UAACoI,KAAK,EAAE;YAAC2E,UAAU,EAAE,MAAM;YAAEI,UAAU,EAAE,MAAM;YAAEC,MAAM,EAAE,MAAM;YAAEC,KAAK,EAAE,OAAO;YAAEC,MAAM,EAAE;UAAS,CAAE;UAAAd,QAAA,EAAC;QAE3I;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAGAtN,WAAW,KAAK,OAAO,iBACtBL,OAAA;QAAKoN,SAAS,EAAC,kBAAkB;QAAAE,QAAA,gBAC/BtN,OAAA;UAAIoN,SAAS,EAAC,OAAO;UAAAE,QAAA,EAAEpL,CAAC,CAAC,UAAU;QAAC;UAAAsL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1C3N,OAAA;UAAGoN,SAAS,EAAC,UAAU;UAAAE,QAAA,EAAC;QAExB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAEH,CAAC5M,IAAI,gBACJf,OAAA;UAAKkJ,KAAK,EAAE;YAAC+E,UAAU,EAAE,SAAS;YAAEI,OAAO,EAAE,MAAM;YAAEC,YAAY,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAM,CAAE;UAAAjB,QAAA,gBACjGtN,OAAA;YAAGkJ,KAAK,EAAE;cAACiF,KAAK,EAAE,SAAS;cAAEI,YAAY,EAAE;YAAM,CAAE;YAAAjB,QAAA,EAChDpL,CAAC,CAAC,eAAe;UAAC;YAAAsL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACJ3N,OAAA;YAAQuN,OAAO,EAAEL,UAAW;YAACE,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EACrDpL,CAAC,CAAC,eAAe;UAAC;YAAAsL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAEN3N,OAAA;UAAMwO,QAAQ,EAAElD,YAAa;UAAAgC,QAAA,gBAC3BtN,OAAA;YAAKoN,SAAS,EAAC,YAAY;YAAAE,QAAA,eACzBtN,OAAA;cACEyO,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEnO,KAAM;cACboO,QAAQ,EAAGxG,CAAC,IAAK3H,QAAQ,CAAC2H,CAAC,CAACyG,MAAM,CAACF,KAAK,CAAE;cAC1CG,WAAW,EAAE3M,CAAC,CAAC,kBAAkB,CAAE;cACnCkL,SAAS,EAAC,YAAY;cACtB0B,QAAQ,EAAEnO;YAAU;cAAA6M,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN3N,OAAA;YACEyO,IAAI,EAAC,QAAQ;YACbK,QAAQ,EAAEnO,SAAS,IAAI,CAACJ,KAAK,CAACgL,IAAI,CAAC,CAAE;YACrC6B,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EAE1B3M,SAAS,gBACRX,OAAA,CAAAE,SAAA;cAAAoN,QAAA,gBACEtN,OAAA;gBAAMoN,SAAS,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAChCzL,CAAC,CAAC,YAAY,CAAC;YAAA,eAChB,CAAC,gBAEHlC,OAAA,CAAAE,SAAA;cAAAoN,QAAA,EACGpL,CAAC,CAAC,kBAAkB;YAAC,gBACtB;UACH;YAAAsL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGAtN,WAAW,KAAK,MAAM,IAAI0B,IAAI,iBAC7B/B,OAAA;QAAKoN,SAAS,EAAC,gBAAgB;QAAAE,QAAA,gBAE7BtN,OAAA;UAAKoN,SAAS,EAAC,mBAAmB;UAAAE,QAAA,gBAChCtN,OAAA;YAAKoN,SAAS,EAAC,iBAAiB;YAACvK,EAAE,EAAC,iBAAiB;YAAAyK,QAAA,gBAEnDtN,OAAA;cAAKoN,SAAS,EAAC,oBAAoB;cAAAE,QAAA,eACjCtN,OAAA;gBAAKoN,SAAS,EAAC,oBAAoB;gBAAAE,QAAA,gBACjCtN,OAAA;kBAAAsN,QAAA,EAAKvL,IAAI,CAACqD;gBAAI;kBAAAoI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpB3N,OAAA;kBAAQuN,OAAO,EAAER,MAAO;kBAACK,SAAS,EAAC,4BAA4B;kBAAAE,QAAA,EAC5DpL,CAAC,CAAC,YAAY;gBAAC;kBAAAsL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGL5L,IAAI,CAAC2C,MAAM,CAACyC,GAAG,CAAC,CAACzE,MAAM,EAAEiC,KAAK,KAAK;cAClC,MAAMoK,aAAa,GAAGhN,IAAI,CAAC2C,MAAM,CAACX,MAAM;cACxC,MAAMiL,KAAK,GAAIrK,KAAK,GAAG,GAAG,GAAIoK,aAAa;cAC3C,MAAME,UAAU,GAAG,GAAG;cACtB,MAAMC,WAAW,GAAG,CAACxM,MAAM,CAAC6E,KAAK,IAAI,CAAC,IAAI,GAAG;cAC7C,MAAM4H,MAAM,GAAGF,UAAU,GAAGC,WAAW;;cAEvC;cACA,MAAME,WAAW,GAAItH,IAAI,CAACuH,GAAG,CAAC1K,KAAK,GAAG,GAAG,CAAC,GAAG,EAAG;cAChD,MAAM2K,UAAU,GAAGN,KAAK,GAAGI,WAAW;cAEtC,MAAMhO,CAAC,GAAG0G,IAAI,CAACyH,GAAG,CAAED,UAAU,GAAGxH,IAAI,CAAC0H,EAAE,GAAI,GAAG,CAAC,GAAGL,MAAM;cACzD,MAAM9N,CAAC,GAAGyG,IAAI,CAACuH,GAAG,CAAEC,UAAU,GAAGxH,IAAI,CAAC0H,EAAE,GAAI,GAAG,CAAC,GAAGL,MAAM;cAEzD,oBACEnP,OAAA;gBAEEoN,SAAS,EAAE,oBAAoBpL,cAAc,KAAKU,MAAM,GAAG,UAAU,GAAG,EAAE,EAAG;gBAC7EwG,KAAK,EAAE;kBACLM,SAAS,EAAE,aAAapI,CAAC,OAAOC,CAAC,KAAK;kBACtC,gBAAgB,EAAE,GAAGiO,UAAU;gBACjC,CAAE;gBACF,cAAY3K,KAAM;gBAClB,cAAYjC,MAAM,CAAC6E,KAAK,IAAI,CAAE;gBAC9BgG,OAAO,EAAEA,CAAA,KAAMpJ,kBAAkB,CAACzB,MAAM,CAAE;gBAC1C+M,aAAa,EAAEA,CAAA,KAAMjN,wBAAwB,CAACE,MAAM,CAAE;gBAAA4K,QAAA,gBAGtDtN,OAAA;kBAAKoN,SAAS,EAAC,wBAAwB;kBAAClE,KAAK,EAAE;oBAC7CM,SAAS,EAAE,UAAUwF,KAAK,GAAG,GAAG,MAAM;oBACtCU,KAAK,EAAE,GAAGP,MAAM;kBAClB;gBAAE;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAET3N,OAAA;kBAAKoN,SAAS,EAAC,gBAAgB;kBAAAE,QAAA,gBAC7BtN,OAAA;oBAAKoN,SAAS,EAAC,cAAc;oBAAAE,QAAA,EAAE5K,MAAM,CAACiN;kBAAK;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClD3N,OAAA;oBAAIoN,SAAS,EAAC,aAAa;oBAAAE,QAAA,EAAE5K,MAAM,CAACO;kBAAI;oBAAAuK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9C3N,OAAA;oBAAGoN,SAAS,EAAC,oBAAoB;oBAAAE,QAAA,EAAE5K,MAAM,CAAC2C;kBAAS;oBAAAmI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAEvDjL,MAAM,CAACkN,YAAY,iBAClB5P,OAAA;oBAAKoN,SAAS,EAAC,sBAAsB;oBAAAE,QAAA,EAClC5K,MAAM,CAACkN,YAAY,CAAC1I,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC0I,GAAG,EAAEC,QAAQ,kBACjD9P,OAAA;sBAAqBoN,SAAS,EAAC,iBAAiB;sBAAAE,QAAA,EAC7CuC;oBAAG,GADKC,QAAQ;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEb,CACP;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA,GA/BDhJ,KAAK;gBAAA6I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgCP,CAAC;YAEV,CAAC,CAAC,EAEDhN,SAAS,iBACRX,OAAA;cAAKoN,SAAS,EAAC,iBAAiB;cAAAE,QAAA,gBAC9BtN,OAAA;gBAAMoN,SAAS,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjC3N,OAAA;gBAAAsN,QAAA,EAAOpL,CAAC,CAAC,SAAS;cAAC;gBAAAsL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN3N,OAAA;YAAKoN,SAAS,EAAC,iBAAiB;YAAAE,QAAA,gBAC9BtN,OAAA;cAAQoN,SAAS,EAAC,aAAa;cAACG,OAAO,EAAEA,CAAA,KAAM5F,UAAU,CAAC,GAAG,CAAE;cAAA2F,QAAA,EAAC;YAAG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5E3N,OAAA;cAAQoN,SAAS,EAAC,aAAa;cAACG,OAAO,EAAEA,CAAA,KAAM5F,UAAU,CAAC,GAAG,CAAE;cAAA2F,QAAA,EAAC;YAAG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5E3N,OAAA;cAAQoN,SAAS,EAAC,aAAa;cAACG,OAAO,EAAEA,CAAA,KAAMtF,eAAe,CAAC,CAAE;cAAAqF,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN3N,OAAA;UAAKoN,SAAS,EAAC,kBAAkB;UAAAE,QAAA,eAC/BtN,OAAA;YAAKoN,SAAS,EAAC,kBAAkB;YAAAE,QAAA,gBAC/BtN,OAAA;cAAKoN,SAAS,EAAC,eAAe;cAAAE,QAAA,gBAC5BtN,OAAA;gBAAAsN,QAAA,EAAKvL,IAAI,CAACqD;cAAI;gBAAAoI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpB3N,OAAA;gBAAQuN,OAAO,EAAER,MAAO;gBAACK,SAAS,EAAC,mBAAmB;gBAAAE,QAAA,EACnDpL,CAAC,CAAC,YAAY;cAAC;gBAAAsL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN3N,OAAA;cAAKoN,SAAS,EAAC,yBAAyB;cAACvK,EAAE,EAAC,eAAe;cAAAyK,QAAA,EACxDvL,IAAI,CAAC2C,MAAM,CAACyC,GAAG,CAAC,CAACzE,MAAM,EAAEiC,KAAK,kBAC7B3E,OAAA;gBAEEoN,SAAS,EAAE,sBAAsBpL,cAAc,KAAKU,MAAM,GAAG,UAAU,GAAG,EAAE,EAAG;gBAC/E,cAAYiC,KAAM;gBAClB4I,OAAO,EAAEA,CAAA,KAAMpJ,kBAAkB,CAACzB,MAAM,CAAE;gBAC1C+M,aAAa,EAAEA,CAAA,KAAMjN,wBAAwB,CAACE,MAAM,CAAE;gBACtDqN,YAAY,EAAG5H,CAAC,IAAK;kBACnB,MAAM6H,KAAK,GAAG7H,CAAC,CAAC8H,OAAO,CAAC,CAAC,CAAC;kBAC1B9H,CAAC,CAAC+H,aAAa,CAACC,cAAc,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;kBAC3ClI,CAAC,CAAC+H,aAAa,CAACI,WAAW,GAAGN,KAAK,CAAC3H,OAAO;kBAC3CF,CAAC,CAAC+H,aAAa,CAACK,WAAW,GAAGP,KAAK,CAAC1H,OAAO;gBAC7C,CAAE;gBACFkI,UAAU,EAAGrI,CAAC,IAAK;kBACjB,MAAM6H,KAAK,GAAG7H,CAAC,CAACsI,cAAc,CAAC,CAAC,CAAC;kBACjC,MAAMC,SAAS,GAAGN,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIlI,CAAC,CAAC+H,aAAa,CAACC,cAAc,IAAI,CAAC,CAAC;kBACpE,MAAMQ,MAAM,GAAG7I,IAAI,CAAC8I,GAAG,CAACZ,KAAK,CAAC3H,OAAO,IAAIF,CAAC,CAAC+H,aAAa,CAACI,WAAW,IAAI,CAAC,CAAC,CAAC;kBAC3E,MAAM3H,MAAM,GAAGb,IAAI,CAAC8I,GAAG,CAACZ,KAAK,CAAC1H,OAAO,IAAIH,CAAC,CAAC+H,aAAa,CAACK,WAAW,IAAI,CAAC,CAAC,CAAC;;kBAE3E;kBACA,IAAIG,SAAS,GAAG,GAAG,IAAIC,MAAM,GAAG,EAAE,IAAIhI,MAAM,GAAG,EAAE,EAAE;oBACjDR,CAAC,CAACO,cAAc,CAAC,CAAC;oBAClB5D,YAAY,CAACpC,MAAM,EAAEiC,KAAK,CAAC;kBAC7B;kBACA;kBAAA,KACK,IAAI+L,SAAS,GAAG,GAAG,IAAIC,MAAM,GAAG,EAAE,IAAIhI,MAAM,GAAG,EAAE,EAAE;oBACtD,MAAM0H,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;oBACtB,MAAMQ,OAAO,GAAG1I,CAAC,CAAC+H,aAAa,CAACY,WAAW,IAAI,CAAC;oBAChD,IAAIT,GAAG,GAAGQ,OAAO,GAAG,GAAG,EAAE;sBACvB1I,CAAC,CAACO,cAAc,CAAC,CAAC;sBAClBlG,wBAAwB,CAACE,MAAM,CAAC;oBAClC;oBACAyF,CAAC,CAAC+H,aAAa,CAACY,WAAW,GAAGT,GAAG;kBACnC;gBACF,CAAE;gBAAA/C,QAAA,gBAEFtN,OAAA;kBAAKoN,SAAS,EAAC,qBAAqB;kBAAAE,QAAA,gBAClCtN,OAAA;oBAAKoN,SAAS,EAAC,oBAAoB;oBAAAE,QAAA,EAAE5K,MAAM,CAACiN;kBAAK;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACxD3N,OAAA;oBAAIoN,SAAS,EAAC,mBAAmB;oBAAAE,QAAA,EAAE5K,MAAM,CAACO;kBAAI;oBAAAuK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpD3N,OAAA;oBAAGoN,SAAS,EAAC,0BAA0B;oBAAAE,QAAA,EAAE5K,MAAM,CAAC2C;kBAAS;oBAAAmI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAE7DjL,MAAM,CAACkN,YAAY,iBAClB5P,OAAA;oBAAKoN,SAAS,EAAC,sBAAsB;oBAAAE,QAAA,GAClC5K,MAAM,CAACkN,YAAY,CAAC1I,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC0I,GAAG,EAAEC,QAAQ,kBACjD9P,OAAA;sBAAqBoN,SAAS,EAAC,wBAAwB;sBAAAE,QAAA,EACpDuC;oBAAG,GADKC,QAAQ;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEb,CACP,CAAC,EACDjL,MAAM,CAACkN,YAAY,CAAC7L,MAAM,GAAG,CAAC,iBAC7B/D,OAAA;sBAAMoN,SAAS,EAAC,6BAA6B;sBAAAE,QAAA,GAAC,GAC3C,EAAC5K,MAAM,CAACkN,YAAY,CAAC7L,MAAM,GAAG,CAAC;oBAAA;sBAAAyJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CACP;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CACN,eAED3N,OAAA;oBAAKoN,SAAS,EAAC,qBAAqB;oBAAAE,QAAA,gBAClCtN,OAAA;sBAAMoN,SAAS,EAAC,oBAAoB;sBAAAE,QAAA,EAAC;oBAA0B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtE3N,OAAA;sBAAMoN,SAAS,EAAC,oBAAoB;sBAAAE,QAAA,EAAC;oBAA+B;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGL,CAACjL,MAAM,CAAC6E,KAAK,IAAI,CAAC,IAAI,CAAC,iBACtBvH,OAAA;kBAAKoN,SAAS,EAAC,wBAAwB;kBAAAE,QAAA,eACrCtN,OAAA;oBAAKoN,SAAS,EAAC,aAAa;oBAAAE,QAAA,GAAC,QAAM,EAAC5K,MAAM,CAAC6E,KAAK;kBAAA;oBAAAiG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CACN;cAAA,GAjEIhJ,KAAK;gBAAA6I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAkEP,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAELhN,SAAS,iBACRX,OAAA;cAAKoN,SAAS,EAAC,gBAAgB;cAAAE,QAAA,gBAC7BtN,OAAA;gBAAMoN,SAAS,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjC3N,OAAA;gBAAAsN,QAAA,EAAOpL,CAAC,CAAC,SAAS;cAAC;gBAAAsL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAtN,WAAW,KAAK,SAAS,IAAI4B,OAAO,iBACnCjC,OAAA;QAAKoN,SAAS,EAAC,mBAAmB;QAAAE,QAAA,eAChCtN,OAAA;UAAKoN,SAAS,EAAC,cAAc;UAAAE,QAAA,gBAC3BtN,OAAA;YAAKoN,SAAS,EAAC,gBAAgB;YAAAE,QAAA,gBAC7BtN,OAAA;cAAQuN,OAAO,EAAER,MAAO;cAACK,SAAS,EAAC,oCAAoC;cAAAE,QAAA,EACpEpL,CAAC,CAAC,YAAY;YAAC;cAAAsL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGT3N,OAAA;cAAKoN,SAAS,EAAC,kBAAkB;cAAClE,KAAK,EAAE;gBACvC6H,OAAO,EAAE,MAAM;gBACfC,GAAG,EAAE,KAAK;gBACVC,SAAS,EAAE,MAAM;gBACjBC,QAAQ,EAAE;cACZ,CAAE;cAAA5D,QAAA,gBAEAtN,OAAA;gBAAKoN,SAAS,EAAC,yBAAyB;gBAAClE,KAAK,EAAE;kBAC9C6H,OAAO,EAAE,MAAM;kBACfI,UAAU,EAAE,QAAQ;kBACpBH,GAAG,EAAE,KAAK;kBACV3C,OAAO,EAAE,UAAU;kBACnBJ,UAAU,EAAE,SAAS;kBACrBK,YAAY,EAAE,KAAK;kBACnBJ,MAAM,EAAE;gBACV,CAAE;gBAAAZ,QAAA,gBACAtN,OAAA;kBACEuN,OAAO,EAAE3B,kBAAmB;kBAC5BwB,SAAS,EAAC,UAAU;kBACpBhK,KAAK,EAAC,mBAAmB;kBACzB8F,KAAK,EAAE;oBACL+E,UAAU,EAAE,MAAM;oBAClBC,MAAM,EAAE,MAAM;oBACdkD,QAAQ,EAAE,MAAM;oBAChBhD,MAAM,EAAE,SAAS;oBACjBC,OAAO,EAAE;kBACX,CAAE;kBAAAf,QAAA,EAEDpO,aAAa,CAAC2M,SAAS,CAAC,CAAC,CAACC,SAAS,GAAG,IAAI,GAAG;gBAAI;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,eACT3N,OAAA;kBACEuN,OAAO,EAAEtB,gBAAiB;kBAC1BmB,SAAS,EAAC,UAAU;kBACpBhK,KAAK,EAAC,aAAa;kBACnB8F,KAAK,EAAE;oBACL+E,UAAU,EAAE,MAAM;oBAClBC,MAAM,EAAE,MAAM;oBACdkD,QAAQ,EAAE,MAAM;oBAChBhD,MAAM,EAAE,SAAS;oBACjBC,OAAO,EAAE;kBACX,CAAE;kBAAAf,QAAA,EACH;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT3N,OAAA;kBACEyO,IAAI,EAAC,OAAO;kBACZzG,GAAG,EAAC,KAAK;kBACTD,GAAG,EAAC,GAAG;kBACPsJ,IAAI,EAAC,KAAK;kBACVC,YAAY,EAAC,GAAG;kBAChB3C,QAAQ,EAAGxG,CAAC,IAAKgE,sBAAsB,CAACoF,UAAU,CAACpJ,CAAC,CAACyG,MAAM,CAACF,KAAK,CAAC,CAAE;kBACpExF,KAAK,EAAE;oBAACwG,KAAK,EAAE;kBAAM,CAAE;kBACvBtM,KAAK,EAAC;gBAAc;kBAAAoK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACF3N,OAAA;kBAAMkJ,KAAK,EAAE;oBAACkI,QAAQ,EAAE,MAAM;oBAAEjD,KAAK,EAAE;kBAAS,CAAE;kBAAAb,QAAA,EAAC;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eAGN3N,OAAA;gBAAKoN,SAAS,EAAC,yBAAyB;gBAAClE,KAAK,EAAE;kBAC9C6H,OAAO,EAAE,MAAM;kBACfC,GAAG,EAAE;gBACP,CAAE;gBAAA1D,QAAA,gBACAtN,OAAA;kBACEuN,OAAO,EAAEX,qBAAsB;kBAC/BQ,SAAS,EAAC,mBAAmB;kBAC7BlE,KAAK,EAAE;oBAACmF,OAAO,EAAE,UAAU;oBAAE+C,QAAQ,EAAE;kBAAM,CAAE;kBAC/ChO,KAAK,EAAC,mBAAmB;kBAAAkK,QAAA,EAC1B;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT3N,OAAA;kBACEuN,OAAO,EAAEjB,eAAgB;kBACzBc,SAAS,EAAC,mBAAmB;kBAC7BlE,KAAK,EAAE;oBAACmF,OAAO,EAAE,UAAU;oBAAE+C,QAAQ,EAAE;kBAAM,CAAE;kBAC/ChO,KAAK,EAAC,eAAe;kBAAAkK,QAAA,EACtB;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT3N,OAAA;kBACEuN,OAAO,EAAEb,gBAAiB;kBAC1BU,SAAS,EAAC,mBAAmB;kBAC7BlE,KAAK,EAAE;oBAACmF,OAAO,EAAE,UAAU;oBAAE+C,QAAQ,EAAE;kBAAM,CAAE;kBAC/ChO,KAAK,EAAC,gBAAgB;kBAAAkK,QAAA,EACvB;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3N,OAAA;YAAIoN,SAAS,EAAC,OAAO;YAAAE,QAAA,EAAE,CAAArL,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEmB,KAAK,KAAI;UAAY;YAAAoK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC3D3N,OAAA;YAAKkJ,KAAK,EAAE;cAACiF,KAAK,EAAE,SAAS;cAAEI,YAAY,EAAE,MAAM;cAAE6C,QAAQ,EAAE;YAAQ,CAAE;YAAA9D,QAAA,gBACvEtN,OAAA;cAAAsN,QAAA,GAAOpL,CAAC,CAAC,QAAQ,CAAC,EAAC,IAAE,EAAC,CAAAD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE1B,KAAK,KAAI,SAAS;YAAA;cAAAiN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACxD,CAAA1L,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEU,KAAK,KAAIV,OAAO,CAACU,KAAK,CAACoB,MAAM,GAAG,CAAC,iBACzC/D,OAAA;cAAMkJ,KAAK,EAAE;gBAAC2E,UAAU,EAAE;cAAM,CAAE;cAAAP,QAAA,GAC/BpL,CAAC,CAAC,OAAO,CAAC,EAAC,IAAE,EAACD,OAAO,CAACU,KAAK,CAAC6O,IAAI,CAAC,IAAI,CAAC;YAAA;cAAAhE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN3N,OAAA;YAAKoN,SAAS,EAAC,iBAAiB;YAAClE,KAAK,EAAE;cAACuI,UAAU,EAAE,KAAK;cAAEL,QAAQ,EAAE;YAAQ,CAAE;YAAA9D,QAAA,EAC7ErL,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEqB,OAAO,GAAGrB,OAAO,CAACqB,OAAO,CAACoO,KAAK,CAAC,IAAI,CAAC,CAACvK,GAAG,CAAC,CAACwK,SAAS,EAAEhN,KAAK,KACnEgN,SAAS,CAACpG,IAAI,CAAC,CAAC,iBACdvL,OAAA;cAAekJ,KAAK,EAAE;gBAACqF,YAAY,EAAE;cAAM,CAAE;cAAAjB,QAAA,EAC1CqE;YAAS,GADJhN,KAAK;cAAA6I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CAEN,CAAC,gBACA3N,OAAA;cAAAsN,QAAA,EAAG;YAA0B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UACjC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACvN,EAAA,CA1iCID,YAAY;EAAA,QAsBFN,cAAc;AAAA;AAAA+R,EAAA,GAtBxBzR,YAAY;AA4iClB,eAAeA,YAAY;AAAC,IAAAyR,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}