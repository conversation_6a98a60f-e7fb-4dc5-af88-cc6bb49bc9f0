{"ast": null, "code": "// OpenRouter API Service for Knowledge Tree Generation\nimport webSearchService from './webSearchService';\nconst OPENROUTER_API_KEY = process.env.REACT_APP_OPENROUTER_API_KEY || 'your-api-key-here';\nconst OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';\nconst MODEL = process.env.REACT_APP_OPENROUTER_MODEL || 'deepseek/deepseek-r1-0528:free';\n\n// Site configuration for OpenRouter rankings\nconst SITE_CONFIG = {\n  'HTTP-Referer': process.env.REACT_APP_SITE_URL || 'http://localhost:3000',\n  'X-Title': process.env.REACT_APP_SITE_NAME || 'Knowledge Tree Explorer'\n};\nclass OpenRouterClient {\n  constructor() {\n    this.baseURL = OPENROUTER_BASE_URL;\n    this.apiKey = OPENROUTER_API_KEY;\n\n    // Debug logging\n    if (process.env.REACT_APP_DEBUG_MODE === 'true') {\n      console.log('🔧 OpenRouter Client initialized:');\n      console.log('- Base URL:', this.baseURL);\n      console.log('- Model:', MODEL);\n      console.log('- API Key:', this.apiKey ? `${this.apiKey.substring(0, 10)}...` : 'NOT SET');\n      console.log('- Site Config:', SITE_CONFIG);\n    }\n  }\n  async makeRequest(messages, temperature = 0.7) {\n    try {\n      const response = await fetch(`${this.baseURL}/chat/completions`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${this.apiKey}`,\n          'Content-Type': 'application/json',\n          ...SITE_CONFIG\n        },\n        body: JSON.stringify({\n          model: MODEL,\n          messages,\n          temperature,\n          max_tokens: 1500,\n          // Optimizat pentru răspunsuri mai rapide\n          stream: false\n        })\n      });\n      if (!response.ok) {\n        var _errorData$error;\n        const errorData = await response.json().catch(() => ({}));\n        throw new Error(`OpenRouter API Error: ${response.status} - ${((_errorData$error = errorData.error) === null || _errorData$error === void 0 ? void 0 : _errorData$error.message) || 'Unknown error'}`);\n      }\n      const data = await response.json();\n      return data.choices[0].message.content;\n    } catch (error) {\n      console.error('OpenRouter API request failed:', error);\n      throw error;\n    }\n  }\n}\nconst client = new OpenRouterClient();\n\n// Generate Knowledge Tree from topic\nexport async function generateKnowledgeTree(topic) {\n  const prompt = `Analizează cu atenție subiectul \"${topic}\" și creează un arbore de cunoștințe FOARTE SPECIFIC și RELEVANT pentru acest domeniu exact.\n\nIMPORTANT: Generează ramuri care sunt DIRECT LEGATE de \"${topic}\", nu concepte generale!\n\nReturnează DOAR un obiect JSON valid cu această structură exactă:\n{\n  \"tema\": \"${topic}\",\n  \"ramuri\": [\n    {\n      \"nume\": \"Nume Ramură Specifică\",\n      \"descriere\": \"Descriere scurtă și precisă\",\n      \"emoji\": \"📚\",\n      \"subcategorii\": [\"subcategorie1\", \"subcategorie2\", \"subcategorie3\"]\n    }\n  ]\n}\n\nCerințe STRICTE:\n- Generează 6-8 ramuri principale SPECIFICE pentru \"${topic}\"\n- Fiecare ramură TREBUIE să fie direct legată de subiectul principal\n- Emoji-uri relevante pentru fiecare ramură\n- 3-4 subcategorii specifice pentru fiecare ramură\n- Descrieri de maxim 1 propoziție\n- Focalizează-te pe aspectele PRACTICE și APLICABILE\n- Evită conceptele generale sau irelevante\n\nExemplu pentru \"Codul Civil al României\":\n- Ramuri: \"Dreptul Proprietății\", \"Contractele\", \"Răspunderea Civilă\", etc.\n- NU: \"Fundamentals\", \"Applications\", \"Advanced Topics\"\n\nSubiect: ${topic}`;\n  try {\n    const response = await client.makeRequest([{\n      role: 'system',\n      content: 'Expert în organizarea cunoștințelor. Generează arbori de cunoștințe specifici în format JSON valid. Răspunde DOAR cu JSON, fără text explicativ.'\n    }, {\n      role: 'user',\n      content: prompt\n    }], 0.3); // Temperatură mai mică pentru răspunsuri mai consistente și rapide\n\n    // Parse and validate the JSON response\n    const cleanResponse = response.trim();\n    const jsonMatch = cleanResponse.match(/\\{[\\s\\S]*\\}/);\n    if (!jsonMatch) {\n      throw new Error('No valid JSON found in response');\n    }\n    const tree = JSON.parse(jsonMatch[0]);\n\n    // Validate structure\n    if (!tree.tema || !Array.isArray(tree.ramuri)) {\n      throw new Error('Invalid tree structure');\n    }\n    return tree;\n  } catch (error) {\n    console.error('Error generating knowledge tree:', error);\n\n    // Fallback tree structure\n    return {\n      tema: topic,\n      ramuri: [{\n        nume: \"Fundamentals\",\n        descriere: `Basic concepts and principles of ${topic}`,\n        emoji: \"📚\",\n        subcategorii: [\"Core Concepts\", \"Key Principles\", \"Basic Theory\"]\n      }, {\n        nume: \"Applications\",\n        descriere: `Practical applications and use cases of ${topic}`,\n        emoji: \"🔧\",\n        subcategorii: [\"Real-world Uses\", \"Industry Applications\", \"Case Studies\"]\n      }, {\n        nume: \"Advanced Topics\",\n        descriere: `Complex and specialized aspects of ${topic}`,\n        emoji: \"🎓\",\n        subcategorii: [\"Expert Level\", \"Research Areas\", \"Cutting Edge\"]\n      }]\n    };\n  }\n}\n\n// Generate Article for specific branch\nexport async function generateArticle(topic, branch, flags = ['-a']) {\n  var _branch$subcategorii;\n  const flagInstructions = {\n    // Basic flags\n    '-a': 'Write a standard informative article (400-600 words)',\n    '-t': 'Format the content as tables and structured data',\n    '-ex': 'Include 3 practical examples with detailed explanations',\n    '-p': 'Include code examples and technical demonstrations',\n    '-q': 'End with a 5-question quiz to test understanding',\n    '-rap': 'Write an exhaustive report with comprehensive coverage',\n    '-def': 'Focus on expert-level definitions and technical terminology',\n    // Learning & Visualization flags\n    '-path': 'Create personalized learning paths with step-by-step progression and milestones',\n    '-vis': 'Generate infographics, diagrams, and interactive visualizations with detailed descriptions',\n    '-vid': 'Suggest relevant videos and create video scripts with timestamps and key points',\n    '-mind': 'Present information as an interactive mind map with connected concepts',\n    '-flow': 'Create flowcharts and process diagrams with decision points and outcomes',\n    // Industry-specific flags\n    '-case': 'Include real case studies with measurable results and detailed analysis',\n    '-scenario': 'Create practical scenarios and simulations with step-by-step solutions',\n    '-lab': 'Design experiments and practical tests with materials and procedures',\n    '-mentor': 'Include expert advice and mentorship tips from industry professionals',\n    '-mistakes': 'Analyze common mistakes and provide prevention strategies',\n    // Interactive tools flags\n    '-calc': 'Include calculators and interactive tools for calculations with formulas',\n    '-template': 'Provide actionable templates and checklists ready for immediate use',\n    '-workshop': 'Format as workshop content with practical exercises and group activities',\n    '-game': 'Add gamification elements with points, achievements, and competitions',\n    '-team': 'Optimize content for teams and collaboration with group exercises',\n    // Sharing & presentation flags\n    '-share': 'Format for easy sharing with colleagues including summary points',\n    '-present': 'Generate PowerPoint-style presentation with slides and talking points',\n    '-meeting': 'Create meeting agenda and discussion points with time allocations',\n    '-offline': 'Ensure content is available offline with downloadable resources',\n    // Analytics & benchmarking flags\n    '-kpi': 'Include relevant KPIs and measurable metrics with tracking methods',\n    '-benchmark': 'Compare with industry best practices and competitive analysis',\n    '-timeline': 'Add temporal planning and milestones with realistic deadlines',\n    // Localization flags\n    '-ro': 'Adapt for Romanian market and legislation with local examples',\n    '-eu': 'Focus on EU regulations and practices with compliance guidelines',\n    '-local': 'Include local examples and regional practices',\n    // Advanced features flags\n    '-auto': 'Focus on process automation with tools and implementation steps',\n    '-predict': 'Include data-based predictions and future trends analysis',\n    '-optimize': 'Provide continuous optimization suggestions with improvement metrics'\n  };\n  const activeFlags = flags.filter(flag => flagInstructions[flag]);\n  const flagText = activeFlags.map(flag => flagInstructions[flag]).join('. ');\n\n  // Special handling for Romanian and localization flags\n  const hasRomanianFlag = flags.includes('-ro');\n  const hasEUFlag = flags.includes('-eu');\n  const hasLocalFlag = flags.includes('-local');\n  let localizationContext = '';\n  if (hasRomanianFlag) {\n    localizationContext = 'Adaptează conținutul pentru piața românească, include legislația locală, exemple de companii românești și practici specifice României.';\n  } else if (hasEUFlag) {\n    localizationContext = 'Focus on European Union regulations, GDPR compliance, EU market practices, and European case studies.';\n  } else if (hasLocalFlag) {\n    localizationContext = 'Include local examples, regional practices, and area-specific considerations.';\n  }\n\n  // Special handling for advanced features\n  const hasAdvancedFlags = flags.some(flag => ['-auto', '-predict', '-optimize'].includes(flag));\n  let advancedContext = '';\n  if (hasAdvancedFlags) {\n    advancedContext = 'Include specific tools, software recommendations, implementation steps, and measurable outcomes.';\n  }\n\n  // Search for web sources\n  let webSources = [];\n  let sourcesContext = '';\n  try {\n    console.log('Searching for web sources...');\n    webSources = await webSearchService.searchSources(branch.nume, 5);\n    console.log('Found sources:', webSources);\n    if (webSources.length > 0) {\n      sourcesContext = `\\n\\nWeb Sources Found:\\n${webSources.map(source => `- ${source.title}: ${source.description} (${source.source})`).join('\\n')}`;\n    }\n  } catch (error) {\n    console.warn('Web search failed, continuing without sources:', error);\n  }\n  const prompt = `Generate an article about \"${branch.nume}\" in the context of \"${topic}\".\n\nArticle Requirements:\n${flagText || flagInstructions['-a']}\n\n${localizationContext ? `Localization Requirements: ${localizationContext}` : ''}\n${advancedContext ? `Advanced Features: ${advancedContext}` : ''}\n${sourcesContext}\n\nContent Guidelines:\n- Write in an engaging, educational style suitable for professionals\n- Use clear headings and structure with markdown formatting\n- Include relevant examples and explanations\n- Make it suitable for interactive learning\n- Target length: 400-600 words (unless -rap flag is used, then 800-1200 words)\n- If visualization flags are used, describe diagrams and charts in detail\n- If interactive flags are used, provide step-by-step instructions\n- If Romanian flag is used, write in Romanian language\n\nReturn ONLY a valid JSON object with this structure:\n{\n  \"titlu\": \"Article Title\",\n  \"continut\": \"Full article content with proper markdown formatting\",\n  \"subcategorii\": [\"related topic 1\", \"related topic 2\", \"related topic 3\"],\n  \"flags\": ${JSON.stringify(flags)},\n  \"pozitie\": \"${topic} → ${branch.nume}\",\n  \"estimatedReadTime\": \"X minutes\",\n  \"difficulty\": \"Beginner|Intermediate|Advanced\",\n  \"practicalValue\": \"High|Medium|Low\"\n}\n\nTopic Context: ${topic}\nBranch: ${branch.nume}\nBranch Description: ${branch.descriere}\nSubcategories: ${((_branch$subcategorii = branch.subcategorii) === null || _branch$subcategorii === void 0 ? void 0 : _branch$subcategorii.join(', ')) || 'None'}\nSelected Flags: ${flags.join(' ')}`;\n  try {\n    const response = await client.makeRequest([{\n      role: 'system',\n      content: 'You are an expert content writer and educator. Generate comprehensive, well-structured articles in valid JSON format only. Do not include any explanatory text outside the JSON.'\n    }, {\n      role: 'user',\n      content: prompt\n    }], 0.8);\n\n    // Parse and validate the JSON response\n    const cleanResponse = response.trim();\n    const jsonMatch = cleanResponse.match(/\\{[\\s\\S]*\\}/);\n    if (!jsonMatch) {\n      throw new Error('No valid JSON found in response');\n    }\n    const article = JSON.parse(jsonMatch[0]);\n\n    // Validate structure\n    if (!article.titlu || !article.continut) {\n      throw new Error('Invalid article structure');\n    }\n\n    // Add web sources to the article\n    if (webSources.length > 0) {\n      const sourcesHTML = webSearchService.formatSourcesHTML(webSources);\n      article.continut += '\\n\\n' + sourcesHTML;\n      article.webSources = webSources;\n    }\n    return article;\n  } catch (error) {\n    console.error('Error generating article:', error);\n\n    // Fallback article\n    return {\n      titlu: `${branch.nume} - ${topic}`,\n      continut: `# ${branch.nume}\n\nThis section explores ${branch.nume} in the context of ${topic}.\n\n## Overview\n${branch.descriere}\n\n## Key Concepts\nUnderstanding ${branch.nume} is essential for mastering ${topic}. This area covers fundamental principles and practical applications.\n\n## Applications\nThe concepts in ${branch.nume} have wide-ranging applications across various domains and industries.\n\n## Further Learning\nTo deepen your understanding, consider exploring related topics and practical exercises.`,\n      subcategorii: branch.subcategorii || [],\n      flags: flags,\n      pozitie: `${topic} → ${branch.nume}`\n    };\n  }\n}\n\n// Test API connection\nexport async function testConnection() {\n  try {\n    const response = await client.makeRequest([{\n      role: 'user',\n      content: 'Hello, please respond with \"API connection successful\"'\n    }]);\n    return response.includes('successful');\n  } catch (error) {\n    console.error('API connection test failed:', error);\n    return false;\n  }\n}", "map": {"version": 3, "names": ["webSearchService", "OPENROUTER_API_KEY", "process", "env", "REACT_APP_OPENROUTER_API_KEY", "OPENROUTER_BASE_URL", "MODEL", "REACT_APP_OPENROUTER_MODEL", "SITE_CONFIG", "REACT_APP_SITE_URL", "REACT_APP_SITE_NAME", "OpenRouterClient", "constructor", "baseURL", "<PERSON><PERSON><PERSON><PERSON>", "REACT_APP_DEBUG_MODE", "console", "log", "substring", "makeRequest", "messages", "temperature", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "model", "max_tokens", "stream", "ok", "_errorData$error", "errorData", "json", "catch", "Error", "status", "error", "message", "data", "choices", "content", "client", "generateKnowledgeTree", "topic", "prompt", "role", "cleanResponse", "trim", "jsonMatch", "match", "tree", "parse", "tema", "Array", "isArray", "<PERSON><PERSON>", "nume", "desc<PERSON><PERSON>", "emoji", "subcategorii", "generateArticle", "branch", "flags", "_branch$subcategorii", "flagInstructions", "activeFlags", "filter", "flag", "flagText", "map", "join", "hasRomanianFlag", "includes", "hasEUFlag", "hasLocalFlag", "localizationContext", "hasAdvancedFlags", "some", "advancedContext", "webSources", "sourcesContext", "searchSources", "length", "source", "title", "description", "warn", "article", "titlu", "continut", "sourcesHTML", "formatSourcesHTML", "pozitie", "testConnection"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Appv1/src/services/openRouterService.js"], "sourcesContent": ["// OpenRouter API Service for Knowledge Tree Generation\nimport webSearchService from './webSearchService';\n\nconst OPENROUTER_API_KEY = process.env.REACT_APP_OPENROUTER_API_KEY || 'your-api-key-here';\nconst OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';\nconst MODEL = process.env.REACT_APP_OPENROUTER_MODEL || 'deepseek/deepseek-r1-0528:free';\n\n// Site configuration for OpenRouter rankings\nconst SITE_CONFIG = {\n  'HTTP-Referer': process.env.REACT_APP_SITE_URL || 'http://localhost:3000',\n  'X-Title': process.env.REACT_APP_SITE_NAME || 'Knowledge Tree Explorer'\n};\n\nclass OpenRouterClient {\n  constructor() {\n    this.baseURL = OPENROUTER_BASE_URL;\n    this.apiKey = OPENROUTER_API_KEY;\n\n    // Debug logging\n    if (process.env.REACT_APP_DEBUG_MODE === 'true') {\n      console.log('🔧 OpenRouter Client initialized:');\n      console.log('- Base URL:', this.baseURL);\n      console.log('- Model:', MODEL);\n      console.log('- API Key:', this.apiKey ? `${this.apiKey.substring(0, 10)}...` : 'NOT SET');\n      console.log('- Site Config:', SITE_CONFIG);\n    }\n  }\n\n  async makeRequest(messages, temperature = 0.7) {\n    try {\n      const response = await fetch(`${this.baseURL}/chat/completions`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${this.apiKey}`,\n          'Content-Type': 'application/json',\n          ...SITE_CONFIG\n        },\n        body: JSON.stringify({\n          model: MODEL,\n          messages,\n          temperature,\n          max_tokens: 1500, // Optimizat pentru răspunsuri mai rapide\n          stream: false\n        })\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        throw new Error(`OpenRouter API Error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);\n      }\n\n      const data = await response.json();\n      return data.choices[0].message.content;\n    } catch (error) {\n      console.error('OpenRouter API request failed:', error);\n      throw error;\n    }\n  }\n}\n\nconst client = new OpenRouterClient();\n\n// Generate Knowledge Tree from topic\nexport async function generateKnowledgeTree(topic) {\n  const prompt = `Analizează cu atenție subiectul \"${topic}\" și creează un arbore de cunoștințe FOARTE SPECIFIC și RELEVANT pentru acest domeniu exact.\n\nIMPORTANT: Generează ramuri care sunt DIRECT LEGATE de \"${topic}\", nu concepte generale!\n\nReturnează DOAR un obiect JSON valid cu această structură exactă:\n{\n  \"tema\": \"${topic}\",\n  \"ramuri\": [\n    {\n      \"nume\": \"Nume Ramură Specifică\",\n      \"descriere\": \"Descriere scurtă și precisă\",\n      \"emoji\": \"📚\",\n      \"subcategorii\": [\"subcategorie1\", \"subcategorie2\", \"subcategorie3\"]\n    }\n  ]\n}\n\nCerințe STRICTE:\n- Generează 6-8 ramuri principale SPECIFICE pentru \"${topic}\"\n- Fiecare ramură TREBUIE să fie direct legată de subiectul principal\n- Emoji-uri relevante pentru fiecare ramură\n- 3-4 subcategorii specifice pentru fiecare ramură\n- Descrieri de maxim 1 propoziție\n- Focalizează-te pe aspectele PRACTICE și APLICABILE\n- Evită conceptele generale sau irelevante\n\nExemplu pentru \"Codul Civil al României\":\n- Ramuri: \"Dreptul Proprietății\", \"Contractele\", \"Răspunderea Civilă\", etc.\n- NU: \"Fundamentals\", \"Applications\", \"Advanced Topics\"\n\nSubiect: ${topic}`;\n\n  try {\n    const response = await client.makeRequest([\n      {\n        role: 'system',\n        content: 'Expert în organizarea cunoștințelor. Generează arbori de cunoștințe specifici în format JSON valid. Răspunde DOAR cu JSON, fără text explicativ.'\n      },\n      {\n        role: 'user',\n        content: prompt\n      }\n    ], 0.3); // Temperatură mai mică pentru răspunsuri mai consistente și rapide\n\n    // Parse and validate the JSON response\n    const cleanResponse = response.trim();\n    const jsonMatch = cleanResponse.match(/\\{[\\s\\S]*\\}/);\n    \n    if (!jsonMatch) {\n      throw new Error('No valid JSON found in response');\n    }\n\n    const tree = JSON.parse(jsonMatch[0]);\n    \n    // Validate structure\n    if (!tree.tema || !Array.isArray(tree.ramuri)) {\n      throw new Error('Invalid tree structure');\n    }\n\n    return tree;\n  } catch (error) {\n    console.error('Error generating knowledge tree:', error);\n    \n    // Fallback tree structure\n    return {\n      tema: topic,\n      ramuri: [\n        {\n          nume: \"Fundamentals\",\n          descriere: `Basic concepts and principles of ${topic}`,\n          emoji: \"📚\",\n          subcategorii: [\"Core Concepts\", \"Key Principles\", \"Basic Theory\"]\n        },\n        {\n          nume: \"Applications\",\n          descriere: `Practical applications and use cases of ${topic}`,\n          emoji: \"🔧\",\n          subcategorii: [\"Real-world Uses\", \"Industry Applications\", \"Case Studies\"]\n        },\n        {\n          nume: \"Advanced Topics\",\n          descriere: `Complex and specialized aspects of ${topic}`,\n          emoji: \"🎓\",\n          subcategorii: [\"Expert Level\", \"Research Areas\", \"Cutting Edge\"]\n        }\n      ]\n    };\n  }\n}\n\n// Generate Article for specific branch\nexport async function generateArticle(topic, branch, flags = ['-a']) {\n  const flagInstructions = {\n    // Basic flags\n    '-a': 'Write a standard informative article (400-600 words)',\n    '-t': 'Format the content as tables and structured data',\n    '-ex': 'Include 3 practical examples with detailed explanations',\n    '-p': 'Include code examples and technical demonstrations',\n    '-q': 'End with a 5-question quiz to test understanding',\n    '-rap': 'Write an exhaustive report with comprehensive coverage',\n    '-def': 'Focus on expert-level definitions and technical terminology',\n\n    // Learning & Visualization flags\n    '-path': 'Create personalized learning paths with step-by-step progression and milestones',\n    '-vis': 'Generate infographics, diagrams, and interactive visualizations with detailed descriptions',\n    '-vid': 'Suggest relevant videos and create video scripts with timestamps and key points',\n    '-mind': 'Present information as an interactive mind map with connected concepts',\n    '-flow': 'Create flowcharts and process diagrams with decision points and outcomes',\n\n    // Industry-specific flags\n    '-case': 'Include real case studies with measurable results and detailed analysis',\n    '-scenario': 'Create practical scenarios and simulations with step-by-step solutions',\n    '-lab': 'Design experiments and practical tests with materials and procedures',\n    '-mentor': 'Include expert advice and mentorship tips from industry professionals',\n    '-mistakes': 'Analyze common mistakes and provide prevention strategies',\n\n    // Interactive tools flags\n    '-calc': 'Include calculators and interactive tools for calculations with formulas',\n    '-template': 'Provide actionable templates and checklists ready for immediate use',\n    '-workshop': 'Format as workshop content with practical exercises and group activities',\n    '-game': 'Add gamification elements with points, achievements, and competitions',\n    '-team': 'Optimize content for teams and collaboration with group exercises',\n\n    // Sharing & presentation flags\n    '-share': 'Format for easy sharing with colleagues including summary points',\n    '-present': 'Generate PowerPoint-style presentation with slides and talking points',\n    '-meeting': 'Create meeting agenda and discussion points with time allocations',\n    '-offline': 'Ensure content is available offline with downloadable resources',\n\n    // Analytics & benchmarking flags\n    '-kpi': 'Include relevant KPIs and measurable metrics with tracking methods',\n    '-benchmark': 'Compare with industry best practices and competitive analysis',\n    '-timeline': 'Add temporal planning and milestones with realistic deadlines',\n\n    // Localization flags\n    '-ro': 'Adapt for Romanian market and legislation with local examples',\n    '-eu': 'Focus on EU regulations and practices with compliance guidelines',\n    '-local': 'Include local examples and regional practices',\n\n    // Advanced features flags\n    '-auto': 'Focus on process automation with tools and implementation steps',\n    '-predict': 'Include data-based predictions and future trends analysis',\n    '-optimize': 'Provide continuous optimization suggestions with improvement metrics'\n  };\n\n  const activeFlags = flags.filter(flag => flagInstructions[flag]);\n  const flagText = activeFlags.map(flag => flagInstructions[flag]).join('. ');\n\n  // Special handling for Romanian and localization flags\n  const hasRomanianFlag = flags.includes('-ro');\n  const hasEUFlag = flags.includes('-eu');\n  const hasLocalFlag = flags.includes('-local');\n\n  let localizationContext = '';\n  if (hasRomanianFlag) {\n    localizationContext = 'Adaptează conținutul pentru piața românească, include legislația locală, exemple de companii românești și practici specifice României.';\n  } else if (hasEUFlag) {\n    localizationContext = 'Focus on European Union regulations, GDPR compliance, EU market practices, and European case studies.';\n  } else if (hasLocalFlag) {\n    localizationContext = 'Include local examples, regional practices, and area-specific considerations.';\n  }\n\n  // Special handling for advanced features\n  const hasAdvancedFlags = flags.some(flag => ['-auto', '-predict', '-optimize'].includes(flag));\n  let advancedContext = '';\n  if (hasAdvancedFlags) {\n    advancedContext = 'Include specific tools, software recommendations, implementation steps, and measurable outcomes.';\n  }\n\n  // Search for web sources\n  let webSources = [];\n  let sourcesContext = '';\n\n  try {\n    console.log('Searching for web sources...');\n    webSources = await webSearchService.searchSources(branch.nume, 5);\n    console.log('Found sources:', webSources);\n\n    if (webSources.length > 0) {\n      sourcesContext = `\\n\\nWeb Sources Found:\\n${webSources.map(source =>\n        `- ${source.title}: ${source.description} (${source.source})`\n      ).join('\\n')}`;\n    }\n  } catch (error) {\n    console.warn('Web search failed, continuing without sources:', error);\n  }\n\n  const prompt = `Generate an article about \"${branch.nume}\" in the context of \"${topic}\".\n\nArticle Requirements:\n${flagText || flagInstructions['-a']}\n\n${localizationContext ? `Localization Requirements: ${localizationContext}` : ''}\n${advancedContext ? `Advanced Features: ${advancedContext}` : ''}\n${sourcesContext}\n\nContent Guidelines:\n- Write in an engaging, educational style suitable for professionals\n- Use clear headings and structure with markdown formatting\n- Include relevant examples and explanations\n- Make it suitable for interactive learning\n- Target length: 400-600 words (unless -rap flag is used, then 800-1200 words)\n- If visualization flags are used, describe diagrams and charts in detail\n- If interactive flags are used, provide step-by-step instructions\n- If Romanian flag is used, write in Romanian language\n\nReturn ONLY a valid JSON object with this structure:\n{\n  \"titlu\": \"Article Title\",\n  \"continut\": \"Full article content with proper markdown formatting\",\n  \"subcategorii\": [\"related topic 1\", \"related topic 2\", \"related topic 3\"],\n  \"flags\": ${JSON.stringify(flags)},\n  \"pozitie\": \"${topic} → ${branch.nume}\",\n  \"estimatedReadTime\": \"X minutes\",\n  \"difficulty\": \"Beginner|Intermediate|Advanced\",\n  \"practicalValue\": \"High|Medium|Low\"\n}\n\nTopic Context: ${topic}\nBranch: ${branch.nume}\nBranch Description: ${branch.descriere}\nSubcategories: ${branch.subcategorii?.join(', ') || 'None'}\nSelected Flags: ${flags.join(' ')}`;\n\n  try {\n    const response = await client.makeRequest([\n      {\n        role: 'system',\n        content: 'You are an expert content writer and educator. Generate comprehensive, well-structured articles in valid JSON format only. Do not include any explanatory text outside the JSON.'\n      },\n      {\n        role: 'user',\n        content: prompt\n      }\n    ], 0.8);\n\n    // Parse and validate the JSON response\n    const cleanResponse = response.trim();\n    const jsonMatch = cleanResponse.match(/\\{[\\s\\S]*\\}/);\n    \n    if (!jsonMatch) {\n      throw new Error('No valid JSON found in response');\n    }\n\n    const article = JSON.parse(jsonMatch[0]);\n    \n    // Validate structure\n    if (!article.titlu || !article.continut) {\n      throw new Error('Invalid article structure');\n    }\n\n    // Add web sources to the article\n    if (webSources.length > 0) {\n      const sourcesHTML = webSearchService.formatSourcesHTML(webSources);\n      article.continut += '\\n\\n' + sourcesHTML;\n      article.webSources = webSources;\n    }\n\n    return article;\n  } catch (error) {\n    console.error('Error generating article:', error);\n    \n    // Fallback article\n    return {\n      titlu: `${branch.nume} - ${topic}`,\n      continut: `# ${branch.nume}\n\nThis section explores ${branch.nume} in the context of ${topic}.\n\n## Overview\n${branch.descriere}\n\n## Key Concepts\nUnderstanding ${branch.nume} is essential for mastering ${topic}. This area covers fundamental principles and practical applications.\n\n## Applications\nThe concepts in ${branch.nume} have wide-ranging applications across various domains and industries.\n\n## Further Learning\nTo deepen your understanding, consider exploring related topics and practical exercises.`,\n      subcategorii: branch.subcategorii || [],\n      flags: flags,\n      pozitie: `${topic} → ${branch.nume}`\n    };\n  }\n}\n\n// Test API connection\nexport async function testConnection() {\n  try {\n    const response = await client.makeRequest([\n      {\n        role: 'user',\n        content: 'Hello, please respond with \"API connection successful\"'\n      }\n    ]);\n    \n    return response.includes('successful');\n  } catch (error) {\n    console.error('API connection test failed:', error);\n    return false;\n  }\n}\n"], "mappings": "AAAA;AACA,OAAOA,gBAAgB,MAAM,oBAAoB;AAEjD,MAAMC,kBAAkB,GAAGC,OAAO,CAACC,GAAG,CAACC,4BAA4B,IAAI,mBAAmB;AAC1F,MAAMC,mBAAmB,GAAG,8BAA8B;AAC1D,MAAMC,KAAK,GAAGJ,OAAO,CAACC,GAAG,CAACI,0BAA0B,IAAI,gCAAgC;;AAExF;AACA,MAAMC,WAAW,GAAG;EAClB,cAAc,EAAEN,OAAO,CAACC,GAAG,CAACM,kBAAkB,IAAI,uBAAuB;EACzE,SAAS,EAAEP,OAAO,CAACC,GAAG,CAACO,mBAAmB,IAAI;AAChD,CAAC;AAED,MAAMC,gBAAgB,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,OAAO,GAAGR,mBAAmB;IAClC,IAAI,CAACS,MAAM,GAAGb,kBAAkB;;IAEhC;IACA,IAAIC,OAAO,CAACC,GAAG,CAACY,oBAAoB,KAAK,MAAM,EAAE;MAC/CC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChDD,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAACJ,OAAO,CAAC;MACxCG,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEX,KAAK,CAAC;MAC9BU,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAACH,MAAM,GAAG,GAAG,IAAI,CAACA,MAAM,CAACI,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAAG,SAAS,CAAC;MACzFF,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAET,WAAW,CAAC;IAC5C;EACF;EAEA,MAAMW,WAAWA,CAACC,QAAQ,EAAEC,WAAW,GAAG,GAAG,EAAE;IAC7C,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACV,OAAO,mBAAmB,EAAE;QAC/DW,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,eAAe,EAAE,UAAU,IAAI,CAACX,MAAM,EAAE;UACxC,cAAc,EAAE,kBAAkB;UAClC,GAAGN;QACL,CAAC;QACDkB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,KAAK,EAAEvB,KAAK;UACZc,QAAQ;UACRC,WAAW;UACXS,UAAU,EAAE,IAAI;UAAE;UAClBC,MAAM,EAAE;QACV,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAACT,QAAQ,CAACU,EAAE,EAAE;QAAA,IAAAC,gBAAA;QAChB,MAAMC,SAAS,GAAG,MAAMZ,QAAQ,CAACa,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACzD,MAAM,IAAIC,KAAK,CAAC,yBAAyBf,QAAQ,CAACgB,MAAM,MAAM,EAAAL,gBAAA,GAAAC,SAAS,CAACK,KAAK,cAAAN,gBAAA,uBAAfA,gBAAA,CAAiBO,OAAO,KAAI,eAAe,EAAE,CAAC;MAC9G;MAEA,MAAMC,IAAI,GAAG,MAAMnB,QAAQ,CAACa,IAAI,CAAC,CAAC;MAClC,OAAOM,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,CAACF,OAAO,CAACG,OAAO;IACxC,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdvB,OAAO,CAACuB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAMA,KAAK;IACb;EACF;AACF;AAEA,MAAMK,MAAM,GAAG,IAAIjC,gBAAgB,CAAC,CAAC;;AAErC;AACA,OAAO,eAAekC,qBAAqBA,CAACC,KAAK,EAAE;EACjD,MAAMC,MAAM,GAAG,oCAAoCD,KAAK;AAC1D;AACA,0DAA0DA,KAAK;AAC/D;AACA;AACA;AACA,aAAaA,KAAK;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsDA,KAAK;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAWA,KAAK,EAAE;EAEhB,IAAI;IACF,MAAMxB,QAAQ,GAAG,MAAMsB,MAAM,CAACzB,WAAW,CAAC,CACxC;MACE6B,IAAI,EAAE,QAAQ;MACdL,OAAO,EAAE;IACX,CAAC,EACD;MACEK,IAAI,EAAE,MAAM;MACZL,OAAO,EAAEI;IACX,CAAC,CACF,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET;IACA,MAAME,aAAa,GAAG3B,QAAQ,CAAC4B,IAAI,CAAC,CAAC;IACrC,MAAMC,SAAS,GAAGF,aAAa,CAACG,KAAK,CAAC,aAAa,CAAC;IAEpD,IAAI,CAACD,SAAS,EAAE;MACd,MAAM,IAAId,KAAK,CAAC,iCAAiC,CAAC;IACpD;IAEA,MAAMgB,IAAI,GAAG1B,IAAI,CAAC2B,KAAK,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC;;IAErC;IACA,IAAI,CAACE,IAAI,CAACE,IAAI,IAAI,CAACC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACK,MAAM,CAAC,EAAE;MAC7C,MAAM,IAAIrB,KAAK,CAAC,wBAAwB,CAAC;IAC3C;IAEA,OAAOgB,IAAI;EACb,CAAC,CAAC,OAAOd,KAAK,EAAE;IACdvB,OAAO,CAACuB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;IAExD;IACA,OAAO;MACLgB,IAAI,EAAET,KAAK;MACXY,MAAM,EAAE,CACN;QACEC,IAAI,EAAE,cAAc;QACpBC,SAAS,EAAE,oCAAoCd,KAAK,EAAE;QACtDe,KAAK,EAAE,IAAI;QACXC,YAAY,EAAE,CAAC,eAAe,EAAE,gBAAgB,EAAE,cAAc;MAClE,CAAC,EACD;QACEH,IAAI,EAAE,cAAc;QACpBC,SAAS,EAAE,2CAA2Cd,KAAK,EAAE;QAC7De,KAAK,EAAE,IAAI;QACXC,YAAY,EAAE,CAAC,iBAAiB,EAAE,uBAAuB,EAAE,cAAc;MAC3E,CAAC,EACD;QACEH,IAAI,EAAE,iBAAiB;QACvBC,SAAS,EAAE,sCAAsCd,KAAK,EAAE;QACxDe,KAAK,EAAE,IAAI;QACXC,YAAY,EAAE,CAAC,cAAc,EAAE,gBAAgB,EAAE,cAAc;MACjE,CAAC;IAEL,CAAC;EACH;AACF;;AAEA;AACA,OAAO,eAAeC,eAAeA,CAACjB,KAAK,EAAEkB,MAAM,EAAEC,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE;EAAA,IAAAC,oBAAA;EACnE,MAAMC,gBAAgB,GAAG;IACvB;IACA,IAAI,EAAE,sDAAsD;IAC5D,IAAI,EAAE,kDAAkD;IACxD,KAAK,EAAE,yDAAyD;IAChE,IAAI,EAAE,oDAAoD;IAC1D,IAAI,EAAE,kDAAkD;IACxD,MAAM,EAAE,wDAAwD;IAChE,MAAM,EAAE,6DAA6D;IAErE;IACA,OAAO,EAAE,iFAAiF;IAC1F,MAAM,EAAE,4FAA4F;IACpG,MAAM,EAAE,iFAAiF;IACzF,OAAO,EAAE,wEAAwE;IACjF,OAAO,EAAE,0EAA0E;IAEnF;IACA,OAAO,EAAE,yEAAyE;IAClF,WAAW,EAAE,wEAAwE;IACrF,MAAM,EAAE,sEAAsE;IAC9E,SAAS,EAAE,uEAAuE;IAClF,WAAW,EAAE,2DAA2D;IAExE;IACA,OAAO,EAAE,0EAA0E;IACnF,WAAW,EAAE,qEAAqE;IAClF,WAAW,EAAE,0EAA0E;IACvF,OAAO,EAAE,uEAAuE;IAChF,OAAO,EAAE,mEAAmE;IAE5E;IACA,QAAQ,EAAE,kEAAkE;IAC5E,UAAU,EAAE,uEAAuE;IACnF,UAAU,EAAE,mEAAmE;IAC/E,UAAU,EAAE,iEAAiE;IAE7E;IACA,MAAM,EAAE,oEAAoE;IAC5E,YAAY,EAAE,+DAA+D;IAC7E,WAAW,EAAE,+DAA+D;IAE5E;IACA,KAAK,EAAE,+DAA+D;IACtE,KAAK,EAAE,kEAAkE;IACzE,QAAQ,EAAE,+CAA+C;IAEzD;IACA,OAAO,EAAE,iEAAiE;IAC1E,UAAU,EAAE,2DAA2D;IACvE,WAAW,EAAE;EACf,CAAC;EAED,MAAMC,WAAW,GAAGH,KAAK,CAACI,MAAM,CAACC,IAAI,IAAIH,gBAAgB,CAACG,IAAI,CAAC,CAAC;EAChE,MAAMC,QAAQ,GAAGH,WAAW,CAACI,GAAG,CAACF,IAAI,IAAIH,gBAAgB,CAACG,IAAI,CAAC,CAAC,CAACG,IAAI,CAAC,IAAI,CAAC;;EAE3E;EACA,MAAMC,eAAe,GAAGT,KAAK,CAACU,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMC,SAAS,GAAGX,KAAK,CAACU,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAME,YAAY,GAAGZ,KAAK,CAACU,QAAQ,CAAC,QAAQ,CAAC;EAE7C,IAAIG,mBAAmB,GAAG,EAAE;EAC5B,IAAIJ,eAAe,EAAE;IACnBI,mBAAmB,GAAG,wIAAwI;EAChK,CAAC,MAAM,IAAIF,SAAS,EAAE;IACpBE,mBAAmB,GAAG,uGAAuG;EAC/H,CAAC,MAAM,IAAID,YAAY,EAAE;IACvBC,mBAAmB,GAAG,+EAA+E;EACvG;;EAEA;EACA,MAAMC,gBAAgB,GAAGd,KAAK,CAACe,IAAI,CAACV,IAAI,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,WAAW,CAAC,CAACK,QAAQ,CAACL,IAAI,CAAC,CAAC;EAC9F,IAAIW,eAAe,GAAG,EAAE;EACxB,IAAIF,gBAAgB,EAAE;IACpBE,eAAe,GAAG,kGAAkG;EACtH;;EAEA;EACA,IAAIC,UAAU,GAAG,EAAE;EACnB,IAAIC,cAAc,GAAG,EAAE;EAEvB,IAAI;IACFnE,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC3CiE,UAAU,GAAG,MAAMlF,gBAAgB,CAACoF,aAAa,CAACpB,MAAM,CAACL,IAAI,EAAE,CAAC,CAAC;IACjE3C,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEiE,UAAU,CAAC;IAEzC,IAAIA,UAAU,CAACG,MAAM,GAAG,CAAC,EAAE;MACzBF,cAAc,GAAG,2BAA2BD,UAAU,CAACV,GAAG,CAACc,MAAM,IAC/D,KAAKA,MAAM,CAACC,KAAK,KAAKD,MAAM,CAACE,WAAW,KAAKF,MAAM,CAACA,MAAM,GAC5D,CAAC,CAACb,IAAI,CAAC,IAAI,CAAC,EAAE;IAChB;EACF,CAAC,CAAC,OAAOlC,KAAK,EAAE;IACdvB,OAAO,CAACyE,IAAI,CAAC,gDAAgD,EAAElD,KAAK,CAAC;EACvE;EAEA,MAAMQ,MAAM,GAAG,8BAA8BiB,MAAM,CAACL,IAAI,wBAAwBb,KAAK;AACvF;AACA;AACA,EAAEyB,QAAQ,IAAIJ,gBAAgB,CAAC,IAAI,CAAC;AACpC;AACA,EAAEW,mBAAmB,GAAG,8BAA8BA,mBAAmB,EAAE,GAAG,EAAE;AAChF,EAAEG,eAAe,GAAG,sBAAsBA,eAAe,EAAE,GAAG,EAAE;AAChE,EAAEE,cAAc;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAaxD,IAAI,CAACC,SAAS,CAACqC,KAAK,CAAC;AAClC,gBAAgBnB,KAAK,MAAMkB,MAAM,CAACL,IAAI;AACtC;AACA;AACA;AACA;AACA;AACA,iBAAiBb,KAAK;AACtB,UAAUkB,MAAM,CAACL,IAAI;AACrB,sBAAsBK,MAAM,CAACJ,SAAS;AACtC,iBAAiB,EAAAM,oBAAA,GAAAF,MAAM,CAACF,YAAY,cAAAI,oBAAA,uBAAnBA,oBAAA,CAAqBO,IAAI,CAAC,IAAI,CAAC,KAAI,MAAM;AAC1D,kBAAkBR,KAAK,CAACQ,IAAI,CAAC,GAAG,CAAC,EAAE;EAEjC,IAAI;IACF,MAAMnD,QAAQ,GAAG,MAAMsB,MAAM,CAACzB,WAAW,CAAC,CACxC;MACE6B,IAAI,EAAE,QAAQ;MACdL,OAAO,EAAE;IACX,CAAC,EACD;MACEK,IAAI,EAAE,MAAM;MACZL,OAAO,EAAEI;IACX,CAAC,CACF,EAAE,GAAG,CAAC;;IAEP;IACA,MAAME,aAAa,GAAG3B,QAAQ,CAAC4B,IAAI,CAAC,CAAC;IACrC,MAAMC,SAAS,GAAGF,aAAa,CAACG,KAAK,CAAC,aAAa,CAAC;IAEpD,IAAI,CAACD,SAAS,EAAE;MACd,MAAM,IAAId,KAAK,CAAC,iCAAiC,CAAC;IACpD;IAEA,MAAMqD,OAAO,GAAG/D,IAAI,CAAC2B,KAAK,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC;;IAExC;IACA,IAAI,CAACuC,OAAO,CAACC,KAAK,IAAI,CAACD,OAAO,CAACE,QAAQ,EAAE;MACvC,MAAM,IAAIvD,KAAK,CAAC,2BAA2B,CAAC;IAC9C;;IAEA;IACA,IAAI6C,UAAU,CAACG,MAAM,GAAG,CAAC,EAAE;MACzB,MAAMQ,WAAW,GAAG7F,gBAAgB,CAAC8F,iBAAiB,CAACZ,UAAU,CAAC;MAClEQ,OAAO,CAACE,QAAQ,IAAI,MAAM,GAAGC,WAAW;MACxCH,OAAO,CAACR,UAAU,GAAGA,UAAU;IACjC;IAEA,OAAOQ,OAAO;EAChB,CAAC,CAAC,OAAOnD,KAAK,EAAE;IACdvB,OAAO,CAACuB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;;IAEjD;IACA,OAAO;MACLoD,KAAK,EAAE,GAAG3B,MAAM,CAACL,IAAI,MAAMb,KAAK,EAAE;MAClC8C,QAAQ,EAAE,KAAK5B,MAAM,CAACL,IAAI;AAChC;AACA,wBAAwBK,MAAM,CAACL,IAAI,sBAAsBb,KAAK;AAC9D;AACA;AACA,EAAEkB,MAAM,CAACJ,SAAS;AAClB;AACA;AACA,gBAAgBI,MAAM,CAACL,IAAI,+BAA+Bb,KAAK;AAC/D;AACA;AACA,kBAAkBkB,MAAM,CAACL,IAAI;AAC7B;AACA;AACA,yFAAyF;MACnFG,YAAY,EAAEE,MAAM,CAACF,YAAY,IAAI,EAAE;MACvCG,KAAK,EAAEA,KAAK;MACZ8B,OAAO,EAAE,GAAGjD,KAAK,MAAMkB,MAAM,CAACL,IAAI;IACpC,CAAC;EACH;AACF;;AAEA;AACA,OAAO,eAAeqC,cAAcA,CAAA,EAAG;EACrC,IAAI;IACF,MAAM1E,QAAQ,GAAG,MAAMsB,MAAM,CAACzB,WAAW,CAAC,CACxC;MACE6B,IAAI,EAAE,MAAM;MACZL,OAAO,EAAE;IACX,CAAC,CACF,CAAC;IAEF,OAAOrB,QAAQ,CAACqD,QAAQ,CAAC,YAAY,CAAC;EACxC,CAAC,CAAC,OAAOpC,KAAK,EAAE;IACdvB,OAAO,CAACuB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACnD,OAAO,KAAK;EACd;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}