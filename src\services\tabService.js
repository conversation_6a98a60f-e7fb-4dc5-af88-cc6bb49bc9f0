// Tab Service - Multi-tree generation management
class TabService {
  constructor() {
    this.tabs = new Map();
    this.activeTabId = null;
    this.nextTabId = 1;
    this.maxTabs = 4; // Maximum 4 tabs simultaneously
    this.listeners = new Set();
  }

  // Create a new tab
  createTab(topic) {
    if (this.tabs.size >= this.maxTabs) {
      throw new Error(`Maximum ${this.maxTabs} tabs allowed`);
    }

    const tabId = `tab-${this.nextTabId++}`;
    const tab = {
      id: tabId,
      topic: topic,
      status: 'pending', // pending, generating, completed, error
      tree: null,
      selectedBranch: null,
      article: null,
      createdAt: new Date(),
      progress: 0
    };

    this.tabs.set(tabId, tab);
    this.activeTabId = tabId;
    this.notifyListeners();
    
    console.log('📂 Created new tab:', tabId, 'for topic:', topic);
    return tab;
  }

  // Get tab by ID
  getTab(tabId) {
    return this.tabs.get(tabId);
  }

  // Get active tab
  getActiveTab() {
    return this.activeTabId ? this.tabs.get(this.activeTabId) : null;
  }

  // Set active tab
  setActiveTab(tabId) {
    if (this.tabs.has(tabId)) {
      this.activeTabId = tabId;
      this.notifyListeners();
      console.log('🔄 Switched to tab:', tabId);
    }
  }

  // Update tab status
  updateTabStatus(tabId, status, data = {}) {
    const tab = this.tabs.get(tabId);
    if (tab) {
      tab.status = status;
      tab.progress = data.progress || tab.progress;
      
      if (data.tree) tab.tree = data.tree;
      if (data.selectedBranch) tab.selectedBranch = data.selectedBranch;
      if (data.article) tab.article = data.article;
      
      this.notifyListeners();
      console.log('📊 Updated tab status:', tabId, '→', status);
    }
  }

  // Close tab
  closeTab(tabId) {
    if (this.tabs.has(tabId)) {
      this.tabs.delete(tabId);
      
      // If closing active tab, switch to another tab
      if (this.activeTabId === tabId) {
        const remainingTabs = Array.from(this.tabs.keys());
        this.activeTabId = remainingTabs.length > 0 ? remainingTabs[0] : null;
      }
      
      this.notifyListeners();
      console.log('❌ Closed tab:', tabId);
    }
  }

  // Get all tabs
  getAllTabs() {
    return Array.from(this.tabs.values()).sort((a, b) => a.createdAt - b.createdAt);
  }

  // Get tab status color
  getTabStatusColor(status) {
    const colors = {
      'pending': '#94a3b8',     // gray-400 (neutral)
      'generating': '#f59e0b',  // yellow-500 (generating tree)
      'loading': '#fbbf24',     // yellow-400 (loading article)
      'completed': '#10b981',   // green-500 (ready)
      'error': '#ef4444'        // red-500 (error)
    };
    return colors[status] || '#6b7280'; // gray-500
  }

  // Get tab status icon
  getTabStatusIcon(status) {
    const icons = {
      'pending': '⏳',
      'generating': '🔄',
      'completed': '✅',
      'error': '❌'
    };
    return icons[status] || '📄';
  }

  // Add listener for tab changes
  addListener(callback) {
    this.listeners.add(callback);
  }

  // Remove listener
  removeListener(callback) {
    this.listeners.delete(callback);
  }

  // Notify all listeners
  notifyListeners() {
    this.listeners.forEach(callback => {
      try {
        callback(this.getAllTabs(), this.activeTabId);
      } catch (error) {
        console.error('Tab listener error:', error);
      }
    });
  }

  // Check if can create new tab
  canCreateNewTab() {
    return this.tabs.size < this.maxTabs;
  }

  // Get tab count
  getTabCount() {
    return this.tabs.size;
  }

  // Clear all tabs
  clearAllTabs() {
    this.tabs.clear();
    this.activeTabId = null;
    this.notifyListeners();
    console.log('🗑️ Cleared all tabs');
  }

  // Get tab progress summary
  getProgressSummary() {
    const tabs = this.getAllTabs();
    const summary = {
      total: tabs.length,
      pending: tabs.filter(t => t.status === 'pending').length,
      generating: tabs.filter(t => t.status === 'generating').length,
      completed: tabs.filter(t => t.status === 'completed').length,
      error: tabs.filter(t => t.status === 'error').length
    };
    return summary;
  }
}

// Create singleton instance
const tabService = new TabService();

export default tabService;
