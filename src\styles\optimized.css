/* Knowledge Tree Explorer - Optimized CSS (Pareto 80/20) */
/* High contrast, WCAG AAA compliant design system */

:root {
  /* Core Colors - High Contrast WCAG AAA */
  --primary: #1d4ed8;
  --primary-light: #3b82f6;
  --primary-dark: #1e3a8a;
  
  /* Neutral Colors - Maximum Contrast */
  --white: #ffffff;
  --black: #000000;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-500: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;
  
  /* Semantic Colors - High Contrast */
  --success: #047857;
  --warning: #b45309;
  --error: #b91c1c;
  
  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  
  /* Spacing */
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-12: 3rem;
  
  /* Borders & Shadows */
  --radius: 0.5rem;
  --radius-lg: 1rem;
  --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 20px 25px -5px rgb(0 0 0 / 0.1);
  
  /* Transitions */
  --transition: 200ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global Reset */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  line-height: 1.6;
  color: var(--gray-900);
  background: var(--gray-50);
  -webkit-font-smoothing: antialiased;
}

/* Layout */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: var(--space-8);
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--primary-light) 10%);
  min-height: calc(100vh - 80px);
}

/* Header */
.app-header {
  background: var(--white);
  border-bottom: 1px solid var(--gray-200);
  padding: var(--space-4) var(--space-6);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo-text {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--primary);
}

/* Buttons */
.btn {
  padding: var(--space-3) var(--space-6);
  border: none;
  border-radius: var(--radius);
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  text-decoration: none;
  min-height: 44px; /* Touch target */
}

.btn-primary {
  background: var(--primary);
  color: var(--white);
  box-shadow: var(--shadow);
}

.btn-primary:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: var(--gray-100);
  color: var(--gray-800);
  border: 1px solid var(--gray-300);
}

.btn-secondary:hover {
  background: var(--gray-200);
  color: var(--gray-900);
}

/* Forms */
.form-group {
  margin-bottom: var(--space-6);
}

.form-input {
  width: 100%;
  padding: var(--space-4);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius);
  font-size: var(--font-size-base);
  background: var(--white);
  color: var(--gray-900);
  transition: var(--transition);
  min-height: 44px; /* Touch target */
}

.form-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(29, 78, 216, 0.1);
}

.form-input::placeholder {
  color: var(--gray-500);
}

/* Cards */
.card {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  padding: var(--space-8);
  box-shadow: var(--shadow);
  max-width: 900px;
  width: 100%;
  margin: 0 auto;
  position: relative;
}

/* Typography */
.title {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--space-4);
  line-height: 1.1;
}

.subtitle {
  font-size: var(--font-size-lg);
  color: var(--gray-700);
  margin-bottom: var(--space-8);
  line-height: 1.6;
}

.text-center {
  text-align: center;
}

/* Tree View Specific */
.tree-container {
  max-width: 1000px;
  width: 100%;
  padding: var(--space-6);
}

.tree-header {
  text-align: center;
  margin-bottom: var(--space-8);
  background: var(--white);
  padding: var(--space-6);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
}

.tree-header h1 {
  font-size: var(--font-size-3xl);
  color: var(--gray-900);
  margin-bottom: var(--space-2);
}

.tree-header p {
  color: var(--gray-700);
  font-size: var(--font-size-lg);
}

.branches-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-6);
}

.branch-item {
  background: var(--white);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  cursor: pointer;
  transition: var(--transition);
  box-shadow: var(--shadow);
}

.branch-item:hover {
  border-color: var(--primary-light);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.branch-item.selected {
  border-color: var(--primary);
  background: rgba(29, 78, 216, 0.05);
}

.branch-emoji {
  font-size: var(--font-size-2xl);
  margin-bottom: var(--space-3);
}

.branch-name {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-2);
}

.branch-description {
  color: var(--gray-700);
  line-height: 1.5;
  margin-bottom: var(--space-4);
}

/* Sub-branch styling for tree effect */
.branch-item[data-is-sub-branch="true"] {
  margin-left: 40px;
  border-left: 3px solid var(--primary);
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, var(--white) 100%);
  position: relative;
}

.branch-item[data-is-sub-branch="true"]::before {
  content: '';
  position: absolute;
  left: -20px;
  top: 50%;
  width: 20px;
  height: 2px;
  background: var(--primary);
  transform: translateY(-50%);
}

.branch-item[data-level="2"] {
  margin-left: 80px;
  border-left-color: #8b5cf6;
}

.branch-item[data-level="3"] {
  margin-left: 120px;
  border-left-color: #f59e0b;
}

/* Loading States */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-3);
  padding: var(--space-8);
}

.spinner {
  width: 24px;
  height: 24px;
  border: 3px solid var(--gray-200);
  border-left-color: var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Error States */
.error {
  background: var(--error);
  color: var(--white);
  padding: var(--space-4) var(--space-6);
  border-radius: var(--radius);
  margin-bottom: var(--space-6);
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

/* Success States */
.success {
  background: var(--success);
  color: var(--white);
  padding: var(--space-4) var(--space-6);
  border-radius: var(--radius);
  margin-bottom: var(--space-6);
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-content {
    padding: var(--space-4);
  }
  
  .card {
    padding: var(--space-6);
  }
  
  .title {
    font-size: var(--font-size-3xl);
  }
  
  .branches-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .header-content {
    padding: 0 var(--space-4);
  }
}

/* Focus States for Accessibility */
.btn:focus-visible,
.form-input:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --gray-500: var(--gray-700);
    --gray-700: var(--gray-800);
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Optimized Gesture & Interactive Features */

/* Flag Wheel Styles */
.flag-wheel {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.flag-item {
  transition: all 0.2s ease;
}

.flag-item:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(29, 78, 216, 0.3);
}

.flag-item.selected {
  animation: flagSelected 0.3s ease;
}

@keyframes flagSelected {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

/* Branch Item Enhancements */
.branch-item {
  position: relative;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.branch-item .gesture-hint {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.branch-item:hover .gesture-hint {
  opacity: 1;
}

/* Mobile Responsive Design - Professional UI */
@media (max-width: 768px) {
  .app {
    min-height: 100vh;
    padding: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .app-header {
    padding: 1rem;
    position: sticky;
    top: 0;
    z-index: 100;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid #e2e8f0;
  }

  .header-content {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
  }

  .logo-text {
    font-size: 1.1rem;
    font-weight: 600;
    color: #3b82f6;
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .main-content {
    padding: 1rem;
    max-width: 100%;
    overflow-x: hidden;
  }

  .card {
    padding: 2rem 1.5rem;
    margin: 0;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    background: white;
    backdrop-filter: blur(10px);
  }

  .title {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-align: center;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .subtitle {
    font-size: 1rem;
    color: #64748b;
    text-align: center;
    margin-bottom: 2rem;
    line-height: 1.6;
  }

  .form-input {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 1.25rem;
    border-radius: 16px;
    border: 2px solid #e2e8f0;
    width: 100%;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
    background: #f8fafc;
  }

  .form-input:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
    background: white;
  }

  .btn {
    padding: 1.25rem 2rem;
    border-radius: 16px;
    font-size: 1.1rem;
    font-weight: 600;
    width: 100%;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
  }

  .btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
  }

  .btn-primary:hover::before {
    left: 100%;
  }

  /* Branches Grid - Mobile Optimized */
  .branches-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-top: 1.5rem;
  }

  .branch-item {
    background: white;
    border-radius: 20px;
    padding: 1.5rem;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #f1f5f9;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
  }

  .branch-item:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    border-color: #3b82f6;
  }

  .branch-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transform: scaleX(0);
    transition: transform 0.3s ease;
  }

  .branch-item:hover::before {
    transform: scaleX(1);
  }

  .branch-emoji {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    display: block;
    text-align: center;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
  }

  .branch-name {
    font-size: 1.2rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.75rem;
    line-height: 1.3;
    text-align: center;
  }

  .branch-description {
    font-size: 0.9rem;
    color: #64748b;
    line-height: 1.5;
    margin-bottom: 1rem;
    text-align: center;
  }

  .branch-subcategories {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
    margin-bottom: 1rem;
  }

  .subcategory-tag {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .gesture-hint {
    font-size: 0.75rem;
    color: #94a3b8;
    font-style: italic;
    text-align: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #f1f5f9;
    opacity: 0.8;
  }

  /* Tab system mobile optimization */
  .tab-manager {
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid #e2e8f0;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .tabs-container {
    display: flex;
    gap: 0.75rem;
    min-width: max-content;
    padding-bottom: 0.5rem;
  }

  .tab {
    padding: 0.75rem 1.25rem;
    border-radius: 12px;
    font-size: 0.875rem;
    font-weight: 500;
    white-space: nowrap;
    min-width: 120px;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
  }

  .tab.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  }

  .tab:not(.active) {
    background: #f8fafc;
    color: #64748b;
    border-color: #e2e8f0;
  }

  /* Loading and error states */
  .loading, .error {
    padding: 2rem 1rem;
    text-align: center;
    border-radius: 16px;
    margin: 1rem 0;
    backdrop-filter: blur(10px);
  }

  .error {
    background: rgba(254, 242, 242, 0.9);
    color: #dc2626;
    border: 1px solid #fecaca;
  }

  .loading {
    background: rgba(248, 250, 252, 0.9);
    color: #64748b;
  }

  .spinner {
    width: 32px;
    height: 32px;
    margin: 0 auto 1rem;
    border: 3px solid #e2e8f0;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Branch Level Indicators */
  .branch-level-indicator {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    opacity: 0.7;
  }

  .level-dots {
    display: flex;
    gap: 0.25rem;
  }

  .level-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
  }

  .level-text {
    font-size: 0.7rem;
    color: #64748b;
    font-weight: 500;
  }

  /* Enhanced Subcategory Tags */
  .branch-subcategories {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
    margin: 1rem 0;
  }

  .subcategory-tag {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    transition: all 0.2s ease;
  }

  .subcategory-tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  }

  .subcategory-tag.more {
    background: linear-gradient(135deg, #94a3b8, #64748b);
  }

  .subcategory-tag.small {
    padding: 0.2rem 0.5rem;
    font-size: 0.7rem;
  }

  /* Enhanced Gesture Hints */
  .gesture-hint {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    font-size: 0.75rem;
    color: #94a3b8;
    text-align: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #f1f5f9;
    opacity: 0.8;
  }

  .action-hint {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
    font-style: italic;
  }

  /* Branch Connection Lines for Sub-branches */
  .sub-branches-container {
    margin-left: 2rem;
    position: relative;
  }

  .sub-branches-container::before {
    content: '';
    position: absolute;
    left: -1rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(180deg, #667eea, #764ba2);
    opacity: 0.3;
  }

  .branch-connection-line {
    position: absolute;
    left: -1rem;
    top: 50%;
    width: 1rem;
    height: 2px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    opacity: 0.3;
  }

  /* Enhanced Branch Items */
  .branch-item.level-0 {
    border-left: 4px solid #3b82f6;
  }

  .branch-item.level-1 {
    border-left: 4px solid #8b5cf6;
    margin-left: 1rem;
  }

  .branch-item.level-2 {
    border-left: 4px solid #06b6d4;
    margin-left: 2rem;
  }

  .branch-item.sub-branch {
    background: rgba(248, 250, 252, 0.8);
    border: 1px solid #e2e8f0;
    margin-top: 0.5rem;
  }

  .branch-item.expanded {
    box-shadow: 0 8px 30px rgba(102, 126, 234, 0.2);
    border-color: #667eea;
  }

  .expand-indicator {
    position: absolute;
    top: 0.75rem;
    left: 0.75rem;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 50%;
    transition: all 0.3s ease;
  }

  .expand-icon {
    font-size: 0.75rem;
    color: #667eea;
    transition: transform 0.3s ease;
  }

  .expand-icon.expanded {
    transform: rotate(180deg);
  }
}

/* Speech Controls */
.speech-controls-compact {
  animation: slideInFromLeft 0.3s ease-out;
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Export Controls */
.export-controls-compact {
  animation: slideInFromRight 0.3s ease-out;
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Header Right Section */
.header-right {
  display: flex;
  align-items: center;
}

/* Gamification UI Enhancements */
.gamification-ui {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Button Icon Styles */
.btn-icon {
  transition: transform 0.2s ease;
}

.btn-icon:hover {
  transform: scale(1.1);
}

.btn-icon:active {
  transform: scale(0.95);
}

/* Article Controls */
.article-controls {
  animation: slideInFromTop 0.4s ease-out;
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Touch Targets for Mobile */
@media (max-width: 768px) {
  .btn-icon {
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .flag-item {
    min-width: 44px;
    min-height: 44px;
  }

  .speech-controls-compact,
  .export-controls-compact {
    flex-wrap: wrap;
  }

  .article-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .speech-controls-compact {
    justify-content: center;
  }
}

/* High Contrast Mode Enhancements */
@media (prefers-contrast: high) {
  .flag-wheel {
    border-width: 3px;
    box-shadow: 0 0 0 2px var(--primary);
  }

  .flag-item {
    border-width: 3px;
  }

  .speech-controls-compact,
  .export-controls-compact {
    border-width: 2px;
  }
}

/* Print Styles for Export */
@media print {
  .article-controls,
  .speech-controls-compact,
  .export-controls-compact,
  .gamification-ui,
  .gesture-hint {
    display: none !important;
  }

  .article-content {
    font-size: 12pt;
    line-height: 1.6;
    color: #000;
  }
}

/* Language Switcher */
.language-switcher {
  position: relative;
  display: inline-block;
}

.language-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: var(--white);
  border: 1px solid var(--gray-300);
  border-radius: 0.5rem;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--gray-700);
  transition: all 0.2s ease;
  min-width: 80px;
}

.language-button:hover {
  background: var(--gray-50);
  border-color: var(--primary);
  transform: translateY(-1px);
}

.language-flag {
  font-size: 1.2em;
}

.language-code {
  font-weight: 600;
  color: var(--primary);
}

.dropdown-arrow {
  font-size: 0.75em;
  color: var(--gray-500);
  transition: transform 0.2s ease;
}

.language-button:hover .dropdown-arrow {
  color: var(--primary);
}

.language-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.25rem;
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  z-index: 1000;
  min-width: 140px;
  overflow: hidden;
}

.language-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.75rem 1rem;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
  color: var(--gray-700);
  transition: background-color 0.2s ease;
  text-align: left;
}

.language-option:hover {
  background: var(--gray-50);
}

.language-option.active {
  background: var(--primary-light);
  color: var(--primary);
  font-weight: 600;
}

.language-name {
  flex: 1;
}

.checkmark {
  color: var(--primary);
  font-weight: bold;
}

/* Web Sources Styling */
.article-sources {
  margin-top: 2rem;
  padding: 1.5rem;
  background: var(--gray-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--gray-200);
}

.article-sources h3 {
  margin: 0 0 1rem 0;
  color: var(--gray-900);
  font-size: 1.25rem;
  font-weight: 600;
}

.sources-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.source-item {
  background: var(--white);
  padding: 1rem;
  border-radius: var(--radius);
  border: 1px solid var(--gray-200);
  transition: var(--transition);
}

.source-item:hover {
  border-color: var(--primary-light);
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.source-header {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.source-icon {
  font-size: 1.2rem;
  margin-top: 0.1rem;
}

.source-title {
  color: var(--primary);
  text-decoration: none;
  font-weight: 600;
  font-size: 0.95rem;
  line-height: 1.4;
  flex: 1;
}

.source-title:hover {
  text-decoration: underline;
}

.source-description {
  color: var(--gray-700);
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 0.5rem;
}

.source-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
  color: var(--gray-500);
}

.source-domain {
  font-weight: 500;
}

.source-type {
  background: var(--primary-light);
  color: var(--primary);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius);
  font-weight: 500;
  text-transform: capitalize;
}

/* Responsive sources */
@media (max-width: 768px) {
  .article-sources {
    margin-top: 1.5rem;
    padding: 1rem;
  }

  .source-item {
    padding: 0.75rem;
  }

  .source-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
}

/* Tab Manager Styling */
.tab-manager {
  background: var(--white);
  border-bottom: 1px solid var(--gray-200);
  position: sticky;
  top: 0;
  z-index: 100;
}

.tab-bar {
  display: flex;
  align-items: flex-end;
  gap: 2px;
  padding: 0 1rem;
  background: var(--gray-50);
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.tab-bar::-webkit-scrollbar {
  display: none;
}

.tab-item {
  display: flex;
  flex-direction: column;
  min-width: 180px;
  max-width: 220px;
  background: var(--gray-100);
  border: 1px solid var(--gray-200);
  border-bottom: none;
  border-radius: var(--radius-md) var(--radius-md) 0 0;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.tab-item:hover {
  background: var(--gray-50);
  transform: translateY(-2px);
}

.tab-item.active {
  background: var(--white);
  border-color: var(--gray-300);
  transform: translateY(0);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.tab-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  min-height: 44px;
}

.tab-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.tab-title {
  flex: 1;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--gray-700);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tab-item.active .tab-title {
  color: var(--gray-900);
  font-weight: 600;
}

.tab-close {
  background: none;
  border: none;
  color: var(--gray-400);
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 2px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.tab-close:hover {
  background: var(--gray-200);
  color: var(--gray-600);
}

.tab-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gray-200);
}

.tab-progress-bar {
  height: 100%;
  background: var(--primary);
  transition: width 0.3s ease;
  border-radius: 0 2px 0 0;
}

.tab-new {
  background: var(--gray-100);
  border: 1px dashed var(--gray-300);
  border-bottom: none;
  border-radius: var(--radius-md) var(--radius-md) 0 0;
  color: var(--gray-600);
  font-size: 0.875rem;
  font-weight: 500;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 100px;
}

.tab-new:hover {
  background: var(--gray-50);
  border-color: var(--primary);
  color: var(--primary);
}

.tab-summary {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1rem;
  background: var(--white);
  border-top: 1px solid var(--gray-100);
  font-size: 0.75rem;
}

.tab-count {
  color: var(--gray-500);
  font-weight: 500;
}

.tab-status-indicators {
  display: flex;
  gap: 1rem;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-weight: 500;
}

.status-indicator.generating {
  color: var(--yellow-600);
}

.status-indicator.completed {
  color: var(--green-600);
}

/* Responsive tabs */
@media (max-width: 768px) {
  .tab-item {
    min-width: 140px;
    max-width: 160px;
  }

  .tab-content {
    padding: 0.5rem 0.75rem;
  }

  .tab-title {
    font-size: 0.8rem;
  }

  .tab-summary {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }
}
