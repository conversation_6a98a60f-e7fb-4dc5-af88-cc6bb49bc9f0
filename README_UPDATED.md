# 🌳 Knowledge Tree Explorer - AI-Powered Learning Platform

> **Versiunea 2.0** - Platformă avansată de învățare cu AI, optimizată pentru mobil și desktop

Un generator interactiv de copaci de cunoștințe alimentat de AI care creează căi de învățare comprehensive pentru orice subiect, folosind modelul DeepSeek R1 și căutare web în timp real.

## ✨ **Funcționalități Principale**

### 🤖 **AI Avansat cu DeepSeek R1**
- **Model R1 Integrat**: Folosește cel mai avansat model DeepSeek R1 pentru generarea de conținut
- **Căutare Web în Timp Real**: Minimum 5 surse web pentru fiecare articol
- **Generare Inteligentă**: Sub-ramuri generate specific cu AI, nu aleatoriu
- **Suport Multilingv**: Română și Engleză cu respectarea strictă a limbii selectate

### 📱 **Design Responsive Profesional**
- **Mobile-First**: Interfață optimizată pentru toate device-urile
- **Touch Gestures**: Double-tap pentru articole, long-press pentru expansiune
- **Design Modern**: Gradienți, animații fluide și shadows profesionale
- **UX Intuitiv**: Feedback vizual și animații interactive

### 🌿 **Sistem Avansat de Ramuri**
- **Organizare Ierarhică**: Indicatori vizuali pentru nivele și conexiuni
- **Expansiune Inteligentă**: Sub-ramuri generate cu AI specific pentru fiecare ramură
- **Marcare Clară**: Culori distinctive și linii de conectare
- **Navigare Intuitivă**: Breadcrumb vizual și organizare logică

### 🎯 **Sistem de Flag-uri Extins**
- **-a**: Articol standard (600-800 cuvinte)
- **-ex**: Include 3 exemple practice detaliate
- **-q**: Generează exact 5 întrebări tip grilă cu răspunsuri
- **-vis**: Descrieri pentru 3+ infografice/diagrame
- **-path**: Cale de învățare cu 5-7 pași concreți
- **-case**: 2-3 studii de caz reale cu rezultate măsurabile
- **-ro**: Adaptare pentru piața românească cu legislație locală
- **-game**: Elemente de gamificare cu sistem de punctaj
- **-auto**: Focus pe automatizare cu instrumente software

### 🎮 **Gamificare Avansată**
- **Sistem de Puncte**: Puncte pentru fiecare acțiune (generare copac, articol, export)
- **Realizări**: Sistem de achievements cu notificări
- **Leaderboard**: Clasament utilizatori după numărul de subiecte explorate
- **Progresie**: Nivele și recompense pentru angajament

### 📊 **Management Avansat de Tab-uri**
- **Tab-uri Multiple**: Generează mai mulți copaci simultan
- **Status Vizual**: Galben (pending), Verde (completed), Roșu (error)
- **Acces Rapid**: Click pe tab pentru schimbare, acces direct la articole
- **Organizare**: Gestionare inteligentă a sesiunilor de învățare

### 🔊 **Funcționalități Multimedia**
- **Text-to-Speech**: Sinteză vocală cu control viteză
- **Export Multiplu**: PDF, Word, Clipboard cu formatare păstrată
- **Surse Web**: Secțiune dedicată cu minimum 5 surse verificate
- **Formatare Curată**: Markdown optimizat fără simboluri speciale

## 🚀 **Instalare și Configurare**

### **Cerințe de Sistem**
- Node.js 16+ 
- npm sau yarn
- Browser modern (Chrome, Firefox, Safari, Edge)

### **Pași de Instalare**

1. **Clonează repository-ul**:
   ```bash
   git clone <repository-url>
   cd knowledge-tree-explorer
   ```

2. **Instalează dependențele**:
   ```bash
   npm install
   ```

3. **Configurează API Key-ul**:
   - API Key-ul DeepSeek R1 este deja configurat în `src/services/openRouterService.js`
   - Model: `deepseek/deepseek-r1-0528:free`

4. **Pornește serverul de dezvoltare**:
   ```bash
   npm start
   ```

5. **Deschide aplicația** la `http://localhost:3000`

## 📖 **Ghid de Utilizare**

### **1. Autentificare**
- Click pe "Quick Login" pentru acces rapid în dezvoltare
- Sistemul suportă autentificare multi-provider (email, Google, Apple, Microsoft)

### **2. Generarea Copacului de Cunoștințe**
1. Introdu orice subiect în câmpul de text
2. Click "Explorează Cunoștințele" 
3. AI-ul generează un copac structurat cu 6-8 ramuri principale

### **3. Explorarea Ramurilor**
- **Single Click**: Selectează ramura
- **Double-Click/Double-Tap**: Generează articol cu flag wheel
- **Long-Press (500ms+)**: Expandează ramura cu sub-ramuri AI

### **4. Generarea Articolelor**
1. Double-click pe orice ramură
2. Selectează flag-urile dorite din wheel-ul circular
3. AI-ul generează articol cu DeepSeek R1 + 5 surse web
4. Articolul respectă strict limba selectată

### **5. Funcționalități Avansate**
- **Tab-uri**: Creează mai multe copaci simultan
- **Export**: PDF, Word sau Clipboard
- **Speech**: Text-to-speech cu control viteză
- **Gamificare**: Câștigă puncte și realizări

## 🛠️ **Arhitectura Tehnică**

### **Stack Tehnologic**
- **Frontend**: React 18 cu hooks moderne
- **AI**: OpenRouter API cu DeepSeek R1 model
- **Styling**: CSS custom cu design responsive
- **State Management**: React Context + local state
- **Services**: Arhitectură modulară scalabilă

### **Structura Proiectului**
```
src/
├── components/
│   ├── OptimizedApp.jsx      # Componenta principală
│   ├── TabManager.jsx        # Management tab-uri
│   └── LanguageSwitcher.jsx  # Schimbător de limbă
├── services/
│   ├── openRouterService.js  # Integrare AI DeepSeek R1
│   ├── gamificationService.js # Sistem de puncte
│   ├── speechService.js      # Text-to-speech
│   ├── exportService.js      # Export PDF/Word
│   ├── tabService.js         # Management tab-uri
│   └── webSearchService.js   # Căutare web
├── styles/
│   └── optimized.css         # Styling responsive
├── utils/
│   └── i18n.js              # Internationalizare
└── App.js                   # Entry point
```

## 🎯 **Caracteristici Unice**

### **1. AI-Powered Sub-Branch Generation**
- Sub-ramurile sunt generate specific cu AI pentru fiecare ramură
- Nu sunt copii generice - fiecare expansiune este unică și relevantă
- Folosește contextul complet al ramuri principale

### **2. Strict Language Compliance**
- Respectă 100% limba selectată în interfață
- Nu amestecă română cu engleză
- Prompt-uri specifice pentru fiecare limbă

### **3. Professional Mobile Interface**
- Touch gestures native optimizate
- Design responsive cu 120% efficiency standards
- Animații fluide și feedback vizual

### **4. Advanced Content Generation**
- Minimum 600-800 cuvinte per articol
- 5+ surse web verificate automat
- Flag system cu prompt-uri foarte specifice
- Formatare curată fără simboluri speciale

## 🔧 **Configurare Avansată**

### **Personalizare Flag-uri**
Modifică `flagInstructions` în `openRouterService.js` pentru prompt-uri custom:

```javascript
const flagInstructions = {
  ro: {
    '-custom': 'Prompt-ul tău custom în română...'
  },
  en: {
    '-custom': 'Your custom prompt in English...'
  }
};
```

### **Configurare Gamificare**
Ajustează punctele în `gamificationService.js`:

```javascript
const POINTS = {
  TREE_GENERATED: 100,
  ARTICLE_GENERATED: 50,
  BRANCH_EXPANDED: 25
};
```

## 🐛 **Troubleshooting**

### **Probleme Comune**

1. **Articolele nu se generează**:
   - Verifică conexiunea la internet
   - Verifică API key-ul în console
   - Încearcă refresh la pagină

2. **Limba nu se respectă**:
   - Verifică setarea din localStorage
   - Schimbă limba din header
   - Refresh aplicația

3. **Touch gestures nu funcționează**:
   - Verifică că ești pe device mobil
   - Încearcă long-press de 500ms+
   - Verifică că nu ai scroll activ

## 📈 **Roadmap Viitor**

### **V2.1 - În Dezvoltare**
- [ ] Autentificare completă multi-provider
- [ ] Sistem de plăți cu Stripe/Revolut
- [ ] Export avansat cu template-uri
- [ ] Colaborare în timp real

### **V2.2 - Planificat**
- [ ] Integrare cu sisteme LMS
- [ ] API public pentru dezvoltatori
- [ ] Plugin-uri pentru browser
- [ ] Aplicație mobilă nativă

## 🤝 **Contribuții**

1. Fork repository-ul
2. Creează o ramură pentru feature (`git checkout -b feature/AmazingFeature`)
3. Commit schimbările (`git commit -m 'Add AmazingFeature'`)
4. Push la ramură (`git push origin feature/AmazingFeature`)
5. Deschide un Pull Request

## 📄 **Licență**

Acest proiect este licențiat sub MIT License - vezi fișierul [LICENSE](LICENSE) pentru detalii.

---

**🌟 Dacă îți place proiectul, dă-i o stea pe GitHub! 🌟**

*Ultima actualizare: Decembrie 2024 - Versiunea 2.0 cu DeepSeek R1 și design responsive*
