{"ast": null, "code": "// Tab Service - Multi-tree generation management\nclass TabService {\n  constructor() {\n    this.tabs = new Map();\n    this.activeTabId = null;\n    this.nextTabId = 1;\n    this.maxTabs = 4; // Maximum 4 tabs simultaneously\n    this.listeners = new Set();\n  }\n\n  // Create a new tab\n  createTab(topic) {\n    if (this.tabs.size >= this.maxTabs) {\n      throw new Error(`Maximum ${this.maxTabs} tabs allowed`);\n    }\n    const tabId = `tab-${this.nextTabId++}`;\n    const tab = {\n      id: tabId,\n      topic: topic,\n      status: 'pending',\n      // pending, generating, completed, error\n      tree: null,\n      selectedBranch: null,\n      article: null,\n      createdAt: new Date(),\n      progress: 0\n    };\n    this.tabs.set(tabId, tab);\n    this.activeTabId = tabId;\n    this.notifyListeners();\n    console.log('📂 Created new tab:', tabId, 'for topic:', topic);\n    return tab;\n  }\n\n  // Get tab by ID\n  getTab(tabId) {\n    return this.tabs.get(tabId);\n  }\n\n  // Get active tab\n  getActiveTab() {\n    return this.activeTabId ? this.tabs.get(this.activeTabId) : null;\n  }\n\n  // Set active tab\n  setActiveTab(tabId) {\n    if (this.tabs.has(tabId)) {\n      this.activeTabId = tabId;\n      this.notifyListeners();\n      console.log('🔄 Switched to tab:', tabId);\n    }\n  }\n\n  // Update tab status\n  updateTabStatus(tabId, status, data = {}) {\n    const tab = this.tabs.get(tabId);\n    if (tab) {\n      tab.status = status;\n      tab.progress = data.progress || tab.progress;\n      if (data.tree) tab.tree = data.tree;\n      if (data.selectedBranch) tab.selectedBranch = data.selectedBranch;\n      if (data.article) tab.article = data.article;\n      this.notifyListeners();\n      console.log('📊 Updated tab status:', tabId, '→', status);\n    }\n  }\n\n  // Close tab\n  closeTab(tabId) {\n    if (this.tabs.has(tabId)) {\n      this.tabs.delete(tabId);\n\n      // If closing active tab, switch to another tab\n      if (this.activeTabId === tabId) {\n        const remainingTabs = Array.from(this.tabs.keys());\n        this.activeTabId = remainingTabs.length > 0 ? remainingTabs[0] : null;\n      }\n      this.notifyListeners();\n      console.log('❌ Closed tab:', tabId);\n    }\n  }\n\n  // Get all tabs\n  getAllTabs() {\n    return Array.from(this.tabs.values()).sort((a, b) => a.createdAt - b.createdAt);\n  }\n\n  // Get tab status color\n  getTabStatusColor(status) {\n    const colors = {\n      'pending': '#fbbf24',\n      // yellow-400\n      'generating': '#f59e0b',\n      // yellow-500\n      'completed': '#10b981',\n      // green-500\n      'error': '#ef4444' // red-500\n    };\n    return colors[status] || '#6b7280'; // gray-500\n  }\n\n  // Get tab status icon\n  getTabStatusIcon(status) {\n    const icons = {\n      'pending': '⏳',\n      'generating': '🔄',\n      'completed': '✅',\n      'error': '❌'\n    };\n    return icons[status] || '📄';\n  }\n\n  // Add listener for tab changes\n  addListener(callback) {\n    this.listeners.add(callback);\n  }\n\n  // Remove listener\n  removeListener(callback) {\n    this.listeners.delete(callback);\n  }\n\n  // Notify all listeners\n  notifyListeners() {\n    this.listeners.forEach(callback => {\n      try {\n        callback(this.getAllTabs(), this.activeTabId);\n      } catch (error) {\n        console.error('Tab listener error:', error);\n      }\n    });\n  }\n\n  // Check if can create new tab\n  canCreateNewTab() {\n    return this.tabs.size < this.maxTabs;\n  }\n\n  // Get tab count\n  getTabCount() {\n    return this.tabs.size;\n  }\n\n  // Clear all tabs\n  clearAllTabs() {\n    this.tabs.clear();\n    this.activeTabId = null;\n    this.notifyListeners();\n    console.log('🗑️ Cleared all tabs');\n  }\n\n  // Get tab progress summary\n  getProgressSummary() {\n    const tabs = this.getAllTabs();\n    const summary = {\n      total: tabs.length,\n      pending: tabs.filter(t => t.status === 'pending').length,\n      generating: tabs.filter(t => t.status === 'generating').length,\n      completed: tabs.filter(t => t.status === 'completed').length,\n      error: tabs.filter(t => t.status === 'error').length\n    };\n    return summary;\n  }\n}\n\n// Create singleton instance\nconst tabService = new TabService();\nexport default tabService;", "map": {"version": 3, "names": ["TabService", "constructor", "tabs", "Map", "activeTabId", "nextTabId", "maxTabs", "listeners", "Set", "createTab", "topic", "size", "Error", "tabId", "tab", "id", "status", "tree", "<PERSON><PERSON><PERSON><PERSON>", "article", "createdAt", "Date", "progress", "set", "notifyListeners", "console", "log", "getTab", "get", "getActiveTab", "setActiveTab", "has", "updateTabStatus", "data", "closeTab", "delete", "remainingTabs", "Array", "from", "keys", "length", "getAllTabs", "values", "sort", "a", "b", "getTabStatusColor", "colors", "getTabStatusIcon", "icons", "addListener", "callback", "add", "removeListener", "for<PERSON>ach", "error", "canCreateNewTab", "getTabCount", "clearAllTabs", "clear", "getProgressSummary", "summary", "total", "pending", "filter", "t", "generating", "completed", "tabService"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Appv1/src/services/tabService.js"], "sourcesContent": ["// Tab Service - Multi-tree generation management\nclass TabService {\n  constructor() {\n    this.tabs = new Map();\n    this.activeTabId = null;\n    this.nextTabId = 1;\n    this.maxTabs = 4; // Maximum 4 tabs simultaneously\n    this.listeners = new Set();\n  }\n\n  // Create a new tab\n  createTab(topic) {\n    if (this.tabs.size >= this.maxTabs) {\n      throw new Error(`Maximum ${this.maxTabs} tabs allowed`);\n    }\n\n    const tabId = `tab-${this.nextTabId++}`;\n    const tab = {\n      id: tabId,\n      topic: topic,\n      status: 'pending', // pending, generating, completed, error\n      tree: null,\n      selectedBranch: null,\n      article: null,\n      createdAt: new Date(),\n      progress: 0\n    };\n\n    this.tabs.set(tabId, tab);\n    this.activeTabId = tabId;\n    this.notifyListeners();\n    \n    console.log('📂 Created new tab:', tabId, 'for topic:', topic);\n    return tab;\n  }\n\n  // Get tab by ID\n  getTab(tabId) {\n    return this.tabs.get(tabId);\n  }\n\n  // Get active tab\n  getActiveTab() {\n    return this.activeTabId ? this.tabs.get(this.activeTabId) : null;\n  }\n\n  // Set active tab\n  setActiveTab(tabId) {\n    if (this.tabs.has(tabId)) {\n      this.activeTabId = tabId;\n      this.notifyListeners();\n      console.log('🔄 Switched to tab:', tabId);\n    }\n  }\n\n  // Update tab status\n  updateTabStatus(tabId, status, data = {}) {\n    const tab = this.tabs.get(tabId);\n    if (tab) {\n      tab.status = status;\n      tab.progress = data.progress || tab.progress;\n      \n      if (data.tree) tab.tree = data.tree;\n      if (data.selectedBranch) tab.selectedBranch = data.selectedBranch;\n      if (data.article) tab.article = data.article;\n      \n      this.notifyListeners();\n      console.log('📊 Updated tab status:', tabId, '→', status);\n    }\n  }\n\n  // Close tab\n  closeTab(tabId) {\n    if (this.tabs.has(tabId)) {\n      this.tabs.delete(tabId);\n      \n      // If closing active tab, switch to another tab\n      if (this.activeTabId === tabId) {\n        const remainingTabs = Array.from(this.tabs.keys());\n        this.activeTabId = remainingTabs.length > 0 ? remainingTabs[0] : null;\n      }\n      \n      this.notifyListeners();\n      console.log('❌ Closed tab:', tabId);\n    }\n  }\n\n  // Get all tabs\n  getAllTabs() {\n    return Array.from(this.tabs.values()).sort((a, b) => a.createdAt - b.createdAt);\n  }\n\n  // Get tab status color\n  getTabStatusColor(status) {\n    const colors = {\n      'pending': '#fbbf24',     // yellow-400\n      'generating': '#f59e0b',  // yellow-500\n      'completed': '#10b981',   // green-500\n      'error': '#ef4444'        // red-500\n    };\n    return colors[status] || '#6b7280'; // gray-500\n  }\n\n  // Get tab status icon\n  getTabStatusIcon(status) {\n    const icons = {\n      'pending': '⏳',\n      'generating': '🔄',\n      'completed': '✅',\n      'error': '❌'\n    };\n    return icons[status] || '📄';\n  }\n\n  // Add listener for tab changes\n  addListener(callback) {\n    this.listeners.add(callback);\n  }\n\n  // Remove listener\n  removeListener(callback) {\n    this.listeners.delete(callback);\n  }\n\n  // Notify all listeners\n  notifyListeners() {\n    this.listeners.forEach(callback => {\n      try {\n        callback(this.getAllTabs(), this.activeTabId);\n      } catch (error) {\n        console.error('Tab listener error:', error);\n      }\n    });\n  }\n\n  // Check if can create new tab\n  canCreateNewTab() {\n    return this.tabs.size < this.maxTabs;\n  }\n\n  // Get tab count\n  getTabCount() {\n    return this.tabs.size;\n  }\n\n  // Clear all tabs\n  clearAllTabs() {\n    this.tabs.clear();\n    this.activeTabId = null;\n    this.notifyListeners();\n    console.log('🗑️ Cleared all tabs');\n  }\n\n  // Get tab progress summary\n  getProgressSummary() {\n    const tabs = this.getAllTabs();\n    const summary = {\n      total: tabs.length,\n      pending: tabs.filter(t => t.status === 'pending').length,\n      generating: tabs.filter(t => t.status === 'generating').length,\n      completed: tabs.filter(t => t.status === 'completed').length,\n      error: tabs.filter(t => t.status === 'error').length\n    };\n    return summary;\n  }\n}\n\n// Create singleton instance\nconst tabService = new TabService();\n\nexport default tabService;\n"], "mappings": "AAAA;AACA,MAAMA,UAAU,CAAC;EACfC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,IAAI,GAAG,IAAIC,GAAG,CAAC,CAAC;IACrB,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC,CAAC;IAClB,IAAI,CAACC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC5B;;EAEA;EACAC,SAASA,CAACC,KAAK,EAAE;IACf,IAAI,IAAI,CAACR,IAAI,CAACS,IAAI,IAAI,IAAI,CAACL,OAAO,EAAE;MAClC,MAAM,IAAIM,KAAK,CAAC,WAAW,IAAI,CAACN,OAAO,eAAe,CAAC;IACzD;IAEA,MAAMO,KAAK,GAAG,OAAO,IAAI,CAACR,SAAS,EAAE,EAAE;IACvC,MAAMS,GAAG,GAAG;MACVC,EAAE,EAAEF,KAAK;MACTH,KAAK,EAAEA,KAAK;MACZM,MAAM,EAAE,SAAS;MAAE;MACnBC,IAAI,EAAE,IAAI;MACVC,cAAc,EAAE,IAAI;MACpBC,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;MACrBC,QAAQ,EAAE;IACZ,CAAC;IAED,IAAI,CAACpB,IAAI,CAACqB,GAAG,CAACV,KAAK,EAAEC,GAAG,CAAC;IACzB,IAAI,CAACV,WAAW,GAAGS,KAAK;IACxB,IAAI,CAACW,eAAe,CAAC,CAAC;IAEtBC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEb,KAAK,EAAE,YAAY,EAAEH,KAAK,CAAC;IAC9D,OAAOI,GAAG;EACZ;;EAEA;EACAa,MAAMA,CAACd,KAAK,EAAE;IACZ,OAAO,IAAI,CAACX,IAAI,CAAC0B,GAAG,CAACf,KAAK,CAAC;EAC7B;;EAEA;EACAgB,YAAYA,CAAA,EAAG;IACb,OAAO,IAAI,CAACzB,WAAW,GAAG,IAAI,CAACF,IAAI,CAAC0B,GAAG,CAAC,IAAI,CAACxB,WAAW,CAAC,GAAG,IAAI;EAClE;;EAEA;EACA0B,YAAYA,CAACjB,KAAK,EAAE;IAClB,IAAI,IAAI,CAACX,IAAI,CAAC6B,GAAG,CAAClB,KAAK,CAAC,EAAE;MACxB,IAAI,CAACT,WAAW,GAAGS,KAAK;MACxB,IAAI,CAACW,eAAe,CAAC,CAAC;MACtBC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEb,KAAK,CAAC;IAC3C;EACF;;EAEA;EACAmB,eAAeA,CAACnB,KAAK,EAAEG,MAAM,EAAEiB,IAAI,GAAG,CAAC,CAAC,EAAE;IACxC,MAAMnB,GAAG,GAAG,IAAI,CAACZ,IAAI,CAAC0B,GAAG,CAACf,KAAK,CAAC;IAChC,IAAIC,GAAG,EAAE;MACPA,GAAG,CAACE,MAAM,GAAGA,MAAM;MACnBF,GAAG,CAACQ,QAAQ,GAAGW,IAAI,CAACX,QAAQ,IAAIR,GAAG,CAACQ,QAAQ;MAE5C,IAAIW,IAAI,CAAChB,IAAI,EAAEH,GAAG,CAACG,IAAI,GAAGgB,IAAI,CAAChB,IAAI;MACnC,IAAIgB,IAAI,CAACf,cAAc,EAAEJ,GAAG,CAACI,cAAc,GAAGe,IAAI,CAACf,cAAc;MACjE,IAAIe,IAAI,CAACd,OAAO,EAAEL,GAAG,CAACK,OAAO,GAAGc,IAAI,CAACd,OAAO;MAE5C,IAAI,CAACK,eAAe,CAAC,CAAC;MACtBC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEb,KAAK,EAAE,GAAG,EAAEG,MAAM,CAAC;IAC3D;EACF;;EAEA;EACAkB,QAAQA,CAACrB,KAAK,EAAE;IACd,IAAI,IAAI,CAACX,IAAI,CAAC6B,GAAG,CAAClB,KAAK,CAAC,EAAE;MACxB,IAAI,CAACX,IAAI,CAACiC,MAAM,CAACtB,KAAK,CAAC;;MAEvB;MACA,IAAI,IAAI,CAACT,WAAW,KAAKS,KAAK,EAAE;QAC9B,MAAMuB,aAAa,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACpC,IAAI,CAACqC,IAAI,CAAC,CAAC,CAAC;QAClD,IAAI,CAACnC,WAAW,GAAGgC,aAAa,CAACI,MAAM,GAAG,CAAC,GAAGJ,aAAa,CAAC,CAAC,CAAC,GAAG,IAAI;MACvE;MAEA,IAAI,CAACZ,eAAe,CAAC,CAAC;MACtBC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEb,KAAK,CAAC;IACrC;EACF;;EAEA;EACA4B,UAAUA,CAAA,EAAG;IACX,OAAOJ,KAAK,CAACC,IAAI,CAAC,IAAI,CAACpC,IAAI,CAACwC,MAAM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACxB,SAAS,GAAGyB,CAAC,CAACzB,SAAS,CAAC;EACjF;;EAEA;EACA0B,iBAAiBA,CAAC9B,MAAM,EAAE;IACxB,MAAM+B,MAAM,GAAG;MACb,SAAS,EAAE,SAAS;MAAM;MAC1B,YAAY,EAAE,SAAS;MAAG;MAC1B,WAAW,EAAE,SAAS;MAAI;MAC1B,OAAO,EAAE,SAAS,CAAQ;IAC5B,CAAC;IACD,OAAOA,MAAM,CAAC/B,MAAM,CAAC,IAAI,SAAS,CAAC,CAAC;EACtC;;EAEA;EACAgC,gBAAgBA,CAAChC,MAAM,EAAE;IACvB,MAAMiC,KAAK,GAAG;MACZ,SAAS,EAAE,GAAG;MACd,YAAY,EAAE,IAAI;MAClB,WAAW,EAAE,GAAG;MAChB,OAAO,EAAE;IACX,CAAC;IACD,OAAOA,KAAK,CAACjC,MAAM,CAAC,IAAI,IAAI;EAC9B;;EAEA;EACAkC,WAAWA,CAACC,QAAQ,EAAE;IACpB,IAAI,CAAC5C,SAAS,CAAC6C,GAAG,CAACD,QAAQ,CAAC;EAC9B;;EAEA;EACAE,cAAcA,CAACF,QAAQ,EAAE;IACvB,IAAI,CAAC5C,SAAS,CAAC4B,MAAM,CAACgB,QAAQ,CAAC;EACjC;;EAEA;EACA3B,eAAeA,CAAA,EAAG;IAChB,IAAI,CAACjB,SAAS,CAAC+C,OAAO,CAACH,QAAQ,IAAI;MACjC,IAAI;QACFA,QAAQ,CAAC,IAAI,CAACV,UAAU,CAAC,CAAC,EAAE,IAAI,CAACrC,WAAW,CAAC;MAC/C,CAAC,CAAC,OAAOmD,KAAK,EAAE;QACd9B,OAAO,CAAC8B,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC7C;IACF,CAAC,CAAC;EACJ;;EAEA;EACAC,eAAeA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACtD,IAAI,CAACS,IAAI,GAAG,IAAI,CAACL,OAAO;EACtC;;EAEA;EACAmD,WAAWA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACvD,IAAI,CAACS,IAAI;EACvB;;EAEA;EACA+C,YAAYA,CAAA,EAAG;IACb,IAAI,CAACxD,IAAI,CAACyD,KAAK,CAAC,CAAC;IACjB,IAAI,CAACvD,WAAW,GAAG,IAAI;IACvB,IAAI,CAACoB,eAAe,CAAC,CAAC;IACtBC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;EACrC;;EAEA;EACAkC,kBAAkBA,CAAA,EAAG;IACnB,MAAM1D,IAAI,GAAG,IAAI,CAACuC,UAAU,CAAC,CAAC;IAC9B,MAAMoB,OAAO,GAAG;MACdC,KAAK,EAAE5D,IAAI,CAACsC,MAAM;MAClBuB,OAAO,EAAE7D,IAAI,CAAC8D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjD,MAAM,KAAK,SAAS,CAAC,CAACwB,MAAM;MACxD0B,UAAU,EAAEhE,IAAI,CAAC8D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjD,MAAM,KAAK,YAAY,CAAC,CAACwB,MAAM;MAC9D2B,SAAS,EAAEjE,IAAI,CAAC8D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjD,MAAM,KAAK,WAAW,CAAC,CAACwB,MAAM;MAC5De,KAAK,EAAErD,IAAI,CAAC8D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjD,MAAM,KAAK,OAAO,CAAC,CAACwB;IAChD,CAAC;IACD,OAAOqB,OAAO;EAChB;AACF;;AAEA;AACA,MAAMO,UAAU,GAAG,IAAIpE,UAAU,CAAC,CAAC;AAEnC,eAAeoE,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}