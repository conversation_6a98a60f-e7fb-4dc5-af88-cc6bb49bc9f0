{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Appv1\\\\src\\\\components\\\\OptimizedApp.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport '../styles/optimized.css';\nimport gestureService, { createFlagWheel } from '../services/gestureService';\nimport speechService from '../services/speechService';\nimport exportService from '../services/exportService';\nimport gamificationService from '../services/optimizedGamificationService';\nimport { generateKnowledgeTree as generateTreeAPI, generateArticle as generateArticleAPI, testConnection } from '../services/openRouterService';\nimport tabService from '../services/tabService';\nimport TabManager from './TabManager';\nimport LanguageSwitcher from './LanguageSwitcher';\nimport { useTranslation } from '../utils/i18n';\n\n// Optimized Knowledge Tree Explorer - Pareto 80/20 Implementation\n// Focus on core functionality with maximum impact\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst OptimizedApp = () => {\n  _s();\n  // Core state - now managed by tabs\n  const [currentView, setCurrentView] = useState('input'); // 'input', 'tree', 'article'\n  const [topic, setTopic] = useState('');\n  const [activeTab, setActiveTab] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [user, setUser] = useState(null);\n  const appRef = useRef(null);\n\n  // Get current tab data\n  const tree = (activeTab === null || activeTab === void 0 ? void 0 : activeTab.tree) || null;\n  const selectedBranch = (activeTab === null || activeTab === void 0 ? void 0 : activeTab.selectedBranch) || null;\n  const article = (activeTab === null || activeTab === void 0 ? void 0 : activeTab.article) || null;\n\n  // Translation hook\n  const {\n    t\n  } = useTranslation();\n\n  // Available flags for the optimized version\n  const availableFlags = React.useMemo(() => [{\n    code: '-a',\n    name: 'Article',\n    description: t('flagArticle')\n  }, {\n    code: '-ex',\n    name: 'Examples',\n    description: t('flagExamples')\n  }, {\n    code: '-q',\n    name: 'Quiz',\n    description: t('flagQuiz')\n  }, {\n    code: '-vis',\n    name: 'Visual',\n    description: t('flagVisual')\n  }, {\n    code: '-path',\n    name: 'Learning Path',\n    description: t('flagPath')\n  }, {\n    code: '-case',\n    name: 'Case Study',\n    description: t('flagCase')\n  }, {\n    code: '-ro',\n    name: 'Romanian',\n    description: t('flagRomanian')\n  }], [t]);\n\n  // Gesture handlers\n  const handleDoubleTap = React.useCallback((event, targetInfo) => {\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      // Show flag wheel on double tap of branch\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        createFlagWheel(targetInfo.position, availableFlags, selectedFlags => {\n          console.log('Selected flags:', selectedFlags);\n        }, selectedFlags => {\n          generateArticleForBranch(branch, selectedFlags);\n        });\n      }\n    }\n  }, [tree, availableFlags, generateArticleForBranch]);\n  const handleSingleTap = React.useCallback((event, targetInfo) => {\n    // Single tap for normal selection\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        handleBranchSelect(branch);\n      }\n    }\n  }, [tree, handleBranchSelect]);\n  const handleLongPress = React.useCallback(async (event, targetInfo) => {\n    // Long press to expand branch and create tree effect\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        await expandBranch(branch, targetInfo.branchData.index);\n      }\n    }\n  }, [tree]);\n\n  // Initialize services and authentication\n  useEffect(() => {\n    const storedUser = localStorage.getItem('user');\n    const bypassSecurity = localStorage.getItem('bypassSecurity');\n    if (storedUser || bypassSecurity) {\n      const userData = {\n        id: 'user-1',\n        name: 'User',\n        subscriptionTier: 'premium'\n      };\n      setUser(userData);\n\n      // Award daily login points\n      const result = gamificationService.awardPoints('DAILY_LOGIN');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n\n    // Initialize gesture service\n    if (appRef.current) {\n      gestureService.init(appRef.current, {\n        doubleTap: handleDoubleTap,\n        singleTap: handleSingleTap,\n        longPress: handleLongPress\n      });\n    }\n    return () => {\n      gestureService.destroy();\n    };\n  }, [handleDoubleTap, handleSingleTap, handleLongPress]);\n\n  // Initialize gamification UI when user is logged in\n  useEffect(() => {\n    if (user) {\n      const container = document.getElementById('gamification-container');\n      if (container) {\n        // Clear existing content\n        container.innerHTML = '';\n        // Create gamification UI\n        gamificationService.createGamificationUI(container);\n      }\n\n      // Test API connection\n      testConnection().then(isConnected => {\n        console.log('🔌 API Connection Status:', isConnected ? '✅ Connected' : '❌ Failed');\n        if (!isConnected) {\n          console.warn('⚠️ API connection failed. Check your API key and internet connection.');\n        }\n      }).catch(error => {\n        console.error('❌ API connection test error:', error);\n      });\n    }\n  }, [user]);\n\n  // Core API call - using optimized service with tabs\n  const generateKnowledgeTree = async (topicInput, tabId = null) => {\n    let currentTabId = tabId;\n\n    // Create new tab if none provided\n    if (!currentTabId) {\n      try {\n        const newTab = tabService.createTab(topicInput);\n        currentTabId = newTab.id;\n        setActiveTab(newTab);\n        setCurrentView('tree');\n      } catch (error) {\n        setError(error.message);\n        return;\n      }\n    }\n\n    // Update tab status to generating\n    tabService.updateTabStatus(currentTabId, 'generating', {\n      progress: 10\n    });\n    setIsLoading(true);\n    setError(null);\n    try {\n      console.log('🌳 Generating knowledge tree for:', topicInput, 'in tab:', currentTabId);\n\n      // Update progress\n      tabService.updateTabStatus(currentTabId, 'generating', {\n        progress: 30\n      });\n      const treeData = await generateTreeAPI(topicInput);\n      console.log('✅ Generated tree data:', treeData);\n\n      // Update tab with completed tree\n      tabService.updateTabStatus(currentTabId, 'completed', {\n        tree: treeData,\n        progress: 100\n      });\n\n      // Update active tab if this is the current one\n      if (currentTabId === (activeTab === null || activeTab === void 0 ? void 0 : activeTab.id)) {\n        setActiveTab(tabService.getTab(currentTabId));\n      }\n\n      // Award points for tree generation\n      const result = gamificationService.awardPoints('TREE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (err) {\n      console.error('❌ Error generating tree:', err);\n      tabService.updateTabStatus(currentTabId, 'error');\n      setError(`Failed to generate knowledge tree: ${err.message}. Please try again.`);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Handle form submission\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (topic.trim()) {\n      generateKnowledgeTree(topic.trim());\n      setTopic(''); // Clear input for next topic\n    }\n  };\n\n  // Handle tab changes\n  const handleTabChange = tab => {\n    setActiveTab(tab);\n    if (tab !== null && tab !== void 0 && tab.tree) {\n      setCurrentView('tree');\n    } else {\n      setCurrentView('input');\n    }\n  };\n\n  // Handle new tab creation\n  const handleNewTab = () => {\n    setCurrentView('input');\n    setActiveTab(null);\n    setTopic('');\n  };\n\n  // Handle branch selection (single tap)\n  const handleBranchSelect = React.useCallback(branch => {\n    if (activeTab) {\n      tabService.updateTabStatus(activeTab.id, activeTab.status, {\n        selectedBranch: branch\n      });\n      setActiveTab(tabService.getTab(activeTab.id));\n    }\n  }, [activeTab]);\n\n  // Speech functions\n  const handleSpeechToggle = () => {\n    if (!article) return;\n    if (speechService.getStatus().isPlaying) {\n      speechService.toggle();\n    } else {\n      speechService.speak(article.content);\n      // Award points for using speech\n      const result = gamificationService.awardPoints('SPEECH_USED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleSpeechStop = () => {\n    speechService.stop();\n  };\n  const handleSpeechRateChange = rate => {\n    speechService.setRate(rate);\n  };\n\n  // Export functions\n  const handleExportPDF = () => {\n    if (!article) return;\n    const result = exportService.exportAsPDF(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleExportWord = () => {\n    if (!article) return;\n    const result = exportService.exportAsWord(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleCopyToClipboard = async () => {\n    if (!article) return;\n    const result = await exportService.copyToClipboard(article.content);\n    exportService.showMessage(result.message, result.success ? 'success' : 'error');\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  // Generate article with tabs support\n  const generateArticleForBranch = async (branch, flags = ['-a']) => {\n    if (!activeTab) return;\n    setIsLoading(true);\n    try {\n      console.log('📄 Generating article for branch:', branch.nume);\n      const articleData = await generateArticleAPI(activeTab.topic, branch, flags);\n\n      // Update tab with article\n      tabService.updateTabStatus(activeTab.id, 'completed', {\n        article: articleData\n      });\n      setActiveTab(tabService.getTab(activeTab.id));\n      setCurrentView('article');\n\n      // Award points for article generation\n      const result = gamificationService.awardPoints('ARTICLE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (error) {\n      console.error('❌ Error generating article:', error);\n      setError('Failed to generate article. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Navigation functions\n  const goBack = () => {\n    if (currentView === 'article') {\n      setCurrentView('tree');\n      if (activeTab) {\n        tabService.updateTabStatus(activeTab.id, activeTab.status, {\n          article: null\n        });\n        setActiveTab(tabService.getTab(activeTab.id));\n      }\n    } else if (currentView === 'tree') {\n      setCurrentView('input');\n    }\n  };\n\n  // Expand branch to create sub-branches (tree effect)\n  const expandBranch = async (branch, branchIndex) => {\n    setIsLoading(true);\n    setError(null);\n    try {\n      var _data$choices$, _data$choices$$messag;\n      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${process.env.REACT_APP_OPENROUTER_API_KEY}`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': window.location.origin,\n          'X-Title': 'Knowledge Tree Explorer'\n        },\n        body: JSON.stringify({\n          model: 'deepseek/deepseek-r1-0528:free',\n          messages: [{\n            role: 'user',\n            content: `Expand the topic \"${branch.nume}\" from the context of \"${tree.tema}\". Create 4-6 sub-branches that dive deeper into this specific area. Return JSON with:\n            {\n              \"ramuri\": [\n                {\n                  \"nume\": \"Sub-branch Name\",\n                  \"descriere\": \"Brief description\",\n                  \"emoji\": \"📚\",\n                  \"subcategorii\": [\"Detail1\", \"Detail2\", \"Detail3\"]\n                }\n              ]\n            }\n            Focus on specific, actionable sub-topics within \"${branch.nume}\".`\n          }],\n          temperature: 0.7,\n          max_tokens: 1500\n        })\n      });\n      if (!response.ok) {\n        throw new Error(`API Error: ${response.status}`);\n      }\n      const data = await response.json();\n      const content = (_data$choices$ = data.choices[0]) === null || _data$choices$ === void 0 ? void 0 : (_data$choices$$messag = _data$choices$.message) === null || _data$choices$$messag === void 0 ? void 0 : _data$choices$$messag.content;\n      if (!content) {\n        throw new Error('No content received from API');\n      }\n\n      // Parse JSON response\n      const jsonMatch = content.match(/\\{[\\s\\S]*\\}/);\n      if (!jsonMatch) {\n        throw new Error('Invalid JSON format in response');\n      }\n      const expandedData = JSON.parse(jsonMatch[0]);\n\n      // Update tree with expanded branches\n      const newTree = {\n        ...tree\n      };\n      newTree.ramuri = [...newTree.ramuri.slice(0, branchIndex + 1), ...expandedData.ramuri.map(subBranch => ({\n        ...subBranch,\n        isSubBranch: true,\n        parentBranch: branch.nume,\n        level: (branch.level || 0) + 1\n      })), ...newTree.ramuri.slice(branchIndex + 1)];\n\n      // Update tab with expanded tree\n      if (activeTab) {\n        tabService.updateTabStatus(activeTab.id, 'completed', {\n          tree: newTree\n        });\n        setActiveTab(tabService.getTab(activeTab.id));\n      }\n\n      // Award points for branch expansion\n      const result = gamificationService.awardPoints('BRANCH_EXPANDED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (error) {\n      console.error('Error expanding branch:', error);\n      setError(t('failedToExpand'));\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const goHome = () => {\n    setCurrentView('input');\n    setActiveTab(null);\n    setTopic('');\n    // Clear all tabs\n    tabService.clearAllTabs();\n  };\n\n  // Quick login for development\n  const quickLogin = () => {\n    localStorage.setItem('bypassSecurity', 'true');\n    setUser({\n      id: 'dev-1',\n      name: 'Developer',\n      subscriptionTier: 'premium'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    ref: appRef,\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"app-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: goHome,\n          className: \"logo-text\",\n          children: t('appTitle')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-right\",\n          children: [user && /*#__PURE__*/_jsxDEV(\"div\", {\n            id: \"gamification-container\",\n            style: {\n              marginRight: '16px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(LanguageSwitcher, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 13\n          }, this), !user ? /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: quickLogin,\n            className: \"btn btn-primary\",\n            style: {\n              marginLeft: '12px'\n            },\n            children: t('quickLogin')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              marginLeft: '12px'\n            },\n            children: [t('welcome'), \", \", user.name, \"!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 463,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 462,\n      columnNumber: 7\n    }, this), user && /*#__PURE__*/_jsxDEV(TabManager, {\n      onTabChange: handleTabChange,\n      onNewTab: handleNewTab\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 487,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"main-content\",\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error\",\n        children: [\"\\u26A0\\uFE0F \", error, /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setError(null),\n          style: {\n            marginLeft: 'auto',\n            background: 'none',\n            border: 'none',\n            color: 'white',\n            cursor: 'pointer'\n          },\n          children: \"\\u2715\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 498,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 496,\n        columnNumber: 11\n      }, this), currentView === 'input' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"title\",\n          children: t('appTitle')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"subtitle\",\n          children: \"Enter any topic to generate an interactive knowledge tree with AI-powered content.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 13\n        }, this), !user ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#f1f5f9',\n            padding: '1rem',\n            borderRadius: '0.5rem',\n            marginBottom: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#334155',\n              marginBottom: '1rem'\n            },\n            children: t('loginRequired')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: quickLogin,\n            className: \"btn btn-primary\",\n            children: t('quickLoginDev')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 513,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: topic,\n              onChange: e => setTopic(e.target.value),\n              placeholder: t('topicPlaceholder'),\n              className: \"form-input\",\n              disabled: isLoading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: isLoading || !topic.trim(),\n            className: \"btn btn-primary\",\n            children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 23\n              }, this), t('generating')]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: t('exploreKnowledge')\n            }, void 0, false)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 522,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 506,\n        columnNumber: 11\n      }, this), currentView === 'tree' && tree && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tree-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tree-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: tree.tema\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: t('selectBranch')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 559,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goBack,\n            className: \"btn btn-secondary\",\n            style: {\n              marginTop: '1rem'\n            },\n            children: t('backToTree')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 557,\n          columnNumber: 13\n        }, this), isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 567,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: t('loading')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 566,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"branches-grid\",\n          children: tree.ramuri.map((branch, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `branch-item ${selectedBranch === branch ? 'selected' : ''}`,\n            \"data-index\": index,\n            \"data-name\": branch.nume,\n            \"data-description\": branch.descriere,\n            \"data-is-sub-branch\": branch.isSubBranch || false,\n            \"data-level\": branch.level || 0,\n            onClick: () => handleBranchSelect(branch),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"branch-emoji\",\n              children: branch.emoji\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 583,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"branch-name\",\n              children: branch.nume\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 584,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"branch-description\",\n              children: branch.descriere\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 585,\n              columnNumber: 21\n            }, this), branch.subcategorii && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.875rem',\n                color: '#475569',\n                marginTop: '0.5rem'\n              },\n              children: [t('topics'), \": \", branch.subcategorii.slice(0, 3).join(', '), branch.subcategorii.length > 3 && '...']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 23\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"gesture-hint\",\n              style: {\n                fontSize: '0.75rem',\n                color: '#64748b',\n                marginTop: '0.5rem',\n                fontStyle: 'italic'\n              },\n              children: t('gestureHint')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 592,\n              columnNumber: 21\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 573,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 571,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 556,\n        columnNumber: 11\n      }, this), currentView === 'article' && article && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tree-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"article-header\",\n            style: {\n              marginBottom: '2rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: goBack,\n              className: \"btn btn-secondary\",\n              children: t('backToTree')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 612,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"article-controls\",\n              style: {\n                display: 'flex',\n                gap: '8px',\n                marginTop: '1rem',\n                flexWrap: 'wrap'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"speech-controls-compact\",\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '8px',\n                  padding: '8px 12px',\n                  background: '#f1f5f9',\n                  borderRadius: '6px',\n                  border: '1px solid #e2e8f0'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleSpeechToggle,\n                  className: \"btn-icon\",\n                  title: \"Play/Pause Speech\",\n                  style: {\n                    background: 'none',\n                    border: 'none',\n                    fontSize: '16px',\n                    cursor: 'pointer',\n                    padding: '4px'\n                  },\n                  children: speechService.getStatus().isPlaying ? '⏸️' : '▶️'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 633,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleSpeechStop,\n                  className: \"btn-icon\",\n                  title: \"Stop Speech\",\n                  style: {\n                    background: 'none',\n                    border: 'none',\n                    fontSize: '16px',\n                    cursor: 'pointer',\n                    padding: '4px'\n                  },\n                  children: \"\\u23F9\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 647,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"range\",\n                  min: \"0.5\",\n                  max: \"2\",\n                  step: \"0.1\",\n                  defaultValue: \"1\",\n                  onChange: e => handleSpeechRateChange(parseFloat(e.target.value)),\n                  style: {\n                    width: '60px'\n                  },\n                  title: \"Speech Speed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 661,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '12px',\n                    color: '#64748b'\n                  },\n                  children: \"\\uD83D\\uDDE3\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 671,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 624,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"export-controls-compact\",\n                style: {\n                  display: 'flex',\n                  gap: '4px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleCopyToClipboard,\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '6px 12px',\n                    fontSize: '12px'\n                  },\n                  title: \"Copy to Clipboard\",\n                  children: \"\\uD83D\\uDCCB Copy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 679,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleExportPDF,\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '6px 12px',\n                    fontSize: '12px'\n                  },\n                  title: \"Export as PDF\",\n                  children: \"\\uD83D\\uDCC4 PDF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 687,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleExportWord,\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '6px 12px',\n                    fontSize: '12px'\n                  },\n                  title: \"Export as Word\",\n                  children: \"\\uD83D\\uDCDD Word\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 695,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 675,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 617,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 611,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"title\",\n            children: article.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 707,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#475569',\n              marginBottom: '2rem',\n              fontSize: '0.9rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: [t('partOf'), \": \", article.topic]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 709,\n              columnNumber: 17\n            }, this), article.flags && article.flags.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                marginLeft: '16px'\n              },\n              children: [t('flags'), \": \", article.flags.join(', ')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 711,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 708,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"article-content\",\n            style: {\n              lineHeight: '1.8',\n              fontSize: '1.1rem'\n            },\n            children: article.content.split('\\n').map((paragraph, index) => paragraph.trim() && /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                marginBottom: '1rem'\n              },\n              children: paragraph\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 720,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 717,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 610,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 609,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 494,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 460,\n    columnNumber: 5\n  }, this);\n};\n_s(OptimizedApp, \"RAhusUiJEhU7b1AaF/c59+vQ4go=\", false, function () {\n  return [useTranslation];\n});\n_c = OptimizedApp;\nexport default OptimizedApp;\nvar _c;\n$RefreshReg$(_c, \"OptimizedApp\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "gestureService", "createFlagWheel", "speechService", "exportService", "gamificationService", "generateKnowledgeTree", "generateTreeAPI", "generateArticle", "generateArticleAPI", "testConnection", "tabService", "TabManager", "LanguageSwitcher", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "OptimizedApp", "_s", "current<PERSON>iew", "set<PERSON><PERSON><PERSON>View", "topic", "setTopic", "activeTab", "setActiveTab", "isLoading", "setIsLoading", "error", "setError", "user", "setUser", "appRef", "tree", "<PERSON><PERSON><PERSON><PERSON>", "article", "t", "availableFlags", "useMemo", "code", "name", "description", "handleDoubleTap", "useCallback", "event", "targetInfo", "isBranchItem", "branchData", "branch", "<PERSON><PERSON>", "index", "position", "selected<PERSON><PERSON><PERSON>", "console", "log", "generateArticleForBranch", "handleSingleTap", "handleBranchSelect", "handleLongPress", "expandBranch", "storedUser", "localStorage", "getItem", "bypassSecurity", "userData", "id", "subscriptionTier", "result", "awardPoints", "newAchievements", "length", "for<PERSON>ach", "achievement", "showAchievementNotification", "current", "init", "doubleTap", "singleTap", "longPress", "destroy", "container", "document", "getElementById", "innerHTML", "createGamificationUI", "then", "isConnected", "warn", "catch", "topicInput", "tabId", "currentTabId", "newTab", "createTab", "message", "updateTabStatus", "progress", "treeData", "getTab", "err", "handleSubmit", "e", "preventDefault", "trim", "handleTabChange", "tab", "handleNewTab", "status", "handleSpeechToggle", "getStatus", "isPlaying", "toggle", "speak", "content", "handleSpeechStop", "stop", "handleSpeechRateChange", "rate", "setRate", "handleExportPDF", "exportAsPDF", "title", "replace", "success", "gamResult", "handleExportWord", "exportAsWord", "handleCopyToClipboard", "copyToClipboard", "showMessage", "flags", "nume", "articleData", "goBack", "branchIndex", "_data$choices$", "_data$choices$$messag", "response", "fetch", "method", "headers", "process", "env", "REACT_APP_OPENROUTER_API_KEY", "window", "location", "origin", "body", "JSON", "stringify", "model", "messages", "role", "tema", "temperature", "max_tokens", "ok", "Error", "data", "json", "choices", "jsonMatch", "match", "expandedData", "parse", "newTree", "slice", "map", "subBranch", "isSubBranch", "parentBranch", "level", "goHome", "clearAllTabs", "quickLogin", "setItem", "className", "ref", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginRight", "marginLeft", "onTabChange", "onNewTab", "background", "border", "color", "cursor", "padding", "borderRadius", "marginBottom", "onSubmit", "type", "value", "onChange", "target", "placeholder", "disabled", "marginTop", "desc<PERSON><PERSON>", "emoji", "subcategorii", "fontSize", "join", "fontStyle", "display", "gap", "flexWrap", "alignItems", "min", "max", "step", "defaultValue", "parseFloat", "width", "lineHeight", "split", "paragraph", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Appv1/src/components/OptimizedApp.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport '../styles/optimized.css';\nimport gestureService, { createFlagWheel } from '../services/gestureService';\nimport speechService from '../services/speechService';\nimport exportService from '../services/exportService';\nimport gamificationService from '../services/optimizedGamificationService';\nimport { generateKnowledgeTree as generateTreeAPI, generateArticle as generateArticleAPI, testConnection } from '../services/openRouterService';\nimport tabService from '../services/tabService';\nimport TabManager from './TabManager';\nimport LanguageSwitcher from './LanguageSwitcher';\nimport { useTranslation } from '../utils/i18n';\n\n// Optimized Knowledge Tree Explorer - Pareto 80/20 Implementation\n// Focus on core functionality with maximum impact\n\nconst OptimizedApp = () => {\n  // Core state - now managed by tabs\n  const [currentView, setCurrentView] = useState('input'); // 'input', 'tree', 'article'\n  const [topic, setTopic] = useState('');\n  const [activeTab, setActiveTab] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [user, setUser] = useState(null);\n  const appRef = useRef(null);\n\n  // Get current tab data\n  const tree = activeTab?.tree || null;\n  const selectedBranch = activeTab?.selectedBranch || null;\n  const article = activeTab?.article || null;\n\n  // Translation hook\n  const { t } = useTranslation();\n\n  // Available flags for the optimized version\n  const availableFlags = React.useMemo(() => [\n    { code: '-a', name: 'Article', description: t('flagArticle') },\n    { code: '-ex', name: 'Examples', description: t('flagExamples') },\n    { code: '-q', name: 'Quiz', description: t('flagQuiz') },\n    { code: '-vis', name: 'Visual', description: t('flagVisual') },\n    { code: '-path', name: 'Learning Path', description: t('flagPath') },\n    { code: '-case', name: 'Case Study', description: t('flagCase') },\n    { code: '-ro', name: 'Romanian', description: t('flagRomanian') }\n  ], [t]);\n\n\n\n  // Gesture handlers\n  const handleDoubleTap = React.useCallback((event, targetInfo) => {\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      // Show flag wheel on double tap of branch\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        createFlagWheel(\n          targetInfo.position,\n          availableFlags,\n          (selectedFlags) => {\n            console.log('Selected flags:', selectedFlags);\n          },\n          (selectedFlags) => {\n            generateArticleForBranch(branch, selectedFlags);\n          }\n        );\n      }\n    }\n  }, [tree, availableFlags, generateArticleForBranch]);\n\n  const handleSingleTap = React.useCallback((event, targetInfo) => {\n    // Single tap for normal selection\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        handleBranchSelect(branch);\n      }\n    }\n  }, [tree, handleBranchSelect]);\n\n  const handleLongPress = React.useCallback(async (event, targetInfo) => {\n    // Long press to expand branch and create tree effect\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        await expandBranch(branch, targetInfo.branchData.index);\n      }\n    }\n  }, [tree]);\n\n  // Initialize services and authentication\n  useEffect(() => {\n    const storedUser = localStorage.getItem('user');\n    const bypassSecurity = localStorage.getItem('bypassSecurity');\n\n    if (storedUser || bypassSecurity) {\n      const userData = {\n        id: 'user-1',\n        name: 'User',\n        subscriptionTier: 'premium'\n      };\n      setUser(userData);\n\n      // Award daily login points\n      const result = gamificationService.awardPoints('DAILY_LOGIN');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n\n    // Initialize gesture service\n    if (appRef.current) {\n      gestureService.init(appRef.current, {\n        doubleTap: handleDoubleTap,\n        singleTap: handleSingleTap,\n        longPress: handleLongPress\n      });\n    }\n\n    return () => {\n      gestureService.destroy();\n    };\n  }, [handleDoubleTap, handleSingleTap, handleLongPress]);\n\n  // Initialize gamification UI when user is logged in\n  useEffect(() => {\n    if (user) {\n      const container = document.getElementById('gamification-container');\n      if (container) {\n        // Clear existing content\n        container.innerHTML = '';\n        // Create gamification UI\n        gamificationService.createGamificationUI(container);\n      }\n\n      // Test API connection\n      testConnection().then(isConnected => {\n        console.log('🔌 API Connection Status:', isConnected ? '✅ Connected' : '❌ Failed');\n        if (!isConnected) {\n          console.warn('⚠️ API connection failed. Check your API key and internet connection.');\n        }\n      }).catch(error => {\n        console.error('❌ API connection test error:', error);\n      });\n    }\n  }, [user]);\n\n  // Core API call - using optimized service with tabs\n  const generateKnowledgeTree = async (topicInput, tabId = null) => {\n    let currentTabId = tabId;\n\n    // Create new tab if none provided\n    if (!currentTabId) {\n      try {\n        const newTab = tabService.createTab(topicInput);\n        currentTabId = newTab.id;\n        setActiveTab(newTab);\n        setCurrentView('tree');\n      } catch (error) {\n        setError(error.message);\n        return;\n      }\n    }\n\n    // Update tab status to generating\n    tabService.updateTabStatus(currentTabId, 'generating', { progress: 10 });\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      console.log('🌳 Generating knowledge tree for:', topicInput, 'in tab:', currentTabId);\n\n      // Update progress\n      tabService.updateTabStatus(currentTabId, 'generating', { progress: 30 });\n\n      const treeData = await generateTreeAPI(topicInput);\n      console.log('✅ Generated tree data:', treeData);\n\n      // Update tab with completed tree\n      tabService.updateTabStatus(currentTabId, 'completed', {\n        tree: treeData,\n        progress: 100\n      });\n\n      // Update active tab if this is the current one\n      if (currentTabId === activeTab?.id) {\n        setActiveTab(tabService.getTab(currentTabId));\n      }\n\n      // Award points for tree generation\n      const result = gamificationService.awardPoints('TREE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (err) {\n      console.error('❌ Error generating tree:', err);\n      tabService.updateTabStatus(currentTabId, 'error');\n      setError(`Failed to generate knowledge tree: ${err.message}. Please try again.`);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n\n\n  // Handle form submission\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    if (topic.trim()) {\n      generateKnowledgeTree(topic.trim());\n      setTopic(''); // Clear input for next topic\n    }\n  };\n\n  // Handle tab changes\n  const handleTabChange = (tab) => {\n    setActiveTab(tab);\n    if (tab?.tree) {\n      setCurrentView('tree');\n    } else {\n      setCurrentView('input');\n    }\n  };\n\n  // Handle new tab creation\n  const handleNewTab = () => {\n    setCurrentView('input');\n    setActiveTab(null);\n    setTopic('');\n  };\n\n  // Handle branch selection (single tap)\n  const handleBranchSelect = React.useCallback((branch) => {\n    if (activeTab) {\n      tabService.updateTabStatus(activeTab.id, activeTab.status, { selectedBranch: branch });\n      setActiveTab(tabService.getTab(activeTab.id));\n    }\n  }, [activeTab]);\n\n  // Speech functions\n  const handleSpeechToggle = () => {\n    if (!article) return;\n\n    if (speechService.getStatus().isPlaying) {\n      speechService.toggle();\n    } else {\n      speechService.speak(article.content);\n      // Award points for using speech\n      const result = gamificationService.awardPoints('SPEECH_USED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleSpeechStop = () => {\n    speechService.stop();\n  };\n\n  const handleSpeechRateChange = (rate) => {\n    speechService.setRate(rate);\n  };\n\n  // Export functions\n  const handleExportPDF = () => {\n    if (!article) return;\n    const result = exportService.exportAsPDF(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleExportWord = () => {\n    if (!article) return;\n    const result = exportService.exportAsWord(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleCopyToClipboard = async () => {\n    if (!article) return;\n    const result = await exportService.copyToClipboard(article.content);\n    exportService.showMessage(result.message, result.success ? 'success' : 'error');\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  // Generate article with tabs support\n  const generateArticleForBranch = async (branch, flags = ['-a']) => {\n    if (!activeTab) return;\n\n    setIsLoading(true);\n    try {\n      console.log('📄 Generating article for branch:', branch.nume);\n      const articleData = await generateArticleAPI(activeTab.topic, branch, flags);\n\n      // Update tab with article\n      tabService.updateTabStatus(activeTab.id, 'completed', {\n        article: articleData\n      });\n\n      setActiveTab(tabService.getTab(activeTab.id));\n      setCurrentView('article');\n\n      // Award points for article generation\n      const result = gamificationService.awardPoints('ARTICLE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (error) {\n      console.error('❌ Error generating article:', error);\n      setError('Failed to generate article. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Navigation functions\n  const goBack = () => {\n    if (currentView === 'article') {\n      setCurrentView('tree');\n      if (activeTab) {\n        tabService.updateTabStatus(activeTab.id, activeTab.status, { article: null });\n        setActiveTab(tabService.getTab(activeTab.id));\n      }\n    } else if (currentView === 'tree') {\n      setCurrentView('input');\n    }\n  };\n\n  // Expand branch to create sub-branches (tree effect)\n  const expandBranch = async (branch, branchIndex) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${process.env.REACT_APP_OPENROUTER_API_KEY}`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': window.location.origin,\n          'X-Title': 'Knowledge Tree Explorer'\n        },\n        body: JSON.stringify({\n          model: 'deepseek/deepseek-r1-0528:free',\n          messages: [{\n            role: 'user',\n            content: `Expand the topic \"${branch.nume}\" from the context of \"${tree.tema}\". Create 4-6 sub-branches that dive deeper into this specific area. Return JSON with:\n            {\n              \"ramuri\": [\n                {\n                  \"nume\": \"Sub-branch Name\",\n                  \"descriere\": \"Brief description\",\n                  \"emoji\": \"📚\",\n                  \"subcategorii\": [\"Detail1\", \"Detail2\", \"Detail3\"]\n                }\n              ]\n            }\n            Focus on specific, actionable sub-topics within \"${branch.nume}\".`\n          }],\n          temperature: 0.7,\n          max_tokens: 1500\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error(`API Error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      const content = data.choices[0]?.message?.content;\n\n      if (!content) {\n        throw new Error('No content received from API');\n      }\n\n      // Parse JSON response\n      const jsonMatch = content.match(/\\{[\\s\\S]*\\}/);\n      if (!jsonMatch) {\n        throw new Error('Invalid JSON format in response');\n      }\n\n      const expandedData = JSON.parse(jsonMatch[0]);\n\n      // Update tree with expanded branches\n      const newTree = { ...tree };\n      newTree.ramuri = [\n        ...newTree.ramuri.slice(0, branchIndex + 1),\n        ...expandedData.ramuri.map(subBranch => ({\n          ...subBranch,\n          isSubBranch: true,\n          parentBranch: branch.nume,\n          level: (branch.level || 0) + 1\n        })),\n        ...newTree.ramuri.slice(branchIndex + 1)\n      ];\n\n      // Update tab with expanded tree\n      if (activeTab) {\n        tabService.updateTabStatus(activeTab.id, 'completed', { tree: newTree });\n        setActiveTab(tabService.getTab(activeTab.id));\n      }\n\n      // Award points for branch expansion\n      const result = gamificationService.awardPoints('BRANCH_EXPANDED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n\n    } catch (error) {\n      console.error('Error expanding branch:', error);\n      setError(t('failedToExpand'));\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const goHome = () => {\n    setCurrentView('input');\n    setActiveTab(null);\n    setTopic('');\n    // Clear all tabs\n    tabService.clearAllTabs();\n  };\n\n  // Quick login for development\n  const quickLogin = () => {\n    localStorage.setItem('bypassSecurity', 'true');\n    setUser({ id: 'dev-1', name: 'Developer', subscriptionTier: 'premium' });\n  };\n\n  return (\n    <div className=\"app\" ref={appRef}>\n      {/* Header */}\n      <header className=\"app-header\">\n        <div className=\"header-content\">\n          <button onClick={goHome} className=\"logo-text\">\n            {t('appTitle')}\n          </button>\n          <div className=\"header-right\">\n            {user && (\n              <div id=\"gamification-container\" style={{ marginRight: '16px' }}>\n                {/* Gamification UI will be inserted here */}\n              </div>\n            )}\n            <LanguageSwitcher />\n            {!user ? (\n              <button onClick={quickLogin} className=\"btn btn-primary\" style={{ marginLeft: '12px' }}>\n                {t('quickLogin')}\n              </button>\n            ) : (\n              <span style={{ marginLeft: '12px' }}>{t('welcome')}, {user.name}!</span>\n            )}\n          </div>\n        </div>\n      </header>\n\n      {/* Tab Manager */}\n      {user && (\n        <TabManager\n          onTabChange={handleTabChange}\n          onNewTab={handleNewTab}\n        />\n      )}\n\n      {/* Main Content */}\n      <main className=\"main-content\">\n        {error && (\n          <div className=\"error\">\n            ⚠️ {error}\n            <button onClick={() => setError(null)} style={{marginLeft: 'auto', background: 'none', border: 'none', color: 'white', cursor: 'pointer'}}>\n              ✕\n            </button>\n          </div>\n        )}\n\n        {/* Topic Input View */}\n        {currentView === 'input' && (\n          <div className=\"card text-center\">\n            <h1 className=\"title\">{t('appTitle')}</h1>\n            <p className=\"subtitle\">\n              Enter any topic to generate an interactive knowledge tree with AI-powered content.\n            </p>\n\n            {!user ? (\n              <div style={{background: '#f1f5f9', padding: '1rem', borderRadius: '0.5rem', marginBottom: '2rem'}}>\n                <p style={{color: '#334155', marginBottom: '1rem'}}>\n                  {t('loginRequired')}\n                </p>\n                <button onClick={quickLogin} className=\"btn btn-primary\">\n                  {t('quickLoginDev')}\n                </button>\n              </div>\n            ) : (\n              <form onSubmit={handleSubmit}>\n                <div className=\"form-group\">\n                  <input\n                    type=\"text\"\n                    value={topic}\n                    onChange={(e) => setTopic(e.target.value)}\n                    placeholder={t('topicPlaceholder')}\n                    className=\"form-input\"\n                    disabled={isLoading}\n                  />\n                </div>\n                <button\n                  type=\"submit\"\n                  disabled={isLoading || !topic.trim()}\n                  className=\"btn btn-primary\"\n                >\n                  {isLoading ? (\n                    <>\n                      <span className=\"spinner\"></span>\n                      {t('generating')}\n                    </>\n                  ) : (\n                    <>\n                      {t('exploreKnowledge')}\n                    </>\n                  )}\n                </button>\n              </form>\n            )}\n          </div>\n        )}\n\n        {/* Tree View */}\n        {currentView === 'tree' && tree && (\n          <div className=\"tree-container\">\n            <div className=\"tree-header\">\n              <h1>{tree.tema}</h1>\n              <p>{t('selectBranch')}</p>\n              <button onClick={goBack} className=\"btn btn-secondary\" style={{marginTop: '1rem'}}>\n                {t('backToTree')}\n              </button>\n            </div>\n\n            {isLoading ? (\n              <div className=\"loading\">\n                <span className=\"spinner\"></span>\n                <span>{t('loading')}</span>\n              </div>\n            ) : (\n              <div className=\"branches-grid\">\n                {tree.ramuri.map((branch, index) => (\n                  <div\n                    key={index}\n                    className={`branch-item ${selectedBranch === branch ? 'selected' : ''}`}\n                    data-index={index}\n                    data-name={branch.nume}\n                    data-description={branch.descriere}\n                    data-is-sub-branch={branch.isSubBranch || false}\n                    data-level={branch.level || 0}\n                    onClick={() => handleBranchSelect(branch)}\n                  >\n                    <div className=\"branch-emoji\">{branch.emoji}</div>\n                    <h3 className=\"branch-name\">{branch.nume}</h3>\n                    <p className=\"branch-description\">{branch.descriere}</p>\n                    {branch.subcategorii && (\n                      <div style={{fontSize: '0.875rem', color: '#475569', marginTop: '0.5rem'}}>\n                        {t('topics')}: {branch.subcategorii.slice(0, 3).join(', ')}\n                        {branch.subcategorii.length > 3 && '...'}\n                      </div>\n                    )}\n                    <div className=\"gesture-hint\" style={{\n                      fontSize: '0.75rem',\n                      color: '#64748b',\n                      marginTop: '0.5rem',\n                      fontStyle: 'italic'\n                    }}>\n                      {t('gestureHint')}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Article View */}\n        {currentView === 'article' && article && (\n          <div className=\"tree-container\">\n            <div className=\"card\">\n              <div className=\"article-header\" style={{marginBottom: '2rem'}}>\n                <button onClick={goBack} className=\"btn btn-secondary\">\n                  {t('backToTree')}\n                </button>\n\n                {/* Article Controls */}\n                <div className=\"article-controls\" style={{\n                  display: 'flex',\n                  gap: '8px',\n                  marginTop: '1rem',\n                  flexWrap: 'wrap'\n                }}>\n                  {/* Speech Controls */}\n                  <div className=\"speech-controls-compact\" style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '8px',\n                    padding: '8px 12px',\n                    background: '#f1f5f9',\n                    borderRadius: '6px',\n                    border: '1px solid #e2e8f0'\n                  }}>\n                    <button\n                      onClick={handleSpeechToggle}\n                      className=\"btn-icon\"\n                      title=\"Play/Pause Speech\"\n                      style={{\n                        background: 'none',\n                        border: 'none',\n                        fontSize: '16px',\n                        cursor: 'pointer',\n                        padding: '4px'\n                      }}\n                    >\n                      {speechService.getStatus().isPlaying ? '⏸️' : '▶️'}\n                    </button>\n                    <button\n                      onClick={handleSpeechStop}\n                      className=\"btn-icon\"\n                      title=\"Stop Speech\"\n                      style={{\n                        background: 'none',\n                        border: 'none',\n                        fontSize: '16px',\n                        cursor: 'pointer',\n                        padding: '4px'\n                      }}\n                    >\n                      ⏹️\n                    </button>\n                    <input\n                      type=\"range\"\n                      min=\"0.5\"\n                      max=\"2\"\n                      step=\"0.1\"\n                      defaultValue=\"1\"\n                      onChange={(e) => handleSpeechRateChange(parseFloat(e.target.value))}\n                      style={{width: '60px'}}\n                      title=\"Speech Speed\"\n                    />\n                    <span style={{fontSize: '12px', color: '#64748b'}}>🗣️</span>\n                  </div>\n\n                  {/* Export Controls */}\n                  <div className=\"export-controls-compact\" style={{\n                    display: 'flex',\n                    gap: '4px'\n                  }}>\n                    <button\n                      onClick={handleCopyToClipboard}\n                      className=\"btn btn-secondary\"\n                      style={{padding: '6px 12px', fontSize: '12px'}}\n                      title=\"Copy to Clipboard\"\n                    >\n                      📋 Copy\n                    </button>\n                    <button\n                      onClick={handleExportPDF}\n                      className=\"btn btn-secondary\"\n                      style={{padding: '6px 12px', fontSize: '12px'}}\n                      title=\"Export as PDF\"\n                    >\n                      📄 PDF\n                    </button>\n                    <button\n                      onClick={handleExportWord}\n                      className=\"btn btn-secondary\"\n                      style={{padding: '6px 12px', fontSize: '12px'}}\n                      title=\"Export as Word\"\n                    >\n                      📝 Word\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              <h1 className=\"title\">{article.title}</h1>\n              <div style={{color: '#475569', marginBottom: '2rem', fontSize: '0.9rem'}}>\n                <span>{t('partOf')}: {article.topic}</span>\n                {article.flags && article.flags.length > 0 && (\n                  <span style={{marginLeft: '16px'}}>\n                    {t('flags')}: {article.flags.join(', ')}\n                  </span>\n                )}\n              </div>\n\n              <div className=\"article-content\" style={{lineHeight: '1.8', fontSize: '1.1rem'}}>\n                {article.content.split('\\n').map((paragraph, index) => (\n                  paragraph.trim() && (\n                    <p key={index} style={{marginBottom: '1rem'}}>\n                      {paragraph}\n                    </p>\n                  )\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n      </main>\n    </div>\n  );\n};\n\nexport default OptimizedApp;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,yBAAyB;AAChC,OAAOC,cAAc,IAAIC,eAAe,QAAQ,4BAA4B;AAC5E,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,mBAAmB,MAAM,0CAA0C;AAC1E,SAASC,qBAAqB,IAAIC,eAAe,EAAEC,eAAe,IAAIC,kBAAkB,EAAEC,cAAc,QAAQ,+BAA+B;AAC/I,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,cAAc,QAAQ,eAAe;;AAE9C;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEA,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACzD,MAAM,CAACyB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiC,IAAI,EAAEC,OAAO,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAMmC,MAAM,GAAGjC,MAAM,CAAC,IAAI,CAAC;;EAE3B;EACA,MAAMkC,IAAI,GAAG,CAAAT,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAES,IAAI,KAAI,IAAI;EACpC,MAAMC,cAAc,GAAG,CAAAV,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEU,cAAc,KAAI,IAAI;EACxD,MAAMC,OAAO,GAAG,CAAAX,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEW,OAAO,KAAI,IAAI;;EAE1C;EACA,MAAM;IAAEC;EAAE,CAAC,GAAGvB,cAAc,CAAC,CAAC;;EAE9B;EACA,MAAMwB,cAAc,GAAGzC,KAAK,CAAC0C,OAAO,CAAC,MAAM,CACzC;IAAEC,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,SAAS;IAAEC,WAAW,EAAEL,CAAC,CAAC,aAAa;EAAE,CAAC,EAC9D;IAAEG,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAE,UAAU;IAAEC,WAAW,EAAEL,CAAC,CAAC,cAAc;EAAE,CAAC,EACjE;IAAEG,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,MAAM;IAAEC,WAAW,EAAEL,CAAC,CAAC,UAAU;EAAE,CAAC,EACxD;IAAEG,IAAI,EAAE,MAAM;IAAEC,IAAI,EAAE,QAAQ;IAAEC,WAAW,EAAEL,CAAC,CAAC,YAAY;EAAE,CAAC,EAC9D;IAAEG,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,eAAe;IAAEC,WAAW,EAAEL,CAAC,CAAC,UAAU;EAAE,CAAC,EACpE;IAAEG,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,YAAY;IAAEC,WAAW,EAAEL,CAAC,CAAC,UAAU;EAAE,CAAC,EACjE;IAAEG,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAE,UAAU;IAAEC,WAAW,EAAEL,CAAC,CAAC,cAAc;EAAE,CAAC,CAClE,EAAE,CAACA,CAAC,CAAC,CAAC;;EAIP;EACA,MAAMM,eAAe,GAAG9C,KAAK,CAAC+C,WAAW,CAAC,CAACC,KAAK,EAAEC,UAAU,KAAK;IAC/D,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,IAAId,IAAI,EAAE;MAC5D;MACA,MAAMe,MAAM,GAAGf,IAAI,CAACgB,MAAM,CAACJ,UAAU,CAACE,UAAU,CAACG,KAAK,CAAC;MACvD,IAAIF,MAAM,EAAE;QACV/C,eAAe,CACb4C,UAAU,CAACM,QAAQ,EACnBd,cAAc,EACbe,aAAa,IAAK;UACjBC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,aAAa,CAAC;QAC/C,CAAC,EACAA,aAAa,IAAK;UACjBG,wBAAwB,CAACP,MAAM,EAAEI,aAAa,CAAC;QACjD,CACF,CAAC;MACH;IACF;EACF,CAAC,EAAE,CAACnB,IAAI,EAAEI,cAAc,EAAEkB,wBAAwB,CAAC,CAAC;EAEpD,MAAMC,eAAe,GAAG5D,KAAK,CAAC+C,WAAW,CAAC,CAACC,KAAK,EAAEC,UAAU,KAAK;IAC/D;IACA,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,IAAId,IAAI,EAAE;MAC5D,MAAMe,MAAM,GAAGf,IAAI,CAACgB,MAAM,CAACJ,UAAU,CAACE,UAAU,CAACG,KAAK,CAAC;MACvD,IAAIF,MAAM,EAAE;QACVS,kBAAkB,CAACT,MAAM,CAAC;MAC5B;IACF;EACF,CAAC,EAAE,CAACf,IAAI,EAAEwB,kBAAkB,CAAC,CAAC;EAE9B,MAAMC,eAAe,GAAG9D,KAAK,CAAC+C,WAAW,CAAC,OAAOC,KAAK,EAAEC,UAAU,KAAK;IACrE;IACA,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,IAAId,IAAI,EAAE;MAC5D,MAAMe,MAAM,GAAGf,IAAI,CAACgB,MAAM,CAACJ,UAAU,CAACE,UAAU,CAACG,KAAK,CAAC;MACvD,IAAIF,MAAM,EAAE;QACV,MAAMW,YAAY,CAACX,MAAM,EAAEH,UAAU,CAACE,UAAU,CAACG,KAAK,CAAC;MACzD;IACF;EACF,CAAC,EAAE,CAACjB,IAAI,CAAC,CAAC;;EAEV;EACAnC,SAAS,CAAC,MAAM;IACd,MAAM8D,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAC/C,MAAMC,cAAc,GAAGF,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAE7D,IAAIF,UAAU,IAAIG,cAAc,EAAE;MAChC,MAAMC,QAAQ,GAAG;QACfC,EAAE,EAAE,QAAQ;QACZzB,IAAI,EAAE,MAAM;QACZ0B,gBAAgB,EAAE;MACpB,CAAC;MACDnC,OAAO,CAACiC,QAAQ,CAAC;;MAEjB;MACA,MAAMG,MAAM,GAAG/D,mBAAmB,CAACgE,WAAW,CAAC,aAAa,CAAC;MAC7D,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5CpE,mBAAmB,CAACqE,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;;IAEA;IACA,IAAIxC,MAAM,CAAC0C,OAAO,EAAE;MAClB1E,cAAc,CAAC2E,IAAI,CAAC3C,MAAM,CAAC0C,OAAO,EAAE;QAClCE,SAAS,EAAElC,eAAe;QAC1BmC,SAAS,EAAErB,eAAe;QAC1BsB,SAAS,EAAEpB;MACb,CAAC,CAAC;IACJ;IAEA,OAAO,MAAM;MACX1D,cAAc,CAAC+E,OAAO,CAAC,CAAC;IAC1B,CAAC;EACH,CAAC,EAAE,CAACrC,eAAe,EAAEc,eAAe,EAAEE,eAAe,CAAC,CAAC;;EAEvD;EACA5D,SAAS,CAAC,MAAM;IACd,IAAIgC,IAAI,EAAE;MACR,MAAMkD,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,wBAAwB,CAAC;MACnE,IAAIF,SAAS,EAAE;QACb;QACAA,SAAS,CAACG,SAAS,GAAG,EAAE;QACxB;QACA/E,mBAAmB,CAACgF,oBAAoB,CAACJ,SAAS,CAAC;MACrD;;MAEA;MACAvE,cAAc,CAAC,CAAC,CAAC4E,IAAI,CAACC,WAAW,IAAI;QACnCjC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEgC,WAAW,GAAG,aAAa,GAAG,UAAU,CAAC;QAClF,IAAI,CAACA,WAAW,EAAE;UAChBjC,OAAO,CAACkC,IAAI,CAAC,uEAAuE,CAAC;QACvF;MACF,CAAC,CAAC,CAACC,KAAK,CAAC5D,KAAK,IAAI;QAChByB,OAAO,CAACzB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACE,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMzB,qBAAqB,GAAG,MAAAA,CAAOoF,UAAU,EAAEC,KAAK,GAAG,IAAI,KAAK;IAChE,IAAIC,YAAY,GAAGD,KAAK;;IAExB;IACA,IAAI,CAACC,YAAY,EAAE;MACjB,IAAI;QACF,MAAMC,MAAM,GAAGlF,UAAU,CAACmF,SAAS,CAACJ,UAAU,CAAC;QAC/CE,YAAY,GAAGC,MAAM,CAAC3B,EAAE;QACxBxC,YAAY,CAACmE,MAAM,CAAC;QACpBvE,cAAc,CAAC,MAAM,CAAC;MACxB,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,QAAQ,CAACD,KAAK,CAACkE,OAAO,CAAC;QACvB;MACF;IACF;;IAEA;IACApF,UAAU,CAACqF,eAAe,CAACJ,YAAY,EAAE,YAAY,EAAE;MAAEK,QAAQ,EAAE;IAAG,CAAC,CAAC;IACxErE,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACFwB,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEmC,UAAU,EAAE,SAAS,EAAEE,YAAY,CAAC;;MAErF;MACAjF,UAAU,CAACqF,eAAe,CAACJ,YAAY,EAAE,YAAY,EAAE;QAAEK,QAAQ,EAAE;MAAG,CAAC,CAAC;MAExE,MAAMC,QAAQ,GAAG,MAAM3F,eAAe,CAACmF,UAAU,CAAC;MAClDpC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE2C,QAAQ,CAAC;;MAE/C;MACAvF,UAAU,CAACqF,eAAe,CAACJ,YAAY,EAAE,WAAW,EAAE;QACpD1D,IAAI,EAAEgE,QAAQ;QACdD,QAAQ,EAAE;MACZ,CAAC,CAAC;;MAEF;MACA,IAAIL,YAAY,MAAKnE,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEyC,EAAE,GAAE;QAClCxC,YAAY,CAACf,UAAU,CAACwF,MAAM,CAACP,YAAY,CAAC,CAAC;MAC/C;;MAEA;MACA,MAAMxB,MAAM,GAAG/D,mBAAmB,CAACgE,WAAW,CAAC,gBAAgB,CAAC;MAChE,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5CpE,mBAAmB,CAACqE,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAO2B,GAAG,EAAE;MACZ9C,OAAO,CAACzB,KAAK,CAAC,0BAA0B,EAAEuE,GAAG,CAAC;MAC9CzF,UAAU,CAACqF,eAAe,CAACJ,YAAY,EAAE,OAAO,CAAC;MACjD9D,QAAQ,CAAC,sCAAsCsE,GAAG,CAACL,OAAO,qBAAqB,CAAC;IAClF,CAAC,SAAS;MACRnE,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAID;EACA,MAAMyE,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIhF,KAAK,CAACiF,IAAI,CAAC,CAAC,EAAE;MAChBlG,qBAAqB,CAACiB,KAAK,CAACiF,IAAI,CAAC,CAAC,CAAC;MACnChF,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB;EACF,CAAC;;EAED;EACA,MAAMiF,eAAe,GAAIC,GAAG,IAAK;IAC/BhF,YAAY,CAACgF,GAAG,CAAC;IACjB,IAAIA,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAExE,IAAI,EAAE;MACbZ,cAAc,CAAC,MAAM,CAAC;IACxB,CAAC,MAAM;MACLA,cAAc,CAAC,OAAO,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMqF,YAAY,GAAGA,CAAA,KAAM;IACzBrF,cAAc,CAAC,OAAO,CAAC;IACvBI,YAAY,CAAC,IAAI,CAAC;IAClBF,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;;EAED;EACA,MAAMkC,kBAAkB,GAAG7D,KAAK,CAAC+C,WAAW,CAAEK,MAAM,IAAK;IACvD,IAAIxB,SAAS,EAAE;MACbd,UAAU,CAACqF,eAAe,CAACvE,SAAS,CAACyC,EAAE,EAAEzC,SAAS,CAACmF,MAAM,EAAE;QAAEzE,cAAc,EAAEc;MAAO,CAAC,CAAC;MACtFvB,YAAY,CAACf,UAAU,CAACwF,MAAM,CAAC1E,SAAS,CAACyC,EAAE,CAAC,CAAC;IAC/C;EACF,CAAC,EAAE,CAACzC,SAAS,CAAC,CAAC;;EAEf;EACA,MAAMoF,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI,CAACzE,OAAO,EAAE;IAEd,IAAIjC,aAAa,CAAC2G,SAAS,CAAC,CAAC,CAACC,SAAS,EAAE;MACvC5G,aAAa,CAAC6G,MAAM,CAAC,CAAC;IACxB,CAAC,MAAM;MACL7G,aAAa,CAAC8G,KAAK,CAAC7E,OAAO,CAAC8E,OAAO,CAAC;MACpC;MACA,MAAM9C,MAAM,GAAG/D,mBAAmB,CAACgE,WAAW,CAAC,aAAa,CAAC;MAC7D,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5CpE,mBAAmB,CAACqE,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAM0C,gBAAgB,GAAGA,CAAA,KAAM;IAC7BhH,aAAa,CAACiH,IAAI,CAAC,CAAC;EACtB,CAAC;EAED,MAAMC,sBAAsB,GAAIC,IAAI,IAAK;IACvCnH,aAAa,CAACoH,OAAO,CAACD,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACpF,OAAO,EAAE;IACd,MAAMgC,MAAM,GAAGhE,aAAa,CAACqH,WAAW,CAACrF,OAAO,EAAE,GAAGA,OAAO,CAACsF,KAAK,CAACC,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,EAAE,CAAC;IACnG,IAAIvD,MAAM,CAACwD,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAGxH,mBAAmB,CAACgE,WAAW,CAAC,aAAa,CAAC;MAChE,IAAIwD,SAAS,CAACvD,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxCsD,SAAS,CAACvD,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/CpE,mBAAmB,CAACqE,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAMqD,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAAC1F,OAAO,EAAE;IACd,MAAMgC,MAAM,GAAGhE,aAAa,CAAC2H,YAAY,CAAC3F,OAAO,EAAE,GAAGA,OAAO,CAACsF,KAAK,CAACC,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,EAAE,CAAC;IACpG,IAAIvD,MAAM,CAACwD,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAGxH,mBAAmB,CAACgE,WAAW,CAAC,aAAa,CAAC;MAChE,IAAIwD,SAAS,CAACvD,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxCsD,SAAS,CAACvD,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/CpE,mBAAmB,CAACqE,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAMuD,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI,CAAC5F,OAAO,EAAE;IACd,MAAMgC,MAAM,GAAG,MAAMhE,aAAa,CAAC6H,eAAe,CAAC7F,OAAO,CAAC8E,OAAO,CAAC;IACnE9G,aAAa,CAAC8H,WAAW,CAAC9D,MAAM,CAAC2B,OAAO,EAAE3B,MAAM,CAACwD,OAAO,GAAG,SAAS,GAAG,OAAO,CAAC;IAC/E,IAAIxD,MAAM,CAACwD,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAGxH,mBAAmB,CAACgE,WAAW,CAAC,aAAa,CAAC;MAChE,IAAIwD,SAAS,CAACvD,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxCsD,SAAS,CAACvD,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/CpE,mBAAmB,CAACqE,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;;EAED;EACA,MAAMjB,wBAAwB,GAAG,MAAAA,CAAOP,MAAM,EAAEkF,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK;IACjE,IAAI,CAAC1G,SAAS,EAAE;IAEhBG,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF0B,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEN,MAAM,CAACmF,IAAI,CAAC;MAC7D,MAAMC,WAAW,GAAG,MAAM5H,kBAAkB,CAACgB,SAAS,CAACF,KAAK,EAAE0B,MAAM,EAAEkF,KAAK,CAAC;;MAE5E;MACAxH,UAAU,CAACqF,eAAe,CAACvE,SAAS,CAACyC,EAAE,EAAE,WAAW,EAAE;QACpD9B,OAAO,EAAEiG;MACX,CAAC,CAAC;MAEF3G,YAAY,CAACf,UAAU,CAACwF,MAAM,CAAC1E,SAAS,CAACyC,EAAE,CAAC,CAAC;MAC7C5C,cAAc,CAAC,SAAS,CAAC;;MAEzB;MACA,MAAM8C,MAAM,GAAG/D,mBAAmB,CAACgE,WAAW,CAAC,mBAAmB,CAAC;MACnE,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5CpE,mBAAmB,CAACqE,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAO5C,KAAK,EAAE;MACdyB,OAAO,CAACzB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDC,QAAQ,CAAC,+CAA+C,CAAC;IAC3D,CAAC,SAAS;MACRF,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAM0G,MAAM,GAAGA,CAAA,KAAM;IACnB,IAAIjH,WAAW,KAAK,SAAS,EAAE;MAC7BC,cAAc,CAAC,MAAM,CAAC;MACtB,IAAIG,SAAS,EAAE;QACbd,UAAU,CAACqF,eAAe,CAACvE,SAAS,CAACyC,EAAE,EAAEzC,SAAS,CAACmF,MAAM,EAAE;UAAExE,OAAO,EAAE;QAAK,CAAC,CAAC;QAC7EV,YAAY,CAACf,UAAU,CAACwF,MAAM,CAAC1E,SAAS,CAACyC,EAAE,CAAC,CAAC;MAC/C;IACF,CAAC,MAAM,IAAI7C,WAAW,KAAK,MAAM,EAAE;MACjCC,cAAc,CAAC,OAAO,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMsC,YAAY,GAAG,MAAAA,CAAOX,MAAM,EAAEsF,WAAW,KAAK;IAClD3G,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MAAA,IAAA0G,cAAA,EAAAC,qBAAA;MACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,+CAA+C,EAAE;QAC5EC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,OAAO,CAACC,GAAG,CAACC,4BAA4B,EAAE;UACrE,cAAc,EAAE,kBAAkB;UAClC,cAAc,EAAEC,MAAM,CAACC,QAAQ,CAACC,MAAM;UACtC,SAAS,EAAE;QACb,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,KAAK,EAAE,gCAAgC;UACvCC,QAAQ,EAAE,CAAC;YACTC,IAAI,EAAE,MAAM;YACZvC,OAAO,EAAE,qBAAqBjE,MAAM,CAACmF,IAAI,0BAA0BlG,IAAI,CAACwH,IAAI;AACxF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+DAA+DzG,MAAM,CAACmF,IAAI;UAChE,CAAC,CAAC;UACFuB,WAAW,EAAE,GAAG;UAChBC,UAAU,EAAE;QACd,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAAClB,QAAQ,CAACmB,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,cAAcpB,QAAQ,CAAC9B,MAAM,EAAE,CAAC;MAClD;MAEA,MAAMmD,IAAI,GAAG,MAAMrB,QAAQ,CAACsB,IAAI,CAAC,CAAC;MAClC,MAAM9C,OAAO,IAAAsB,cAAA,GAAGuB,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,cAAAzB,cAAA,wBAAAC,qBAAA,GAAfD,cAAA,CAAiBzC,OAAO,cAAA0C,qBAAA,uBAAxBA,qBAAA,CAA0BvB,OAAO;MAEjD,IAAI,CAACA,OAAO,EAAE;QACZ,MAAM,IAAI4C,KAAK,CAAC,8BAA8B,CAAC;MACjD;;MAEA;MACA,MAAMI,SAAS,GAAGhD,OAAO,CAACiD,KAAK,CAAC,aAAa,CAAC;MAC9C,IAAI,CAACD,SAAS,EAAE;QACd,MAAM,IAAIJ,KAAK,CAAC,iCAAiC,CAAC;MACpD;MAEA,MAAMM,YAAY,GAAGf,IAAI,CAACgB,KAAK,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC;;MAE7C;MACA,MAAMI,OAAO,GAAG;QAAE,GAAGpI;MAAK,CAAC;MAC3BoI,OAAO,CAACpH,MAAM,GAAG,CACf,GAAGoH,OAAO,CAACpH,MAAM,CAACqH,KAAK,CAAC,CAAC,EAAEhC,WAAW,GAAG,CAAC,CAAC,EAC3C,GAAG6B,YAAY,CAAClH,MAAM,CAACsH,GAAG,CAACC,SAAS,KAAK;QACvC,GAAGA,SAAS;QACZC,WAAW,EAAE,IAAI;QACjBC,YAAY,EAAE1H,MAAM,CAACmF,IAAI;QACzBwC,KAAK,EAAE,CAAC3H,MAAM,CAAC2H,KAAK,IAAI,CAAC,IAAI;MAC/B,CAAC,CAAC,CAAC,EACH,GAAGN,OAAO,CAACpH,MAAM,CAACqH,KAAK,CAAChC,WAAW,GAAG,CAAC,CAAC,CACzC;;MAED;MACA,IAAI9G,SAAS,EAAE;QACbd,UAAU,CAACqF,eAAe,CAACvE,SAAS,CAACyC,EAAE,EAAE,WAAW,EAAE;UAAEhC,IAAI,EAAEoI;QAAQ,CAAC,CAAC;QACxE5I,YAAY,CAACf,UAAU,CAACwF,MAAM,CAAC1E,SAAS,CAACyC,EAAE,CAAC,CAAC;MAC/C;;MAEA;MACA,MAAME,MAAM,GAAG/D,mBAAmB,CAACgE,WAAW,CAAC,iBAAiB,CAAC;MACjE,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5CpE,mBAAmB,CAACqE,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IAEF,CAAC,CAAC,OAAO5C,KAAK,EAAE;MACdyB,OAAO,CAACzB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAACO,CAAC,CAAC,gBAAgB,CAAC,CAAC;IAC/B,CAAC,SAAS;MACRT,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMiJ,MAAM,GAAGA,CAAA,KAAM;IACnBvJ,cAAc,CAAC,OAAO,CAAC;IACvBI,YAAY,CAAC,IAAI,CAAC;IAClBF,QAAQ,CAAC,EAAE,CAAC;IACZ;IACAb,UAAU,CAACmK,YAAY,CAAC,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBjH,YAAY,CAACkH,OAAO,CAAC,gBAAgB,EAAE,MAAM,CAAC;IAC9ChJ,OAAO,CAAC;MAAEkC,EAAE,EAAE,OAAO;MAAEzB,IAAI,EAAE,WAAW;MAAE0B,gBAAgB,EAAE;IAAU,CAAC,CAAC;EAC1E,CAAC;EAED,oBACEnD,OAAA;IAAKiK,SAAS,EAAC,KAAK;IAACC,GAAG,EAAEjJ,MAAO;IAAAkJ,QAAA,gBAE/BnK,OAAA;MAAQiK,SAAS,EAAC,YAAY;MAAAE,QAAA,eAC5BnK,OAAA;QAAKiK,SAAS,EAAC,gBAAgB;QAAAE,QAAA,gBAC7BnK,OAAA;UAAQoK,OAAO,EAAEP,MAAO;UAACI,SAAS,EAAC,WAAW;UAAAE,QAAA,EAC3C9I,CAAC,CAAC,UAAU;QAAC;UAAAgJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACTxK,OAAA;UAAKiK,SAAS,EAAC,cAAc;UAAAE,QAAA,GAC1BpJ,IAAI,iBACHf,OAAA;YAAKkD,EAAE,EAAC,wBAAwB;YAACuH,KAAK,EAAE;cAAEC,WAAW,EAAE;YAAO;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE3D,CACN,eACDxK,OAAA,CAACH,gBAAgB;YAAAwK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACnB,CAACzJ,IAAI,gBACJf,OAAA;YAAQoK,OAAO,EAAEL,UAAW;YAACE,SAAS,EAAC,iBAAiB;YAACQ,KAAK,EAAE;cAAEE,UAAU,EAAE;YAAO,CAAE;YAAAR,QAAA,EACpF9I,CAAC,CAAC,YAAY;UAAC;YAAAgJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,gBAETxK,OAAA;YAAMyK,KAAK,EAAE;cAAEE,UAAU,EAAE;YAAO,CAAE;YAAAR,QAAA,GAAE9I,CAAC,CAAC,SAAS,CAAC,EAAC,IAAE,EAACN,IAAI,CAACU,IAAI,EAAC,GAAC;UAAA;YAAA4I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACxE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAGRzJ,IAAI,iBACHf,OAAA,CAACJ,UAAU;MACTgL,WAAW,EAAEnF,eAAgB;MAC7BoF,QAAQ,EAAElF;IAAa;MAAA0E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CACF,eAGDxK,OAAA;MAAMiK,SAAS,EAAC,cAAc;MAAAE,QAAA,GAC3BtJ,KAAK,iBACJb,OAAA;QAAKiK,SAAS,EAAC,OAAO;QAAAE,QAAA,GAAC,eAClB,EAACtJ,KAAK,eACTb,OAAA;UAAQoK,OAAO,EAAEA,CAAA,KAAMtJ,QAAQ,CAAC,IAAI,CAAE;UAAC2J,KAAK,EAAE;YAACE,UAAU,EAAE,MAAM;YAAEG,UAAU,EAAE,MAAM;YAAEC,MAAM,EAAE,MAAM;YAAEC,KAAK,EAAE,OAAO;YAAEC,MAAM,EAAE;UAAS,CAAE;UAAAd,QAAA,EAAC;QAE3I;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAGAnK,WAAW,KAAK,OAAO,iBACtBL,OAAA;QAAKiK,SAAS,EAAC,kBAAkB;QAAAE,QAAA,gBAC/BnK,OAAA;UAAIiK,SAAS,EAAC,OAAO;UAAAE,QAAA,EAAE9I,CAAC,CAAC,UAAU;QAAC;UAAAgJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1CxK,OAAA;UAAGiK,SAAS,EAAC,UAAU;UAAAE,QAAA,EAAC;QAExB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAEH,CAACzJ,IAAI,gBACJf,OAAA;UAAKyK,KAAK,EAAE;YAACK,UAAU,EAAE,SAAS;YAAEI,OAAO,EAAE,MAAM;YAAEC,YAAY,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAM,CAAE;UAAAjB,QAAA,gBACjGnK,OAAA;YAAGyK,KAAK,EAAE;cAACO,KAAK,EAAE,SAAS;cAAEI,YAAY,EAAE;YAAM,CAAE;YAAAjB,QAAA,EAChD9I,CAAC,CAAC,eAAe;UAAC;YAAAgJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACJxK,OAAA;YAAQoK,OAAO,EAAEL,UAAW;YAACE,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EACrD9I,CAAC,CAAC,eAAe;UAAC;YAAAgJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAENxK,OAAA;UAAMqL,QAAQ,EAAEhG,YAAa;UAAA8E,QAAA,gBAC3BnK,OAAA;YAAKiK,SAAS,EAAC,YAAY;YAAAE,QAAA,eACzBnK,OAAA;cACEsL,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEhL,KAAM;cACbiL,QAAQ,EAAGlG,CAAC,IAAK9E,QAAQ,CAAC8E,CAAC,CAACmG,MAAM,CAACF,KAAK,CAAE;cAC1CG,WAAW,EAAErK,CAAC,CAAC,kBAAkB,CAAE;cACnC4I,SAAS,EAAC,YAAY;cACtB0B,QAAQ,EAAEhL;YAAU;cAAA0J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNxK,OAAA;YACEsL,IAAI,EAAC,QAAQ;YACbK,QAAQ,EAAEhL,SAAS,IAAI,CAACJ,KAAK,CAACiF,IAAI,CAAC,CAAE;YACrCyE,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EAE1BxJ,SAAS,gBACRX,OAAA,CAAAE,SAAA;cAAAiK,QAAA,gBACEnK,OAAA;gBAAMiK,SAAS,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAChCnJ,CAAC,CAAC,YAAY,CAAC;YAAA,eAChB,CAAC,gBAEHrB,OAAA,CAAAE,SAAA;cAAAiK,QAAA,EACG9I,CAAC,CAAC,kBAAkB;YAAC,gBACtB;UACH;YAAAgJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGAnK,WAAW,KAAK,MAAM,IAAIa,IAAI,iBAC7BlB,OAAA;QAAKiK,SAAS,EAAC,gBAAgB;QAAAE,QAAA,gBAC7BnK,OAAA;UAAKiK,SAAS,EAAC,aAAa;UAAAE,QAAA,gBAC1BnK,OAAA;YAAAmK,QAAA,EAAKjJ,IAAI,CAACwH;UAAI;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpBxK,OAAA;YAAAmK,QAAA,EAAI9I,CAAC,CAAC,cAAc;UAAC;YAAAgJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BxK,OAAA;YAAQoK,OAAO,EAAE9C,MAAO;YAAC2C,SAAS,EAAC,mBAAmB;YAACQ,KAAK,EAAE;cAACmB,SAAS,EAAE;YAAM,CAAE;YAAAzB,QAAA,EAC/E9I,CAAC,CAAC,YAAY;UAAC;YAAAgJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAEL7J,SAAS,gBACRX,OAAA;UAAKiK,SAAS,EAAC,SAAS;UAAAE,QAAA,gBACtBnK,OAAA;YAAMiK,SAAS,EAAC;UAAS;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjCxK,OAAA;YAAAmK,QAAA,EAAO9I,CAAC,CAAC,SAAS;UAAC;YAAAgJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,gBAENxK,OAAA;UAAKiK,SAAS,EAAC,eAAe;UAAAE,QAAA,EAC3BjJ,IAAI,CAACgB,MAAM,CAACsH,GAAG,CAAC,CAACvH,MAAM,EAAEE,KAAK,kBAC7BnC,OAAA;YAEEiK,SAAS,EAAE,eAAe9I,cAAc,KAAKc,MAAM,GAAG,UAAU,GAAG,EAAE,EAAG;YACxE,cAAYE,KAAM;YAClB,aAAWF,MAAM,CAACmF,IAAK;YACvB,oBAAkBnF,MAAM,CAAC4J,SAAU;YACnC,sBAAoB5J,MAAM,CAACyH,WAAW,IAAI,KAAM;YAChD,cAAYzH,MAAM,CAAC2H,KAAK,IAAI,CAAE;YAC9BQ,OAAO,EAAEA,CAAA,KAAM1H,kBAAkB,CAACT,MAAM,CAAE;YAAAkI,QAAA,gBAE1CnK,OAAA;cAAKiK,SAAS,EAAC,cAAc;cAAAE,QAAA,EAAElI,MAAM,CAAC6J;YAAK;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClDxK,OAAA;cAAIiK,SAAS,EAAC,aAAa;cAAAE,QAAA,EAAElI,MAAM,CAACmF;YAAI;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9CxK,OAAA;cAAGiK,SAAS,EAAC,oBAAoB;cAAAE,QAAA,EAAElI,MAAM,CAAC4J;YAAS;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACvDvI,MAAM,CAAC8J,YAAY,iBAClB/L,OAAA;cAAKyK,KAAK,EAAE;gBAACuB,QAAQ,EAAE,UAAU;gBAAEhB,KAAK,EAAE,SAAS;gBAAEY,SAAS,EAAE;cAAQ,CAAE;cAAAzB,QAAA,GACvE9I,CAAC,CAAC,QAAQ,CAAC,EAAC,IAAE,EAACY,MAAM,CAAC8J,YAAY,CAACxC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC0C,IAAI,CAAC,IAAI,CAAC,EACzDhK,MAAM,CAAC8J,YAAY,CAACxI,MAAM,GAAG,CAAC,IAAI,KAAK;YAAA;cAAA8G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CACN,eACDxK,OAAA;cAAKiK,SAAS,EAAC,cAAc;cAACQ,KAAK,EAAE;gBACnCuB,QAAQ,EAAE,SAAS;gBACnBhB,KAAK,EAAE,SAAS;gBAChBY,SAAS,EAAE,QAAQ;gBACnBM,SAAS,EAAE;cACb,CAAE;cAAA/B,QAAA,EACC9I,CAAC,CAAC,aAAa;YAAC;cAAAgJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC;UAAA,GAzBDrI,KAAK;YAAAkI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0BP,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGAnK,WAAW,KAAK,SAAS,IAAIe,OAAO,iBACnCpB,OAAA;QAAKiK,SAAS,EAAC,gBAAgB;QAAAE,QAAA,eAC7BnK,OAAA;UAAKiK,SAAS,EAAC,MAAM;UAAAE,QAAA,gBACnBnK,OAAA;YAAKiK,SAAS,EAAC,gBAAgB;YAACQ,KAAK,EAAE;cAACW,YAAY,EAAE;YAAM,CAAE;YAAAjB,QAAA,gBAC5DnK,OAAA;cAAQoK,OAAO,EAAE9C,MAAO;cAAC2C,SAAS,EAAC,mBAAmB;cAAAE,QAAA,EACnD9I,CAAC,CAAC,YAAY;YAAC;cAAAgJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGTxK,OAAA;cAAKiK,SAAS,EAAC,kBAAkB;cAACQ,KAAK,EAAE;gBACvC0B,OAAO,EAAE,MAAM;gBACfC,GAAG,EAAE,KAAK;gBACVR,SAAS,EAAE,MAAM;gBACjBS,QAAQ,EAAE;cACZ,CAAE;cAAAlC,QAAA,gBAEAnK,OAAA;gBAAKiK,SAAS,EAAC,yBAAyB;gBAACQ,KAAK,EAAE;kBAC9C0B,OAAO,EAAE,MAAM;kBACfG,UAAU,EAAE,QAAQ;kBACpBF,GAAG,EAAE,KAAK;kBACVlB,OAAO,EAAE,UAAU;kBACnBJ,UAAU,EAAE,SAAS;kBACrBK,YAAY,EAAE,KAAK;kBACnBJ,MAAM,EAAE;gBACV,CAAE;gBAAAZ,QAAA,gBACAnK,OAAA;kBACEoK,OAAO,EAAEvE,kBAAmB;kBAC5BoE,SAAS,EAAC,UAAU;kBACpBvD,KAAK,EAAC,mBAAmB;kBACzB+D,KAAK,EAAE;oBACLK,UAAU,EAAE,MAAM;oBAClBC,MAAM,EAAE,MAAM;oBACdiB,QAAQ,EAAE,MAAM;oBAChBf,MAAM,EAAE,SAAS;oBACjBC,OAAO,EAAE;kBACX,CAAE;kBAAAf,QAAA,EAEDhL,aAAa,CAAC2G,SAAS,CAAC,CAAC,CAACC,SAAS,GAAG,IAAI,GAAG;gBAAI;kBAAAsE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,eACTxK,OAAA;kBACEoK,OAAO,EAAEjE,gBAAiB;kBAC1B8D,SAAS,EAAC,UAAU;kBACpBvD,KAAK,EAAC,aAAa;kBACnB+D,KAAK,EAAE;oBACLK,UAAU,EAAE,MAAM;oBAClBC,MAAM,EAAE,MAAM;oBACdiB,QAAQ,EAAE,MAAM;oBAChBf,MAAM,EAAE,SAAS;oBACjBC,OAAO,EAAE;kBACX,CAAE;kBAAAf,QAAA,EACH;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTxK,OAAA;kBACEsL,IAAI,EAAC,OAAO;kBACZiB,GAAG,EAAC,KAAK;kBACTC,GAAG,EAAC,GAAG;kBACPC,IAAI,EAAC,KAAK;kBACVC,YAAY,EAAC,GAAG;kBAChBlB,QAAQ,EAAGlG,CAAC,IAAKe,sBAAsB,CAACsG,UAAU,CAACrH,CAAC,CAACmG,MAAM,CAACF,KAAK,CAAC,CAAE;kBACpEd,KAAK,EAAE;oBAACmC,KAAK,EAAE;kBAAM,CAAE;kBACvBlG,KAAK,EAAC;gBAAc;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACFxK,OAAA;kBAAMyK,KAAK,EAAE;oBAACuB,QAAQ,EAAE,MAAM;oBAAEhB,KAAK,EAAE;kBAAS,CAAE;kBAAAb,QAAA,EAAC;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eAGNxK,OAAA;gBAAKiK,SAAS,EAAC,yBAAyB;gBAACQ,KAAK,EAAE;kBAC9C0B,OAAO,EAAE,MAAM;kBACfC,GAAG,EAAE;gBACP,CAAE;gBAAAjC,QAAA,gBACAnK,OAAA;kBACEoK,OAAO,EAAEpD,qBAAsB;kBAC/BiD,SAAS,EAAC,mBAAmB;kBAC7BQ,KAAK,EAAE;oBAACS,OAAO,EAAE,UAAU;oBAAEc,QAAQ,EAAE;kBAAM,CAAE;kBAC/CtF,KAAK,EAAC,mBAAmB;kBAAAyD,QAAA,EAC1B;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTxK,OAAA;kBACEoK,OAAO,EAAE5D,eAAgB;kBACzByD,SAAS,EAAC,mBAAmB;kBAC7BQ,KAAK,EAAE;oBAACS,OAAO,EAAE,UAAU;oBAAEc,QAAQ,EAAE;kBAAM,CAAE;kBAC/CtF,KAAK,EAAC,eAAe;kBAAAyD,QAAA,EACtB;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTxK,OAAA;kBACEoK,OAAO,EAAEtD,gBAAiB;kBAC1BmD,SAAS,EAAC,mBAAmB;kBAC7BQ,KAAK,EAAE;oBAACS,OAAO,EAAE,UAAU;oBAAEc,QAAQ,EAAE;kBAAM,CAAE;kBAC/CtF,KAAK,EAAC,gBAAgB;kBAAAyD,QAAA,EACvB;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxK,OAAA;YAAIiK,SAAS,EAAC,OAAO;YAAAE,QAAA,EAAE/I,OAAO,CAACsF;UAAK;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1CxK,OAAA;YAAKyK,KAAK,EAAE;cAACO,KAAK,EAAE,SAAS;cAAEI,YAAY,EAAE,MAAM;cAAEY,QAAQ,EAAE;YAAQ,CAAE;YAAA7B,QAAA,gBACvEnK,OAAA;cAAAmK,QAAA,GAAO9I,CAAC,CAAC,QAAQ,CAAC,EAAC,IAAE,EAACD,OAAO,CAACb,KAAK;YAAA;cAAA8J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAC1CpJ,OAAO,CAAC+F,KAAK,IAAI/F,OAAO,CAAC+F,KAAK,CAAC5D,MAAM,GAAG,CAAC,iBACxCvD,OAAA;cAAMyK,KAAK,EAAE;gBAACE,UAAU,EAAE;cAAM,CAAE;cAAAR,QAAA,GAC/B9I,CAAC,CAAC,OAAO,CAAC,EAAC,IAAE,EAACD,OAAO,CAAC+F,KAAK,CAAC8E,IAAI,CAAC,IAAI,CAAC;YAAA;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENxK,OAAA;YAAKiK,SAAS,EAAC,iBAAiB;YAACQ,KAAK,EAAE;cAACoC,UAAU,EAAE,KAAK;cAAEb,QAAQ,EAAE;YAAQ,CAAE;YAAA7B,QAAA,EAC7E/I,OAAO,CAAC8E,OAAO,CAAC4G,KAAK,CAAC,IAAI,CAAC,CAACtD,GAAG,CAAC,CAACuD,SAAS,EAAE5K,KAAK,KAChD4K,SAAS,CAACvH,IAAI,CAAC,CAAC,iBACdxF,OAAA;cAAeyK,KAAK,EAAE;gBAACW,YAAY,EAAE;cAAM,CAAE;cAAAjB,QAAA,EAC1C4C;YAAS,GADJ5K,KAAK;cAAAkI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CAEN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACpK,EAAA,CA5sBID,YAAY;EAAA,QAgBFL,cAAc;AAAA;AAAAkN,EAAA,GAhBxB7M,YAAY;AA8sBlB,eAAeA,YAAY;AAAC,IAAA6M,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}